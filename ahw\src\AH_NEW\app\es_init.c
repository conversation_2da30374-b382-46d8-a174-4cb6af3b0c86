#include "es_inc.h"
#include "facelib_inc.h"

// #define ES_INIT_DEBUG
#ifdef ES_INIT_DEBUG
#define es_init_debug es_log_info
#define es_init_error es_log_error
#else
#define es_init_debug(...)
#define es_init_error(...)
#endif

static uint8_t model_need_active = 0;

static ES_S32 es_core1_init(ES_VOID)
{
    if (ES_RET_SUCCESS != es_network_init()) {
        es_init_debug("network init fail");
        return ES_RET_FAILURE;
    }

    return ES_RET_SUCCESS;
}

static ES_VOID es_system_task(ES_VOID)
{
    es_network_task();
#if ES_RELAY_ENABLE
    es_relay_run();
#endif

#if ES_DOOR_ENABLE
    es_door_run();
#endif

#if ES_PASSLOG_ENABLE
    if (ES_OTA_STATUS_IDLE == es_ota_get_status()) {
        es_passlog_try_upload();
    }
#endif

#if ES_AUDIO_ENABLE
    es_audio_run();
#endif
    es_task_queue_core1_run();
#if ES_KEY_MODULE_ENABLE
    es_key_task();
#endif
#if ES_OTA_DL_ENABLE
    es_ota_dl_run();
#endif

#if ES_ENABLE_SPEC_CAR
    es_spec_car_run();
    es_safetybelt_check();
#endif

#if ES_MODEL_ACTIVE_ENABLE
    if (1 == model_need_active) {
        es_model_run();
    }
#endif
}

static ES_VOID es_check_detect_flow(ES_VOID)
{
    static ES_BOOL startup = ES_FALSE;
#if ES_OTA_ENABLE
    if (ES_OTA_STATUS_IDLE != es_ota_get_status()) {
        mf_flow.enable = 0;
        es_ota_run();
        return;
    }
#endif

    if (!startup) {
        if (es_time_get_sytem_ms() > 10*1000) {
            startup = ES_TRUE;
        }
        mf_flow.enable = 0;
        return;
    }

    if (es_facedb_runing()) {
        mf_flow.enable = 0;
        return;
    }

    if (!es_spec_car_is_face_flow()) {
        mf_flow.enable = 0;
        return;
    }

    if (0 == model_need_active) {
        mf_flow.enable = 1;
    }

    if (mf_brd.cfg.factory_flag) {
        mf_flow.enable = 0;
        return;
    }
}

extern void camera_lock_650(void);
extern void camera_unlock_650(void);
extern mf_flow_t mf_flow_display;

// core 0 task
static ES_VOID core0_task(ES_VOID)
{
    uint8_t loop_type = 0; // 0: normal, 1: display.
    uint8_t dis_loop_cnt = 0;
    uint8_t nor_loop_cnt = 0;

    es_voice_play(ES_VOICE_02_CAR_BOOT);

    while (1) {
        es_wdg_feed(0);
        es_check_detect_flow();

        // uint64_t loop_start = sysctl_get_time_us();

        if(0x00 == loop_type) {
            mf_flow.loop();

            nor_loop_cnt ++;
            if(nor_loop_cnt >= 1) {
                nor_loop_cnt = 0;
                loop_type = 1;
                camera_lock_650();
            }
            // es_init_debug("loop RGB+IR %ld\n", sysctl_get_time_us() - loop_start);
        } else {
            mf_flow_display.loop();
            dis_loop_cnt++;
            if(dis_loop_cnt >= 2) {
                dis_loop_cnt = 0;
                loop_type = 0;
                camera_unlock_650();
            }
            // es_init_debug("loop RGB %ld\n", sysctl_get_time_us() - loop_start);
        }

        es_ui_refresh();
        es_task_queue_core0_run();
#if ES_CAPTURE_ENABLE
        es_capture_run();
#endif
#if ES_FLASH_MODULE_ENABLE
        es_flash_run();
#endif
    }
}


// core 1 task
static ES_S32 core1_task(void *ctx)
{
    if (ES_RET_SUCCESS != es_core1_init()) {
        printk("core1 init fail.");
        while(1);
    }

    while (1) {
        es_wdg_feed(1);
        es_system_task();
        msleep(5);
    }
    return 0;
}

static ES_S32 es_app_init_components(ES_VOID)
{
#if ES_DEBUG_LOG_ENABLE
    if (ES_RET_SUCCESS != es_log_init(ES_NULL)) {
        return ES_RET_FAILURE;
    }
#endif

#if ES_FS_ENABLE
    if (ES_RET_SUCCESS != es_fs_init()) {
        es_init_debug("fs init fail");
        return ES_RET_FAILURE;
    }
#endif

    if (ES_RET_SUCCESS != es_time_init()) {
        es_init_debug("time init fail");
        return ES_RET_FAILURE;
    }

    if (ES_RET_SUCCESS != es_task_queue_init()) {
        es_init_debug("task queue init fail");
        return ES_RET_FAILURE;
    }

#if ES_KEY_MODULE_ENABLE
    if (ES_RET_SUCCESS != es_key_init()) {
        es_init_debug("key init fail");
        return ES_RET_FAILURE;
    }
#endif

#if ES_PASSLOG_ENABLE
    if (ES_RET_SUCCESS != es_passlog_init()) {
        es_init_debug("passlog init fail");
        return ES_RET_FAILURE;
    }
#endif

#if ES_FLASH_MODULE_ENABLE
    if (ES_RET_SUCCESS != es_flash_init()) {
        es_init_debug("config flash init fail");
        return ES_RET_FAILURE;
    }

    if (ES_RET_SUCCESS != es_dev_cfg_init()) {
        es_init_debug("dev config init fail");
        return ES_RET_FAILURE;
    }
#endif

    if (ES_RET_SUCCESS != es_ui_init()) {
        es_init_debug("ui init fail");
        return ES_RET_FAILURE;
    }

#if ES_RESOURCE_ENABLE
    if (ES_RET_SUCCESS != es_resource_init()) {
         es_init_debug("resource init fail");
        return ES_RET_FAILURE;
    }
#endif

#if ES_RELAY_ENABLE
    if (ES_RET_SUCCESS != es_relay_init()) {
        es_init_debug("relay init fail");
        return ES_RET_FAILURE;
    }
#endif

#if ES_DOOR_ENABLE
    if (ES_RET_SUCCESS != es_door_init()) {
        es_init_debug("door init fail");
        return ES_RET_FAILURE;
    }
#endif

#if ES_AUDIO_ENABLE
    if (ES_RET_SUCCESS != es_audio_init()) {
        es_init_debug("relay init fail");
        return ES_RET_FAILURE;
    }
#endif

    if (ES_RET_SUCCESS != es_serv_proto_init()) {
        es_init_debug("server protocol init fail");
        return ES_RET_FAILURE;
    }

#if ES_OTA_DL_ENABLE
    if (ES_RET_SUCCESS != es_ota_dl_init()) {
        es_init_debug("ota init download fail");
        return ES_RET_FAILURE;
    }
#endif

#if ES_OTA_ENABLE
    if (ES_RET_SUCCESS != es_ota_init()) {
        es_init_debug("ota init fail");
        return ES_RET_FAILURE;
    }
#endif

#if ES_CAPTURE_ENABLE
    if (ES_RET_SUCCESS != es_capture_init()) {
        es_init_debug("capture init fail");
        return ES_RET_FAILURE;
    }
#endif

#if ES_ENABLE_SPEC_CAR
    if (ES_RET_SUCCESS != es_spec_car_init()) {
        es_init_debug("spec car init fail");
        return ES_RET_FAILURE;
    }
    es_safetybelt_init();
#endif
    return ES_RET_SUCCESS;
}

static ES_S32 es_app_init_hal(ES_VOID)
{
    mf_camera_choose(MF_CAM_GC0328_DUAL);
    mf_cam.init(ES_CAM_DIR, ES_CAM_EXP_TIME);

    if (ES_RET_SUCCESS != es_uart_init()) {
        return ES_RET_FAILURE;
    }
    
    if (ES_RET_SUCCESS != es_hal_i2c_init()) {
        return ES_RET_FAILURE;
    }

    if (ES_RET_SUCCESS != es_hal_lcd_init()) {
        return ES_RET_FAILURE;
    }
    es_hal_lcd_clear(ES_UI_BOOT_BG_COLOR);

    if (ES_RET_SUCCESS != es_hal_ir_init()) {
        return ES_RET_FAILURE;
    }

    if (ES_RET_SUCCESS != es_hal_led_init()) {
        return ES_RET_FAILURE;
    }

    if (ES_RET_SUCCESS != es_hal_rtc_init()) {
        return ES_RET_FAILURE;
    }

    return ES_RET_SUCCESS;
}

static ES_S32 es_app_init_facelib(ES_VOID)
{
    mf_err_t err = MF_ERR_NONE;

    err = mf_flash.init(3, 0, 60000000);
    if(MF_ERR_NONE != err) {
        es_init_error("flash init fail\r\n");
        return ES_RET_FAILURE;
    }

    mf_brd.init(0, ES_NULL);
     if(MF_ERR_NONE != err) {
        es_init_error("board init fail\r\n");
        return ES_RET_FAILURE;
    }

    mf_facedb_init();
	err = mf_facedb.init(ES_FLASH_FACE_DB_ADDR); 
	if(MF_ERR_NONE != err) {
        es_init_error("facedb init fail\r\n");
        return ES_RET_FAILURE;
    }

    err = mf_model.init(MF_MDL_ALL);
    if(MF_ERR_NONE == err) {
        es_init_debug("## mf_model init ok!");
    } else if(MF_ERR_MDL == err) {
        es_init_error("the chip is not activation");
        // return ES_RET_FAILURE;
        model_need_active = 1;
    } else {
        es_init_error("model init error");
        return ES_RET_FAILURE;
    }

#if ES_MODEL_ACTIVE_ENABLE
    es_model_init(model_need_active);
#endif

    extern mf_facecb_t es_facecb;
    mf_flow_choose(MF_FLOW_DUALCAM_VIS2VIS);
    err = mf_flow.init(&es_facecb);
    if(MF_ERR_NONE != err) {
        es_init_error("flow init error");
        mf_flow.enable = 0;
    } else {
        mf_flow.enable = 0;
        // if (0 == model_need_active) {
        //     mf_flow.enable = 1;
        // }
    }

    return ES_RET_SUCCESS;
}


ES_S32 es_app_init(ES_VOID)
{
    if (ES_RET_SUCCESS != es_bsp_init()) {
        return ES_RET_FAILURE;
    }

    if (ES_RET_SUCCESS != es_mem_init()) {
        return ES_RET_FAILURE;
    }

    if (ES_RET_SUCCESS != es_app_init_hal()) {
        return ES_RET_FAILURE;
    }

    if (ES_RET_SUCCESS != es_app_init_facelib()) {
        return ES_RET_FAILURE;
    }

    if (ES_RET_SUCCESS != es_app_init_components()) {
        return ES_RET_FAILURE;
    }

    // 
    register_core1(core1_task, NULL);
    
    return ES_RET_SUCCESS;
}


ES_VOID es_app_run(ES_VOID)
{
    es_init_debug("es_app_run");
    core0_task(); // no return
}
