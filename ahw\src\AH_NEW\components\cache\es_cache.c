/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_cache.c
** bef: implement the interface for cache.
** auth: lines<<EMAIL>>
** create on 2020.06.26 
*/

#include "es_inc.h"

// #define ES_CACHE_DEBUG
#ifdef ES_CACHE_DEBUG
#define es_cache_debug es_log_info
#define es_cache_error es_log_error
#else
#define es_cache_debug(...)
#define es_cache_error(...)
#endif

#define CACHE_MAX_SIZE          (1024*1024)

typedef struct {
    ES_U32  buf_size;
    ES_BYTE *buf;
} es_cache_t;

ES_VOID *es_cache_create(ES_U32 size)
{
    es_cache_t *c = ES_NULL;

    if (size == 0 || size > CACHE_MAX_SIZE) {
        return ES_NULL;
    }

    c = (es_cache_t *)es_malloc(sizeof(es_cache_t));
    if (c == ES_NULL) {
        return ES_NULL;
    }
    es_memset(c, 0x00, sizeof(es_cache_t));
    c->buf_size = size;

    c->buf = (ES_BYTE *)es_malloc(size+1);
    if (c->buf == ES_NULL) {
        es_cache_destroy(c);
        return ES_NULL;
    }
    es_memset(c->buf, 0x00, size+1);

    return (ES_VOID *)c;
}

ES_S32 es_cache_read(const ES_VOID *cache, ES_U32 offset, ES_BYTE *data, ES_U32 len)
{
    es_cache_t *c = (es_cache_t *)cache;
    ES_S32 i;

    if (c == ES_NULL) {
        return ES_RET_FAILURE;
    }

    for (i = 0; i < len; i++) {
        if ((offset + i) >= c->buf_size) {
            break;
        }

        data[i] = c->buf[offset + i];
    }

    return i;
}

const ES_BYTE *es_cache_get_buf_addr(const ES_VOID *cache)
{
    es_cache_t *c = (es_cache_t *)cache;

    if (c == ES_NULL) {
        return ES_NULL;
    }

    return c->buf;
}

ES_S32 es_cache_write(const ES_VOID *cache, ES_U32 offset, const ES_BYTE *data, ES_U32 len)
{
    es_cache_t *c = (es_cache_t *)cache;
    ES_S32 i;

    if (c == ES_NULL) {
        return ES_RET_FAILURE;
    }

    for (i = 0; i < len; i++) {
        if ((offset + i) >= c->buf_size) {
            break;
        }

        c->buf[offset + i] = data[i];
    }

    return i;
}

ES_S32 es_cache_destroy(const ES_VOID *cache)
{
    es_cache_t *c = (es_cache_t *)cache;

    if (c == ES_NULL) {
        return ES_RET_FAILURE;
    }

    if (c->buf) {
        es_free(c->buf);
    }

    es_free(c);
    return ES_RET_SUCCESS;
}