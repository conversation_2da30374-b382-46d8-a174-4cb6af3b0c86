#ifndef _ES_CONSTANTS_H_
#define _ES_CONSTANTS_H_

/////////////////////////////////////// LCD Driver ////////////////////////////////////////////
#define ES_LCD_DRIVER_ILI9488               (1)
#define ES_LCD_DRIVER_ILI9486               (2)
#define ES_LCD_DRIVER_ST7789                (3)
#define ES_LCD_DRIVER_GC9306                (4)
#define ES_LCD_DRIVER_FPGA                  (5)
#define ES_LCD_DRIVER_ST7796U               (6) // 320 * 480
#define ES_LCD_DRIVER_NT35510               (7)

/////////////////////////////////////// BLE Modules ///////////////////////////////////////////
#define ES_BLE_HAL_WS8100                   (1) /* WS8100 */
#define ES_BLE_HAL_JWZH                     (2) /* JWZH module. */
#define ES_BLE_HAL_571                      (3)

#define ES_BLE_MAC_LEN                      (6)
#define ES_BLE_UUID_LEN                     (16)

/////////////////////////////////////// UI Type ///////////////////////////////////////////////
#define ES_UI_TYPE_K28V_240_320             (1)
#define ES_UI_TYPE_K35H_480_320             (2)
#define ES_UI_TYPE_K35V_320_480             (3)
#define ES_UI_TYPE_K40V_480_800             (4)
#define ES_UI_TYPE_K40V_480_800_GPS         (5)
#define ES_UI_TYPE_K35V_240_320_GPS         (6)


/////////////////////////////////////// 4G LTE ////////////////////////////////////////////////
#define ES_4GLTE_MODULE_L505                (1)
#define ES_4GLTE_MODULE_EC200X              (2)
#define ES_4GLTE_MODULE_EC800X              (3)


#define ES_LTE_IMEI_LEN                     (15)
#define ES_LTE_ICCID_LEN                    (20)

// "ad0f","0b7ea31a"
#define ES_LTE_CELL_CI_LEN                  (10)
#define ES_LTE_CELL_LAC_LEN                 (6)


/////////////////////////////////////// Network ///////////////////////////////////////////////
#define ES_IPADDR_STR_LEN                   (16) // ***************
#define ES_MAC_COLON_STR_LEN                (18)  // 11:22:33:44:55:66


/////////////////////////////////////// MD5 ///////////////////////////////////////////////////
#define ES_MD5_HEX_LEN                      (16)
#define ES_MD5_STR_LEN                      ((ES_MD5_HEX_LEN<<1))


#endif
