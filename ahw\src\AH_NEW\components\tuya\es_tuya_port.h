/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_tuya_port.h
** bef: define the interface for tuya adapter. 
** auth: lines<<EMAIL>>
** create on 2021.09.24
*/

#ifndef _ES_TUYA_PORT_H_
#define _ES_TUYA_PORT_H_

#ifdef __cplusplus 
extern "C" { 
#endif

#include "es_types.h"

#define ES_DP_DATA_BUF_LEN          (256)


#define ES_TUYA_DP_TYPE_RAW                     0x00        //RAW 类型
#define ES_TUYA_DP_TYPE_BOOL                    0x01        //bool 类型
#define ES_TUYA_DP_TYPE_VALUE                   0x02        //value 类型
#define ES_TUYA_DP_TYPE_STRING                  0x03        //string 类型
#define ES_TUYA_DP_TYPE_ENUM                    0x04        //enum 类型
#define ES_TUYA_DP_TYPE_BITMAP                  0x05        //fault 类型

typedef struct {
    unsigned char data[ES_DP_DATA_BUF_LEN];
    unsigned char data_len;
    unsigned char dp_id;
    unsigned char dp_type;
    unsigned char fail_count;
} es_tuya_dp_data_t;

typedef struct {
    unsigned int timestamp;
    unsigned int data_len;
    unsigned char *data;
} es_tuya_pic_data_t;


int es_tuya_port_init(void);

int es_tuya_port_uart_send(const unsigned char *data, unsigned int len);

void es_tuya_port_sync_resp(unsigned char result);

void es_tuya_port_upload_pic_resp(unsigned char cmd, unsigned char result, unsigned char err_code);

void es_tuya_port_cycle(void);

int es_tuya_port_add_dp(const es_tuya_dp_data_t *dp);

int es_tuya_port_upload_pic(const es_tuya_pic_data_t *data);

// 0, not connected; 1, connected
int es_tuya_port_wifi_connected(void);

int es_tuya_port_update_wifi_status(unsigned char status);

int es_tuya_port_reset_wifi(void);

int es_tuya_port_unbond(void);

void es_tuya_port_sync_time_ok(void);

#ifdef __cplusplus 
}
#endif
#endif