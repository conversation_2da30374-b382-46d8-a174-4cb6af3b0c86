/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_hal_uart.h
** bef: define the interface for uart 
** auth: lines<<EMAIL>>
** create on 2019.05.08 
*/

#ifndef _ES_UART_H_
#define _ES_UART_H_

#ifdef __cplusplus 
extern "C" { 
#endif

#include "es_types.h"


typedef enum {
    ES_UART_ID_0,
    ES_UART_ID_1,
    ES_UART_ID_2,
    ES_UART_ID_3,
    ES_UART_ID_NUM
}es_uart_id_e;

typedef enum {
    ES_UART_BAUD_1200   = 1200,
    ES_UART_BAUD_2400   = 2400,
    ES_UART_BAUD_4800   = 4800,
    ES_UART_BAUD_9600   = 9600,
    ES_UART_BAUD_38400  = 38400,
    ES_UART_BAUD_115200 = 115200,
    ES_UART_BAUD_921600 = 921600
}es_uart_baud_e;

typedef enum {
    ES_UART_DATA_5_BIT = 5,
    ES_UART_DATA_6_BIT,
    ES_UART_DATA_7_BIT,
    ES_UART_DATA_8_BIT
}es_uart_data_e;


typedef enum {
    ES_UART_STOP_1_BIT,
    ES_UART_STOP_1_5_BIT,
    ES_UART_STOP_2_BIT
}es_uart_stop_e;

typedef enum {
    ES_UART_PARITY_NONE,
    ES_UART_PARITY_ODD,
    ES_UART_PARITY_EVEN,
    ES_UART_PARITY_SPACE,
    ES_UART_PARITY_MARK
} es_uart_parity_e;

typedef ES_VOID (*es_uart_rx_cb_t)(ES_U8 id, const ES_BYTE *buf, ES_U16 len);


typedef struct {
    ES_U32  baud;   /* ref es_uart_baud_e. */
    ES_U8   data;   /* ref es_uart_data_e. */
    ES_U8   stop;   /* ref es_uart_stop_e. */
    ES_U8   parity; /* ref es_uart_parity_e. */
    es_uart_rx_cb_t rx_cb;
}es_uart_param_t;


ES_S32 es_uart_init(ES_VOID);

ES_S32 es_uart_open(ES_U8 id, es_uart_param_t *p);

ES_S32 es_uart_write(ES_U8 id, const ES_BYTE *buf, ES_U16 len);

ES_S32 es_uart_close(ES_U8 id);

#ifdef __cplusplus 
}
#endif

#endif


