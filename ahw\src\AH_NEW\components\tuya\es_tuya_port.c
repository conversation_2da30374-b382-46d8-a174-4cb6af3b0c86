/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_tuya_port.h
** bef: define the interface for tuya adapter.
** auth: lines<<EMAIL>>
** create on 2021.09.24 
*/

#include "es_inc.h"

#if ES_TUYA_MODULE_ENABLE
#include "facelib_inc.h"
#include "tuya_mcu_api.h"
#include "tuya_wifi.h"

// #define ES_TUYA_PORT_DEBUG
#ifdef ES_TUYA_PORT_DEBUG
#define es_tuya_port_debug es_log_info
#define es_tuya_port_error es_log_error
#else
#define es_tuya_port_debug(...)
#define es_tuya_port_error(...)
#endif

#define ES_TUYA_UPLOAD_TIMEOUT_MS           (30000)

#define ES_TUYA_DP_DATA_COUNT               (8)
#define ES_TUYA_INVAILD_DP_ID               (0xFF)

#define ES_TUYA_AUTH_TIMESTAMP              0 //(1647100800)    // 2021.11.10 00:00:00


#if ES_FACE_UPLOAD_ENABLE
#define TUYA_UPLOAD_PIC_BUF_COUNT           (2)
#define TUYA_UPLOAD_PIC_SIZE                (16*1024)
#define TUYA_UPLOAD_FILE_ID                 (0)
#define TUYA_UPLOAD_USE_STATIC_MEM          (0)
#endif

#if ES_FACE_UPLOAD_ENABLE
typedef struct {
    unsigned int timestamp;
    unsigned int data_len;
    unsigned int send_len;
    unsigned short start_upload;
    unsigned short fail_count;
#if TUYA_UPLOAD_USE_STATIC_MEM
    unsigned char data[TUYA_UPLOAD_PIC_SIZE];
#else
    unsigned char *data;
#endif
} es_tuya_upload_pic_t;
#endif

// -1, waiting response
// 0, got response, fail
// 1, got response, success
static int dp_upload_got_resp = -1;
static uint64_t upload_time = 0;
static int wifi_status = -1;

static struct pt pt_dp_upload;
static struct pt pt_pic_upload;
static struct pt_sem pt_sem_upload_resp;
static unsigned char boot_sync_time = 0;

static es_tuya_dp_data_t tuya_dp_data[ES_TUYA_DP_DATA_COUNT];
static unsigned char dev_info_upload_success = 0;
#if ES_PASSLOG_ENABLE
static ES_U32 passlog_count = 0;
static es_passlog_t passlogs[ES_PASSLOG_CACHE_COUNT];
#endif

#if ES_FACE_UPLOAD_ENABLE
static es_tuya_upload_pic_t upload_pic_list[TUYA_UPLOAD_PIC_BUF_COUNT];
static unsigned char pic_idx = 0;
static unsigned char is_upload_dp = 0;
static unsigned char is_upload_pic = 0;

static int es_tuya_pic_add2buf(const es_tuya_pic_data_t *pic)
{
    unsigned char i = 0;
    unsigned char found_idle = 0;

    if (TUYA_UPLOAD_PIC_SIZE < pic->data_len) {
        es_tuya_port_error("pic size:%d", pic->data_len);
        return -1;
    }

    if (0 == upload_pic_list[pic_idx].data_len) {
        i = pic_idx;
        found_idle = 1;
    } else {
        i = (pic_idx + 1) % TUYA_UPLOAD_PIC_BUF_COUNT;
        while (i != pic_idx) {
            if (0 == upload_pic_list[i].data_len) {
                found_idle = 1;
                break;
            }
            i = (i + 1) % TUYA_UPLOAD_PIC_BUF_COUNT;
        }
    }

    if (0 == found_idle) {
        es_tuya_port_debug("no buffer for pic upload");
        return -1;
    }

#if (0 == TUYA_UPLOAD_USE_STATIC_MEM)
    upload_pic_list[i].data = es_malloc(pic->data_len);
    if (ES_NULL == upload_pic_list[i].data) {
        es_tuya_port_error("no memory for pic upload");
        return -1;
    }
#endif
    es_memcpy(upload_pic_list[i].data, pic->data, pic->data_len);
    upload_pic_list[i].data_len = pic->data_len;
    upload_pic_list[i].send_len = 0;
    upload_pic_list[i].timestamp = pic->timestamp;
    es_tuya_port_debug("add pic upload to buffer success");
    return 0;
}

static void es_tuya_pic_reset_buf(unsigned char idx)
{
#if (0 == TUYA_UPLOAD_USE_STATIC_MEM)
    if (ES_NULL != upload_pic_list[idx].data) {
        es_free(upload_pic_list[idx].data);
    }
    upload_pic_list[idx].data = ES_NULL;
#endif
    upload_pic_list[idx].data_len = 0;
    upload_pic_list[idx].send_len = 0;
    upload_pic_list[idx].start_upload = 0;
    upload_pic_list[idx].fail_count = 0;
}

#endif

static int es_tuya_set_upload_param(void)
{
    dp_upload_got_resp = -1;
    upload_time = es_time_get_sytem_ms();

    return 0;
}

static void es_tuya_reset_dp_data(unsigned char idx)
{
    memset(&tuya_dp_data[idx], 0x00, sizeof(es_tuya_dp_data_t));
    tuya_dp_data[idx].dp_id = (unsigned char)ES_TUYA_INVAILD_DP_ID;
    // es_tuya_port_debug("reset dp, idx:%d", idx);
}

// result, -1: timeout, 0: fail, 1: success
static void es_tuya_dp_upload_result_notify(unsigned char dp_idx, int result)
{
    unsigned char need_reset_dp = 0;

    if ((unsigned char)DPID_MASTER_INFORMATION == tuya_dp_data[dp_idx].dp_id && 1 == result) {
        dev_info_upload_success = 1;
    }

    if (1 == result) { // success
        need_reset_dp = 1;
    } else {
        tuya_dp_data[dp_idx].fail_count++;
        if (tuya_dp_data[dp_idx].fail_count >= 2) {
            need_reset_dp = 1;
        }
    }

    if (need_reset_dp) {
        es_tuya_reset_dp_data(dp_idx);
    }
}

static int es_tuya_get_dp_data_index(void)
{
    unsigned char i = 0;

    for (i = 0; i < ES_TUYA_DP_DATA_COUNT; i++) {
        if ((unsigned char)ES_TUYA_INVAILD_DP_ID != (unsigned char) tuya_dp_data[i].dp_id
            && 0 != tuya_dp_data[i].data_len) {
            break;
        }
    }

    return i;
}

static int es_tuya_upload_dp_data(unsigned char idx)
{
    int ret = 0;
    es_tuya_port_debug("dp data, idx:%d, dp_id:%u, len:%d, %s", idx, (unsigned char)tuya_dp_data[idx].dp_id, tuya_dp_data[idx].data_len, tuya_dp_data[idx].data);
    switch (tuya_dp_data[idx].dp_type)
    {
        case ES_TUYA_DP_TYPE_RAW:
            ret = mcu_dp_raw_update_syn(tuya_dp_data[idx].dp_id, (const unsigned char *)tuya_dp_data[idx].data, (unsigned short)tuya_dp_data[idx].data_len);
            break;
        case ES_TUYA_DP_TYPE_STRING:
            ret = mcu_dp_string_update_syn(tuya_dp_data[idx].dp_id, (const unsigned char *)tuya_dp_data[idx].data, (unsigned short)tuya_dp_data[idx].data_len);
            break;
    
        default:
            ret = -1;
            break;
    }

    return ret;
}


static int es_tuya_check_timeout(uint64_t *last_time, uint64_t timeout_ms)
{
    uint64_t t;

    t = es_time_get_sytem_ms();
    if ((t - *last_time)  < ( timeout_ms)) {
        return 0;
    }

    *last_time = t;
    return 1;
}

static int es_tuya_upload_is_timeout(void)
{
    if (es_tuya_check_timeout(&upload_time, (uint64_t)ES_TUYA_UPLOAD_TIMEOUT_MS)) {
        upload_time = 0;
        return 1;
    }

    return 0;
}

static void es_tuya_try_sync_time(void)
{
    static uint64_t lastMillis = 0;
    static int got_time = 0;

    if (1 != es_tuya_port_wifi_connected()) {
        return;
    }
    
    if (0 == got_time) {
        if (0 == es_tuya_check_timeout(&lastMillis, 3000)) {
            return;
        }

        if (0 == es_tuya_port_wifi_connected()) {
            return;
        }

        if (0 == boot_sync_time) {
            goto sync;
        }

        if (es_time_get_timestamp() < (ES_U32)ES_MIN_TIMESTAMP || es_time_get_timestamp() > ES_MAX_TIMESTAMP) {
            goto sync;
        }
        got_time = 1;
        mcu_open_extend_service();
        return;
    }

#if ES_TUYA_AUTH_TIMESTAMP
    if (ES_TUYA_AUTH_TIMESTAMP < es_time_get_timestamp()) {
        es_tuya_port_debug("auth fail, timeout\r\n");
        msleep(10*1000);
        es_hal_sys_reset();
    }
#endif

    if (0 == es_tuya_check_timeout(&lastMillis, (uint64_t)(24*60*60*1000))) {
        return;
    }

sync:
    mcu_get_system_time();
}

static void es_tuya_try_get_wifi_status(void)
{
    static uint64_t lastMillis = 0;

    if (1 == is_upload_pic || 1 == is_upload_dp) {
        return;
    }

    if (1 != es_tuya_check_timeout(&lastMillis, 10*1000)) {
        return;
    }

    mcu_get_wifi_connect_status();
}

#if ES_FACE_UPLOAD_ENABLE

static int tuya_upload_pic_start(void)
{
#define UPLOAD_PIC_START_DATA_BUF_SIZE          (128)
    unsigned char data[UPLOAD_PIC_START_DATA_BUF_SIZE] = {0};
    unsigned short data_len = 0;

    data[data_len] = 0x06;
    data_len++;

    data_len += es_snprintf((char *)(data+data_len), UPLOAD_PIC_START_DATA_BUF_SIZE-data_len,
                            "{\"num\":1,\"ext_info\":null,\"files\":[{\"name\":\"%d000\",\"id\":%d,\"len\":%d,\"type\":6,\"file_info\":null}]}",
                            upload_pic_list[pic_idx].timestamp, TUYA_UPLOAD_FILE_ID, upload_pic_list[pic_idx].data_len);
    // es_tuya_port_debug("upload file start, package_len:%d", data_len);
    // es_tuya_port_debug("package json:%s", (char *)(data+1));
    return mcu_file_upload(data, data_len);
}

static unsigned short tuya_upload_pic_data(void)
{
#define UPLOAD_PIC_DATA_DATA_BUF_SIZE          (1024+7)
    unsigned char data[UPLOAD_PIC_DATA_DATA_BUF_SIZE] = {0};
    unsigned short data_len = 0;
    unsigned short send_data_len = 0;

    data[data_len] = 0x07;
    data_len++;

    data[data_len] = (unsigned short)(((TUYA_UPLOAD_FILE_ID)>>8)&0xFF);
    data_len++;

    data[data_len] = (unsigned short)((TUYA_UPLOAD_FILE_ID)&0xFF);
    data_len++;

    data[data_len] = (unsigned short)((upload_pic_list[pic_idx].send_len>>24)&0xFF);
    data_len++;

    data[data_len] = (unsigned short)((upload_pic_list[pic_idx].send_len>>16)&0xFF);
    data_len++;

    data[data_len] = (unsigned short)((upload_pic_list[pic_idx].send_len>>8)&0xFF);
    data_len++;

    data[data_len] = (unsigned short)(upload_pic_list[pic_idx].send_len&0xFF);
    data_len++;

    send_data_len = UPLOAD_PIC_DATA_DATA_BUF_SIZE-data_len;
    if (send_data_len > (upload_pic_list[pic_idx].data_len-upload_pic_list[pic_idx].send_len)) {
        send_data_len = upload_pic_list[pic_idx].data_len-upload_pic_list[pic_idx].send_len;
    }
    // es_tuya_port_debug("upload file data, send_data_len:%d, finish len:%d", send_data_len, upload_pic_list[pic_idx].send_len);
    es_memcpy((char *)(data+data_len), (char *)(upload_pic_list[pic_idx].data+upload_pic_list[pic_idx].send_len), send_data_len);
    data_len += send_data_len;
    // es_tuya_port_debug("upload file data, package_len:%d, data_len:%d", data_len, send_data_len);
    mcu_file_upload(data, data_len);
    // es_tuya_port_debug("data(send_data_len=%d):", send_data_len);
    // es_log_dump_hex((const ES_BYTE *)(upload_pic_list[pic_idx].data+upload_pic_list[pic_idx].send_len), send_data_len);
    // es_tuya_port_debug("\r\n");
    // es_tuya_port_debug("mcu_file_upload finish");
    return send_data_len;

}

static unsigned short tuya_upload_pic_end(void)
{
#define UPLOAD_PIC_END_DATA_BUF_SIZE            (4)
    unsigned char data[UPLOAD_PIC_END_DATA_BUF_SIZE] = {0};
    unsigned short data_len = 0;

    data[data_len] = 0x08;
    data_len++;

    return mcu_file_upload(data, data_len);
}

static PT_THREAD(es_tuya_try_upload_pic(struct pt *pt))
{
    static unsigned short upload_data_len = 0;
    PT_BEGIN(pt);
    if (0 == upload_pic_list[pic_idx].data_len) {
        return 0;
    }

    if (1 == is_upload_dp) {
        // upload_pic_list[pic_idx].start_upload = 0;
        // es_tuya_port_debug("upload pic reset by dp");
        return 0;
    }

    is_upload_pic = 1;
    if (0 == upload_pic_list[pic_idx].start_upload) {
        // start
        // es_tuya_port_debug("upload pic data:");
        // es_log_dump_hex((const ES_BYTE *)(upload_pic_list[pic_idx].data), upload_pic_list[pic_idx].data_len);
        tuya_upload_pic_start();
    }  else {
        // send data
        upload_data_len = tuya_upload_pic_data();
    }

    // 1 wait response
    es_tuya_set_upload_param();
    // es_tuya_port_debug("wait pt_sem_upload_resp");
    PT_SEM_INIT(&pt_sem_upload_resp, 0);
    PT_SEM_WAIT(pt, &pt_sem_upload_resp);
    es_tuya_port_debug("dp_upload_got_resp:%d, upload_data_len:%d", dp_upload_got_resp, upload_data_len);
    if (upload_data_len) {
        if (1 != dp_upload_got_resp) {
            upload_pic_list[pic_idx].fail_count++;
            upload_data_len = 0;
            goto END;
        }

        // 2 wait upload data finish
        // es_tuya_set_upload_param();
        // es_tuya_port_debug("wait pt_sem_upload_resp");
        // PT_SEM_WAIT(pt, &pt_sem_upload_resp);
        // es_tuya_port_debug("dp_upload_got_resp:%d", dp_upload_got_resp);
        // if (1 != dp_upload_got_resp) {
        //     upload_pic_list[pic_idx].fail_count++;
        //     upload_data_len = 0;
        // }
    } else {
        upload_pic_list[pic_idx].start_upload = 1;
        if (1 != dp_upload_got_resp) {
            upload_pic_list[pic_idx].fail_count++;
            upload_pic_list[pic_idx].start_upload = 0;
            es_tuya_port_debug("upload pic start fail");
        }
    }


END:
    // finish
    upload_pic_list[pic_idx].send_len += upload_data_len;
    upload_data_len = 0;
    if ((upload_pic_list[pic_idx].send_len == upload_pic_list[pic_idx].data_len)
         || (upload_pic_list[pic_idx].fail_count > 10)) {
        es_tuya_pic_reset_buf(pic_idx);
        pic_idx = (pic_idx+1) % TUYA_UPLOAD_PIC_BUF_COUNT;
        if (upload_pic_list[pic_idx].send_len == upload_pic_list[pic_idx].data_len) {
            tuya_upload_pic_end();
            es_tuya_port_debug("send pic end(0x08)");
        }
    }

    is_upload_pic = 0;
    PT_END(pt);
}
#endif

static PT_THREAD(es_tuya_try_upload_dp(struct pt *pt))
{
    static int dp_index = 0;

    PT_BEGIN(pt);
    if (0 == es_tuya_port_wifi_connected()) {
        return 0;
    }

    if (1 == is_upload_pic) {
        return 0;
    }

    dp_index = es_tuya_get_dp_data_index();
    if (dp_index >= ES_TUYA_DP_DATA_COUNT) {
        return 0;
    }

    is_upload_dp = 1;
    es_tuya_upload_dp_data(dp_index);
    es_tuya_set_upload_param();
    es_tuya_port_debug("dp_upload wait sem");

    PT_SEM_INIT(&pt_sem_upload_resp, 0);
    PT_SEM_WAIT(pt, &pt_sem_upload_resp);
    es_tuya_port_debug("dp_upload_got_resp:%d", dp_upload_got_resp);
    es_tuya_dp_upload_result_notify(dp_index, dp_upload_got_resp);
    is_upload_dp = 0;
    PT_END(pt);
}


static void es_tuya_try_upload_dev_info(void)
{
    static uint64_t lastMillis = 0;
    static uint64_t timeout_ms = 5*60*1000;

    if (0 == es_tuya_port_wifi_connected()) {
        return;
    }

    timeout_ms = (uint64_t)(24*60*60*1000);
    if (1 != dev_info_upload_success){
        timeout_ms = 5*60*1000;
    }

    if (0 == es_tuya_check_timeout(&lastMillis, timeout_ms)) {
        return;
    }
    es_dp_send_dev_info();
}

static void es_tuya_check_resp(void)
{
    unsigned char resp_or_timeout = 0;
    if (0 == upload_time) {
        return;
    }

    if (dp_upload_got_resp != -1) {
        resp_or_timeout = 1;
    } 
    
    if (1 == es_tuya_upload_is_timeout()){
        printk("wait resp timeout\r\n");
        resp_or_timeout = 1;
    }

    if (1 == resp_or_timeout) {
        PT_SEM_SIGNAL(pt, &pt_sem_upload_resp);
        upload_time = 0;
    }
}

static void es_tuya_try_config_network(void)
{
    static uint64_t lastMillis = 0;
    es_task_param_t task_param;
    ES_BOOL wifi_been_config = ES_TRUE; 

    if (SMART_CONFIG_STATE == wifi_status ||
        AP_STATE == wifi_status ||
        WIFI_LOW_POWER == wifi_status) {
        wifi_been_config = ES_FALSE;
    }


    if (WIFI_CONN_CLOUD == wifi_status && 1 == mf_brd.cfg.factory_flag) {
        task_param.type = ES_TASK_ACTIVE_DEVICE;
        task_param.param = ES_NULL;
        task_param.timeout = 0;
        es_task_queue_push_wait(&task_param);
        return;
    }

    if (wifi_been_config) {
        return;
    }

    if (0 == lastMillis) {
        lastMillis = es_time_get_sytem_ms();
        if (lastMillis < 3*1000) {
            lastMillis = 0;
            return;
        }
    } else {
        if (0 == es_tuya_check_timeout(&lastMillis, 180*1000)) {
            return;
        }
    }

    mcu_set_wifi_mode(0);
    es_tuya_port_debug("tuya enter wifi smart config");
}

static void es_tuya_upload_offline_log(void)
{
#if ES_PASSLOG_ENABLE
    es_task_param_t task_param;
    static ES_U32 upload_lasttime = 0;

    if (WIFI_CONN_CLOUD != wifi_status) {
        return;
    }

    if (0 == es_passlog_get_count()) {
        return;
    }

    if (ES_RET_SUCCESS != es_time_check_timeout_ms(&upload_lasttime, 10*1000)) {
        return;
    }

    if (1 == is_upload_pic || 1 == is_upload_dp) {
        return;
    }

    // es_tuya_port_debug("now passlog_count:%d", passlog_count);
    if (passlog_count == 0) {
        passlog_count = es_passlog_read_from_cache(passlogs, ES_PASSLOG_CACHE_COUNT);
        if (passlog_count == 0) {
            return;
        }
        // es_tuya_port_debug("read passlog, passlog_count:%d", passlog_count);
    }

    if (0 != es_dp_upload_face_data(passlogs[passlog_count-1].uid, passlogs[passlog_count-1].timestamp, ES_DP_FACE_DATA_PASS)) {
        es_tuya_port_debug("add passlog dp fail");
        return;
    }
    passlog_count--;

    // finish upload
    if (0 == passlog_count) {
        // es_tuya_port_debug("passlog dp upload all");
        es_memset(&task_param, 0x00, sizeof(task_param));
        task_param.type = ES_TASK_PASSLOG_UPLOAD_RESULT;
        task_param.param = (ES_VOID *)(ES_TRUE);
        task_param.timeout = 0;
        es_task_queue_push_wait((const es_task_param_t *)&task_param);
    }
#endif
}


static void es_tuya_port_task(void)
{
    es_tuya_try_sync_time();
    es_tuya_try_upload_dev_info();
    es_tuya_try_upload_dp(&pt_dp_upload);
#if ES_FACE_UPLOAD_ENABLE
    es_tuya_try_upload_pic(&pt_pic_upload);
#endif
    es_tuya_check_resp();
    es_tuya_try_get_wifi_status();
    es_tuya_try_config_network();
    es_tuya_upload_offline_log();
}


static void tuya_adapter_uart_rx_cb(ES_U8 id, const unsigned char *buf, unsigned short len)
{
    (void) id;
    uart_receive_buff_input((unsigned char *)buf, len);
}

static int tuya_adapter_init_uart(void)
{
    es_uart_param_t uart_param;


	uart_param.baud = ES_UART_BAUD_921600;
	uart_param.data = ES_UART_DATA_8_BIT;
	uart_param.stop = ES_UART_STOP_1_BIT;
	uart_param.parity = ES_UART_PARITY_NONE;
	uart_param.rx_cb = tuya_adapter_uart_rx_cb;
    if (ES_RET_SUCCESS != es_uart_open(ES_TUYA_UART_ID, &uart_param)) {
        es_tuya_port_error("open uart (%d) fail", ES_TUYA_UART_ID);
        return -1;
    }

    return 0;
}

int es_tuya_port_init(void)
{
    int i = 0;

    es_memset(tuya_dp_data, 0x00, sizeof(tuya_dp_data));
#if ES_PASSLOG_ENABLE
    es_memset(passlogs, 0x00, sizeof(passlogs));
#endif
#if ES_FACE_UPLOAD_ENABLE
    es_memset(upload_pic_list, 0x00, sizeof(upload_pic_list));
#endif

    PT_INIT(&pt_dp_upload);
    PT_INIT(&pt_pic_upload);
    PT_SEM_INIT(&pt_sem_upload_resp, 0);

    if (0 != tuya_adapter_init_uart()) {
        return -1;
    }
    wifi_protocol_init();

    for (i = 0; i < ES_TUYA_DP_DATA_COUNT; i++) {
        tuya_dp_data[i].dp_id = (unsigned char)ES_TUYA_INVAILD_DP_ID;
    }

    return 0;
}

int es_tuya_port_uart_send(const unsigned char *data, unsigned int len)
{
    return es_uart_write(ES_TUYA_UART_ID, data, len);
}

void es_tuya_port_sync_resp(unsigned char result)
{
    es_tuya_port_debug("sync resp result:%d", result);
    if (0 == result) { // fail
        dp_upload_got_resp = 0;
    } else {
        dp_upload_got_resp = 1;
    }
}

void es_tuya_port_upload_pic_resp(unsigned char cmd, unsigned char result, unsigned char err_code)
{
    es_tuya_port_debug("upload file resp, cmd:%d, result:%d, err_code:%d", cmd, result, err_code);
    if (0 != result) { // fail
        dp_upload_got_resp = 0;
    } else {
        dp_upload_got_resp = 1;
    }
}

void es_tuya_port_cycle(void)
{
    wifi_uart_service();
    es_tuya_port_task();
}

int es_tuya_port_add_dp(const es_tuya_dp_data_t *dp)
{
    int i = 0;

    for (i = 0; i < ES_TUYA_DP_DATA_COUNT; i++) {
        if ((unsigned char)ES_TUYA_INVAILD_DP_ID == (unsigned char) tuya_dp_data[i].dp_id) {
            break;
        }
    }

    if (i == ES_TUYA_DP_DATA_COUNT) {
        return -1;
    }

    memcpy(&tuya_dp_data[i], dp, sizeof(es_tuya_dp_data_t));
    tuya_dp_data[i].fail_count = 0;
    return 0;
}

int es_tuya_port_upload_pic(const es_tuya_pic_data_t *data)
{
#if ES_FACE_UPLOAD_ENABLE
    return es_tuya_pic_add2buf(data);
#else
    return -1;
#endif
}


// 0, not connected; 1, connected
int es_tuya_port_wifi_connected(void)
{
    if (WIFI_CONN_CLOUD == wifi_status) {
        return 1;
    }

    return 0;
}

int es_tuya_port_update_wifi_status(unsigned char status)
{
    wifi_status = status;
    return 0;
}

int es_tuya_port_reset_wifi(void)
{
    mcu_reset_wifi();
    return 0;
}

int es_tuya_port_unbond(void)
{
    es_task_param_t task_param;

    task_param.type = ES_TASK_DEL_FACEDB_ALL;
    task_param.param = ES_NULL;
    task_param.timeout = 0;
    es_task_queue_push_wait(&task_param);

    task_param.type = ES_TASK_RESET_DEVICE;
    task_param.param = ES_NULL;
    task_param.timeout = 0;
    es_task_queue_push_wait(&task_param);

    return 0;
}

void es_tuya_port_sync_time_ok(void)
{
    boot_sync_time = 1;
    es_tuya_port_debug("es_tuya_port_sync_time_ok");
}
#endif
