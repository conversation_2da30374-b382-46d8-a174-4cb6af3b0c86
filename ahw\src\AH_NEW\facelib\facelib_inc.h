#ifndef _FACELIB_INC_H_
#define _FACELIB_INC_H_
/************ Basic *************/
#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "utils.h"
#include <float.h>
#include <math.h>
#include "fmath.h"


/************ Bsp *************/
#include "mf_lib.h"
#include "mf_constants.h"
#include <bsp.h>
#include "atomic.h"
#include "platform.h"
#include "syscalls.h"
#include "utils.h"
#include "dmac.h"
#include "dvp.h"
#include "fpioa.h"
#include "gpiohs.h"
#include "i2c.h"
#include "plic.h"
#include "printf.h"
#include "sha256.h"
#include "sysctl.h"

// facelib
#include "mf_spi.h"
#include "mf_model.h"
#include "mf_flash.h"
#include "mf_facedb_flash.h"
#include "mf_facedb.h"
#include "mf_flow.h"
#include "dualcam_irq.h"
#include "mf_cam.h"
#include "image_op.h"
#include "mf_brd.h"
#include "mf_flow_comm.h"

#endif