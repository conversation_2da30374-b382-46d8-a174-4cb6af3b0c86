/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_audio.c
** bef: define the interface for audio.
** auth: lines<<EMAIL>>
** create on 2022.01.08 
*/

#include "es_inc.h"

#if ES_AUDIO_ENABLE
#include "facelib_inc.h"

// #define ES_AUDIO_DEBUG
#ifdef ES_AUDIO_DEBUG
#define es_audio_debug es_log_info
#define es_audio_error es_log_error
#else
#define es_audio_debug(...)
#define es_audio_error(...)
#endif

#define ES_AUDIO_ENABLE_PLAY_NO_BLOCK       (1)
#if ES_AUDIO_ENABLE_PLAY_NO_BLOCK
#define FRAME_SIZE                          (512) /* 占用的是帧大小4倍的内存 */
#define ES_AUDIO_I2S_DEVICE                 (I2S_DEVICE_0)

#endif

#define ES_AUDIO_PLAY_QUEUE_COUNT           (2)

#if ES_AUDIO_ENABLE_PLAY_NO_BLOCK
typedef struct {
    const ES_BYTE *wav_buf;
    ES_BYTE pa_en;
    ES_BYTE pa_pol;

    ES_U32 frame_id;
    ES_U32 frame_num;

    ES_U32 frame_size;
    ES_U32 last_frame_sz;

    i2s_device_number_t i2s_dev;
    i2s_data_t i2s_data;
    plic_interrupt_t i2s_irq;
} i2s_play_ctx_t;
#endif

typedef struct {
    ES_BYTE *play_data;
    ES_U32 data_len;
} es_audio_data_t;

static es_audio_data_t play_queue[ES_AUDIO_PLAY_QUEUE_COUNT] = {0};
#if ES_AUDIO_ENABLE_PLAY_NO_BLOCK
static volatile i2s_play_ctx_t i2s_play_ctx;
static uint16_t i2s_play_buf[FRAME_SIZE * 2];
static ES_U32 audio_play_index = ES_AUDIO_PLAY_QUEUE_COUNT;
#endif

static ES_VOID es_audio_queue_reset(ES_U32 idx)
{
    play_queue[idx].data_len = 0;
    if (ES_NULL != play_queue[idx].play_data) {
        es_free(play_queue[idx].play_data);
    }
    play_queue[idx].play_data = ES_NULL;
#if ES_AUDIO_ENABLE_PLAY_NO_BLOCK
    audio_play_index = ES_AUDIO_PLAY_QUEUE_COUNT;
#endif
}

static ES_S32 es_audio_hal_init(ES_VOID)
{
    fpioa_set_function(ES_AUDIO_GPIO_PIN, FUNC_GPIO0 + ES_AUDIO_GPIO_HS_NUM);
    gpio_set_drive_mode(ES_AUDIO_GPIO_HS_NUM, GPIO_DM_OUTPUT);
    gpio_set_pin(ES_AUDIO_GPIO_HS_NUM, 1-ES_AUDIO_GPIO_IO_VAL);

    /* both PT8211 & ES8374, I2S */
    fpioa_set_function(ES_AUDIO_I2S_SCLK_PIN, FUNC_I2S0_SCLK);
    fpioa_set_function(ES_AUDIO_I2S_WS_PIN, FUNC_I2S0_WS);
    fpioa_set_function(ES_AUDIO_I2S_DOUT_PIN, FUNC_I2S0_OUT_D0);

    sysctl_pll_set_freq(SYSCTL_PLL2, 45158400UL);
    i2s_init(ES_AUDIO_I2S_DEVICE, I2S_TRANSMITTER, 0x3);
    i2s_tx_channel_config(ES_AUDIO_I2S_DEVICE, I2S_CHANNEL_0,
                          RESOLUTION_16_BIT, SCLK_CYCLES_32,
                          TRIGGER_LEVEL_4, RIGHT_JUSTIFYING_MODE);
    i2s_set_sample_rate(ES_AUDIO_I2S_DEVICE, 8 * 1000);

    return ES_RET_SUCCESS;
}


#if ES_AUDIO_ENABLE_PLAY_NO_BLOCK
static void es_audio_wav_u8_to_pcm_int16(uint16_t *pcm_buf, const uint8_t *wav_buf, size_t length)
{
    for(uint32_t i = 0; i < length; i++) {
        int8_t t = (int8_t)(wav_buf[i] - 0x80);
        int16_t tt = (int16_t)(t << 8);

#if 0
        float d = (float)(tt * 1.1);

        pcm_buf[2 * i] = (uint16_t)d;
        pcm_buf[2 * i + 1] = (uint16_t)d;
#else
        pcm_buf[2 * i] = (uint16_t)tt;
        pcm_buf[2 * i + 1] = (uint16_t)tt;
#endif
    }
}

static ES_S32 es_audio_play_cb(void *ctx)
{
    i2s_play_ctx_t *i2s_data = (i2s_play_ctx_t*)ctx;

    i2s_data_t *data = (i2s_data_t*)&i2s_data->i2s_data;
    plic_interrupt_t *irq = (plic_interrupt_t*)&i2s_data->i2s_irq;

    ES_U32 frame_size, max_frame_cnt;
    const ES_BYTE *buf_ptr = NULL;

    max_frame_cnt = i2s_data->frame_num;
    max_frame_cnt += i2s_data->last_frame_sz ? 1 : 0;

    if(i2s_data->frame_id >= max_frame_cnt) {
        gpio_set_pin(i2s_data->pa_en, 1 - i2s_data->pa_pol);
        es_audio_queue_reset(audio_play_index);
        return 0;
    }

    if(i2s_data->frame_id < i2s_data->frame_num) {
        frame_size = i2s_data->frame_size;
        buf_ptr = i2s_data->wav_buf + i2s_data->frame_id * i2s_data->frame_size;
    } else {
        frame_size = i2s_data->last_frame_sz;
        buf_ptr = i2s_data->wav_buf + i2s_data->frame_num * i2s_data->frame_size;
    }

    i2s_data->frame_id++;

    es_audio_wav_u8_to_pcm_int16((uint16_t*)data->tx_buf, buf_ptr, frame_size);

    data->tx_len = frame_size;
    i2s_handle_data_dma(i2s_data->i2s_dev, *data, irq);

    return 0;
}

static ES_VOID es_audio_hal_play_no_block(ES_BYTE *audio_buf, ES_U32 audio_len)
{
    i2s_data_t *data = (i2s_data_t*)&i2s_play_ctx.i2s_data;
    plic_interrupt_t *irq = (plic_interrupt_t*)&i2s_play_ctx.i2s_irq;

    i2s_play_ctx.wav_buf = audio_buf;

    i2s_play_ctx.frame_id = 1;
    i2s_play_ctx.frame_size = FRAME_SIZE;

    i2s_play_ctx.last_frame_sz = audio_len % FRAME_SIZE;

    i2s_play_ctx.frame_num = audio_len / FRAME_SIZE;

    i2s_play_ctx.i2s_dev = ES_AUDIO_I2S_DEVICE;
    i2s_play_ctx.pa_en = ES_AUDIO_GPIO_HS_NUM;
    i2s_play_ctx.pa_pol = ES_AUDIO_GPIO_IO_VAL;

    data->tx_channel = DMAC_CHANNEL3;
    data->tx_buf = (uint32_t*)i2s_play_buf;
    data->tx_len = FRAME_SIZE;
    data->transfer_mode = I2S_SEND;
    data->nowait_dma_idle = 0;

    irq->callback = es_audio_play_cb;
    irq->ctx = (void *)&i2s_play_ctx;
    irq->priority = 2;

    es_audio_wav_u8_to_pcm_int16((uint16_t*)data->tx_buf, i2s_play_ctx.wav_buf, FRAME_SIZE);

    gpio_set_pin(i2s_play_ctx.pa_en, i2s_play_ctx.pa_pol);
    i2s_set_dma_divide_16(ES_AUDIO_I2S_DEVICE, 1);
    i2s_handle_data_dma(ES_AUDIO_I2S_DEVICE, *data, irq);
}
#else
static ES_VOID es_audio_hal_play(ES_BYTE *audio_buf, ES_U32 audio_len)
{
    gpio_set_pin(ES_AUDIO_GPIO_HS_NUM, ES_AUDIO_GPIO_IO_VAL);
    i2s_play(ES_AUDIO_I2S_DEVICE, DMAC_CHANNEL3, audio_buf, audio_len, 512, 16, 1);
    gpio_set_pin(ES_AUDIO_GPIO_HS_NUM, 1 - ES_AUDIO_GPIO_IO_VAL);
}
#endif

static ES_S32 es_audio_add_play_queue(ES_U32 flash_addr, ES_U32 flash_size)
{
    ES_U8 i;

    if (0 == flash_size || 0 == flash_addr) {
        return ES_RET_FAILURE;
    }

    for (i = 0; i < ES_AUDIO_PLAY_QUEUE_COUNT; i++) {
        if (ES_NULL ==  play_queue[i].play_data) {
            break;
        }
    }
    if (i >= ES_AUDIO_PLAY_QUEUE_COUNT) {
        return ES_RET_FAILURE;
    }

    play_queue[i].play_data = (ES_BYTE *)es_malloc(((flash_size/1024)+1)*1024);
    if (ES_NULL == play_queue[i].play_data) {
        return ES_RET_FAILURE;
    }

    if (0 != mf_flash.read(flash_addr, play_queue[i].play_data, flash_size)) {
        es_free(play_queue[i].play_data);
        play_queue[i].play_data = ES_NULL;
        return ES_RET_FAILURE;
    }
    play_queue[i].data_len = flash_size;

    es_audio_debug("play_addr:%x, size:%d", flash_addr, flash_size);

    return ES_RET_SUCCESS;
}

ES_S32 es_audio_init(ES_VOID)
{
    es_memset(play_queue, 0x00, sizeof(play_queue));
    es_audio_hal_init();
    return ES_RET_SUCCESS;
}

ES_VOID es_audio_run(ES_VOID)
{
    ES_U8 i = 0;

#if ES_AUDIO_ENABLE_PLAY_NO_BLOCK
    if (ES_AUDIO_PLAY_QUEUE_COUNT != audio_play_index) {
        return;
    }
#endif

    for (i = 0; i < ES_AUDIO_PLAY_QUEUE_COUNT; i++) {
        if (ES_NULL != play_queue[i].play_data && 0 != play_queue[i].data_len) {
            break;
        }
    }
    if (i >= ES_AUDIO_PLAY_QUEUE_COUNT) {
        return;
    }
    es_audio_debug("start play");
#if ES_AUDIO_ENABLE_PLAY_NO_BLOCK
    audio_play_index = i;
    es_audio_hal_play_no_block(play_queue[i].play_data, play_queue[i].data_len);
#else
    es_audio_hal_play(play_queue[i].play_data, play_queue[i].data_len);
    es_audio_queue_reset(i);
#endif
}


// please use es_voice_play 
/*
使用es_voice_play替代该接口
*/
ES_S32 es_audio_play(es_audio_play_e play)
{
    ES_U32 flash_addr = 0;
    ES_U32 flash_size = 0;

    if (ES_AUDIO_PLAY_PASS == play) {
        flash_addr = ES_AUDIO_PASS_FLASH_ADDR;
        flash_size = ES_AUDIO_PASS_FLASH_SIZE;
    } else if (ES_AUDIO_PLAY_TEMP_OK == play) {
        flash_addr = ES_AUDIO_TEMP_OK_FLASH_ADDR;
        flash_size = ES_AUDIO_TEMP_OK_FLASH_SIZE;
    } else if (ES_AUDIO_PLAY_TEMP_FAIL == play) {
        flash_addr = ES_AUDIO_TEMP_FAIL_FLASH_ADDR;
        flash_size = ES_AUDIO_TEMP_FAIL_FLASH_SIZE;
    } else {
        return ES_RET_FAILURE;
    }

    return es_audio_add_play_queue(flash_addr, flash_size);
}

ES_S32 es_audio_play_flash(ES_U32 addr, ES_U32 size)
{
    return es_audio_add_play_queue(addr, size);
}


#endif
