#ifndef _NT35310_H_
#define _NT35310_H_

#include <stdint.h>

void es_tft_hard_init(uint8_t freqMhz);
void es_tft_write_command(uint8_t cmd);
void es_tft_write_byte(uint8_t *data_buf, uint32_t length);
void es_tft_write_half(uint16_t *data_buf, uint32_t length);
void es_tft_write_word(uint32_t *data_buf, uint32_t length, uint32_t flag);
void es_tft_fill_data(uint32_t *data_buf, uint32_t length);
void es_tft_write_onebyte(uint8_t data);
void es_tft_write_byte_word(uint8_t *data_buf);
void es_tft_write_command_word(uint8_t *cmd_buf);

#endif
