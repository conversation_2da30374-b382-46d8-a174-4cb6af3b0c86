/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_passlog.c
** bef: define the interface for pass log.
** auth: lines<<EMAIL>>
** create on 2022.01.02 
*/

#include "es_inc.h"

#if ES_PASSLOG_ENABLE
#include "facelib_inc.h"

// #define ES_PASSLOG_DEBUG
#ifdef ES_PASSLOG_DEBUG
#define es_passlog_debug es_log_info
#define es_passlog_error es_log_error
#else
#define es_passlog_debug(...)
#define es_passlog_error(...)
#endif


#if (ES_PASSLOG_UID_LEN == 16)
#define ES_PASSLOG_NODE_SIZE        (32)
#elif (ES_PASSLOG_UID_LEN == 32)
#define ES_PASSLOG_NODE_SIZE        (64)
#else
#error "ES_PASSLOG_UID_LEN error" 
#endif



/*
 * ES_PASSLOG_MAX_CNT,最大支持的记录数量
 * ES_FLASH_PASS_LOG_SIZE, 分配flash空间大小
 * 数据头按照最大2048字节来计算
 * ES_PASSLOG_NODE_SIZE，每条记录的大小，要么是32字节，要么是64字节
 */
#define ES_PASSLOG_MAX_CNT          ((ES_FLASH_PASS_LOG_SIZE-2048)/ES_PASSLOG_NODE_SIZE)

/*
 * 一条记录占一个bit，8条记录就是1字节, 1024字节可以记录8192条数据
 */ 
#if ((((ES_PASSLOG_MAX_CNT)/8) + 8) < 1024)
#define ES_PASSLOG_HEAD_SIZE        (1024)
#else
#define ES_PASSLOG_HEAD_SIZE        (2048)
#endif

/*
 * 记录内容的开始地址
 */ 
#define ES_PASSLOG_NODE_ADDR        (ES_FLASH_PASS_LOG_ADDR+ES_PASSLOG_HEAD_SIZE)

// {"u":"00112233445566778899aabbccddeeff","tm":1641296201,"tp":"36.01"}
#define ES_PASSLOG_NODE_JSON_LEN    (72)

#define PASSLOG_NODE_CPY(src, dst) do { \
        (dst)->timestamp = (src)->timestamp; \
        es_memcpy((dst)->uid, (src)->uid, ES_PASSLOG_UID_LEN); \
        es_memcpy((dst)->temp_val, (src)->temp_val, ES_PASSLOG_TEMP_LEN); \
    } while (0)

/*
 * 数据头占1024字节
 * magic和data_count共8字节,所以data_flags的长度要减8字节
 */
typedef struct {
    ES_U32 magic;
    ES_U32 data_count;
    ES_U8 data_flags[ES_PASSLOG_HEAD_SIZE-8];
} es_passlog_hdr_t;


/*
 * resv,是为了4字节对齐
 * timestamp,是U32类型，占4字节
 */
typedef struct {
    ES_U8 uid[ES_PASSLOG_UID_LEN];
    ES_U32 timestamp;
    ES_CHAR temp_val[ES_PASSLOG_TEMP_LEN];    // temperature value, "36.8" or "36.80"
    ES_U8 resv[ES_PASSLOG_NODE_SIZE-ES_PASSLOG_UID_LEN-4-ES_PASSLOG_TEMP_LEN];
} es_passlog_node_t;

typedef enum {
    PASSLOG_STAT_NOT_UP,
    PASSLOG_STAT_DOING_UP,
    PASSLOG_STAT_FINISH_UP
} passlog_status_e;

typedef struct {
    ES_U32 cache_count;
    ES_U8 logs_status[ES_PASSLOG_CACHE_COUNT]; // ref passlog_status_e
    es_passlog_t logs[ES_PASSLOG_CACHE_COUNT];
} es_passlog_cache_t;


static es_passlog_hdr_t passlog_hdr;
static es_passlog_cache_t passlog_cache;

static ES_S32 es_passlog_reset(ES_VOID)
{
    es_memset(&passlog_hdr, 0x00, sizeof(passlog_hdr));
    passlog_hdr.magic = ES_PASSLOG_HDR_MAGIC;
    if (0 != mf_flash.write(ES_FLASH_PASS_LOG_ADDR, (uint8_t *)&passlog_hdr, ES_PASSLOG_HEAD_SIZE)) {
        return ES_RET_FAILURE;
    }

    return ES_RET_SUCCESS;
}

static ES_S32 es_passlog_reload_cache(ES_VOID)
{
    ES_U32 i = 0;

    es_memset(&passlog_cache, 0x00, sizeof(passlog_cache));
    for (i = 0; i < ES_PASSLOG_CACHE_COUNT; i++) {
        passlog_cache.logs[i].id = ES_PASSLOG_MAX_CNT;
    }

    if (0 == passlog_hdr.data_count) {
        return ES_RET_SUCCESS;
    }

    passlog_cache.cache_count = es_passlog_read_from_flash(passlog_cache.logs, ES_PASSLOG_CACHE_COUNT);
    if (passlog_cache.cache_count == 0) {
        return ES_RET_SUCCESS;
    }
    for (i = 0; i < passlog_cache.cache_count; i++) {
        passlog_cache.logs_status[i] = PASSLOG_STAT_NOT_UP;
    }

    // es_passlog_debug("cache_count:%d", passlog_cache.cache_count);

    return ES_RET_SUCCESS;
}

static ES_U32 es_passlog_log2json(ES_CHAR *str, es_passlog_t *log)
{
    ES_U32 len = 0;

    len += es_sprintf(str+len, "{\"u\":\"");
    es_utils_byte_to_hex_str(str+len, log->uid, ES_PASSLOG_UID_LEN>>1);
    len += ES_PASSLOG_UID_LEN;
    len += es_sprintf(str+len, "\",\"tm\":%d,\"tp\":\"%s\"}", log->timestamp, log->temp_val);

    return len;
}

static ES_S32 es_passlog_get_stat(ES_U32 offset)
{
    if (offset >= (ES_PASSLOG_MAX_CNT)) {
        return ES_RET_FAILURE;
    }

    return (ES_S32)(ES_GET_BIT((passlog_hdr.data_flags[offset/8]), (offset % 8)));
}

static ES_S32 es_passlog_set_stat(ES_U32 offset, ES_U8 stat)
{
    if (offset >= (ES_PASSLOG_MAX_CNT)) {
        return ES_RET_FAILURE;
    }

    if (0 == stat) {
        ES_CLR_BIT((passlog_hdr.data_flags[offset/8]), (offset%8));
    } else {
        ES_SET_BIT((passlog_hdr.data_flags[offset/8]), (offset%8));
    }

    return ES_RET_SUCCESS;
}

static ES_U32 es_passlog_find_idle(ES_VOID)
{
    ES_U32 i = 0;

    if (ES_PASSLOG_MAX_CNT <= passlog_hdr.data_count) {
        return ES_PASSLOG_MAX_CNT;
    }

    for (i = 0; i < ES_PASSLOG_MAX_CNT; i++) {
        if (0 == es_passlog_get_stat(i)) {
            return i;
        }
    }

    return ES_PASSLOG_MAX_CNT;
}

static ES_U32 es_passlog_calc_node_addr(ES_U32 offset)
{
    return (ES_PASSLOG_NODE_ADDR)+(offset*ES_PASSLOG_NODE_SIZE);
}

ES_S32 es_passlog_init(ES_VOID)
{
    es_memset(&passlog_hdr, 0x00, sizeof(passlog_hdr));
    mf_flash.read(ES_FLASH_PASS_LOG_ADDR, (uint8_t *)&passlog_hdr, ES_PASSLOG_HEAD_SIZE);
    if (ES_PASSLOG_HDR_MAGIC != passlog_hdr.magic) {
        if (ES_RET_SUCCESS != es_passlog_reset()) {
            es_passlog_debug("passlog reset fail");
            return ES_RET_FAILURE;
        }
    }

    if (ES_RET_SUCCESS != es_passlog_reload_cache()) {
        es_passlog_debug("passlog reload cache fail");
        return ES_RET_FAILURE;
    }

    es_passlog_debug("passlog_count:%d", passlog_hdr.data_count);
    return ES_RET_SUCCESS;
}

ES_U32 es_passlog_get_count(ES_VOID)
{
    return passlog_hdr.data_count;
}

ES_S32 es_passlog_write(const es_passlog_t *log)
{
    ES_U32 offset;
    ES_U32 flash_addr;
    es_passlog_node_t node;

    offset = es_passlog_find_idle();
    if (ES_PASSLOG_MAX_CNT == offset) {
        return ES_RET_FAILURE;
    }

    flash_addr = es_passlog_calc_node_addr(offset);
    es_memset(&node, 0x00, sizeof(node));
    PASSLOG_NODE_CPY(log, (&node));
    if (0 != mf_flash.write(flash_addr, (uint8_t *)&node, ES_PASSLOG_NODE_SIZE)) {
        return ES_RET_FAILURE;
    }

    if (ES_RET_SUCCESS != es_passlog_set_stat(offset, 0x01)) {
        return ES_RET_FAILURE;
    }
    passlog_hdr.data_count++;

    if (0 != mf_flash.write(ES_FLASH_PASS_LOG_ADDR, (uint8_t *)&passlog_hdr, ES_PASSLOG_HEAD_SIZE)) {
        passlog_hdr.data_count--;
        es_passlog_set_stat(offset, 0x00);
        return ES_RET_FAILURE;
    }

    // add to cache
    if (passlog_cache.cache_count < (ES_PASSLOG_CACHE_COUNT>>1)) {
        passlog_cache.logs[passlog_cache.cache_count].id = offset;
        PASSLOG_NODE_CPY(log, (&passlog_cache.logs[passlog_cache.cache_count]));
        passlog_cache.cache_count++;
    }

    return ES_RET_SUCCESS;
}

ES_S32 es_passlog_read_from_flash(es_passlog_t *list, ES_U32 count)
{
    ES_U32 read_count = 0;
    ES_U32 i = 0;
    ES_U32 j = 0;
    es_passlog_node_t node;
    ES_U32 flash_addr;

    if (0 == passlog_hdr.data_count) {
        return ES_RET_SUCCESS;
    }

    read_count = count;
    if (read_count > passlog_hdr.data_count) {
        read_count = passlog_hdr.data_count;
    }

    for (i = 0; i < ES_PASSLOG_MAX_CNT; i++) {
        if (0 == es_passlog_get_stat(i)) {
            continue;
        }

        flash_addr = es_passlog_calc_node_addr(i);
        if (ES_RET_SUCCESS != mf_flash.read(flash_addr, (uint8_t *)&node, ES_PASSLOG_NODE_SIZE)) {
            break;
        }

        list[j].id = i;
        PASSLOG_NODE_CPY((&node), (&list[j]));
        j++;
        if (j >= read_count) {
            break;
        }
    }

    return j;
}

ES_S32 es_passlog_read_from_cache(es_passlog_t *list, ES_U32 count)
{
    ES_U32 i = 0;
    ES_S32 read_count = 0;

    // es_passlog_debug("cache_count:%d", passlog_cache.cache_count);
    if (0 == passlog_cache.cache_count) {
        return 0;
    }

    for (i = 0; i < passlog_cache.cache_count && i < count; i++) {
        if (PASSLOG_STAT_NOT_UP != passlog_cache.logs_status[i]) {
            es_passlog_debug("cache %d flag no need up", i);
            continue;
        }
        passlog_cache.logs_status[i] = PASSLOG_STAT_DOING_UP;
        memcpy(&list[read_count], &passlog_cache.logs[i], sizeof(es_passlog_t));
        read_count++;
    }

    return read_count;

}

ES_U32 es_passlog_read_json_str(ES_CHAR *str, ES_U32 str_len)
{
    ES_U32 json_str_len = 0;
    ES_U32 i = 0;

    // es_passlog_debug("cache_count:%d", passlog_cache.cache_count);
    if (0 == passlog_cache.cache_count) {
        return 0;
    }

    str[json_str_len] = '[';
    json_str_len++;

    for (i = 0; i < passlog_cache.cache_count; i++) {
         if (PASSLOG_STAT_NOT_UP != passlog_cache.logs_status[i]) {
            es_passlog_debug("cache %d flag no need up", i);
            continue;
        }

        passlog_cache.logs_status[i] = PASSLOG_STAT_DOING_UP;
        if (json_str_len > 1) {
            str[json_str_len] = ',';
            json_str_len++;
        }
        json_str_len += es_passlog_log2json(&str[json_str_len], &passlog_cache.logs[i]);
        if ((json_str_len + ES_PASSLOG_NODE_JSON_LEN) > str_len) {
            break;
        }
    }
    str[json_str_len] = ']';
    json_str_len++;


    return json_str_len;
}

ES_S32 es_passlog_set_upload_result(ES_BOOL success)
{
    ES_U32 i = 0;
    ES_U32 upload_count = 0;
    ES_U32 finish_count = 0;

    if (success) {
        // delete from flash
        for (i = 0; i < passlog_cache.cache_count; i++) {
            if (PASSLOG_STAT_DOING_UP == passlog_cache.logs_status[i]) {
                upload_count++;
                es_passlog_set_stat(passlog_cache.logs[i].id, 0);
                passlog_cache.logs_status[i] = PASSLOG_STAT_FINISH_UP;
                finish_count++;
            } else if (PASSLOG_STAT_FINISH_UP == passlog_cache.logs_status[i]) {
                finish_count++;
            }
        }

        passlog_hdr.data_count -= upload_count;
        mf_flash.write(ES_FLASH_PASS_LOG_ADDR, (uint8_t *)&passlog_hdr, ES_PASSLOG_HEAD_SIZE);

        // reload cache
        if (passlog_cache.cache_count == finish_count) {
            es_passlog_reload_cache();
        }
    } else {
        // recover form cache
        es_memset(passlog_cache.logs_status, 0x00, sizeof(passlog_cache.logs_status));
    }

    return ES_RET_SUCCESS;
}


ES_S32 es_passlog_delete(ES_U32 id)
{
    if (0 == passlog_hdr.data_count) {
        return ES_RET_FAILURE;
    }

    if (1 != es_passlog_get_stat(id)) {
        return ES_RET_FAILURE;
    }

    if (ES_RET_SUCCESS != es_passlog_set_stat(id, 0x00)) {
        return ES_RET_FAILURE;
    }
    passlog_hdr.data_count--;

    if (0 != mf_flash.write(ES_FLASH_PASS_LOG_ADDR, (uint8_t *)&passlog_hdr, ES_PASSLOG_HEAD_SIZE)) {
        passlog_hdr.data_count++;
        es_passlog_set_stat(id, 0x01);
        return ES_RET_FAILURE;
    }

    return ES_RET_SUCCESS;
}

ES_S32 es_passlog_delete_all(ES_VOID)
{
    if (0 == passlog_hdr.data_count) {
        return ES_RET_SUCCESS;
    }

    return es_passlog_reset();
}


ES_VOID es_passlog_try_upload(ES_VOID)
{
#if (0 == ES_TUYA_MODULE_ENABLE)
    static ES_U32 last_time = 0;
    ES_CHAR json_str[ES_PASSLOG_JSON_STR_LEN] = {0};
    ES_U32 json_str_len = 0;

    if (ES_RET_SUCCESS != es_time_check_timeout_ms(&last_time, 10000)) {
        return;
    }

    if (ES_NETWORK_CONNECTED != es_network_get_status()) {
        return;
    }

    if (0 == es_passlog_get_count()) {
        return;
    }

    json_str_len = es_passlog_read_json_str(json_str, ES_PASSLOG_JSON_STR_LEN);
    if (json_str_len < ES_PASSLOG_UID_LEN) {
        json_str_len = 0;
    }
    if (0 == json_str_len) {
        return;
    }

    if (ES_RET_SUCCESS != es_network_mqtt_upload_offline_log(json_str, json_str_len)) {
        es_passlog_set_upload_result(ES_FALSE);
    }
#endif
}

#endif
