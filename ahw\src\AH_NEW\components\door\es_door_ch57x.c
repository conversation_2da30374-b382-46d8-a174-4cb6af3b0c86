/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_door_ch57x.c
** bef: define the interface for ch57x.
** auth: lines<<EMAIL>>
** create on 2022.10.05 
*/

#include "es_inc.h"

#if ES_DOOR_ENABLE

// #define ES_DOOR_DEBUG
#ifdef ES_DOOR_DEBUG
#define es_door_debug es_log_info
#define es_door_error es_log_error
#else
#define es_door_debug(...)
#define es_door_error(...)
#endif

typedef struct {
    ES_U32 start_time;
    ES_U16 continue_ms;
    ES_U16 status; //  0 dile, 1, doing
} es_door_task_t;

static es_door_task_t door_task;
static struct pt pt_door;


static ES_S32 es_door_ch57x_open(ES_VOID)
{
    ES_U32 i = 0;
    es_flash_ble_mac_t flash_ble;
    es_ble_msg_t ble_msg;

    es_flash_read(ES_FLASH_ID_BLE_MAC, (ES_VOID *)&flash_ble);
    for (i = 0; i < flash_ble.count; i++) {
        es_memset(&ble_msg, 0x00, sizeof(ble_msg));
        memcpy(ble_msg.mac, flash_ble.mac[i], ES_BLE_MAC_LEN);
        memcpy(ble_msg.data, flash_ble.data[i], ES_BLE_PAYLOAD_DATA_LEN);
        memcpy(ble_msg.serv_uuid, flash_ble.serv_uuid[i], ES_BLE_UUID_LEN);
        memcpy(ble_msg.char_uuid, flash_ble.char_uuid[i], ES_BLE_UUID_LEN);
        ble_msg.to_slave = 1;
        ble_msg.data_len = flash_ble.data_len[i];
    	es_ble_add_msg(&ble_msg);
    }

    return ES_RET_SUCCESS;
}


static ES_S32 es_door_ch57x_close(ES_VOID)
{
    return ES_RET_SUCCESS;
}


ES_S32 es_door_init(ES_VOID)
{
    es_memset(&door_task, 0x00, sizeof(es_door_task_t));
    PT_INIT(&pt_door);
    return ES_RET_SUCCESS;
}

static PT_THREAD(es_door_task(struct pt *pt))
{
    PT_BEGIN(pt);
    if (1 != door_task.status) {
        goto TASK_END;
    }

    es_door_debug("door open");
    es_door_ch57x_open();

    if (door_task.continue_ms > 0) {
        door_task.start_time = es_time_get_sytem_ms();
        PT_WAIT_UNTIL(pt, ((es_time_get_sytem_ms() - door_task.start_time) > door_task.continue_ms));
        es_door_ch57x_close();
        es_door_debug("door close");
    }

TASK_END:
    es_memset(&door_task, 0x00, sizeof(es_door_task_t));
    PT_END(pt);
}

ES_VOID es_door_run(ES_VOID)
{
    es_door_task(&pt_door);
}

ES_S32 es_door_open(ES_U16 doing_ms)
{
     if (0 == door_task.status) {
        door_task.status = 1;
        door_task.continue_ms = (ES_U16)doing_ms;
        return ES_RET_SUCCESS;
    }

    return ES_RET_FAILURE;
}

#endif
