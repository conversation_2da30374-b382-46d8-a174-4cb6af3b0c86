/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_network_wifi.c
** bef: define the interface for wifi.
** auth: lines<<EMAIL>>
** create on 2021.12.21 
*/

#include "es_inc.h"

#if ES_WIFI_MODULE_ENABLE

// #define ES_NETWORK_WIFI_DEBUG
#ifdef ES_NETWORK_WIFI_DEBUG
#define es_network_wifi_debug es_log_info
#define es_network_wifi_error es_log_error
#else
#define es_network_wifi_debug(...)
#define es_network_wifi_error(...)
#endif


#define ES_WIFI_AT_RESTORE              ("AT+RESTORE\r\n")
#define ES_WIFI_AT_CWMODE               ("AT+CWMODE=1\r\n")
#define ES_WIFI_AT_CONN_AP              ("AT+CWJAP=\"%s\",\"%s\"")

#define WIFI_CMD_MAX_LEN                (128)
#define WIFI_CMD_LIST_COUNT             (5)

typedef enum {
    WIFI_CMD_RESP_OK                = 0x01,
    WIFI_CMD_RESP_GOT_IP            = 0x02,
    WIFI_CMD_RESP_HTTPC_DATA        = 0x03,
} wifi_cmd_resp_e;

typedef struct {
    ES_CHAR cmd[WIFI_CMD_MAX_LEN];
    ES_U8 resp_val;
} es_wifi_cmd_t;

typedef enum {
    HAL_WIFI_STATUS_CONNECTING,
    HAL_WIFI_STATUS_GOT_IP,
    HAL_WIFI_STATUS_INIT,
} es_hal_wifi_status_e;


static struct pt pt_send_cmd;
static struct pt_sem pt_sem_cmd_resp;
static es_wifi_cmd_t wifi_cmd_list[WIFI_CMD_LIST_COUNT] = {0};

static PT_THREAD(es_tuya_try_upload_dp(struct pt *pt))
{
    static int cmd_idx = 0;

    PT_BEGIN(pt);

    // dp_index = es_tuya_get_dp_data_index();
    // if (dp_index >= ES_TUYA_DP_DATA_COUNT) {
    //     return 0;
    // }

    // es_tuya_upload_dp_data(dp_index);
    // es_tuya_set_upload_param();
    // PT_SEM_WAIT(pt, &pt_sem_upload_resp);
    // // es_tuya_port_debug("dp_upload_got_resp:%d", dp_upload_got_resp);
    // es_tuya_dp_upload_result_notify(dp_index, dp_upload_got_resp);
    PT_END(pt);
}

static ES_VOID hal_wifi_uart_rx_cb(ES_U8 id, const ES_BYTE *buf, ES_U16 len)
{
}

static ES_S32 hal_wifi_init_uart(ES_VOID)
{
    es_uart_param_t uart_param;

	uart_param.baud = ES_UART_BAUD_115200;
	uart_param.data = ES_UART_DATA_8_BIT;
	uart_param.stop = ES_UART_STOP_1_BIT;
	uart_param.parity = ES_UART_PARITY_NONE;
	uart_param.rx_cb = hal_wifi_uart_rx_cb;
    if (ES_RET_SUCCESS != es_uart_open(ES_WIFI_UART_ID, &uart_param)) {
        es_network_wifi_error("open uart (%d) fail", ES_WIFI_UART_ID);
        return ES_RET_FAILURE;
    }

    return ES_RET_SUCCESS;
}


ES_S32 es_network_init(ES_VOID)
{
    if (ES_RET_SUCCESS != hal_wifi_init_uart()) {
        return ES_RET_FAILURE;
    }

    es_memset(wifi_cmd_list, 0x00, sizeof(wifi_cmd_list));

    // add cmd
    es_strncpy(wifi_cmd_list[0].cmd, ES_WIFI_AT_RESTORE, WIFI_CMD_MAX_LEN);
    wifi_cmd_list[0].resp_val = WIFI_CMD_RESP_OK;

    es_strncpy(wifi_cmd_list[1].cmd, ES_WIFI_AT_CWMODE, WIFI_CMD_MAX_LEN);
    wifi_cmd_list[1].resp_val = WIFI_CMD_RESP_OK;

    return ES_RET_SUCCESS;
}

ES_VOID es_network_task(ES_VOID)
{
}

ES_S32 es_network_get_status(ES_VOID)
{
    return ES_NETWORK_CONNECTING;
}

#endif
