/*
*---------------------------------------------------------------
*                        Lvgl Font Tool                         
*                                                               
* 注:使用unicode编码                                              
* 注:本字体文件由Lvgl Font Tool V0.2 生成                          
* 作者:阿里(qq:617622104)                                         
*---------------------------------------------------------------
*/


#include "../../lvgl.h"


static const uint8_t glyph_bitmap[] = {
/* 0 */
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x1a,0xfa,0x40,0x00,  //.....+%@@@@%+.....
0x00,0xaf,0xff,0xa0,0x00,  //....%@@@@@@@@%....
0x02,0xff,0xff,0xf8,0x00,  //...%@@@@@@@@@@%...
0x06,0xf9,0x06,0xf9,0x00,  //..+@@@@+..+@@@@+..
0x0b,0xe0,0x00,0xbe,0x00,  //..%@@@......@@@%..
0x0b,0x90,0x00,0x6e,0x00,  //..@@@+......+@@@..
0x1b,0x80,0x00,0x2e,0x40,  //.+@@@........@@@+.
0x2f,0x80,0x00,0x2f,0x80,  //.%@@@........@@@%.
0x2f,0x80,0x00,0x2f,0x80,  //.%@@%........%@@%.
0x2f,0x80,0x00,0x2f,0x80,  //.%@@%........%@@%.
0x2f,0x80,0x00,0x2f,0x80,  //.%@@%........%@@%.
0x2f,0x80,0x00,0x2f,0x80,  //.%@@%........%@@%.
0x2f,0x80,0x00,0x2f,0x80,  //.%@@%........%@@%.
0x2f,0x80,0x00,0x2f,0x80,  //.%@@%........%@@%.
0x2f,0x80,0x00,0x2f,0x80,  //.%@@%........%@@%.
0x2f,0x80,0x00,0x2f,0x80,  //.%@@@........@@@%.
0x1b,0x80,0x00,0x2e,0x40,  //.+@@@........@@@+.
0x0b,0x90,0x00,0x6e,0x00,  //..@@@+......+@@@..
0x0b,0xe0,0x00,0xbe,0x00,  //..%@@@......@@@%..
0x06,0xf9,0x06,0xf9,0x00,  //..+@@@@+..+@@@@+..
0x02,0xff,0xff,0xf8,0x00,  //...%@@@@@@@@@@%...
0x00,0xaf,0xff,0xa0,0x00,  //....%@@@@@@@@%....
0x00,0x1a,0xfa,0x40,0x00,  //.....+%@@@@%+.....
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................


/* 1 */
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x1a,0x40,0x00,  //.........+@@+.....
0x00,0x00,0x2e,0x40,0x00,  //.........@@@+.....
0x00,0x00,0xbe,0x40,0x00,  //........@@@@+.....
0x00,0x02,0xfe,0x40,0x00,  //.......@@@@@+.....
0x00,0x1b,0xfe,0x40,0x00,  //.....+@@@@@@+.....
0x01,0xbf,0xae,0x40,0x00,  //...+@@@@%@@@+.....
0x02,0xfa,0x2e,0x40,0x00,  //...%@@@%.@@@+.....
0x02,0xa0,0x2e,0x40,0x00,  //...%@%...@@@+.....
0x00,0x00,0x2e,0x40,0x00,  //.........@@@+.....
0x00,0x00,0x2e,0x40,0x00,  //.........@@@+.....
0x00,0x00,0x2e,0x40,0x00,  //.........@@@+.....
0x00,0x00,0x2e,0x40,0x00,  //.........@@@+.....
0x00,0x00,0x2e,0x40,0x00,  //.........@@@+.....
0x00,0x00,0x2e,0x40,0x00,  //.........@@@+.....
0x00,0x00,0x2e,0x40,0x00,  //.........@@@+.....
0x00,0x00,0x2e,0x40,0x00,  //.........@@@+.....
0x00,0x00,0x2e,0x40,0x00,  //.........@@@+.....
0x00,0x00,0x2e,0x40,0x00,  //.........@@@+.....
0x00,0x00,0x2e,0x40,0x00,  //.........@@@+.....
0x00,0x00,0x2e,0x40,0x00,  //.........@@@+.....
0x00,0x00,0x2e,0x40,0x00,  //.........@@@+.....
0x00,0x00,0x2e,0x40,0x00,  //.........@@@+.....
0x00,0x00,0x2e,0x40,0x00,  //.........@@@+.....
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................


/* 2 */
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x2b,0xfa,0x40,0x00,  //.....%%@@@@%+.....
0x02,0xbf,0xff,0xe0,0x00,  //...%@@@@@@@@@%....
0x0b,0xff,0xff,0xf8,0x00,  //..%@@@@@@@@@@@@...
0x1b,0xe4,0x06,0xf9,0x00,  //.+@@@@+...+@@@@+..
0x2f,0x80,0x00,0xbe,0x00,  //.%@@@.......@@@@..
0x2f,0x80,0x00,0x6e,0x00,  //.@@@%.......+@@@..
0x00,0x00,0x00,0x6e,0x00,  //............+@@@..
0x00,0x00,0x00,0x6e,0x00,  //............+@@@..
0x00,0x00,0x00,0xbe,0x00,  //............%@@@..
0x00,0x00,0x01,0xb9,0x00,  //...........+@@@+..
0x00,0x00,0x02,0xf8,0x00,  //...........@@@@...
0x00,0x00,0x0b,0xe4,0x00,  //..........@@@@+...
0x00,0x00,0x2f,0x90,0x00,  //.........@@@@+....
0x00,0x01,0xbe,0x40,0x00,  //.......+@@@@+.....
0x00,0x0a,0xf9,0x00,0x00,  //......%@@@@+......
0x00,0x2f,0xe0,0x00,0x00,  //.....%@@@%........
0x00,0xbf,0x80,0x00,0x00,  //....%@@@%.........
0x02,0xf9,0x00,0x00,0x00,  //...%@@@+..........
0x0b,0xe4,0x00,0x00,0x00,  //..%@@@+...........
0x1b,0x90,0x00,0x00,0x00,  //.+@@@+............
0x2f,0xff,0xff,0xfe,0x40,  //.%@@@@@@@@@@@@@@+.
0x2f,0xff,0xff,0xfe,0x40,  //.@@@@@@@@@@@@@@@+.
0x6f,0xff,0xff,0xfe,0x40,  //+@@@@@@@@@@@@@@@+.
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................


/* 3 */
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x1b,0xfa,0x00,0x00,  //.....+%@@@@%......
0x00,0xbf,0xff,0xa0,0x00,  //....@@@@@@@@@%....
0x02,0xff,0xff,0xf8,0x00,  //...@@@@@@@@@@@%...
0x0b,0xe8,0x06,0xf8,0x00,  //..%@@@%...+@@@@...
0x0b,0x90,0x01,0xb9,0x00,  //..@@@+.....+@@@+..
0x1b,0x80,0x00,0xbe,0x00,  //.+@@@.......%@@%..
0x00,0x00,0x00,0xbe,0x00,  //............@@@%..
0x00,0x00,0x01,0xb9,0x00,  //...........+@@@+..
0x00,0x00,0x0a,0xf8,0x00,  //..........%@@@%...
0x00,0x02,0xff,0xa0,0x00,  //.......@@@@@@%....
0x00,0x02,0xff,0xa0,0x00,  //.......@@@@@@%....
0x00,0x02,0xab,0xf8,0x00,  //.......%%%@@@@@...
0x00,0x00,0x01,0xbe,0x00,  //...........+@@@@..
0x00,0x00,0x00,0x6e,0x40,  //............+@@@+.
0x00,0x00,0x00,0x2f,0x80,  //.............%@@%.
0x00,0x00,0x00,0x2f,0x80,  //.............%@@%.
0x00,0x00,0x00,0x2f,0x80,  //.............%@@%.
0x2f,0x80,0x00,0x2f,0x80,  //.%@@%........@@@%.
0x1b,0x90,0x00,0xbe,0x40,  //.+@@@+......%@@@+.
0x0b,0xe8,0x06,0xfe,0x00,  //..@@@@%...+%@@@%..
0x06,0xff,0xff,0xf8,0x00,  //..+@@@@@@@@@@@@...
0x01,0xbf,0xff,0xe0,0x00,  //...+@@@@@@@@@%....
0x00,0x1b,0xfa,0x40,0x00,  //.....+%@@@@%+.....
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................


/* 4 */
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x06,0xe0,0x00,  //..........+@@%....
0x00,0x00,0x1b,0xe0,0x00,  //.........+@@@%....
0x00,0x00,0x2f,0xe0,0x00,  //.........@@@@%....
0x00,0x00,0xbf,0xe0,0x00,  //........%@@@@%....
0x00,0x01,0xbf,0xe0,0x00,  //.......+@@@@@%....
0x00,0x02,0xeb,0xe0,0x00,  //.......@@@%@@%....
0x00,0x0b,0xeb,0xe0,0x00,  //......%@@%%@@%....
0x00,0x1b,0x8b,0xe0,0x00,  //.....+@@%.%@@%....
0x00,0x2e,0x0b,0xe0,0x00,  //.....@@@..%@@%....
0x00,0xb9,0x0b,0xe0,0x00,  //....@@@+..%@@%....
0x02,0xf8,0x0b,0xe0,0x00,  //...%@@%...%@@%....
0x06,0xe0,0x0b,0xe0,0x00,  //..+@@@....%@@%....
0x0b,0x90,0x0b,0xe0,0x00,  //..@@@+....%@@%....
0x2f,0x80,0x0b,0xe0,0x00,  //.%@@%.....%@@%....
0x6e,0x00,0x0b,0xe0,0x00,  //+@@@......%@@%....
0xbf,0xff,0xff,0xff,0x80,  //%@@@@@@@@@@@@@@@%.
0xbf,0xff,0xff,0xff,0x80,  //%@@@@@@@@@@@@@@@%.
0xbf,0xff,0xff,0xff,0x80,  //%@@@@@@@@@@@@@@@%.
0x00,0x00,0x0b,0xe0,0x00,  //..........%@@%....
0x00,0x00,0x0b,0xe0,0x00,  //..........%@@%....
0x00,0x00,0x0b,0xe0,0x00,  //..........%@@%....
0x00,0x00,0x0b,0xe0,0x00,  //..........%@@%....
0x00,0x00,0x0b,0xe0,0x00,  //..........%@@%....
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................


/* 5 */
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0xbf,0xff,0xfe,0x00,  //....@@@@@@@@@@@%..
0x01,0xbf,0xff,0xfe,0x00,  //...+@@@@@@@@@@@%..
0x02,0xff,0xff,0xfe,0x00,  //...%@@@@@@@@@@@%..
0x02,0xf8,0x00,0x00,0x00,  //...%@@%...........
0x02,0xe4,0x00,0x00,0x00,  //...@@@+...........
0x02,0xe0,0x00,0x00,0x00,  //...@@@............
0x06,0xe0,0x00,0x00,0x00,  //..+@@@............
0x0b,0xe6,0xfe,0x40,0x00,  //..%@@%+%@@@%+.....
0x0b,0xaf,0xff,0xe0,0x00,  //..%@@%@@@@@@@%....
0x0b,0xff,0xff,0xf8,0x00,  //..@@@@@@@@@@@@@...
0x0b,0xe8,0x06,0xfe,0x00,  //..@@@@%...+%@@@%..
0x1b,0x90,0x00,0xbe,0x40,  //.+@@@+......%@@@+.
0x00,0x00,0x00,0x2f,0x80,  //.............@@@%.
0x00,0x00,0x00,0x2f,0x80,  //.............%@@%.
0x00,0x00,0x00,0x2f,0x80,  //.............%@@%.
0x00,0x00,0x00,0x2f,0x80,  //.............%@@%.
0x00,0x00,0x00,0x2f,0x80,  //.............%@@%.
0x2f,0x80,0x00,0x6e,0x40,  //.%@@%.......+@@@+.
0x1b,0x90,0x00,0xbe,0x00,  //.+@@@+......@@@@..
0x0b,0xe8,0x06,0xf9,0x00,  //..@@@@%...+@@@@+..
0x06,0xff,0xff,0xf8,0x00,  //..+@@@@@@@@@@@%...
0x01,0xbf,0xff,0xa0,0x00,  //...+@@@@@@@@@%....
0x00,0x1b,0xfa,0x00,0x00,  //.....+%@@@@%......
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................


/* 6 */
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x0a,0xfe,0x80,0x00,  //......%%@@@%%.....
0x00,0x6f,0xff,0xe4,0x00,  //....+@@@@@@@@@+...
0x01,0xbf,0xff,0xf8,0x00,  //...+@@@@@@@@@@@...
0x06,0xfa,0x02,0xbe,0x00,  //..+@@@@%...%@@@%..
0x0b,0xe4,0x00,0xbe,0x00,  //..%@@@+.....%@@@..
0x0b,0x90,0x00,0x2e,0x40,  //..@@@+.......@@@+.
0x1b,0x80,0x00,0x00,0x00,  //.+@@@.............
0x2f,0x80,0x00,0x00,0x00,  //.%@@%.............
0x2f,0x86,0xbe,0x80,0x00,  //.%@@%.+%@@@@%.....
0x2e,0x6f,0xff,0xe4,0x00,  //.@@@+%@@@@@@@@+...
0x2e,0xbf,0xff,0xf9,0x00,  //.@@@%@@@@@@@@@@+..
0x2f,0xf9,0x02,0xfe,0x00,  //.@@@@@%+...%@@@@..
0x2f,0xe0,0x00,0xbe,0x40,  //.@@@@%......%@@@+.
0x2f,0x80,0x00,0x2f,0x80,  //.@@@@........@@@%.
0x2f,0x80,0x00,0x2f,0x80,  //.@@@%........%@@%.
0x2f,0x80,0x00,0x2f,0x80,  //.%@@%........%@@%.
0x2f,0x80,0x00,0x2f,0x80,  //.%@@@........%@@%.
0x1b,0x90,0x00,0x2f,0x80,  //.+@@@+.......@@@%.
0x0b,0xe0,0x00,0xbe,0x40,  //..@@@@......%@@@+.
0x06,0xf9,0x02,0xfe,0x00,  //..+@@@@+...%@@@%..
0x02,0xff,0xff,0xf8,0x00,  //...%@@@@@@@@@@@...
0x00,0xaf,0xff,0xe0,0x00,  //....%@@@@@@@@%....
0x00,0x0a,0xfa,0x40,0x00,  //......%@@@@%+.....
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................


/* 7 */
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x2f,0xff,0xff,0xff,0x80,  //.%@@@@@@@@@@@@@@%.
0x2f,0xff,0xff,0xff,0x80,  //.%@@@@@@@@@@@@@@%.
0x2f,0xff,0xff,0xff,0x80,  //.%@@@@@@@@@@@@@@%.
0x00,0x00,0x00,0xbe,0x00,  //............%@@@..
0x00,0x00,0x02,0xf8,0x00,  //...........%@@@...
0x00,0x00,0x06,0xe4,0x00,  //..........+@@@+...
0x00,0x00,0x0b,0xe0,0x00,  //..........@@@%....
0x00,0x00,0x2f,0x80,0x00,  //.........%@@@.....
0x00,0x00,0x2e,0x40,0x00,  //.........@@@+.....
0x00,0x00,0xbe,0x00,0x00,  //........%@@@......
0x00,0x01,0xb9,0x00,0x00,  //.......+@@@+......
0x00,0x02,0xf8,0x00,0x00,  //.......%@@@.......
0x00,0x02,0xe4,0x00,0x00,  //.......@@@+.......
0x00,0x06,0xe0,0x00,0x00,  //......+@@@........
0x00,0x0b,0xe0,0x00,0x00,  //......%@@%........
0x00,0x0b,0x90,0x00,0x00,  //......@@@+........
0x00,0x1b,0x80,0x00,0x00,  //.....+@@@.........
0x00,0x2f,0x80,0x00,0x00,  //.....%@@%.........
0x00,0x2e,0x40,0x00,0x00,  //.....@@@+.........
0x00,0x2e,0x40,0x00,0x00,  //.....@@@+.........
0x00,0x6e,0x00,0x00,0x00,  //....+@@@..........
0x00,0x6e,0x00,0x00,0x00,  //....+@@@..........
0x00,0x6e,0x00,0x00,0x00,  //....+@@@..........
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................


/* 8 */
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x1a,0xfa,0x40,0x00,  //.....+%@@@@%+.....
0x00,0xbf,0xff,0xe0,0x00,  //....%@@@@@@@@%....
0x02,0xff,0xff,0xf8,0x00,  //...@@@@@@@@@@@%...
0x06,0xf9,0x06,0xf9,0x00,  //..+@@@%+..+@@@@+..
0x0b,0xe0,0x00,0xbe,0x00,  //..%@@@......@@@%..
0x0b,0x90,0x00,0xbe,0x00,  //..@@@+......%@@@..
0x0b,0x90,0x00,0xbe,0x00,  //..@@@+......%@@@..
0x0b,0xe0,0x00,0xbe,0x00,  //..%@@@......@@@%..
0x06,0xf9,0x06,0xf8,0x00,  //..+@@@%+..+%@@@...
0x01,0xbf,0xff,0xe4,0x00,  //...+@@@@@@@@@@+...
0x00,0x6f,0xff,0x80,0x00,  //....+@@@@@@@%.....
0x02,0xff,0xff,0xe8,0x00,  //...%@@@@@@@@@@%...
0x0b,0xf8,0x02,0xfe,0x00,  //..%@@@%....%@@@%..
0x1b,0xe0,0x00,0xbe,0x40,  //.+@@@%......%@@@+.
0x2f,0x80,0x00,0x2f,0x80,  //.%@@@........@@@%.
0x2f,0x80,0x00,0x2f,0x80,  //.%@@%........%@@%.
0x2f,0x80,0x00,0x2f,0x80,  //.%@@%........%@@%.
0x2f,0x80,0x00,0x2f,0x80,  //.%@@@........@@@%.
0x1b,0xe0,0x00,0xbe,0x40,  //.+@@@%......%@@@+.
0x0b,0xf8,0x02,0xfe,0x00,  //..@@@@%....%@@@@..
0x06,0xff,0xff,0xf9,0x00,  //..+@@@@@@@@@@@@+..
0x00,0xbf,0xff,0xe0,0x00,  //....@@@@@@@@@@....
0x00,0x1a,0xfa,0x40,0x00,  //.....+%@@@@%+.....
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................


/* 9 */
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x1b,0xfa,0x00,0x00,  //.....+%@@@@%......
0x00,0xbf,0xff,0x90,0x00,  //....@@@@@@@@@+....
0x02,0xff,0xff,0xf8,0x00,  //...@@@@@@@@@@@%...
0x0b,0xf9,0x06,0xf9,0x00,  //..%@@@%+..+@@@@+..
0x1b,0xe0,0x00,0xbe,0x00,  //.+@@@%......@@@%..
0x2f,0x80,0x00,0x6e,0x40,  //.%@@@.......+@@@+.
0x2f,0x80,0x00,0x2e,0x40,  //.%@@%........@@@+.
0x2f,0x80,0x00,0x2f,0x80,  //.%@@%........%@@%.
0x2f,0x80,0x00,0x2f,0x80,  //.%@@%........@@@%.
0x2f,0x80,0x00,0x6f,0x80,  //.%@@@.......+@@@%.
0x1b,0xe0,0x00,0xbf,0x80,  //.+@@@%......%@@@%.
0x0b,0xf8,0x06,0xff,0x80,  //..@@@@%...+%@@@@%.
0x06,0xff,0xff,0xef,0x80,  //..+@@@@@@@@@@%@@%.
0x01,0xbf,0xfe,0xaf,0x80,  //...+@@@@@@@@%%@@%.
0x00,0x2b,0xe8,0x2f,0x80,  //.....%@@@@%..%@@%.
0x00,0x00,0x00,0x2e,0x40,  //.............@@@+.
0x00,0x00,0x00,0x6e,0x40,  //............+@@@+.
0x1b,0x80,0x00,0xbe,0x00,  //.+@@@.......%@@@..
0x0b,0xe0,0x01,0xbe,0x00,  //..@@@%.....+@@@%..
0x0b,0xe8,0x0a,0xf8,0x00,  //..%@@@%...%@@@@...
0x06,0xff,0xff,0xe4,0x00,  //..+@@@@@@@@@@@+...
0x01,0xbf,0xff,0x90,0x00,  //...+@@@@@@@@@+....
0x00,0x2b,0xf9,0x00,0x00,  //.....%@@@@%+......
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................
0x00,0x00,0x00,0x00,0x00,  //..................


/* : */
0x00,0x00,0x00,  //.........
0x00,0x00,0x00,  //.........
0x00,0x00,0x00,  //.........
0x00,0x00,0x00,  //.........
0x00,0x00,0x00,  //.........
0x00,0x00,0x00,  //.........
0x00,0x00,0x00,  //.........
0x00,0x00,0x00,  //.........
0x00,0x00,0x00,  //.........
0x00,0x00,0x00,  //.........
0x00,0x00,0x00,  //.........
0x00,0x00,0x00,  //.........
0x06,0xe4,0x00,  //..+@@@+..
0x06,0xe4,0x00,  //..+@@@+..
0x06,0xe4,0x00,  //..+@@@+..
0x00,0x00,0x00,  //.........
0x00,0x00,0x00,  //.........
0x00,0x00,0x00,  //.........
0x00,0x00,0x00,  //.........
0x00,0x00,0x00,  //.........
0x00,0x00,0x00,  //.........
0x00,0x00,0x00,  //.........
0x00,0x00,0x00,  //.........
0x00,0x00,0x00,  //.........
0x00,0x00,0x00,  //.........
0x00,0x00,0x00,  //.........
0x06,0xe4,0x00,  //..+@@@+..
0x06,0xe4,0x00,  //..+@@@+..
0x06,0xe4,0x00,  //..+@@@+..
0x00,0x00,0x00,  //.........
0x00,0x00,0x00,  //.........
0x00,0x00,0x00,  //.........
0x00,0x00,0x00,  //.........
0x00,0x00,0x00,  //.........
0x00,0x00,0x00,  //.........
0x00,0x00,0x00,  //.........


};


static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0,	.adv_w = 18, .box_h = 36, .box_w = 20, .ofs_x = 0, .ofs_y = 0},/*(0)*/
    {.bitmap_index = 180,	.adv_w = 18, .box_h = 36, .box_w = 20, .ofs_x = 0, .ofs_y = 0},/*(1)*/
    {.bitmap_index = 360,	.adv_w = 18, .box_h = 36, .box_w = 20, .ofs_x = 0, .ofs_y = 0},/*(2)*/
    {.bitmap_index = 540,	.adv_w = 18, .box_h = 36, .box_w = 20, .ofs_x = 0, .ofs_y = 0},/*(3)*/
    {.bitmap_index = 720,	.adv_w = 18, .box_h = 36, .box_w = 20, .ofs_x = 0, .ofs_y = 0},/*(4)*/
    {.bitmap_index = 900,	.adv_w = 18, .box_h = 36, .box_w = 20, .ofs_x = 0, .ofs_y = 0},/*(5)*/
    {.bitmap_index = 1080,	.adv_w = 18, .box_h = 36, .box_w = 20, .ofs_x = 0, .ofs_y = 0},/*(6)*/
    {.bitmap_index = 1260,	.adv_w = 18, .box_h = 36, .box_w = 20, .ofs_x = 0, .ofs_y = 0},/*(7)*/
    {.bitmap_index = 1440,	.adv_w = 18, .box_h = 36, .box_w = 20, .ofs_x = 0, .ofs_y = 0},/*(8)*/
    {.bitmap_index = 1620,	.adv_w = 18, .box_h = 36, .box_w = 20, .ofs_x = 0, .ofs_y = 0},/*(9)*/
    {.bitmap_index = 1800,	.adv_w = 9, .box_h = 36, .box_w = 12, .ofs_x = 0, .ofs_y = 0},/*(:)*/
};


static const uint16_t unicode_list_1[] = {
    0x0030,	/*(0)*/
    0x0031,	/*(1)*/
    0x0032,	/*(2)*/
    0x0033,	/*(3)*/
    0x0034,	/*(4)*/
    0x0035,	/*(5)*/
    0x0036,	/*(6)*/
    0x0037,	/*(7)*/
    0x0038,	/*(8)*/
    0x0039,	/*(9)*/
    0x003a,	/*(:)*/
    0x0000,    /*End indicator*/
};


static const lv_font_fmt_txt_cmap_t cmaps[] = {
    {
        .range_start = 48,
        .range_length = 11,
        .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY,
        .glyph_id_start = 0,
        .unicode_list = unicode_list_1,
        .glyph_id_ofs_list = NULL,
        .list_length = 11,
    }
};


static lv_font_fmt_txt_dsc_t font_dsc = {
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .cmap_num = 1,
    .bpp = 2,

    .kern_scale = 0,
    .kern_dsc = NULL,
    .kern_classes = 0,
};


static int binsearch(const uint16_t *sortedSeq, int seqLength, uint16_t keyData) {
    int low = 0, mid, high = seqLength - 1;
    while (low <= high) {
        mid = (low + high) / 2;//奇数，无论奇偶，有个值就行
        if (keyData < sortedSeq[mid]) {
            high = mid - 1;//是mid-1，因为mid已经比较过了
        }
        else if (keyData > sortedSeq[mid]) {
            low = mid + 1;
        }
        else {
            return mid;
        }
    }
    return -1;
}


static const uint8_t * __user_font_get_bitmap(const lv_font_t * font, uint32_t unicode_letter) {
    lv_font_fmt_txt_dsc_t * fdsc = (lv_font_fmt_txt_dsc_t *) font->dsc;

    if(unicode_letter < fdsc->cmaps[0].range_start) return NULL;

    int i = binsearch(fdsc->cmaps[0].unicode_list, fdsc->cmaps[0].list_length, unicode_letter);
    if( i != -1 ) {
        const lv_font_fmt_txt_glyph_dsc_t * gdsc = &fdsc->glyph_dsc[i];
        return &fdsc->glyph_bitmap[gdsc->bitmap_index];
    }
    return NULL;
}


static bool __user_font_get_glyph_dsc(const lv_font_t * font, lv_font_glyph_dsc_t * dsc_out, uint32_t unicode_letter, uint32_t unicode_letter_next) {
    lv_font_fmt_txt_dsc_t * fdsc = (lv_font_fmt_txt_dsc_t *) font->dsc;

    if(unicode_letter < fdsc->cmaps[0].range_start) return false;

    int i = binsearch(fdsc->cmaps[0].unicode_list, fdsc->cmaps[0].list_length, unicode_letter);
    if( i != -1 ) {
        const lv_font_fmt_txt_glyph_dsc_t * gdsc = &fdsc->glyph_dsc[i];
        dsc_out->adv_w = gdsc->adv_w;
        dsc_out->box_h = gdsc->box_h;
        dsc_out->box_w = gdsc->box_w;
        dsc_out->ofs_x = gdsc->ofs_x;
        dsc_out->ofs_y = gdsc->ofs_y;
        dsc_out->bpp   = fdsc->bpp;
        return true;
    }
    return false;
}


//Arial,Regular,24
//字模高度：36
//内部字体
//使用排序和二分查表
const lv_font_t lv_font_gb2312_28 = {
    .dsc = &font_dsc,
    .get_glyph_bitmap = __user_font_get_bitmap,
    .get_glyph_dsc = __user_font_get_glyph_dsc,
    .line_height = 36,
    .base_line = 0,
};

//end of file