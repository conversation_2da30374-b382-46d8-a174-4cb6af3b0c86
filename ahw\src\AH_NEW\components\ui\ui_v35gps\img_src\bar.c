#include "es_inc.h"
#if (ES_UI_TYPE == ES_UI_TYPE_K35V_240_320_GPS)
//LVGL SJPG C ARRAY
#include "lvgl.h"

const uint8_t bar_map[] = {
	0x5f,	0x53,	0x4a,	0x50,	0x47,	0x5f,	0x5f,	0x0,	0x56,	0x31,	0x2e,	0x30,	0x30,	0x0,	0xf0,	0x0,
	0x50,	0x0,	0x5,	0x0,	0x10,	0x0,	0xaf,	0x2,	0xaf,	0x2,	0xaf,	0x2,	0xaf,	0x2,	0xaf,	0x2,
	0xff,	0xd8,	0xff,	0xe0,	0x0,	0x10,	0x4a,	0x46,	0x49,	0x46,	0x0,	0x1,	0x1,	0x0,	0x0,	0x1,
	0x0,	0x1,	0x0,	0x0,	0xff,	0xdb,	0x0,	0x43,	0x0,	0x3,	0x2,	0x2,	0x3,	0x2,	0x2,	0x3,
	0x3,	0x3,	0x3,	0x4,	0x3,	0x3,	0x4,	0x5,	0x8,	0x5,	0x5,	0x4,	0x4,	0x5,	0xa,	0x7,
	0x7,	0x6,	0x8,	0xc,	0xa,	0xc,	0xc,	0xb,	0xa,	0xb,	0xb,	0xd,	0xe,	0x12,	0x10,	0xd,
	0xe,	0x11,	0xe,	0xb,	0xb,	0x10,	0x16,	0x10,	0x11,	0x13,	0x14,	0x15,	0x15,	0x15,	0xc,	0xf,
	0x17,	0x18,	0x16,	0x14,	0x18,	0x12,	0x14,	0x15,	0x14,	0xff,	0xdb,	0x0,	0x43,	0x1,	0x3,	0x4,
	0x4,	0x5,	0x4,	0x5,	0x9,	0x5,	0x5,	0x9,	0x14,	0xd,	0xb,	0xd,	0x14,	0x14,	0x14,	0x14,
	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,
	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,
	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0xff,	0xc0,
	0x0,	0x11,	0x8,	0x0,	0x10,	0x0,	0xf0,	0x3,	0x1,	0x22,	0x0,	0x2,	0x11,	0x1,	0x3,	0x11,
	0x1,	0xff,	0xc4,	0x0,	0x1f,	0x0,	0x0,	0x1,	0x5,	0x1,	0x1,	0x1,	0x1,	0x1,	0x1,	0x0,
	0x0,	0x0,	0x0,	0x0,	0x0,	0x0,	0x0,	0x1,	0x2,	0x3,	0x4,	0x5,	0x6,	0x7,	0x8,	0x9,
	0xa,	0xb,	0xff,	0xc4,	0x0,	0xb5,	0x10,	0x0,	0x2,	0x1,	0x3,	0x3,	0x2,	0x4,	0x3,	0x5,
	0x5,	0x4,	0x4,	0x0,	0x0,	0x1,	0x7d,	0x1,	0x2,	0x3,	0x0,	0x4,	0x11,	0x5,	0x12,	0x21,
	0x31,	0x41,	0x6,	0x13,	0x51,	0x61,	0x7,	0x22,	0x71,	0x14,	0x32,	0x81,	0x91,	0xa1,	0x8,	0x23,
	0x42,	0xb1,	0xc1,	0x15,	0x52,	0xd1,	0xf0,	0x24,	0x33,	0x62,	0x72,	0x82,	0x9,	0xa,	0x16,	0x17,
	0x18,	0x19,	0x1a,	0x25,	0x26,	0x27,	0x28,	0x29,	0x2a,	0x34,	0x35,	0x36,	0x37,	0x38,	0x39,	0x3a,
	0x43,	0x44,	0x45,	0x46,	0x47,	0x48,	0x49,	0x4a,	0x53,	0x54,	0x55,	0x56,	0x57,	0x58,	0x59,	0x5a,
	0x63,	0x64,	0x65,	0x66,	0x67,	0x68,	0x69,	0x6a,	0x73,	0x74,	0x75,	0x76,	0x77,	0x78,	0x79,	0x7a,
	0x83,	0x84,	0x85,	0x86,	0x87,	0x88,	0x89,	0x8a,	0x92,	0x93,	0x94,	0x95,	0x96,	0x97,	0x98,	0x99,
	0x9a,	0xa2,	0xa3,	0xa4,	0xa5,	0xa6,	0xa7,	0xa8,	0xa9,	0xaa,	0xb2,	0xb3,	0xb4,	0xb5,	0xb6,	0xb7,
	0xb8,	0xb9,	0xba,	0xc2,	0xc3,	0xc4,	0xc5,	0xc6,	0xc7,	0xc8,	0xc9,	0xca,	0xd2,	0xd3,	0xd4,	0xd5,
	0xd6,	0xd7,	0xd8,	0xd9,	0xda,	0xe1,	0xe2,	0xe3,	0xe4,	0xe5,	0xe6,	0xe7,	0xe8,	0xe9,	0xea,	0xf1,
	0xf2,	0xf3,	0xf4,	0xf5,	0xf6,	0xf7,	0xf8,	0xf9,	0xfa,	0xff,	0xc4,	0x0,	0x1f,	0x1,	0x0,	0x3,
	0x1,	0x1,	0x1,	0x1,	0x1,	0x1,	0x1,	0x1,	0x1,	0x0,	0x0,	0x0,	0x0,	0x0,	0x0,	0x1,
	0x2,	0x3,	0x4,	0x5,	0x6,	0x7,	0x8,	0x9,	0xa,	0xb,	0xff,	0xc4,	0x0,	0xb5,	0x11,	0x0,
	0x2,	0x1,	0x2,	0x4,	0x4,	0x3,	0x4,	0x7,	0x5,	0x4,	0x4,	0x0,	0x1,	0x2,	0x77,	0x0,
	0x1,	0x2,	0x3,	0x11,	0x4,	0x5,	0x21,	0x31,	0x6,	0x12,	0x41,	0x51,	0x7,	0x61,	0x71,	0x13,
	0x22,	0x32,	0x81,	0x8,	0x14,	0x42,	0x91,	0xa1,	0xb1,	0xc1,	0x9,	0x23,	0x33,	0x52,	0xf0,	0x15,
	0x62,	0x72,	0xd1,	0xa,	0x16,	0x24,	0x34,	0xe1,	0x25,	0xf1,	0x17,	0x18,	0x19,	0x1a,	0x26,	0x27,
	0x28,	0x29,	0x2a,	0x35,	0x36,	0x37,	0x38,	0x39,	0x3a,	0x43,	0x44,	0x45,	0x46,	0x47,	0x48,	0x49,
	0x4a,	0x53,	0x54,	0x55,	0x56,	0x57,	0x58,	0x59,	0x5a,	0x63,	0x64,	0x65,	0x66,	0x67,	0x68,	0x69,
	0x6a,	0x73,	0x74,	0x75,	0x76,	0x77,	0x78,	0x79,	0x7a,	0x82,	0x83,	0x84,	0x85,	0x86,	0x87,	0x88,
	0x89,	0x8a,	0x92,	0x93,	0x94,	0x95,	0x96,	0x97,	0x98,	0x99,	0x9a,	0xa2,	0xa3,	0xa4,	0xa5,	0xa6,
	0xa7,	0xa8,	0xa9,	0xaa,	0xb2,	0xb3,	0xb4,	0xb5,	0xb6,	0xb7,	0xb8,	0xb9,	0xba,	0xc2,	0xc3,	0xc4,
	0xc5,	0xc6,	0xc7,	0xc8,	0xc9,	0xca,	0xd2,	0xd3,	0xd4,	0xd5,	0xd6,	0xd7,	0xd8,	0xd9,	0xda,	0xe2,
	0xe3,	0xe4,	0xe5,	0xe6,	0xe7,	0xe8,	0xe9,	0xea,	0xf2,	0xf3,	0xf4,	0xf5,	0xf6,	0xf7,	0xf8,	0xf9,
	0xfa,	0xff,	0xda,	0x0,	0xc,	0x3,	0x1,	0x0,	0x2,	0x11,	0x3,	0x11,	0x0,	0x3f,	0x0,	0xfc,
	0xac,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,
	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,
	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,
	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x3,	0xff,	0xd9,	0xff,
	0xd8,	0xff,	0xe0,	0x0,	0x10,	0x4a,	0x46,	0x49,	0x46,	0x0,	0x1,	0x1,	0x0,	0x0,	0x1,	0x0,
	0x1,	0x0,	0x0,	0xff,	0xdb,	0x0,	0x43,	0x0,	0x3,	0x2,	0x2,	0x3,	0x2,	0x2,	0x3,	0x3,
	0x3,	0x3,	0x4,	0x3,	0x3,	0x4,	0x5,	0x8,	0x5,	0x5,	0x4,	0x4,	0x5,	0xa,	0x7,	0x7,
	0x6,	0x8,	0xc,	0xa,	0xc,	0xc,	0xb,	0xa,	0xb,	0xb,	0xd,	0xe,	0x12,	0x10,	0xd,	0xe,
	0x11,	0xe,	0xb,	0xb,	0x10,	0x16,	0x10,	0x11,	0x13,	0x14,	0x15,	0x15,	0x15,	0xc,	0xf,	0x17,
	0x18,	0x16,	0x14,	0x18,	0x12,	0x14,	0x15,	0x14,	0xff,	0xdb,	0x0,	0x43,	0x1,	0x3,	0x4,	0x4,
	0x5,	0x4,	0x5,	0x9,	0x5,	0x5,	0x9,	0x14,	0xd,	0xb,	0xd,	0x14,	0x14,	0x14,	0x14,	0x14,
	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,
	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,
	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0xff,	0xc0,	0x0,
	0x11,	0x8,	0x0,	0x10,	0x0,	0xf0,	0x3,	0x1,	0x22,	0x0,	0x2,	0x11,	0x1,	0x3,	0x11,	0x1,
	0xff,	0xc4,	0x0,	0x1f,	0x0,	0x0,	0x1,	0x5,	0x1,	0x1,	0x1,	0x1,	0x1,	0x1,	0x0,	0x0,
	0x0,	0x0,	0x0,	0x0,	0x0,	0x0,	0x1,	0x2,	0x3,	0x4,	0x5,	0x6,	0x7,	0x8,	0x9,	0xa,
	0xb,	0xff,	0xc4,	0x0,	0xb5,	0x10,	0x0,	0x2,	0x1,	0x3,	0x3,	0x2,	0x4,	0x3,	0x5,	0x5,
	0x4,	0x4,	0x0,	0x0,	0x1,	0x7d,	0x1,	0x2,	0x3,	0x0,	0x4,	0x11,	0x5,	0x12,	0x21,	0x31,
	0x41,	0x6,	0x13,	0x51,	0x61,	0x7,	0x22,	0x71,	0x14,	0x32,	0x81,	0x91,	0xa1,	0x8,	0x23,	0x42,
	0xb1,	0xc1,	0x15,	0x52,	0xd1,	0xf0,	0x24,	0x33,	0x62,	0x72,	0x82,	0x9,	0xa,	0x16,	0x17,	0x18,
	0x19,	0x1a,	0x25,	0x26,	0x27,	0x28,	0x29,	0x2a,	0x34,	0x35,	0x36,	0x37,	0x38,	0x39,	0x3a,	0x43,
	0x44,	0x45,	0x46,	0x47,	0x48,	0x49,	0x4a,	0x53,	0x54,	0x55,	0x56,	0x57,	0x58,	0x59,	0x5a,	0x63,
	0x64,	0x65,	0x66,	0x67,	0x68,	0x69,	0x6a,	0x73,	0x74,	0x75,	0x76,	0x77,	0x78,	0x79,	0x7a,	0x83,
	0x84,	0x85,	0x86,	0x87,	0x88,	0x89,	0x8a,	0x92,	0x93,	0x94,	0x95,	0x96,	0x97,	0x98,	0x99,	0x9a,
	0xa2,	0xa3,	0xa4,	0xa5,	0xa6,	0xa7,	0xa8,	0xa9,	0xaa,	0xb2,	0xb3,	0xb4,	0xb5,	0xb6,	0xb7,	0xb8,
	0xb9,	0xba,	0xc2,	0xc3,	0xc4,	0xc5,	0xc6,	0xc7,	0xc8,	0xc9,	0xca,	0xd2,	0xd3,	0xd4,	0xd5,	0xd6,
	0xd7,	0xd8,	0xd9,	0xda,	0xe1,	0xe2,	0xe3,	0xe4,	0xe5,	0xe6,	0xe7,	0xe8,	0xe9,	0xea,	0xf1,	0xf2,
	0xf3,	0xf4,	0xf5,	0xf6,	0xf7,	0xf8,	0xf9,	0xfa,	0xff,	0xc4,	0x0,	0x1f,	0x1,	0x0,	0x3,	0x1,
	0x1,	0x1,	0x1,	0x1,	0x1,	0x1,	0x1,	0x1,	0x0,	0x0,	0x0,	0x0,	0x0,	0x0,	0x1,	0x2,
	0x3,	0x4,	0x5,	0x6,	0x7,	0x8,	0x9,	0xa,	0xb,	0xff,	0xc4,	0x0,	0xb5,	0x11,	0x0,	0x2,
	0x1,	0x2,	0x4,	0x4,	0x3,	0x4,	0x7,	0x5,	0x4,	0x4,	0x0,	0x1,	0x2,	0x77,	0x0,	0x1,
	0x2,	0x3,	0x11,	0x4,	0x5,	0x21,	0x31,	0x6,	0x12,	0x41,	0x51,	0x7,	0x61,	0x71,	0x13,	0x22,
	0x32,	0x81,	0x8,	0x14,	0x42,	0x91,	0xa1,	0xb1,	0xc1,	0x9,	0x23,	0x33,	0x52,	0xf0,	0x15,	0x62,
	0x72,	0xd1,	0xa,	0x16,	0x24,	0x34,	0xe1,	0x25,	0xf1,	0x17,	0x18,	0x19,	0x1a,	0x26,	0x27,	0x28,
	0x29,	0x2a,	0x35,	0x36,	0x37,	0x38,	0x39,	0x3a,	0x43,	0x44,	0x45,	0x46,	0x47,	0x48,	0x49,	0x4a,
	0x53,	0x54,	0x55,	0x56,	0x57,	0x58,	0x59,	0x5a,	0x63,	0x64,	0x65,	0x66,	0x67,	0x68,	0x69,	0x6a,
	0x73,	0x74,	0x75,	0x76,	0x77,	0x78,	0x79,	0x7a,	0x82,	0x83,	0x84,	0x85,	0x86,	0x87,	0x88,	0x89,
	0x8a,	0x92,	0x93,	0x94,	0x95,	0x96,	0x97,	0x98,	0x99,	0x9a,	0xa2,	0xa3,	0xa4,	0xa5,	0xa6,	0xa7,
	0xa8,	0xa9,	0xaa,	0xb2,	0xb3,	0xb4,	0xb5,	0xb6,	0xb7,	0xb8,	0xb9,	0xba,	0xc2,	0xc3,	0xc4,	0xc5,
	0xc6,	0xc7,	0xc8,	0xc9,	0xca,	0xd2,	0xd3,	0xd4,	0xd5,	0xd6,	0xd7,	0xd8,	0xd9,	0xda,	0xe2,	0xe3,
	0xe4,	0xe5,	0xe6,	0xe7,	0xe8,	0xe9,	0xea,	0xf2,	0xf3,	0xf4,	0xf5,	0xf6,	0xf7,	0xf8,	0xf9,	0xfa,
	0xff,	0xda,	0x0,	0xc,	0x3,	0x1,	0x0,	0x2,	0x11,	0x3,	0x11,	0x0,	0x3f,	0x0,	0xfc,	0xac,
	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x0,
	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x0,
	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x0,
	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x3,	0xff,	0xd9,	0xff,	0xd8,
	0xff,	0xe0,	0x0,	0x10,	0x4a,	0x46,	0x49,	0x46,	0x0,	0x1,	0x1,	0x0,	0x0,	0x1,	0x0,	0x1,
	0x0,	0x0,	0xff,	0xdb,	0x0,	0x43,	0x0,	0x3,	0x2,	0x2,	0x3,	0x2,	0x2,	0x3,	0x3,	0x3,
	0x3,	0x4,	0x3,	0x3,	0x4,	0x5,	0x8,	0x5,	0x5,	0x4,	0x4,	0x5,	0xa,	0x7,	0x7,	0x6,
	0x8,	0xc,	0xa,	0xc,	0xc,	0xb,	0xa,	0xb,	0xb,	0xd,	0xe,	0x12,	0x10,	0xd,	0xe,	0x11,
	0xe,	0xb,	0xb,	0x10,	0x16,	0x10,	0x11,	0x13,	0x14,	0x15,	0x15,	0x15,	0xc,	0xf,	0x17,	0x18,
	0x16,	0x14,	0x18,	0x12,	0x14,	0x15,	0x14,	0xff,	0xdb,	0x0,	0x43,	0x1,	0x3,	0x4,	0x4,	0x5,
	0x4,	0x5,	0x9,	0x5,	0x5,	0x9,	0x14,	0xd,	0xb,	0xd,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,
	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,
	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,
	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0xff,	0xc0,	0x0,	0x11,
	0x8,	0x0,	0x10,	0x0,	0xf0,	0x3,	0x1,	0x22,	0x0,	0x2,	0x11,	0x1,	0x3,	0x11,	0x1,	0xff,
	0xc4,	0x0,	0x1f,	0x0,	0x0,	0x1,	0x5,	0x1,	0x1,	0x1,	0x1,	0x1,	0x1,	0x0,	0x0,	0x0,
	0x0,	0x0,	0x0,	0x0,	0x0,	0x1,	0x2,	0x3,	0x4,	0x5,	0x6,	0x7,	0x8,	0x9,	0xa,	0xb,
	0xff,	0xc4,	0x0,	0xb5,	0x10,	0x0,	0x2,	0x1,	0x3,	0x3,	0x2,	0x4,	0x3,	0x5,	0x5,	0x4,
	0x4,	0x0,	0x0,	0x1,	0x7d,	0x1,	0x2,	0x3,	0x0,	0x4,	0x11,	0x5,	0x12,	0x21,	0x31,	0x41,
	0x6,	0x13,	0x51,	0x61,	0x7,	0x22,	0x71,	0x14,	0x32,	0x81,	0x91,	0xa1,	0x8,	0x23,	0x42,	0xb1,
	0xc1,	0x15,	0x52,	0xd1,	0xf0,	0x24,	0x33,	0x62,	0x72,	0x82,	0x9,	0xa,	0x16,	0x17,	0x18,	0x19,
	0x1a,	0x25,	0x26,	0x27,	0x28,	0x29,	0x2a,	0x34,	0x35,	0x36,	0x37,	0x38,	0x39,	0x3a,	0x43,	0x44,
	0x45,	0x46,	0x47,	0x48,	0x49,	0x4a,	0x53,	0x54,	0x55,	0x56,	0x57,	0x58,	0x59,	0x5a,	0x63,	0x64,
	0x65,	0x66,	0x67,	0x68,	0x69,	0x6a,	0x73,	0x74,	0x75,	0x76,	0x77,	0x78,	0x79,	0x7a,	0x83,	0x84,
	0x85,	0x86,	0x87,	0x88,	0x89,	0x8a,	0x92,	0x93,	0x94,	0x95,	0x96,	0x97,	0x98,	0x99,	0x9a,	0xa2,
	0xa3,	0xa4,	0xa5,	0xa6,	0xa7,	0xa8,	0xa9,	0xaa,	0xb2,	0xb3,	0xb4,	0xb5,	0xb6,	0xb7,	0xb8,	0xb9,
	0xba,	0xc2,	0xc3,	0xc4,	0xc5,	0xc6,	0xc7,	0xc8,	0xc9,	0xca,	0xd2,	0xd3,	0xd4,	0xd5,	0xd6,	0xd7,
	0xd8,	0xd9,	0xda,	0xe1,	0xe2,	0xe3,	0xe4,	0xe5,	0xe6,	0xe7,	0xe8,	0xe9,	0xea,	0xf1,	0xf2,	0xf3,
	0xf4,	0xf5,	0xf6,	0xf7,	0xf8,	0xf9,	0xfa,	0xff,	0xc4,	0x0,	0x1f,	0x1,	0x0,	0x3,	0x1,	0x1,
	0x1,	0x1,	0x1,	0x1,	0x1,	0x1,	0x1,	0x0,	0x0,	0x0,	0x0,	0x0,	0x0,	0x1,	0x2,	0x3,
	0x4,	0x5,	0x6,	0x7,	0x8,	0x9,	0xa,	0xb,	0xff,	0xc4,	0x0,	0xb5,	0x11,	0x0,	0x2,	0x1,
	0x2,	0x4,	0x4,	0x3,	0x4,	0x7,	0x5,	0x4,	0x4,	0x0,	0x1,	0x2,	0x77,	0x0,	0x1,	0x2,
	0x3,	0x11,	0x4,	0x5,	0x21,	0x31,	0x6,	0x12,	0x41,	0x51,	0x7,	0x61,	0x71,	0x13,	0x22,	0x32,
	0x81,	0x8,	0x14,	0x42,	0x91,	0xa1,	0xb1,	0xc1,	0x9,	0x23,	0x33,	0x52,	0xf0,	0x15,	0x62,	0x72,
	0xd1,	0xa,	0x16,	0x24,	0x34,	0xe1,	0x25,	0xf1,	0x17,	0x18,	0x19,	0x1a,	0x26,	0x27,	0x28,	0x29,
	0x2a,	0x35,	0x36,	0x37,	0x38,	0x39,	0x3a,	0x43,	0x44,	0x45,	0x46,	0x47,	0x48,	0x49,	0x4a,	0x53,
	0x54,	0x55,	0x56,	0x57,	0x58,	0x59,	0x5a,	0x63,	0x64,	0x65,	0x66,	0x67,	0x68,	0x69,	0x6a,	0x73,
	0x74,	0x75,	0x76,	0x77,	0x78,	0x79,	0x7a,	0x82,	0x83,	0x84,	0x85,	0x86,	0x87,	0x88,	0x89,	0x8a,
	0x92,	0x93,	0x94,	0x95,	0x96,	0x97,	0x98,	0x99,	0x9a,	0xa2,	0xa3,	0xa4,	0xa5,	0xa6,	0xa7,	0xa8,
	0xa9,	0xaa,	0xb2,	0xb3,	0xb4,	0xb5,	0xb6,	0xb7,	0xb8,	0xb9,	0xba,	0xc2,	0xc3,	0xc4,	0xc5,	0xc6,
	0xc7,	0xc8,	0xc9,	0xca,	0xd2,	0xd3,	0xd4,	0xd5,	0xd6,	0xd7,	0xd8,	0xd9,	0xda,	0xe2,	0xe3,	0xe4,
	0xe5,	0xe6,	0xe7,	0xe8,	0xe9,	0xea,	0xf2,	0xf3,	0xf4,	0xf5,	0xf6,	0xf7,	0xf8,	0xf9,	0xfa,	0xff,
	0xda,	0x0,	0xc,	0x3,	0x1,	0x0,	0x2,	0x11,	0x3,	0x11,	0x0,	0x3f,	0x0,	0xfc,	0xac,	0xa2,
	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,
	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,
	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,
	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x3,	0xff,	0xd9,	0xff,	0xd8,	0xff,
	0xe0,	0x0,	0x10,	0x4a,	0x46,	0x49,	0x46,	0x0,	0x1,	0x1,	0x0,	0x0,	0x1,	0x0,	0x1,	0x0,
	0x0,	0xff,	0xdb,	0x0,	0x43,	0x0,	0x3,	0x2,	0x2,	0x3,	0x2,	0x2,	0x3,	0x3,	0x3,	0x3,
	0x4,	0x3,	0x3,	0x4,	0x5,	0x8,	0x5,	0x5,	0x4,	0x4,	0x5,	0xa,	0x7,	0x7,	0x6,	0x8,
	0xc,	0xa,	0xc,	0xc,	0xb,	0xa,	0xb,	0xb,	0xd,	0xe,	0x12,	0x10,	0xd,	0xe,	0x11,	0xe,
	0xb,	0xb,	0x10,	0x16,	0x10,	0x11,	0x13,	0x14,	0x15,	0x15,	0x15,	0xc,	0xf,	0x17,	0x18,	0x16,
	0x14,	0x18,	0x12,	0x14,	0x15,	0x14,	0xff,	0xdb,	0x0,	0x43,	0x1,	0x3,	0x4,	0x4,	0x5,	0x4,
	0x5,	0x9,	0x5,	0x5,	0x9,	0x14,	0xd,	0xb,	0xd,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,
	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,
	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,
	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0xff,	0xc0,	0x0,	0x11,	0x8,
	0x0,	0x10,	0x0,	0xf0,	0x3,	0x1,	0x22,	0x0,	0x2,	0x11,	0x1,	0x3,	0x11,	0x1,	0xff,	0xc4,
	0x0,	0x1f,	0x0,	0x0,	0x1,	0x5,	0x1,	0x1,	0x1,	0x1,	0x1,	0x1,	0x0,	0x0,	0x0,	0x0,
	0x0,	0x0,	0x0,	0x0,	0x1,	0x2,	0x3,	0x4,	0x5,	0x6,	0x7,	0x8,	0x9,	0xa,	0xb,	0xff,
	0xc4,	0x0,	0xb5,	0x10,	0x0,	0x2,	0x1,	0x3,	0x3,	0x2,	0x4,	0x3,	0x5,	0x5,	0x4,	0x4,
	0x0,	0x0,	0x1,	0x7d,	0x1,	0x2,	0x3,	0x0,	0x4,	0x11,	0x5,	0x12,	0x21,	0x31,	0x41,	0x6,
	0x13,	0x51,	0x61,	0x7,	0x22,	0x71,	0x14,	0x32,	0x81,	0x91,	0xa1,	0x8,	0x23,	0x42,	0xb1,	0xc1,
	0x15,	0x52,	0xd1,	0xf0,	0x24,	0x33,	0x62,	0x72,	0x82,	0x9,	0xa,	0x16,	0x17,	0x18,	0x19,	0x1a,
	0x25,	0x26,	0x27,	0x28,	0x29,	0x2a,	0x34,	0x35,	0x36,	0x37,	0x38,	0x39,	0x3a,	0x43,	0x44,	0x45,
	0x46,	0x47,	0x48,	0x49,	0x4a,	0x53,	0x54,	0x55,	0x56,	0x57,	0x58,	0x59,	0x5a,	0x63,	0x64,	0x65,
	0x66,	0x67,	0x68,	0x69,	0x6a,	0x73,	0x74,	0x75,	0x76,	0x77,	0x78,	0x79,	0x7a,	0x83,	0x84,	0x85,
	0x86,	0x87,	0x88,	0x89,	0x8a,	0x92,	0x93,	0x94,	0x95,	0x96,	0x97,	0x98,	0x99,	0x9a,	0xa2,	0xa3,
	0xa4,	0xa5,	0xa6,	0xa7,	0xa8,	0xa9,	0xaa,	0xb2,	0xb3,	0xb4,	0xb5,	0xb6,	0xb7,	0xb8,	0xb9,	0xba,
	0xc2,	0xc3,	0xc4,	0xc5,	0xc6,	0xc7,	0xc8,	0xc9,	0xca,	0xd2,	0xd3,	0xd4,	0xd5,	0xd6,	0xd7,	0xd8,
	0xd9,	0xda,	0xe1,	0xe2,	0xe3,	0xe4,	0xe5,	0xe6,	0xe7,	0xe8,	0xe9,	0xea,	0xf1,	0xf2,	0xf3,	0xf4,
	0xf5,	0xf6,	0xf7,	0xf8,	0xf9,	0xfa,	0xff,	0xc4,	0x0,	0x1f,	0x1,	0x0,	0x3,	0x1,	0x1,	0x1,
	0x1,	0x1,	0x1,	0x1,	0x1,	0x1,	0x0,	0x0,	0x0,	0x0,	0x0,	0x0,	0x1,	0x2,	0x3,	0x4,
	0x5,	0x6,	0x7,	0x8,	0x9,	0xa,	0xb,	0xff,	0xc4,	0x0,	0xb5,	0x11,	0x0,	0x2,	0x1,	0x2,
	0x4,	0x4,	0x3,	0x4,	0x7,	0x5,	0x4,	0x4,	0x0,	0x1,	0x2,	0x77,	0x0,	0x1,	0x2,	0x3,
	0x11,	0x4,	0x5,	0x21,	0x31,	0x6,	0x12,	0x41,	0x51,	0x7,	0x61,	0x71,	0x13,	0x22,	0x32,	0x81,
	0x8,	0x14,	0x42,	0x91,	0xa1,	0xb1,	0xc1,	0x9,	0x23,	0x33,	0x52,	0xf0,	0x15,	0x62,	0x72,	0xd1,
	0xa,	0x16,	0x24,	0x34,	0xe1,	0x25,	0xf1,	0x17,	0x18,	0x19,	0x1a,	0x26,	0x27,	0x28,	0x29,	0x2a,
	0x35,	0x36,	0x37,	0x38,	0x39,	0x3a,	0x43,	0x44,	0x45,	0x46,	0x47,	0x48,	0x49,	0x4a,	0x53,	0x54,
	0x55,	0x56,	0x57,	0x58,	0x59,	0x5a,	0x63,	0x64,	0x65,	0x66,	0x67,	0x68,	0x69,	0x6a,	0x73,	0x74,
	0x75,	0x76,	0x77,	0x78,	0x79,	0x7a,	0x82,	0x83,	0x84,	0x85,	0x86,	0x87,	0x88,	0x89,	0x8a,	0x92,
	0x93,	0x94,	0x95,	0x96,	0x97,	0x98,	0x99,	0x9a,	0xa2,	0xa3,	0xa4,	0xa5,	0xa6,	0xa7,	0xa8,	0xa9,
	0xaa,	0xb2,	0xb3,	0xb4,	0xb5,	0xb6,	0xb7,	0xb8,	0xb9,	0xba,	0xc2,	0xc3,	0xc4,	0xc5,	0xc6,	0xc7,
	0xc8,	0xc9,	0xca,	0xd2,	0xd3,	0xd4,	0xd5,	0xd6,	0xd7,	0xd8,	0xd9,	0xda,	0xe2,	0xe3,	0xe4,	0xe5,
	0xe6,	0xe7,	0xe8,	0xe9,	0xea,	0xf2,	0xf3,	0xf4,	0xf5,	0xf6,	0xf7,	0xf8,	0xf9,	0xfa,	0xff,	0xda,
	0x0,	0xc,	0x3,	0x1,	0x0,	0x2,	0x11,	0x3,	0x11,	0x0,	0x3f,	0x0,	0xfc,	0xac,	0xa2,	0x8a,
	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,
	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,
	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,
	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x3,	0xff,	0xd9,	0xff,	0xd8,	0xff,	0xe0,
	0x0,	0x10,	0x4a,	0x46,	0x49,	0x46,	0x0,	0x1,	0x1,	0x0,	0x0,	0x1,	0x0,	0x1,	0x0,	0x0,
	0xff,	0xdb,	0x0,	0x43,	0x0,	0x3,	0x2,	0x2,	0x3,	0x2,	0x2,	0x3,	0x3,	0x3,	0x3,	0x4,
	0x3,	0x3,	0x4,	0x5,	0x8,	0x5,	0x5,	0x4,	0x4,	0x5,	0xa,	0x7,	0x7,	0x6,	0x8,	0xc,
	0xa,	0xc,	0xc,	0xb,	0xa,	0xb,	0xb,	0xd,	0xe,	0x12,	0x10,	0xd,	0xe,	0x11,	0xe,	0xb,
	0xb,	0x10,	0x16,	0x10,	0x11,	0x13,	0x14,	0x15,	0x15,	0x15,	0xc,	0xf,	0x17,	0x18,	0x16,	0x14,
	0x18,	0x12,	0x14,	0x15,	0x14,	0xff,	0xdb,	0x0,	0x43,	0x1,	0x3,	0x4,	0x4,	0x5,	0x4,	0x5,
	0x9,	0x5,	0x5,	0x9,	0x14,	0xd,	0xb,	0xd,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,
	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,
	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,
	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0x14,	0xff,	0xc0,	0x0,	0x11,	0x8,	0x0,
	0x10,	0x0,	0xf0,	0x3,	0x1,	0x22,	0x0,	0x2,	0x11,	0x1,	0x3,	0x11,	0x1,	0xff,	0xc4,	0x0,
	0x1f,	0x0,	0x0,	0x1,	0x5,	0x1,	0x1,	0x1,	0x1,	0x1,	0x1,	0x0,	0x0,	0x0,	0x0,	0x0,
	0x0,	0x0,	0x0,	0x1,	0x2,	0x3,	0x4,	0x5,	0x6,	0x7,	0x8,	0x9,	0xa,	0xb,	0xff,	0xc4,
	0x0,	0xb5,	0x10,	0x0,	0x2,	0x1,	0x3,	0x3,	0x2,	0x4,	0x3,	0x5,	0x5,	0x4,	0x4,	0x0,
	0x0,	0x1,	0x7d,	0x1,	0x2,	0x3,	0x0,	0x4,	0x11,	0x5,	0x12,	0x21,	0x31,	0x41,	0x6,	0x13,
	0x51,	0x61,	0x7,	0x22,	0x71,	0x14,	0x32,	0x81,	0x91,	0xa1,	0x8,	0x23,	0x42,	0xb1,	0xc1,	0x15,
	0x52,	0xd1,	0xf0,	0x24,	0x33,	0x62,	0x72,	0x82,	0x9,	0xa,	0x16,	0x17,	0x18,	0x19,	0x1a,	0x25,
	0x26,	0x27,	0x28,	0x29,	0x2a,	0x34,	0x35,	0x36,	0x37,	0x38,	0x39,	0x3a,	0x43,	0x44,	0x45,	0x46,
	0x47,	0x48,	0x49,	0x4a,	0x53,	0x54,	0x55,	0x56,	0x57,	0x58,	0x59,	0x5a,	0x63,	0x64,	0x65,	0x66,
	0x67,	0x68,	0x69,	0x6a,	0x73,	0x74,	0x75,	0x76,	0x77,	0x78,	0x79,	0x7a,	0x83,	0x84,	0x85,	0x86,
	0x87,	0x88,	0x89,	0x8a,	0x92,	0x93,	0x94,	0x95,	0x96,	0x97,	0x98,	0x99,	0x9a,	0xa2,	0xa3,	0xa4,
	0xa5,	0xa6,	0xa7,	0xa8,	0xa9,	0xaa,	0xb2,	0xb3,	0xb4,	0xb5,	0xb6,	0xb7,	0xb8,	0xb9,	0xba,	0xc2,
	0xc3,	0xc4,	0xc5,	0xc6,	0xc7,	0xc8,	0xc9,	0xca,	0xd2,	0xd3,	0xd4,	0xd5,	0xd6,	0xd7,	0xd8,	0xd9,
	0xda,	0xe1,	0xe2,	0xe3,	0xe4,	0xe5,	0xe6,	0xe7,	0xe8,	0xe9,	0xea,	0xf1,	0xf2,	0xf3,	0xf4,	0xf5,
	0xf6,	0xf7,	0xf8,	0xf9,	0xfa,	0xff,	0xc4,	0x0,	0x1f,	0x1,	0x0,	0x3,	0x1,	0x1,	0x1,	0x1,
	0x1,	0x1,	0x1,	0x1,	0x1,	0x0,	0x0,	0x0,	0x0,	0x0,	0x0,	0x1,	0x2,	0x3,	0x4,	0x5,
	0x6,	0x7,	0x8,	0x9,	0xa,	0xb,	0xff,	0xc4,	0x0,	0xb5,	0x11,	0x0,	0x2,	0x1,	0x2,	0x4,
	0x4,	0x3,	0x4,	0x7,	0x5,	0x4,	0x4,	0x0,	0x1,	0x2,	0x77,	0x0,	0x1,	0x2,	0x3,	0x11,
	0x4,	0x5,	0x21,	0x31,	0x6,	0x12,	0x41,	0x51,	0x7,	0x61,	0x71,	0x13,	0x22,	0x32,	0x81,	0x8,
	0x14,	0x42,	0x91,	0xa1,	0xb1,	0xc1,	0x9,	0x23,	0x33,	0x52,	0xf0,	0x15,	0x62,	0x72,	0xd1,	0xa,
	0x16,	0x24,	0x34,	0xe1,	0x25,	0xf1,	0x17,	0x18,	0x19,	0x1a,	0x26,	0x27,	0x28,	0x29,	0x2a,	0x35,
	0x36,	0x37,	0x38,	0x39,	0x3a,	0x43,	0x44,	0x45,	0x46,	0x47,	0x48,	0x49,	0x4a,	0x53,	0x54,	0x55,
	0x56,	0x57,	0x58,	0x59,	0x5a,	0x63,	0x64,	0x65,	0x66,	0x67,	0x68,	0x69,	0x6a,	0x73,	0x74,	0x75,
	0x76,	0x77,	0x78,	0x79,	0x7a,	0x82,	0x83,	0x84,	0x85,	0x86,	0x87,	0x88,	0x89,	0x8a,	0x92,	0x93,
	0x94,	0x95,	0x96,	0x97,	0x98,	0x99,	0x9a,	0xa2,	0xa3,	0xa4,	0xa5,	0xa6,	0xa7,	0xa8,	0xa9,	0xaa,
	0xb2,	0xb3,	0xb4,	0xb5,	0xb6,	0xb7,	0xb8,	0xb9,	0xba,	0xc2,	0xc3,	0xc4,	0xc5,	0xc6,	0xc7,	0xc8,
	0xc9,	0xca,	0xd2,	0xd3,	0xd4,	0xd5,	0xd6,	0xd7,	0xd8,	0xd9,	0xda,	0xe2,	0xe3,	0xe4,	0xe5,	0xe6,
	0xe7,	0xe8,	0xe9,	0xea,	0xf2,	0xf3,	0xf4,	0xf5,	0xf6,	0xf7,	0xf8,	0xf9,	0xfa,	0xff,	0xda,	0x0,
	0xc,	0x3,	0x1,	0x0,	0x2,	0x11,	0x3,	0x11,	0x0,	0x3f,	0x0,	0xfc,	0xac,	0xa2,	0x8a,	0x28,
	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,
	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,
	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,
	0x0,	0xa2,	0x8a,	0x28,	0x0,	0xa2,	0x8a,	0x28,	0x3,	0xff,	0xd9,
};

lv_img_dsc_t bar_pic = {
	.header.always_zero = 0,
	.header.w = 240,
	.header.h = 80,
	.data_size = 3467,
	.header.cf = LV_IMG_CF_RAW,
	.data = bar_map,
};

ES_VOID *es_ui_res_bar(ES_VOID)
{
	return (ES_VOID *)&bar_pic;
}

#endif
