#include "es_inc.h"

#include "mf_brd.h"

ES_S32 es_hal_led_init(ES_VOID)
{
    fpioa_set_function(ES_LED_FLASH_PIN, FUNC_TIMER0_TOGGLE1 + ES_LED_FLASH_PWM_CHANNEL);
    pwm_set_frequency(ES_LED_FLASH_PWM_DEV, ES_LED_FLASH_PWM_CHANNEL, 1000,  ES_LED_FLASH_IO_OPEN_VAL);
    es_hal_led_close();

    return ES_RET_SUCCESS;
}

ES_S32 es_hal_led_open(ES_BOOL strong)
{
    // printk("es_hal_led_open\r\n");
    if (strong) {
        pwm_set_frequency(ES_LED_FLASH_PWM_DEV, ES_LED_FLASH_PWM_CHANNEL, 1000, (double)mf_brd.cfg.led.high);
    } else {
        pwm_set_frequency(ES_LED_FLASH_PWM_DEV, ES_LED_FLASH_PWM_CHANNEL, 1000, (double)mf_brd.cfg.led.low);
    }
	pwm_set_enable(ES_LED_FLASH_PWM_DEV, ES_LED_FLASH_PWM_CHANNEL, 1);
    return ES_RET_SUCCESS;
}

ES_S32 es_hal_led_open_with_duty(ES_U32 duty)
{
    double tmp_duty = 0;

    tmp_duty = (double)(((double)duty) / 200.0);

    pwm_set_frequency(ES_LED_FLASH_PWM_DEV, ES_LED_FLASH_PWM_CHANNEL, 1000, tmp_duty);
	pwm_set_enable(ES_LED_FLASH_PWM_DEV, ES_LED_FLASH_PWM_CHANNEL, 1);
    return ES_RET_SUCCESS;
}

ES_S32 es_hal_led_close(ES_VOID)
{
    // printk("es_hal_led_close\r\n");
    pwm_set_frequency(ES_LED_FLASH_PWM_DEV, ES_LED_FLASH_PWM_CHANNEL, 1000, 0);
	pwm_set_enable(ES_LED_FLASH_PWM_DEV, ES_LED_FLASH_PWM_CHANNEL, 1);
    return ES_RET_SUCCESS;
}