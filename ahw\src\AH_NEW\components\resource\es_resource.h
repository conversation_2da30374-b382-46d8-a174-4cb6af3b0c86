/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_resource.h
** bef: define the interface resource. 
** auth: lines<<EMAIL>>
** create on 2022.12.13
*/

#ifndef _ES_RESOURCE_H_
#define _ES_RESOURCE_H_

#ifdef __cplusplus 
extern "C" { 
#endif

#include "es_types.h"


// such as file name
typedef enum {
    ES_RESOURCE_UI_SAVER_DEFAULT,
    ES_RESOURCE_UI_SAVER_USER,
} es_resource_type_t; 

typedef struct {
    ES_U32 size;
    ES_U32 crc32;
    ES_BYTE *data;
    ES_U32 timestramp;
} es_resource_data_t;

ES_S32 es_resource_init(ES_VOID);

ES_S32 es_resource_read(es_resource_type_t type, es_resource_data_t *res);

ES_S32 es_resource_write(es_resource_type_t type, es_resource_data_t *res);

#ifdef __cplusplus 
}
#endif

#endif