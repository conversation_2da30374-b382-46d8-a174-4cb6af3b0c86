/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_types.h
** bef: define the type of data 
** auth: lines<<EMAIL>>
** create on 2016.10.20 
*/

#ifndef _ES_TYPES_H_
#define _ES_TYPES_H_

#if 0
typedef unsigned long long  ES_U64;
typedef long long           ES_S64;
typedef unsigned int        ES_U32;
typedef int                 ES_S32;
typedef unsigned short      ES_U16;
typedef short               ES_S16;
typedef unsigned char       ES_U8;
typedef unsigned char       ES_BYTE;
typedef char                ES_S8;
typedef char                ES_CHAR;
typedef char                ES_BOOL;
typedef void                ES_VOID;
typedef size_t              ES_SIZE_T;
// typedef ES_U32              es_time_t;
typedef double              ES_DOUBLE;
typedef float               ES_FLOAT;
#else
#define ES_U64 unsigned long long
#define ES_S64 long long
#define ES_U32 unsigned int
#define ES_S32 int
#define ES_U16 unsigned short
#define ES_S16 short
#define ES_U8 unsigned char
#define ES_BYTE unsigned char
#define ES_S8 char
#define ES_CHAR char
#define ES_BOOL char
#define ES_VOID void
#define ES_SIZE_T size_t
// #define es_time_t ES_U32
#define ES_DOUBLE double
#define ES_FLOAT float
#endif

#ifdef ES_OS_WIN32
typedef SOCKET es_event_handle_t;
#else
typedef ES_S32 es_event_handle_t;
#endif

#ifdef ES_OS_FREERTOS
#define TASK_RET_TYPE   ES_VOID
#define TASK_RET_NULL   return
#elif defined ES_OS_LINUX
#define TASK_RET_TYPE   ES_VOID*
#define TASK_RET_NULL   return NULL
#elif defined ES_OS_UCOS
#define TASK_RET_TYPE   ES_VOID*
#define TASK_RET_NULL   return NULL
#endif

#define ES_SUPPORT_IPV6     (0)
#if ES_SUPPORT_IPV6
typedef ES_U16              es_ip6addr_t[16];
typedef es_ip6addr_t        es_ipaddr_t;
#else 
typedef ES_U32              es_ipaddr_t;
#endif



#define ES_ARRAY_SIZE(a) (sizeof(a)/sizeof(a[0]))

#define ES_NULL                 (0)
#define ES_FALSE                (0)
#define ES_TRUE                 (!ES_FALSE)

#define ES_RET_SUCCESS          (0)
#define ES_RET_FAILURE          (-1)
#define ES_RET_NO_MEMORY        (-2)
#define ES_RET_PARAM_ERR        (-3)
#define ES_RET_FAIL_REINIT      (-4)


#ifndef es_strstr
#define es_strstr strstr
#define es_strlen strlen
#define es_strncmp strncmp
#define es_strncpy strncpy
#define es_atoi atoi
#define es_snprintf snprintf
#define es_sprintf sprintf
#define es_memset memset
#define es_memcpy memcpy
#define es_memcmp memcmp
#define es_va_list va_list
#define es_va_start va_start
#define es_va_end va_end
#define es_vsnprintf vsnprintf
// #define es_malloc malloc
// #define es_free free
#endif

#ifndef es_isdigit
#define es_isdigit(c)  ((unsigned)((c) - '0') < 10)
#endif

#define es_isxdigit(c)          (('0' <= (c) && (c) <= '9') \
                                  || ('a' <= (c) && (c) <= 'f') \
                                  || ('A' <= (c) && (c) <= 'F'))

#define es_isprint(c)           ((0x20 <= (c) && (c) <= 0x7e) || ' ' == (c))
#define es_isascii(c)           (((unsigned char)(c))<=0x7f)
#define es_toascii(c)           (((unsigned char)(c))&0x7f)
#define es_tolower(x)           ((x) | 0x20)

#define ES_GPIO_VAL_HIGH            (1)
#define ES_GPIO_VAL_LOW             (0)

#endif

