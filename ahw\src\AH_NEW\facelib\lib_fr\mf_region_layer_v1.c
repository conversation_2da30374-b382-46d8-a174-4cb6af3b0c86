#include "facelib_inc.h"

#if (0)//(CONFIG_DETECT_VERTICAL == 0)
/* region_layer.c */
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/* local */
#define activate_array iu4je1ievohbahzeeL9Muohiviinae
#define entry_index ioyuam5ohsae4tiph1Goshohchaegh
#define nms_comparator ouyephifiz6ahL3thohnoodaicai6b

/* global */
//#define region_layer_init cheet9theigiH7jaoquu4aiBik6eic
//#define region_layer_deinit zuo7phosh2ieRoJaephiet0gud0co5
//#define region_layer_run peeguz1<PERSON><PERSON><PERSON><PERSON>9<PERSON>9lee5<PERSON>hbai
/* call */

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/* kpu.c */
typedef struct
{
    union
    {
        uint64_t reg;
        struct
        {
            uint64_t int_en : 1;
            uint64_t ram_flag : 1;
            uint64_t full_add : 1;
            uint64_t depth_wise_layer : 1;
            uint64_t reserved : 60;
        } data;
    } interrupt_enabe;

    union
    {
        uint64_t reg;
        struct
        {
            uint64_t image_src_addr : 15;
            uint64_t reserved0 : 17;
            uint64_t image_dst_addr : 15;
            uint64_t reserved1 : 17;
        } data;
    } image_addr;

    union
    {
        uint64_t reg;
        struct
        {
            uint64_t i_ch_num : 10;
            uint64_t reserved0 : 22;
            uint64_t o_ch_num : 10;
            uint64_t reserved1 : 6;
            uint64_t o_ch_num_coef : 10;
            uint64_t reserved2 : 6;
        } data;
    } image_channel_num;

    union
    {
        uint64_t reg;
        struct
        {
            uint64_t i_row_wid : 10;
            uint64_t i_col_high : 9;
            uint64_t reserved0 : 13;
            uint64_t o_row_wid : 10;
            uint64_t o_col_high : 9;
            uint64_t reserved1 : 13;
        } data;
    } image_size;

    union
    {
        uint64_t reg;
        struct
        {
            uint64_t kernel_type : 3;
            uint64_t pad_type : 1;
            uint64_t pool_type : 4;
            uint64_t first_stride : 1;
            uint64_t bypass_conv : 1;
            uint64_t load_para : 1;
            uint64_t reserved0 : 5;
            uint64_t dma_burst_size : 8;
            uint64_t pad_value : 8;
            uint64_t bwsx_base_addr : 32;
        } data;
    } kernel_pool_type_cfg;

    union
    {
        uint64_t reg;
        struct
        {
            uint64_t load_coor : 1;
            uint64_t load_time : 6;
            uint64_t reserved0 : 8;
            uint64_t para_size : 17;
            uint64_t para_start_addr : 32;
        } data;
    } kernel_load_cfg;

    union
    {
        uint64_t reg;
        struct
        {
            uint64_t coef_column_offset : 4;
            uint64_t coef_row_offset : 12;
            uint64_t reserved0 : 48;
        } data;
    } kernel_offset;

    union
    {
        uint64_t reg;
        struct
        {
            uint64_t channel_switch_addr : 15;
            uint64_t reserved : 1;
            uint64_t row_switch_addr : 4;
            uint64_t coef_size : 8;
            uint64_t coef_group : 3;
            uint64_t load_act : 1;
            uint64_t active_addr : 32;
        } data;
    } kernel_calc_type_cfg;

    union
    {
        uint64_t reg;
        struct
        {
            uint64_t wb_channel_switch_addr : 15;
            uint64_t reserved0 : 1;
            uint64_t wb_row_switch_addr : 4;
            uint64_t wb_group : 3;
            uint64_t reserved1 : 41;
        } data;
    } write_back_cfg;

    union
    {
        uint64_t reg;
        struct
        {
            uint64_t shr_w : 4;
            uint64_t shr_x : 4;
            uint64_t arg_w : 24;
            uint64_t arg_x : 24;
            uint64_t reserved0 : 8;
        } data;
    } conv_value;

    union
    {
        uint64_t reg;
        struct
        {
            uint64_t arg_add : 40;
            uint64_t reserved : 24;
        } data;
    } conv_value2;

    union
    {
        uint64_t reg;
        struct
        {
            uint64_t send_data_out : 1;
            uint64_t reserved : 15;
            uint64_t channel_byte_num : 16;
            uint64_t dma_total_byte : 32;
        } data;
    } dma_parameter;
} kpu_layer_argument_t;

typedef struct
{
    uint32_t address;
    uint32_t size;
} kpu_model_output_t;

typedef struct
{
    uint32_t type;
    uint32_t body_size;
} kpu_model_layer_header_t;

typedef struct _quantize_param
{
    float scale;
    float bias;
} quantize_param_t;

typedef void (*kpu_done_callback_t)(void *userdata);

typedef struct
{
    const uint8_t *model_buffer;
    uint8_t *main_buffer;
    uint32_t output_count;
    const kpu_model_output_t *outputs;
    const kpu_model_layer_header_t *layer_headers;
    const uint8_t *body_start;
    uint32_t layers_length;
    volatile uint32_t current_layer;
    const uint8_t *volatile current_body;
    dmac_channel_number_t dma_ch;
    kpu_done_callback_t done_callback;
    void *userdata;
} kpu_model_context_t;

typedef struct
{
    uint32_t weigths_offset;
    uint32_t bn_offset;
    uint32_t act_offset;
    float input_scale;
    float input_bias;
    float output_scale;
    float output_bias;
} kpu_model_layer_metadata_t;

typedef struct
{
    kpu_layer_argument_t *layers;
    kpu_layer_argument_t *remain_layers;
    plic_irq_callback_t callback;
    void *ctx;
    uint64_t *src;
    uint64_t *dst;
    uint32_t src_length;
    uint32_t dst_length;
    uint32_t layers_length;
    uint32_t remain_layers_length;
    dmac_channel_number_t dma_ch;
    uint32_t eight_bit_mode;
    float output_scale;
    float output_bias;
    float input_scale;
    float input_bias;
} kpu_task_t;

typedef struct
{
    uint64_t calc_done_int : 1;
    uint64_t layer_cfg_almost_empty_int : 1;
    uint64_t layer_cfg_almost_full_int : 1;
    uint64_t reserved : 61;
} kpu_config_interrupt_t;

typedef struct
{
    uint64_t fifo_full_threshold : 4;
    uint64_t fifo_empty_threshold : 4;
    uint64_t reserved : 56;
} kpu_config_fifo_threshold_t;

typedef struct
{
    uint64_t dma_fifo_flush_n : 1;
    uint64_t gs_fifo_flush_n : 1;
    uint64_t cfg_fifo_flush_n : 1;
    uint64_t cmd_fifo_flush_n : 1;
    uint64_t resp_fifo_flush_n : 1;
    uint64_t reserved : 59;
} kpu_config_fifo_ctrl_t;

typedef struct
{
    uint64_t eight_bit_mode : 1;
    uint64_t reserved : 63;
} kpu_config_eight_bit_mode_t;

typedef struct
{
    volatile uint64_t layer_argument_fifo;

    volatile union
    {
        uint64_t reg;
        kpu_config_interrupt_t data;
    } interrupt_status;

    volatile union
    {
        uint64_t reg;
        kpu_config_interrupt_t data;
    } interrupt_raw;

    volatile union
    {
        uint64_t reg;
        kpu_config_interrupt_t data;
    } interrupt_mask;

    volatile union
    {
        uint64_t reg;
        kpu_config_interrupt_t data;
    } interrupt_clear;

    volatile union
    {
        uint64_t reg;
        kpu_config_fifo_threshold_t data;
    } fifo_threshold;

    volatile uint64_t fifo_data_out;

    volatile union
    {
        uint64_t reg;
        kpu_config_fifo_ctrl_t data;
    } fifo_ctrl;

    volatile union
    {
        uint64_t reg;
        kpu_config_eight_bit_mode_t data;
    } eight_bit_mode;
} kpu_config_t;

extern volatile kpu_config_t *const kpu;

extern int kpu_run(kpu_task_t *task, dmac_channel_number_t dma_ch, const void *src, void *dest, plic_irq_callback_t callback);
extern uint8_t *kpu_get_output_buf(kpu_task_t *task);
extern void kpu_release_output_buf(uint8_t *output_buf);
extern int kpu_start(kpu_task_t *task);
extern int kpu_single_task_init(kpu_task_t *task);
extern int kpu_single_task_deinit(kpu_task_t *task);
extern int kpu_model_load_from_buffer(kpu_task_t *task, uint8_t *buffer, kpu_model_layer_metadata_t **meta);
extern void kpu_init(int eight_bit_mode, plic_irq_callback_t callback, void *userdata);
extern void kpu_input_dma(const kpu_layer_argument_t *layer, const uint8_t *src, dmac_channel_number_t dma_ch, plic_irq_callback_t callback, void *userdata);
extern void kpu_input_with_padding(kpu_layer_argument_t *layer, const uint8_t *src, int width, int height, int channels);
extern void kpu_conv2d(kpu_layer_argument_t *layer);
extern void kpu_conv2d_output(kpu_layer_argument_t *layer, dmac_channel_number_t dma_ch, uint8_t *dest, plic_irq_callback_t callback, void *userdata);
extern void kpu_global_average_pool(const uint8_t *src, const quantize_param_t *src_param, int kernel_size, int channels, uint8_t *dest, const quantize_param_t *dest_param);
extern void kpu_global_average_pool_float(const uint8_t *src, const quantize_param_t *src_param, int kernel_size, int channels, float *dest);
extern void kpu_fully_connected(const float *src, const float *weights, const float *biases, float *dest, int input_channels, int output_channels);
extern void kpu_matmul_end(const uint8_t *src, int channels, float *dest, const quantize_param_t *dest_param);
extern void kpu_dequantize(const uint8_t *src, const quantize_param_t *src_param, size_t count, float *dest);
extern int kpu_load_kmodel(kpu_model_context_t *ctx, const uint8_t *buffer);
extern void kpu_model_free(kpu_model_context_t *ctx);
extern int kpu_get_output(kpu_model_context_t *ctx, uint32_t index, uint8_t **data, size_t *size);
extern int kpu_run_kmodel(kpu_model_context_t *ctx, const uint8_t *src, dmac_channel_number_t dma_ch, kpu_done_callback_t done_callback, void *userdata);
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

typedef struct
{
    float threshold;
    float nms_value;
    uint32_t coords;
    uint32_t anchor_number;
    float *anchor;
    uint32_t image_width;
    uint32_t image_height;
    uint32_t classes;
    uint32_t net_width;
    uint32_t net_height;
    uint32_t layer_width;
    uint32_t layer_height;
    uint32_t boxes_number;
    uint32_t output_number;
    float scale;
    float bias;
    void *boxes;
    uint8_t *input;
    float *output;
    float *probs_buf;
    float **probs;
    float *activate;
    float *softmax;
} region_layer_t;

typedef struct
{
    float x;
    float y;
    float w;
    float h;
} box_t;

typedef struct
{
    int index;
    int class;
    float **probs;
} sortable_box_t;

int region_layer_init(region_layer_t *rl, kpu_task_t *task)
{
    int flag = 0;
    kpu_layer_argument_t *last_layer = &task->layers[task->layers_length - 1];
    kpu_layer_argument_t *first_layer = &task->layers[0];

    rl->coords = 4;
    rl->image_width = 320;
    rl->image_height = 240;

    rl->classes = (last_layer->image_channel_num.data.o_ch_num + 1) / 5 - 5;
    rl->net_width = first_layer->image_size.data.i_row_wid + 1;
    rl->net_height = first_layer->image_size.data.i_col_high + 1;
    rl->layer_width = last_layer->image_size.data.o_row_wid + 1;
    rl->layer_height = last_layer->image_size.data.o_col_high + 1;
    rl->boxes_number = (rl->layer_width * rl->layer_height * rl->anchor_number);
    rl->output_number = (rl->boxes_number * (rl->classes + rl->coords + 1));
    rl->input = (uint8_t *)task->dst;
    rl->scale = task->output_scale;
    rl->bias = task->output_bias;
    rl->output = malloc(rl->output_number * sizeof(float));
    if(rl->output == NULL)
    {
        flag = -1;
        goto malloc_error;
    }
    rl->boxes = malloc(rl->boxes_number * sizeof(box_t));
    if(rl->boxes == NULL)
    {
        flag = -2;
        goto malloc_error;
    }
    rl->probs_buf = malloc(rl->boxes_number * (rl->classes + 1) * sizeof(float));
    if(rl->probs_buf == NULL)
    {
        flag = -3;
        goto malloc_error;
    }
    rl->probs = malloc(rl->boxes_number * sizeof(float *));
    if(rl->probs == NULL)
    {
        flag = -4;
        goto malloc_error;
    }
    rl->activate = malloc(256 * sizeof(float));
    if(rl->activate == NULL)
    {
        flag = -5;
        goto malloc_error;
    }
    rl->softmax = malloc(256 * sizeof(float));
    if(rl->softmax == NULL)
    {
        flag = -5;
        goto malloc_error;
    }
    for(int i = 0; i < 256; i++)
    {
        rl->activate[i] = 1.0 / (1.0 + expf(-(i * rl->scale + rl->bias)));
        rl->softmax[i] = expf(rl->scale * (i - 255));
    }
    for(uint32_t i = 0; i < rl->boxes_number; i++)
        rl->probs[i] = &(rl->probs_buf[i * (rl->classes + 1)]);
    return 0;
malloc_error:
    free(rl->output);
    free(rl->boxes);
    free(rl->probs_buf);
    free(rl->probs);
    free(rl->activate);
    free(rl->softmax);
    return flag;
}

void region_layer_deinit(region_layer_t *rl)
{
    free(rl->output);
    free(rl->boxes);
    free(rl->probs_buf);
    free(rl->probs);
    free(rl->activate);
    free(rl->softmax);
}

static void activate_array(region_layer_t *rl, int index, int n)
{
    float *output = &rl->output[index];
    uint8_t *input = &rl->input[index];

    for(int i = 0; i < n; ++i)
        output[i] = rl->activate[input[i]];
}

static int entry_index(region_layer_t *rl, int location, int entry)
{
    int wh = rl->layer_width * rl->layer_height;
    int n = location / wh;
    int loc = location % wh;

    return n * wh * (rl->coords + rl->classes + 1) + entry * wh + loc;
}

static void softmax(region_layer_t *rl, uint8_t *input, int n, int stride, float *output)
{
    int i;
    int diff;
    float e;
    float sum = 0;
    uint8_t largest_i = input[0];

    for(i = 0; i < n; ++i)
    {
        if(input[i * stride] > largest_i)
            largest_i = input[i * stride];
    }

    for(i = 0; i < n; ++i)
    {
        diff = input[i * stride] - largest_i;
        e = rl->softmax[diff + 255];
        sum += e;
        output[i * stride] = e;
    }
    for(i = 0; i < n; ++i)
        output[i * stride] /= sum;
}

static void softmax_cpu(region_layer_t *rl, uint8_t *input, int n, int batch, int batch_offset, int groups, int stride, float *output)
{
    int g, b;

    for(b = 0; b < batch; ++b)
    {
        for(g = 0; g < groups; ++g)
            softmax(rl, input + b * batch_offset + g, n, stride, output + b * batch_offset + g);
    }
}

static void forward_region_layer(region_layer_t *rl)
{
    int index;

    for(index = 0; index < rl->output_number; index++)
        rl->output[index] = rl->input[index] * rl->scale + rl->bias;

    for(int n = 0; n < rl->anchor_number; ++n)
    {
        index = entry_index(rl, n * rl->layer_width * rl->layer_height, 0);
        activate_array(rl, index, 2 * rl->layer_width * rl->layer_height);
        index = entry_index(rl, n * rl->layer_width * rl->layer_height, 4);
        activate_array(rl, index, rl->layer_width * rl->layer_height);
    }

    index = entry_index(rl, 0, rl->coords + 1);
    softmax_cpu(rl, rl->input + index, rl->classes, rl->anchor_number,
                rl->output_number / rl->anchor_number, rl->layer_width * rl->layer_height,
                rl->layer_width * rl->layer_height, rl->output + index);
}

static void correct_region_boxes(region_layer_t *rl, box_t *boxes)
{
    uint32_t net_width = rl->net_width;
    uint32_t net_height = rl->net_height;
    uint32_t image_width = rl->image_width;
    uint32_t image_height = rl->image_height;
    uint32_t boxes_number = rl->boxes_number;
    int new_w = 0;
    int new_h = 0;

    if(((float)net_width / image_width) <
       ((float)net_height / image_height))
    {
        new_w = net_width;
        new_h = (image_height * net_width) / image_width;
    } else
    {
        new_h = net_height;
        new_w = (image_width * net_height) / image_height;
    }
    for(int i = 0; i < boxes_number; ++i)
    {
        box_t b = boxes[i];

        b.x = (b.x - (net_width - new_w) / 2. / net_width) /
              ((float)new_w / net_width);
        b.y = (b.y - (net_height - new_h) / 2. / net_height) /
              ((float)new_h / net_height);
        b.w *= (float)net_width / new_w;
        b.h *= (float)net_height / new_h;
        boxes[i] = b;
    }
}

static box_t get_region_box(float *x, float *biases, int n, int index, int i, int j, int w, int h, int stride)
{
    volatile box_t b;

    b.x = (i + x[index + 0 * stride]) / w;
    b.y = (j + x[index + 1 * stride]) / h;
    b.w = expf(x[index + 2 * stride]) * biases[2 * n] / w;
    b.h = expf(x[index + 3 * stride]) * biases[2 * n + 1] / h;
    return b;
}

static void get_region_boxes(region_layer_t *rl, float *predictions, float **probs, box_t *boxes)
{
    uint32_t layer_width = rl->layer_width;
    uint32_t layer_height = rl->layer_height;
    uint32_t anchor_number = rl->anchor_number;
    uint32_t classes = rl->classes;
    uint32_t coords = rl->coords;
    float threshold = rl->threshold;

    for(int i = 0; i < layer_width * layer_height; ++i)
    {
        int row = i / layer_width;
        int col = i % layer_width;

        for(int n = 0; n < anchor_number; ++n)
        {
            int index = n * layer_width * layer_height + i;

            for(int j = 0; j < classes; ++j)
                probs[index][j] = 0;
            int obj_index = entry_index(rl, n * layer_width * layer_height + i, coords);
            int box_index = entry_index(rl, n * layer_width * layer_height + i, 0);
            float scale = predictions[obj_index];

            boxes[index] = get_region_box(predictions, rl->anchor, n, box_index, col, row,
                                          layer_width, layer_height, layer_width * layer_height);

            float max = 0;

            for(int j = 0; j < classes; ++j)
            {
                int class_index = entry_index(rl, n * layer_width * layer_height + i, coords + 1 + j);
                float prob = scale * predictions[class_index];

                probs[index][j] = (prob > threshold) ? prob : 0;
                if(prob > max)
                    max = prob;
            }
            probs[index][classes] = max;
        }
    }
    correct_region_boxes(rl, boxes);
}

static int nms_comparator(void *pa, void *pb)
{
    sortable_box_t a = *(sortable_box_t *)pa;
    sortable_box_t b = *(sortable_box_t *)pb;
    float diff = a.probs[a.index][b.class] - b.probs[b.index][b.class];

    if(diff < 0)
        return 1;
    else if(diff > 0)
        return -1;
    return 0;
}

static float overlap(float x1, float w1, float x2, float w2)
{
    float l1 = x1 - w1 / 2;
    float l2 = x2 - w2 / 2;
    float left = l1 > l2 ? l1 : l2;
    float r1 = x1 + w1 / 2;
    float r2 = x2 + w2 / 2;
    float right = r1 < r2 ? r1 : r2;

    return right - left;
}

static float box_intersection(box_t a, box_t b)
{
    float w = overlap(a.x, a.w, b.x, b.w);
    float h = overlap(a.y, a.h, b.y, b.h);

    if(w < 0 || h < 0)
        return 0;
    return w * h;
}

static float box_union(box_t a, box_t b)
{
    float i = box_intersection(a, b);
    float u = a.w * a.h + b.w * b.h - i;

    return u;
}

static float box_iou(box_t a, box_t b)
{
    return box_intersection(a, b) / box_union(a, b);
}

static void do_nms_sort(region_layer_t *rl, box_t *boxes, float **probs)
{
    uint32_t boxes_number = rl->boxes_number;
    uint32_t classes = rl->classes;
    float nms_value = rl->nms_value;
    int i, j, k;
    sortable_box_t s[boxes_number];

    for(i = 0; i < boxes_number; ++i)
    {
        s[i].index = i;
        s[i].class = 0;
        s[i].probs = probs;
    }

    for(k = 0; k < classes; ++k)
    {
        for(i = 0; i < boxes_number; ++i)
            s[i].class = k;
        qsort(s, boxes_number, sizeof(sortable_box_t), (void *)nms_comparator);
        for(i = 0; i < boxes_number; ++i)
        {
            if(probs[s[i].index][k] == 0)
                continue;
            box_t a = boxes[s[i].index];

            for(j = i + 1; j < boxes_number; ++j)
            {
                box_t b = boxes[s[j].index];

                if(box_iou(a, b) > nms_value)
                    probs[s[j].index][k] = 0;
            }
        }
    }
}

static int max_index(float *a, int n)
{
    int i, max_i = 0;
    float max = a[0];

    for(i = 1; i < n; ++i)
    {
        if(a[i] > max)
        {
            max = a[i];
            max_i = i;
        }
    }
    return max_i;
}

static void region_layer_output(region_layer_t *rl, face_obj_info_t *obj_info)
{
    uint32_t obj_number = 0;
    uint32_t image_width = rl->image_width;
    uint32_t image_height = rl->image_height;
    float threshold = rl->threshold;
    box_t *boxes = (box_t *)rl->boxes;

    for(int i = 0; i < rl->boxes_number; ++i)
    {
        int class = max_index(rl->probs[i], rl->classes);
        float prob = rl->probs[i][class];

        if(prob > threshold)
        {
            box_t *b = boxes + i;
            obj_info->obj[obj_number].x1 = b->x * image_width - (b->w * image_width / 2);
            obj_info->obj[obj_number].y1 = b->y * image_height - (b->h * image_height / 2);
            obj_info->obj[obj_number].x2 = b->x * image_width + (b->w * image_width / 2);
            obj_info->obj[obj_number].y2 = b->y * image_height + (b->h * image_height / 2);
            obj_info->obj[obj_number].class_id = class;
            obj_info->obj[obj_number].prob = prob;
            obj_number++;
        }
    }
    obj_info->obj_number = obj_number;
}

void region_layer_run(region_layer_t *rl, face_obj_info_t *obj_info)
{
    forward_region_layer(rl);
    get_region_boxes(rl, rl->output, rl->probs, rl->boxes);
    do_nms_sort(rl, rl->boxes, rl->probs);
    region_layer_output(rl, obj_info);
}
#endif
