#ifndef _MF_MDL_FR_H
#define _MF_MDL_FR_H

#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include "mf_lib.h"

/*****************************************************************************/
// Types
/*****************************************************************************/

/*****************************************************************************/
// Functions
/*****************************************************************************/

fr_ret_t _mf_model_run_fr(image_t* kpu_img, uint8_t out_ftr, face_step_t step, uint8_t ir_or_rgb, uint8_t only_biggest);
void _mf_model_free_fr(fr_ret_t face_ret);

#endif