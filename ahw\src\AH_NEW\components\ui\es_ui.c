#include "es_inc.h"
#include "facelib_inc.h"

// #define ES_UI_DEBUG
#ifdef ES_UI_DEBUG
#define es_ui_debug es_log_info
#define es_ui_error es_log_error
#else
#define es_ui_debug(...)
#define es_ui_error(...)
#endif


typedef enum {
    ES_UI_MODE_FACTORY,
    ES_UI_MODE_SAVER,
    ES_UI_MODE_WORK,
} es_ui_mode_e;

lv_disp_t *cam_disp;
lv_disp_t *bar_disp;

// for camera area
static lv_disp_draw_buf_t cam_disp_buf;
static lv_disp_drv_t cam_disp_drv;
static lv_color_t g_cam_buf[ES_UI_WIDTH * ES_UI_DRAW_BUF_LINE];

// for bar area
static lv_disp_draw_buf_t disp_buf;
static lv_disp_drv_t disp_drv;
static lv_color_t g_buf[ES_UI_BAR_WIDTH * ES_UI_DRAW_BUF_LINE];

static ES_U8 ui_mode = ES_UI_MODE_WORK;


static ES_U8 es_ui_get_mode(ES_VOID)
{
    ES_U32 now_time;

    if (mf_brd.cfg.factory_flag) {
        return ES_UI_MODE_FACTORY;
    }
    now_time = es_time_get_sytem_ms();

#if (0 == ES_ENABLE_SPEC_CAR)
    if (now_time < ES_UI_WORK_MODE_TIME_AFTER_BOOT) {
        return ES_UI_MODE_WORK;
    }
#endif

    if (es_utils_check_timeout(es_facecb_get_detect_time_ms(), now_time, ES_UI_ENTER_SAVER_TIME)) {
        return ES_UI_MODE_SAVER;
    }

#if ES_ENABLE_SPEC_CAR
    if (es_spec_car_is_lock()) {
        return ES_UI_MODE_SAVER;
    }
#endif

    return ES_UI_MODE_WORK;
}

static ES_VOID es_ui_set_mode(ES_U8 mode)
{
    if (ES_UI_MODE_FACTORY == mode) {
        es_ui_cam_show(ES_FALSE);
        es_ui_infobar_show(ES_FALSE);
        es_ui_saver_show(ES_FALSE);
        es_ui_factory_show(ES_TRUE);
    } else if (ES_UI_MODE_SAVER == mode) {
        es_ui_cam_show(ES_FALSE);
        es_ui_infobar_show(ES_FALSE);
        es_ui_saver_show(ES_TRUE);
        es_ui_factory_show(ES_FALSE);
    } else {
        es_ui_cam_show(ES_TRUE);
        es_ui_infobar_show(ES_TRUE);
        es_ui_saver_show(ES_FALSE);
        es_ui_factory_show(ES_FALSE);
    }
}

static ES_VOID es_ui_check_mode(ES_VOID)
{
    ES_U8 mode;

    mode = es_ui_get_mode();
    if (ui_mode == mode) {
        return;
    }
    ui_mode = mode;
    es_ui_set_mode(mode);
}

static ES_S32 es_ui_lvgl_widgets(ES_VOID)
{
    if (ES_RET_SUCCESS != es_ui_cam_init()) {
        es_ui_error("ui cam init fail");
        return ES_RET_FAILURE;
    }

    if (ES_RET_SUCCESS != es_ui_face_init()) {
        es_ui_error("ui face init fail");
        return ES_RET_FAILURE;
    }

    if (ES_RET_SUCCESS != es_ui_font_init()) {
        es_ui_error("ui face init fail");
        return ES_RET_FAILURE;
    }

    if (ES_RET_SUCCESS != es_ui_infobar_init()) {
        es_ui_error("ui infobar init fail");
        return ES_RET_FAILURE;
    }

    if (ES_RET_SUCCESS != es_ui_saver_init()) {
        es_ui_error("ui saver init fail");
        return ES_RET_FAILURE;
    }

    if (ES_RET_SUCCESS != es_ui_factory_init()) {
        es_ui_error("ui factory init fail");
        return ES_RET_FAILURE;
    }

    ui_mode = es_ui_get_mode();
    es_ui_set_mode(ui_mode);

    return ES_RET_SUCCESS;
}

#if ES_UI_BAR_WIDTH && ES_UI_BAR_HEIGHT
static void lvgl_bar_area_flush_cb(lv_disp_drv_t * disp_drv, const lv_area_t * area, lv_color_t * color_p)
{
    ES_U16 w = (area->x2 - area->x1 + 1);
    ES_U16 h = (area->y2 - area->y1 + 1);

#if ES_LCD_PIX_NEED_SWAP
    uint16_t tmp, *pix = (uint16_t*)color_p;
    int i;

    for (i = 0; i < (w * h);) {
        tmp = pix[i];
        pix[i] = pix[i + 1];
        pix[i + 1] = tmp;
        i += 2;
    }
#endif
    es_ui_debug("bar, x:%d, y:%d, w:%d, h:%d", area->x1, area->y1, w, h);
    es_hal_lcd_draw_picture(area->x1, area->y1 + 640, w, h, (const ES_BYTE *) color_p);

    lv_disp_flush_ready(disp_drv);

    return;
}

static ES_U16 cam_area_scale_buf[480 * ES_UI_DRAW_BUF_LINE * 2];
static void lvgl_camera_area_flush_cb(lv_disp_drv_t * disp_drv, const lv_area_t * area, lv_color_t * color_p)
{
    ES_U16 t = 0;
    ES_U16 w = (area->x2 - area->x1 + 1);
    ES_U16 h = (area->y2 - area->y1 + 1);

#if 0 // direct draw origin pic.
    es_hal_lcd_draw_picture(area->x1, area->y1, w, h, (const ES_BYTE *) color_p);
#else
    // uint64_t scale_time = sysctl_get_time_us();

    for(int i = 0; i < ES_UI_DRAW_BUF_LINE; i++) {
    #if 0 // test only scale y axis.
        // copy one line no scale, just for test.
        memcpy(&cam_area_scale_buf[(i * 2 + 0) * 480], &(((const ES_U16 *)color_p)[i * 240]), 240 * 2);
    #else
        // scale one line for x scale.
        ES_U16 *p_src = (ES_U16 *)(&(((ES_U16 *)color_p)[i * 240]));
        ES_U16 *p_dst = (ES_U16 *)(&cam_area_scale_buf[(i * 2 + 0) * 480]);

        // manual expand the loop
        for(int j = 0; j < 240; j += 4) {
            t = *p_src++;
            *p_dst++ = t; *p_dst++ = t;

            t = *p_src++;
            *p_dst++ = t; *p_dst++ = t;

            t = *p_src++;
            *p_dst++ = t; *p_dst++ = t;

            t = *p_src++;
            *p_dst++ = t; *p_dst++ = t;
        }
    #endif

        // copy one line for y scale.
        memcpy(&cam_area_scale_buf[(i * 2 + 1) * 480], p_dst, 480 * 2);
    }
    // es_ui_debug("scale %ld us\n", sysctl_get_time_us() - scale_time);

    // uint64_t time = sysctl_get_time_us();

    es_hal_lcd_draw_picture(area->x1, area->y1 * 2, w * 2, h * 2, (const ES_BYTE *) cam_area_scale_buf);
    // es_ui_debug("flush %ld us\n", sysctl_get_time_us() - time);

    // es_ui_debug("Camera -> x1:%dy1:%dx2:%dy2:%d\n", area->x1, area->y1, area->x2, area->y2);
#endif

    lv_disp_flush_ready(disp_drv);
    return;
}
#else
static void lvgl_camera_area_flush_cb(lv_disp_drv_t * disp_drv, const lv_area_t * area, lv_color_t * color_p)
{
    ES_U16 w = (area->x2 - area->x1 + 1);
    ES_U16 h = (area->y2 - area->y1 + 1);

#if ES_LCD_PIX_NEED_SWAP
    uint16_t tmp, *pix = (uint16_t*)color_p;
    int i;

    for (i = 0; i < (w * h);) {
        tmp = pix[i];
        pix[i] = pix[i + 1];
        pix[i + 1] = tmp;
        i += 2;
    }
#endif

    es_hal_lcd_draw_picture(area->x1, area->y1, w, h, (const ES_BYTE *) color_p);
    lv_disp_flush_ready(disp_drv);
    return;
}
#endif

static ES_S32 es_ui_lvgl_buf_init(ES_VOID)
{
    // for camear area
    lv_disp_draw_buf_init(&cam_disp_buf, g_cam_buf, NULL, ES_UI_WIDTH * ES_UI_DRAW_BUF_LINE);
    lv_disp_drv_init(&cam_disp_drv);
    cam_disp_drv.draw_buf = &cam_disp_buf;
    cam_disp_drv.flush_cb = lvgl_camera_area_flush_cb;
    cam_disp_drv.hor_res = ES_UI_WIDTH;
    cam_disp_drv.ver_res = ES_UI_HEIGHT;
    cam_disp = lv_disp_drv_register(&cam_disp_drv);

#if ES_UI_BAR_WIDTH && ES_UI_BAR_HEIGHT
    // for bar area
    lv_disp_draw_buf_init(&disp_buf, g_buf, NULL, ES_UI_WIDTH * ES_UI_DRAW_BUF_LINE);
    lv_disp_drv_init(&disp_drv);
    disp_drv.draw_buf = &disp_buf;
    disp_drv.flush_cb = lvgl_bar_area_flush_cb;
    disp_drv.hor_res = ES_UI_BAR_WIDTH;
    disp_drv.ver_res = ES_UI_BAR_HEIGHT;
    bar_disp = lv_disp_drv_register(&disp_drv);
    lv_disp_set_bg_color(bar_disp, lv_color_black());
#endif

    lv_disp_set_default(cam_disp);

    return ES_RET_SUCCESS;
}


ES_S32 es_ui_init(ES_VOID)
{
    lv_init();
#if ES_FS_ENABLE
    lv_fs_if_init();
#endif
    lv_split_jpeg_init();
    es_ui_lvgl_buf_init();
    es_ui_lvgl_widgets();

    return ES_RET_SUCCESS;
}

ES_S32 es_ui_refresh(ES_VOID)
{
    static ES_U32 last_update_time_ms = 0;
    ES_U32 now_time_ms = 0;

    es_ui_check_mode();

    now_time_ms = es_time_get_sytem_ms();
    lv_tick_inc(now_time_ms-last_update_time_ms);
    lv_task_handler();
    last_update_time_ms = now_time_ms;

    return ES_RET_SUCCESS;
}

