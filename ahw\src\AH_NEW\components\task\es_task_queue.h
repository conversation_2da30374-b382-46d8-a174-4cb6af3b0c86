/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_task_queue.h
** bef: define the interface for task queue. 
** auth: lines<<EMAIL>>
** create on 2020.11.26
*/

#ifndef _ES_TASK_QUEUE_H_
#define _ES_TASK_QUEUE_H_

#ifdef __cplusplus 
extern "C" { 
#endif

#include "es_types.h"


typedef enum {
    ES_TASK_QUEUE_NONE,
    ES_TASK_PARSE_FACE_FILE,
    ES_TASK_PARSE_FACE_TUYA_ADD,
    ES_TASK_PARSE_FACE_TUYA_DEL,
    ES_TASK_ACTIVE_DEVICE,
    ES_TASK_RESET_DEVICE,
    ES_TASK_SAVE_BRD_CFG,
    ES_TASK_DEL_FACEDB_ALL,
    ES_TASK_PASSLOG_READ_JSON_STR,
    ES_TASK_PASSLOG_UPLOAD_RESULT,
    ES_TASK_FACE_FEATRUE_GET_SHA256,
    ES_TASK_MODEL_ACTIVE,
    ES_TASK_PARSE_MQTT_FACE_FEA,
    ES_TASK_PARSE_PERIP_BLE_CFG,
    ES_TASK_VOICE_PLAY,
    ES_TASK1_QUEUE_START = 0x1000,  // don't use this
    ES_TASK_UPLOAD_FACE_PIC,
} es_task_type_e;


typedef struct {
    ES_U8 type;
    ES_VOID *param;
    ES_U32 timeout;
} es_task_param_t;

ES_S32 es_task_queue_init(ES_VOID);

// only push, no wait finish
ES_S32 es_task_queue_push(const es_task_param_t *p);

// push to queue, wait for finish or timeout
ES_S32 es_task_queue_push_wait(const es_task_param_t *p);

// flash opt;
ES_VOID es_task_queue_core0_run(ES_VOID);

// upload pic
ES_VOID es_task_queue_core1_run(ES_VOID);

ES_S32 es_task_queue_play_voice(ES_U8 type);


#ifdef __cplusplus 
}
#endif

#endif