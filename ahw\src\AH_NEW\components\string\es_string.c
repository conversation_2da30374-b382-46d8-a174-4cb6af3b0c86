/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_string.c
** bef: define the interface for string.
** auth: lines<<EMAIL>>
** create on 2021.11.24 
*/

#include "es_inc.h"

// #define ES_STRING_DEBUG
#ifdef ES_STRING_DEBUG
#define es_string_debug es_log_info
#define es_string_error es_log_error
#else
#define es_string_debug(...)
#define es_string_error(...)
#endif

ES_CHAR *es_string_find_char(const ES_CHAR *p, ES_CHAR c)
{
    const ES_CHAR *pos = p;

    while (1) {
        if (*pos == c) {
            return (ES_CHAR *)pos;
        }

        if (*pos == 0) {
            return ES_NULL;
        }

        pos++;
    }

    return ES_NULL;
}


ES_BYTE es_string_char2hex(ES_CHAR c)
{
    if ('0' <= c && c <= '9') {
        return c - '0';
    } else if ('A' <= c && c <= 'F') {
        return c - 'A' + 10;
    } else if ('a' <= c && c <= 'f') {
        return c - 'a' + 10;
    }

    return 0x10;
}

ES_U32 es_string_str2hex(const ES_CHAR *str, ES_BYTE *hex, ES_U32 hex_len)
{
    ES_BYTE tmp = 0;
    ES_U32 bytes_len = 0;
    const ES_CHAR *p = str;

    if (ES_NULL == p) {
        return 0;
    }

    while (*p && bytes_len < hex_len) {
        tmp = es_string_char2hex(*p);
        if (tmp > 0x0F) {
            return bytes_len;
        }
        hex[bytes_len] = (tmp << 4) & 0xF0;
        p++;

        if (*p == 0) {
            return bytes_len;
        }

        tmp = es_string_char2hex(*p);
        if (tmp > 0x0F) {
            return bytes_len;
        }
        hex[bytes_len] += (tmp & 0x0F);
        bytes_len++;
        p++;
    }

    return bytes_len;
}

ES_CHAR *es_string_get_next_line(const ES_CHAR *p)
{
    ES_CHAR *next_line = ES_NULL;

    next_line = (ES_CHAR *)es_strstr((char *)p, "\r\n");
    if (ES_NULL == next_line) {
        return ES_NULL;
    }

    next_line = (ES_CHAR *)(next_line + 2);
    return next_line;
}

ES_BOOL es_string_start_with(const ES_CHAR *string, const ES_CHAR *start)
{
    const ES_CHAR *p1 = string;
    const ES_CHAR *p2 = start;

    if (ES_NULL == p1  && ES_NULL == p2) {
        return ES_FALSE;
    }

    while (*p2 && *p2) {
        if (*p1 != *p2) {
            return ES_FALSE;
        }

        p1++;
        p2++;
    }

    return ES_TRUE;
}