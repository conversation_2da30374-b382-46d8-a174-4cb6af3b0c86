/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_key.h
** bef: define the interface for key. 
** auth: lines<<EMAIL>>
** create on 2020.12.21
*/

#ifndef _ES_KEY_H_
#define _ES_KEY_H_

#ifdef __cplusplus 
extern "C" { 
#endif

#include "es_types.h"

typedef enum {
    ES_KEY_EVT_SIGLE,   // single click.
    ES_KEY_EVT_DOUBLE,  // double key
    ES_KEY_EVT_LONG,    // long press
} es_key_evt_e;

ES_S32 es_key_init(ES_VOID);

ES_VOID es_key_task(ES_VOID);

#ifdef __cplusplus 
}
#endif

#endif