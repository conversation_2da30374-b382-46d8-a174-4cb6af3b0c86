#include "facelib_inc.h"

#define TO_STRING(x)  #x
#define NAME_STR(x)  TO_STRING(x)


static volatile uint8_t lock_camera_rgb = 0;

static volatile uint8_t rgb_inuse_flag = 0; //前台是否正在使用buf
static volatile uint8_t kpu_inuse_flag = 0;

static volatile uint8_t kpu_buf_state[2] = {0, 0}; //0,前台，1，后台
static volatile uint8_t rgb_buf_state[2] = {0, 0}; //0,前台，1，后台

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////



static void dvp_set_kpu_buf(void)
{
    dvp_set_ai_addr(_IOMEM_ADDR(mf_cam.kpu_image[1 - mf_cam.kpu_buf_index]),
                    _IOMEM_ADDR(mf_cam.kpu_image[1 - mf_cam.kpu_buf_index]) + MF_CAM_W * MF_CAM_H,
                    _IOMEM_ADDR(mf_cam.kpu_image[1 - mf_cam.kpu_buf_index]) + MF_CAM_W * MF_CAM_H * 2);
    return;
}

static void dvp_set_rgb_buf(void)
{
    // dvp_set_display_addr(_IOMEM_ADDR(mf_cam.rgb_image[1 - mf_cam.rgb_buf_index]));/* 删除，2019.11.11 by XEL */
    dvp_set_display_addr((uint32_t)(mf_cam.rgb_image[1 - mf_cam.rgb_buf_index]));
    return;
}

//采集逻辑
//每个镜头处于两种状态：前台正在使用buf，前台结束使用buf; 后台正在采集，后台采集完成。
//inuse 由前台设置，后台读取
//fin：当在inuse时fin了，则置位，否则切换i，不置位
//start的时候，inuse&&fin 略过，inuse&&~fin 开启，~inuse&&fin 切换buf并清fin，~inuse&&~fin 开启

//传输的地址在start处设定
//i由前台设定，清fin

//buf 4个状态：
//前台buf待补，后台buf待补
//前台buf待补，后台buf完成
//前台buf已有，后台buf待补
//前台buf已有，后台buf完成

#if 1
#define DBG_FRAME(x)
#else
#define DBG_FRAME(x)                                                    \
	{ 	_t1 = sysctl_get_time_us()-_t0;                                 \
		printk("%s %ld.%ld\r\n", NAME_STR(x),_t1/1000, (_t1/100)%10);   \
		_t0 = sysctl_get_time_us();                                     \
	}
#endif
		
int dvp_irq_gc0328_dual(void *ctx)
{
	//static uint64_t _t0=0;uint64_t _t1;
    //循环切换两个摄像头
    if(mf_cam.index == 0) /* 850 */
    {
        if(dvp_get_interrupt(DVP_STS_FRAME_FINISH))
        {
            //printk("F8 %ld.%ld\r\n", (sysctl_get_time_us()/1000)%1000, (sysctl_get_time_us()/100)%10);
			DBG_FRAME(F8);
            //850抓取完成，切换到650
            dvp_clear_interrupt(DVP_STS_FRAME_FINISH);
            mf_cam.switch_650();

            uint16_t * kpu_image_io = (uint16_t *)_IOMEM_ADDR(mf_cam.kpu_image[1 - mf_cam.kpu_buf_index]);

            if(kpu_image_io[MF_CAM_W * MF_CAM_H - 1] != 0xFF00 || kpu_image_io[MF_CAM_W * MF_CAM_H - 2] != 0xFF00)
            {
                //注意这里的判断需要结合屏幕的旋转方向
                kpu_buf_state[1] = 1; //标记完成
            } else
            {
                // printk("850 Dirty, retry!\r\n");
                memset((uint8_t*)(_IOMEM_ADDR(mf_cam.kpu_image[1 - mf_cam.kpu_buf_index])), 0, MF_CAM_W * MF_CAM_H * 2);
            }
        } else
        {
            //printk("S8 %ld.%ld\r\n", (sysctl_get_time_us()/1000)%1000, (sysctl_get_time_us()/100)%10);
			DBG_FRAME(S8);
            //开始抓取
            dvp_clear_interrupt(DVP_STS_FRAME_START);
            dvp_set_kpu_buf(); //更新最新的buf位置(前台更改)
            if(kpu_inuse_flag == 0)
            {
                //空闲时候来新帧
                if(kpu_buf_state[0] == 0 && kpu_buf_state[1] == 0)
                {
                    //前后台buf均空
                    dvp_start_convert();

                    if(mf_brd.dvp_open_ir)
                    {
                        mf_brd.set_IR_LED(1);
                    }
                } else if(kpu_buf_state[0] == 0 && kpu_buf_state[1] == 1)
                {
                    //前台空，后台有，上次采集好的，前台还没执行到lock使用，略过
                } else if(kpu_buf_state[0] == 1 && kpu_buf_state[1] == 0)
                {
                    //前台有，后台空
                    dvp_start_convert();

                    if(mf_brd.dvp_open_ir)
                    {
                        mf_brd.set_IR_LED(1);
                    }
                } else if(kpu_buf_state[0] == 1 && kpu_buf_state[1] == 1)
                {
                    //前台有，后台有,略过
                    mf_cam.switch_650();
                }
            } else /* kpu_inuse_flag == 1 */
            {
                /*
                //使用时候来新帧
                if(kpu_buf_state[0] == 0)
                {
                    //前台buf空，不可能，使用的时候肯定是有的
                    printk("inuse: BAD! L%d\r\n", __LINE__);
                    while(1)
                    {
                    };
                } else if(kpu_buf_state[0] == 1 && kpu_buf_state[1] == 0)
                */
                if((kpu_buf_state[0] == 1 && kpu_buf_state[1] == 0) || (kpu_buf_state[0] == 0))
                {
                    //前台有，后台空
                    dvp_start_convert();

                    if(mf_brd.dvp_open_ir)
                    {
                        mf_brd.set_IR_LED(1);
                    }
                } else if(kpu_buf_state[0] == 1 && kpu_buf_state[1] == 1)
                {
                    //前台有，后台有,略过
                    mf_cam.switch_650();
                }
            }
        }
    } else /* 650 */
    {
        if(dvp_get_interrupt(DVP_STS_FRAME_FINISH))
        {
            //printk("F6 %ld.%ld\r\n", (sysctl_get_time_us()/1000)%1000, (sysctl_get_time_us()/100)%10);
			DBG_FRAME(F6);
            dvp_clear_interrupt(DVP_STS_FRAME_FINISH);
            if(0x00 == lock_camera_rgb) {
                mf_cam.switch_850();
             }

            uint16_t *rgb_image_io = (uint16_t *)(mf_cam.rgb_image[1 - mf_cam.rgb_buf_index]);

            if(rgb_image_io[MF_CAM_W * MF_CAM_H - 1] != 0xFF00 || rgb_image_io[MF_CAM_W * MF_CAM_H - 2] != 0xFF00)
            {   //这里判断的依据是正常摄像头刷完缓冲，会覆盖掉我们之前的标记
                //注意这里的判断需要结合屏幕的旋转方向
                rgb_buf_state[1] = 1;
            } else
            {
                printk("650 Dirty, retry!\r\n");
            }
        } else
        {
            //printk("S6 %ld.%ld\r\n", (sysctl_get_time_us()/1000)%1000, (sysctl_get_time_us()/100)%10);
			DBG_FRAME(S6);
            //开始抓取
            dvp_clear_interrupt(DVP_STS_FRAME_START);
            dvp_set_rgb_buf(); //更新最新的buf位置(前台更改)

            if(rgb_inuse_flag == 0)
            {
                //空闲时候来新帧
                if(rgb_buf_state[0] == 0 && rgb_buf_state[1] == 0)
                {
                    //前后台buf均空
                    dvp_start_convert();
                    mf_cam.update_lum(); /* ADD, update night */

                    if(mf_brd.dvp_open_ir)
                    {
                        mf_brd.set_IR_LED(0);
                    }
                } else if(rgb_buf_state[0] == 0 && rgb_buf_state[1] == 1)
                {
                    //前台空，后台有，上次采集好的，前台还没执行到lock使用，略过
                } else if(rgb_buf_state[0] == 1 && rgb_buf_state[1] == 0)
                {
                    //前台有，后台空
                    dvp_start_convert();
                    mf_cam.update_lum(); /* ADD, update night */

                    if(mf_brd.dvp_open_ir)
                    {
                        mf_brd.set_IR_LED(0);
                    }
                } else if(rgb_buf_state[0] == 1 && rgb_buf_state[1] == 1)
                {
                    //前台有，后台有,略过
                    if(0x00 == lock_camera_rgb) {
                        mf_cam.switch_850();
                    }
                }
            } else /* rgb_inuse_flag == 1 */
            {
                /*
                //使用时候来新帧
                if(rgb_buf_state[0] == 0)
                {
                    //前台buf空，不可能，使用的时候肯定是有的
                    printk("inuse: BAD! L%d\r\n", __LINE__);
                    while(1)
                    {
                    };
                } else if(rgb_buf_state[0] == 1 && rgb_buf_state[1] == 0)
                */
                if((rgb_buf_state[0] == 1 && rgb_buf_state[1] == 0) || (rgb_buf_state[0] == 0))
                {
                    //前台有，后台空
                    dvp_start_convert();

                    mf_cam.update_lum(); /* ADD, update night */

                    if(mf_brd.dvp_open_ir)
                    {
                        mf_brd.set_IR_LED(0);
                    }
                } else if(rgb_buf_state[0] == 1 && rgb_buf_state[1] == 1)
                {
                    //前台有，后台有,略过
                    if(0x00 == lock_camera_rgb) {
                        mf_cam.switch_850();
                    }
                }
            }
        }
    }
    return 0;
}

//i切换时机
//如果使用完成后的unlock，已经完成后台采集，则此时切换
//或者等到lock的时候再切换
void kpu_unlock_buf(void)
{
    uint16_t *pixel = (uint16_t*)(_IOMEM_ADDR(mf_cam.kpu_image[mf_cam.kpu_buf_index]) + MF_CAM_W * 230 * 2);

    for(uint32_t i = 0; i < MF_CAM_W * 10; i++)  //mark, 标记本帧为旧的
    {
        *(pixel + i) = 0xFF00;
    }

    if(kpu_buf_state[1] == 1) //后台缓冲好了
    {
        //TODO:原子操作
        kpu_buf_state[0] = 1;
        kpu_buf_state[1] = 0;
        mf_cam.kpu_buf_index = 1 - mf_cam.kpu_buf_index; //成功切换缓冲
    } else
    {
        //仍然等待后台采集
        kpu_buf_state[0] = 0;
    }
    kpu_inuse_flag = 0;
    return;
}

void kpu_lock_buf(void)
{
    // uint64_t t0 = sysctl_get_time_us();
    if(kpu_buf_state[0] == 0) //之前没填充上去
    {
        while(kpu_buf_state[1] == 0)
        {
            /* 等待后台buf完成 */
            msleep(1);
        };
        kpu_buf_state[1] = 0;
        kpu_buf_state[0] = 1;
        mf_cam.kpu_buf_index = 1 - mf_cam.kpu_buf_index; //成功切换缓冲
    } else
    {
        //已经缓冲好
        //bypass
    }
    kpu_inuse_flag = 1;
    //printk("kpu lock wait %ld\r\n", (sysctl_get_time_us()-t0)/1000);
    return;
}

void rgb_unlock_buf(void)
{
    uint16_t *pixel = (uint16_t*)(mf_cam.rgb_image[mf_cam.rgb_buf_index] + MF_CAM_W * 230 * 2);

    for(uint32_t i = 0; i < MF_CAM_W * 10; i++) //mark, 标记本帧为旧的
    {
        *(pixel + i) = 0xFF00;
    }

    if(rgb_buf_state[1] == 1) //后台缓冲好了
    {
        /* TODO:原子操作 */
        rgb_buf_state[0] = 1;
        rgb_buf_state[1] = 0;
        mf_cam.rgb_buf_index = 1 - mf_cam.rgb_buf_index; //成功切换缓冲
    } else
    {
        //仍然等待后台采集
        rgb_buf_state[0] = 0;
    }
    rgb_inuse_flag = 0;
    return;
}

void rgb_lock_buf(void)
{
    // uint64_t t0 = sysctl_get_time_us();
    if(rgb_buf_state[0] == 0)
    {
        while(rgb_buf_state[1] == 0)
        {
            /* 等待后台buf完成 */
            usleep(100);
        };
        rgb_buf_state[0] = 1;
        rgb_buf_state[1] = 0;
        mf_cam.rgb_buf_index = 1 - mf_cam.rgb_buf_index; //成功切换缓冲
    } else
    {
        //已经缓冲好
    }
    rgb_inuse_flag = 1;
    //printk("rgb lock wait %ld\r\n", (sysctl_get_time_us()-t0)/1000);
    return;
}

void cam_dualbuf_first_run(void)
{
	kpu_lock_buf();
	rgb_lock_buf();
	while(kpu_buf_state[1] == 0)
	{
		msleep(1);
	};
	while(rgb_buf_state[1] == 0)
	{
		msleep(1);
	};
	kpu_unlock_buf();
	rgb_unlock_buf();
	return;
}

void cam_dualbuf_lock_sync(void)
{
    if(rgb_buf_state[1] == 1) {
        rgb_buf_state[1] = 0;
	}
	return;
}

uint8_t cam_dualbuf_lock_buf(void)
{
	if(rgb_buf_state[0] == 1) return 1;	//rgb好了
	else return 0; //否则不管好没好，都先lock ir
}

void camera_lock_650(void)
{
    while(0x00 == mf_cam.index);
    lock_camera_rgb = 1;
}

void camera_unlock_650(void)
{
    lock_camera_rgb = 0;
}

