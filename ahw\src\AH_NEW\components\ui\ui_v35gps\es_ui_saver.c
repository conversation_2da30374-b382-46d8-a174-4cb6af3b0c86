#include "es_inc.h"

#if (ES_UI_TYPE == ES_UI_TYPE_K35V_240_320_GPS)
#include "es_ui_img_res.h"

// #define ES_UI_SAVER_DEBUG
#ifdef ES_UI_SAVER_DEBUG
#define es_ui_saver_debug es_log_info
#define es_ui_saver_error es_log_error
#else
#define es_ui_saver_debug(...)
#define es_ui_saver_error(...)
#endif

#define ES_UI_QRCODE_SIZE           (85)
#define ES_UI_QR_DATA_LEN           (90)

static lv_obj_t *lv_qrcode = ES_NULL;
static ES_CHAR qrcode_data[ES_UI_QR_DATA_LEN];

static lv_obj_t *lv_saver_bg = ES_NULL;
extern lv_disp_t *cam_disp;

#define DEF_QRCODE ("http://forklift.hsznforklift.cn/forklift/mqtt/login?unionId=%s")
static ES_S32 default_qrcode(const ES_CHAR *qrcode_str)
{
    ES_CHAR lte_mqtt_id[ES_MAC_COLON_STR_LEN+1] = {0};

    if (ES_RET_SUCCESS != es_hal_lte_get_mqtt_id(lte_mqtt_id)) {
        return ES_RET_FAILURE;
    }

    es_snprintf((char *)qrcode_str, ES_FLASH_USER_QRCODE_LEN, DEF_QRCODE, lte_mqtt_id);

    es_dev_cfg_update_qrcode((const ES_CHAR *)qrcode_str);

    return ES_RET_SUCCESS;
}

static ES_S32 es_ui_saver_show_dynamic_qr(ES_VOID)
{
    memset(qrcode_data, 0x00, sizeof(qrcode_data));
    if (ES_RET_SUCCESS != es_dev_cfg_get_qrcode(qrcode_data)) {
        if(ES_RET_SUCCESS != default_qrcode(qrcode_data)){
            return ES_RET_FAILURE;
        }
    }

    if(0 != strncmp(qrcode_data, DEF_QRCODE, strlen(DEF_QRCODE))) {
        default_qrcode(qrcode_data);
    }

    // lv_obj_clean(lv_saver_bg);
    if (ES_NULL == lv_qrcode) {
        lv_qrcode = lv_qrcode_create(lv_saver_bg, ES_UI_QRCODE_SIZE, lv_color_hex3(0x000), lv_color_hex3(0xeef));
    }

    lv_obj_align(lv_qrcode, LV_ALIGN_TOP_LEFT, 240-ES_UI_QR_DATA_LEN, 320-ES_UI_QR_DATA_LEN-10);
    lv_qrcode_update(lv_qrcode, qrcode_data, strlen(qrcode_data));

    return ES_RET_SUCCESS;
}

static ES_S32 es_ui_saver_close_dynamic_qr(ES_VOID)
{
    if (ES_NULL != lv_qrcode) {
        lv_qrcode_delete(lv_qrcode);
        lv_qrcode = ES_NULL;
    }
    return ES_RET_SUCCESS;
}

ES_S32 es_ui_saver_create_widgets(ES_VOID)
{
    lv_obj_t *obj;

    // lv_saver_bg = lv_obj_create(lv_scr_act());
    lv_saver_bg = lv_obj_create(lv_disp_get_scr_act(cam_disp));
    lv_obj_remove_style_all(lv_saver_bg);
    lv_obj_set_size(lv_saver_bg, ES_UI_WIDTH, ES_UI_HEIGHT);
    lv_obj_align(lv_saver_bg, LV_ALIGN_TOP_LEFT, 0, 0);

    obj = lv_img_create(lv_saver_bg);
    lv_obj_align(obj, LV_ALIGN_TOP_LEFT, 0, 0);
    lv_img_set_src(obj, ES_UI_IMG_SRC_SAVER);

    return ES_RET_SUCCESS;
}

ES_S32 es_ui_saver_init(ES_VOID)
{
    es_ui_saver_create_widgets();
    es_ui_saver_show(ES_FALSE);

    return ES_RET_SUCCESS;
}

ES_S32 es_ui_saver_show(ES_BOOL show)
{
    if (show) {
        es_ui_saver_show_dynamic_qr();
        lv_obj_clear_flag(lv_saver_bg, LV_OBJ_FLAG_HIDDEN);
    } else {
        es_ui_saver_close_dynamic_qr();
        lv_obj_add_flag(lv_saver_bg, LV_OBJ_FLAG_HIDDEN);
    }

    return ES_RET_SUCCESS;
}

#endif