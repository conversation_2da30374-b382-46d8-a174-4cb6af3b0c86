/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_model.h
** bef: define the interface for model. 
** auth: lines<<EMAIL>>
** create on 2022.10.15
*/

#ifndef _ES_MODEL_H_
#define _ES_MODEL_H_

#ifdef __cplusplus 
extern "C" { 
#endif

#include "es_types.h"

ES_S32 es_model_init(ES_BOOL active);

ES_VOID es_model_run(ES_VOID);

ES_S32 es_model_active(const ES_BYTE *active_key);

#ifdef __cplusplus
}
#endif
#endif