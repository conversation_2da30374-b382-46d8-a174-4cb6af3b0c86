/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_gps.h
** bef: define the interface for gps. 
** auth: lines<<EMAIL>>
** create on 2022.12.03
*/

#ifndef _ES_GPS_MINMEA_H_
#define _ES_GPS_MINMEA_H_

#ifdef __cplusplus 
extern "C" { 
#endif

#include "es_types.h"

typedef struct {
    char latitude[10];  //dd.ddddd
    char longitude[10]; // ddd.ddddd
    char hdop[4];       // 0.5~99.9
    char altitude[8];   // 9999.9
    char fix[2];        // 2 or 3
    char cog[8];        // ddd.mm
    char spkm[8];       // xxxx.x
    char spkn[8];       // xxxx.x
    char nsta[4];       // 0-12
} es_gps_info_t;

ES_S32 es_gps_minmea_parse(const ES_CHAR *minmea, es_gps_info_t *info);

#ifdef __cplusplus 
}
#endif

#endif
