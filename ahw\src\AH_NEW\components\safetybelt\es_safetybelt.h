/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_safetybelt.h
** bef: define the interface for safetybelt. 
** auth: lines<<EMAIL>>
** create on 2022.01.08
*/

#ifndef _ES_SAFETBELT_H_
#define _ES_SAFETBELT_H_

#ifdef __cplusplus 
extern "C" { 
#endif

#include "es_types.h"


ES_S32 es_safetybelt_init(ES_VOID);

ES_BOOL es_safetybelt_check(ES_VOID);

ES_BOOL es_safetybelt_get(ES_VOID);

#ifdef __cplusplus 
}
#endif

#endif
