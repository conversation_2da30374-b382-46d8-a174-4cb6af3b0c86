#include "es_inc.h"

#if (ES_LCD_DRIVER_ST7789 == ES_LCD_DRIVER_TYPE)
#include "es_lcd_tft_spi.h"

static ES_U16 lcd_width = ES_LCD_WIDTH;
static ES_U16 lcd_height = ES_LCD_HEIGHT;

/* clang-format off */
#define NO_OPERATION            0x00
#define SOFTWARE_RESET          0x01
#define READ_ID                 0x04
#define READ_STATUS             0x09
#define READ_POWER_MODE         0x0A
#define READ_MADCTL             0x0B
#define READ_PIXEL_FORMAT       0x0C
#define READ_IMAGE_FORMAT       0x0D
#define READ_SIGNAL_MODE        0x0E
#define READ_SELT_DIAG_RESULT   0x0F
#define SLEEP_ON                0x10
#define SLEEP_OFF               0x11
#define PARTIAL_DISPALY_ON      0x12
#define NORMAL_DISPALY_ON       0x13
#define INVERSION_DISPALY_OFF   0x20
#define INVERSION_DISPALY_ON    0x21
#define GAMMA_SET               0x26
#define DISPALY_OFF             0x28
#define DISPALY_ON              0x29
#define HORIZONTAL_ADDRESS_SET  0x2A
#define VERTICAL_ADDRESS_SET    0x2B
#define MEMORY_WRITE            0x2C
#define COLOR_SET               0x2D
#define MEMORY_READ             0x2E
#define PARTIAL_AREA            0x30
#define VERTICAL_SCROL_DEFINE   0x33
#define TEAR_EFFECT_LINE_OFF    0x34
#define TEAR_EFFECT_LINE_ON     0x35
#define MEMORY_ACCESS_CTL       0x36
#define VERTICAL_SCROL_S_ADD    0x37
#define IDLE_MODE_OFF           0x38
#define IDLE_MODE_ON            0x39
#define PIXEL_FORMAT_SET        0x3A
#define WRITE_MEMORY_CONTINUE   0x3C
#define READ_MEMORY_CONTINUE    0x3E
#define SET_TEAR_SCANLINE       0x44
#define GET_SCANLINE            0x45
#define WRITE_BRIGHTNESS        0x51
#define READ_BRIGHTNESS         0x52
#define WRITE_CTRL_DISPALY      0x53
#define READ_CTRL_DISPALY       0x54
#define WRITE_BRIGHTNESS_CTL    0x55
#define READ_BRIGHTNESS_CTL     0x56
#define WRITE_MIN_BRIGHTNESS    0x5E
#define READ_MIN_BRIGHTNESS     0x5F
#define READ_ID1                0xDA
#define READ_ID2                0xDB
#define READ_ID3                0xDC
#define RGB_IF_SIGNAL_CTL       0xB0
#define NORMAL_FRAME_CTL        0xB1
#define IDLE_FRAME_CTL          0xB2
#define PARTIAL_FRAME_CTL       0xB3
#define INVERSION_CTL           0xB4
#define BLANK_PORCH_CTL         0xB5
#define DISPALY_FUNCTION_CTL    0xB6
#define ENTRY_MODE_SET          0xB7
#define BACKLIGHT_CTL1          0xB8
#define BACKLIGHT_CTL2          0xB9
#define BACKLIGHT_CTL3          0xBA
#define BACKLIGHT_CTL4          0xBB
#define BACKLIGHT_CTL5          0xBC
#define BACKLIGHT_CTL7          0xBE
#define BACKLIGHT_CTL8          0xBF
#define POWER_CTL1              0xC0
#define POWER_CTL2              0xC1
#define VCOM_CTL1               0xC5
#define VCOM_CTL2               0xC7
#define NV_MEMORY_WRITE         0xD0
#define NV_MEMORY_PROTECT_KEY   0xD1
#define NV_MEMORY_STATUS_READ   0xD2
#define READ_ID4                0xD3
#define POSITIVE_GAMMA_CORRECT  0xE0
#define NEGATIVE_GAMMA_CORRECT  0xE1
#define DIGITAL_GAMMA_CTL1      0xE2
#define DIGITAL_GAMMA_CTL2      0xE3
#define INTERFACE_CTL           0xF6
/* clang-format on */

static void init_seq(void)
{
    uint8_t mult_data[6] = {0};
    uint8_t i = 0;

    //soft reset
    es_tft_write_command(SOFTWARE_RESET);
    usleep(6 * 1000);
    //exit sleep
    es_tft_write_command(SLEEP_OFF);
    usleep(6 * 1000);
    //pixel format
    es_tft_write_command(PIXEL_FORMAT_SET);
    es_tft_write_onebyte(0x05);
    // if(l_lcd_para->inverse) {
        es_tft_write_command(INVERSION_DISPALY_ON);
    // }

    es_tft_write_command(0xDF);
    mult_data[i++] = (0x5A);
    mult_data[i++] = (0x69);
    mult_data[i++] = (0x02);
    mult_data[i++] = (0x01);
    es_tft_write_byte(mult_data, i);

    es_tft_write_command(WRITE_BRIGHTNESS_CTL); //CACB
    es_tft_write_onebyte(0xb3);
    es_tft_write_command(WRITE_MIN_BRIGHTNESS);
    es_tft_write_onebyte(0x80);
    es_tft_write_command(DISPALY_ON);
}

static ES_S32 es_hal_lcd_set_direction(ES_U8 dir)
{
    uint16_t tmp = 0;

    if(dir & ES_LCD_DIR_XY_MASK) {
        if(lcd_width < lcd_height) {
            tmp = lcd_width;
            lcd_width = lcd_height;
            lcd_height = tmp;
        }
    } else {
        if(lcd_width > lcd_height) {
            tmp = lcd_width;
            lcd_width = lcd_height;
            lcd_height = tmp;
        }
    }
    es_tft_write_command(MEMORY_ACCESS_CTL);
    es_tft_write_byte((uint8_t *)&dir, 1);

    return ES_RET_SUCCESS;
}


static void st7789_gpio_init(void)
{
    // backlight
    fpioa_set_function(ES_LCD_BL_PIN,   FUNC_GPIOHS0 + ES_LCD_BL_HS_NUM);
    gpiohs_set_drive_mode(ES_LCD_BL_HS_NUM, GPIO_DM_OUTPUT);
    gpiohs_set_pin(ES_LCD_BL_HS_NUM, 1);

    /* LCD IO*/
    fpioa_set_function(ES_LCD_RST_PIN, FUNC_GPIOHS0 + ES_LCD_RST_HS_NUM);
    fpioa_set_function(ES_LCD_DCX_PIN, FUNC_GPIOHS0 + ES_LCD_DCX_HS_NUM);
    fpioa_set_function(ES_LCD_CS_PIN, FUNC_SPI0_SS3);
    fpioa_set_function(ES_LCD_SCK_PIN, FUNC_SPI0_SCLK);

    fpioa_set_io_driving(ES_LCD_RST_PIN, FPIOA_DRIVING_15);
    fpioa_set_io_driving(ES_LCD_DCX_PIN, FPIOA_DRIVING_15);
    fpioa_set_io_driving(ES_LCD_CS_PIN, FPIOA_DRIVING_15);
    fpioa_set_io_driving(ES_LCD_SCK_PIN, FPIOA_DRIVING_15);

}

ES_S32 es_hal_lcd_init(ES_VOID)
{
    st7789_gpio_init();
    es_tft_hard_init(15);
    init_seq();
    printk("init lcd st7789\r\n");

    es_hal_lcd_on();
    es_hal_lcd_set_direction(ES_LCD_DIR_PARAM);

    return ES_RET_SUCCESS;
}


ES_S32 es_hal_lcd_set_area(ES_U16 x, ES_U16 y, ES_U16 w, ES_U16 h)
{
    uint8_t data[4] = {0};

    data[0] = (uint8_t)(x >> 8);
    data[1] = (uint8_t)(x);
    data[2] = (uint8_t)((x + w - 1) >> 8);
    data[3] = (uint8_t)((x + w - 1));
    es_tft_write_command(HORIZONTAL_ADDRESS_SET);
    // es_tft_write_command(VERTICAL_ADDRESS_SET);
    es_tft_write_byte(data, 4);

    data[0] = (uint8_t)(y >> 8);
    data[1] = (uint8_t)(y);
    data[2] = (uint8_t)((y + h - 1) >> 8);
    data[3] = (uint8_t)((y + h - 1));
    es_tft_write_command(VERTICAL_ADDRESS_SET);
    // es_tft_write_command(HORIZONTAL_ADDRESS_SET);
    es_tft_write_byte(data, 4);

    es_tft_write_command(MEMORY_WRITE);

    return ES_RET_SUCCESS;
}

ES_S32 es_hal_lcd_clear(ES_U16 rgb565_color)
{
    uint32_t data = ((uint32_t)rgb565_color << 16) | (uint32_t)rgb565_color;

    es_hal_lcd_set_area(0, 0, lcd_width, lcd_height);
    es_tft_fill_data(&data, lcd_width * lcd_height / 2);
    return ES_RET_SUCCESS;
}

ES_S32 es_hal_lcd_draw_picture(ES_U16 x, ES_U16 y, ES_U16 w, ES_U16 h, const ES_BYTE *imamge)
{
    es_hal_lcd_set_area(x, y, w, h);
    es_tft_write_word((uint32_t*)imamge, w * h / 2, 0);
    return ES_RET_SUCCESS;
}

ES_U32 es_hal_lcd_get_width(ES_VOID)
{
    return lcd_width;
}

ES_U32 es_hal_lcd_get_height(ES_VOID)
{
    return lcd_height;
}

ES_S32 es_hal_lcd_on(ES_VOID)
{
    gpiohs_set_pin(ES_LCD_BL_HS_NUM, 0);
    return ES_RET_SUCCESS;
}

ES_S32 es_hal_lcd_standby(ES_VOID)
{
    fpioa_set_function(ES_LCD_BL_PIN, FUNC_TIMER0_TOGGLE1 + ES_LCD_PWDM_CHN_BL);
    pwm_init(ES_LCD_PWM_DEV_BL);
    pwm_set_frequency(ES_LCD_PWM_DEV_BL, ES_LCD_PWDM_CHN_BL, 1000, 0.2);
    pwm_set_enable(ES_LCD_PWM_DEV_BL, ES_LCD_PWDM_CHN_BL, 1);
    return ES_RET_SUCCESS;
}

#endif
