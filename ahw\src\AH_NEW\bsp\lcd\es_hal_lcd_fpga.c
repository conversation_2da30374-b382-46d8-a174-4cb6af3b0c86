#include "es_inc.h"

#if (ES_LCD_DRIVER_FPGA == ES_LCD_DRIVER_TYPE)
#include "facelib_inc.h"

#define LCD_WRITE_REG                   (0x80)

#define LCD_SCALE_DISABLE				(0x00)
#define LCD_SCALE_ENABLE				(0x01)

#define LCD_SCALE_NONE					(0x00)
#define LCD_SCALE_2X2					(0x01)
#define LCD_SCALE_3X3					(0x02)
#define LCD_SCALE_4X4					(0x03)

#define LCD_SCALE_X						(0x00)
#define LCD_SCALE_Y						(0x01)

#define LCD_PHASE_PCLK                  (0x00)
#define LCD_PHASE_HSYNC                 (0x01)
#define LCD_PHASE_VSYNC                 (0x02)
#define LCD_PHASE_DE                    (0x03)

#define LCD_ADDR_VBP					(0x00)
#define LCD_ADDR_H						(0x01)
#define LCD_ADDR_VFP					(0x02)
#define LCD_ADDR_HBP					(0x03)
#define LCD_ADDR_W						(0x04)
#define LCD_ADDR_HFP					(0x05)
#define LCD_ADDR_DIVCFG					(0x06)	//b7:0 dis,1 en; b6: 0x,1y; b[5:3]: mul part1; b[2:0]: mul part2
#define LCD_ADDR_POS					(0x07)
#define LCD_ADDR_PHASE                  (0x08)  //b0: pclk b1: hsync b2:vsync b3:de
#define LCD_ADDR_START                  (0x09)
#define LCD_ADDR_INITDONE               (0x0A)

#define GPIOHS_OUT_HIGH(io) (*(volatile uint32_t *)0x3800100CU) |= (1 << (io))
#define GPIOHS_OUT_LOWX(io) (*(volatile uint32_t *)0x3800100CU) &= ~(1 << (io))
#define GET_GPIOHS_VALX(io) (((*(volatile uint32_t *)0x38001000U) >> (io)) & 1)


// static mf_sipeed_lcd_para_t sipeed_lcd_para_5p0_ips = {
// 	.vbp = 30, .h = 856/4, .vfp = 12,
// 	.hbp = 252,.w = 484/4, .hfp = 40,
// 	.enable=1, .div_xy = 1,.div_pos = 640/4, //y方向分割到640
// 	.lcd_fpga_mula_M=1, .lcd_fpga_mula_N = 2, //2倍缩放
// 	.mulb_M=1, .mulb_N = 1, //不缩放
// 	.phase_de=1, .phase_vsync=1, .phase_hsync=1, .phase_pclk=0,
// 	.refresh_time = 25, .spi_speed = 100
// };
#define lcd_fpga_mula_M                     (1)
#define lcd_fpga_mula_N                     (2)
#define lcd_fpga_mulb_M                     (1)
#define lcd_fpga_mulb_N                     (1)
#define lcd_fpga_vbp                        (30)
#define lcd_fpga_h                          (856/4)
#define lcd_fpga_vfp                        (12)
#define lcd_fpga_hbp                        (252)
#define lcd_fpga_w                          (484/4)
#define lcd_fpga_hfp                        (40)
#define lcd_fpga_enable                     (1)
#define lcd_fpga_div_xy                     (1)
#define lcd_fpga_div_pos                    (640/4)
#define lcd_fpga_phase_de                   (1)
#define lcd_fpga_phase_vsync                (1)
#define lcd_fpga_phase_hsync                (1)
#define lcd_fpga_phase_pclk                 (0)
#define lcd_fpga_refresh_time               (25)
#define lcd_fpga_spi_speed                  (100)


#define ES_LCD_FPGA_SPI_FREQ              (100000000)
#define ES_LCD_FPGA_SPI_DEV               (SPI_DEVICE_0)
#define ES_LCD_FPGA_SPI_SS                (SPI_CHIP_SELECT_3)
#define ES_LCD_INIT_LINE                    (2)

#define CONFIG_LCD_SPI_PIN_RST              (23)
#define CONFIG_LCD_SPI_PIN_MOSI             (22)
#define CONFIG_LCD_SPI_PIN_SCLK             (21)
#define CONFIG_LCD_SPI_PIN_CS               (20)

/* LCD 3-Wire SPI */
#define CONFIG_LCD_SPI_GPIOHS_RST           (18)
#define CONFIG_LCD_SPI_GPIOHS_MOSI          (19)
#define CONFIG_LCD_SPI_GPIOHS_SCLK          (20)
#define CONFIG_LCD_SPI_GPIOHS_CS            (21)

static uint16_t fpga_lcd_w = 0;
static uint16_t fpga_lcd_h = 0;
static int32_t PageCount = 0;
static uint8_t lcd_firstinit = 0;
static uint8_t dis_flag = 0;
static uint8_t *disp_buf = NULL;
// static uint8_t *disp_banner_buf = NULL;
static void (*lcd_irq_rs_sync)(void);


extern void gpiohs_irq_disable(size_t pin);

static uint8_t mf_spi_rw_low_spd(uint16_t data)
{
    for(uint8_t i = 0; i < 9; i++)
    {
        GPIOHS_OUT_LOWX(CONFIG_LCD_SPI_GPIOHS_SCLK);
        if(data & 0x100)
        {
            GPIOHS_OUT_HIGH(CONFIG_LCD_SPI_GPIOHS_MOSI);
        } else
        {
            GPIOHS_OUT_LOWX(CONFIG_LCD_SPI_GPIOHS_MOSI);
        }
        data <<= 1;
        usleep(5);
        GPIOHS_OUT_HIGH(CONFIG_LCD_SPI_GPIOHS_SCLK);
        usleep(5);
    }
    return 0;
}

// static void WriteComm_16(uint16_t cmd)
// {
//     uint16_t tmp = 0;
//     uint8_t dat[2];

//     dat[0] = (uint8_t)((cmd >> 8) & 0xFF);
//     dat[1] = (uint8_t)((cmd)&0xFF);

//     GPIOHS_OUT_LOWX(CONFIG_LCD_SPI_GPIOHS_CS);
//     tmp = (0 << 8) | dat[0];
//     mf_spi_rw_low_spd(tmp);

//     tmp = (0 << 8) | dat[1];
//     mf_spi_rw_low_spd(tmp);

//     GPIOHS_OUT_HIGH(CONFIG_LCD_SPI_GPIOHS_CS);
// }

static void WriteComm(uint8_t cmd)
{
    uint16_t tmp = 0;

    GPIOHS_OUT_LOWX(CONFIG_LCD_SPI_GPIOHS_CS);
    tmp = (0 << 8) | cmd;
    mf_spi_rw_low_spd(tmp);

    GPIOHS_OUT_HIGH(CONFIG_LCD_SPI_GPIOHS_CS);
}

static void WriteData(uint8_t dat)
{
    uint16_t tmp = 0;

    GPIOHS_OUT_LOWX(CONFIG_LCD_SPI_GPIOHS_CS);
    tmp = (1 << 8) | dat;
    mf_spi_rw_low_spd(tmp);

    GPIOHS_OUT_HIGH(CONFIG_LCD_SPI_GPIOHS_CS);
}

static void lcd_st7701s_480_854_true_ips_init(void)
{
    printk("lcd is 480x854, st7701s, true IPS, BL 12V\r\n");
    WriteComm(0x11);
    msleep(120);

    WriteComm(0xFF);
    WriteData(0x77);
    WriteData(0x01);
    WriteData(0x00);
    WriteData(0x00);
    WriteData(0x13);

    WriteComm(0xEF);
    WriteData(0x08);

    WriteComm(0xFF);
    WriteData(0x77);
    WriteData(0x01);
    WriteData(0x00);
    WriteData(0x00);
    WriteData(0x10);

    WriteComm(0xC0);
    WriteData(0xE9);
    WriteData(0x03);

    WriteComm(0xC1);
    WriteData(0x09);
    WriteData(0x02);

    WriteComm(0xC2);
    WriteData(0x01);
    WriteData(0x08);

    WriteComm(0xCC);
    WriteData(0x10);

    WriteComm(0xB0);
    WriteData(0x00);
    WriteData(0x0B);
    WriteData(0x10);
    WriteData(0x0D);
    WriteData(0x11);
    WriteData(0x06);
    WriteData(0x01);
    WriteData(0x08);
    WriteData(0x08);
    WriteData(0x1D);
    WriteData(0x04);
    WriteData(0x10);
    WriteData(0x10);
    WriteData(0x27);
    WriteData(0x30);
    WriteData(0x19);

    WriteComm(0xB1);
    WriteData(0x00);
    WriteData(0x0B);
    WriteData(0x14);
    WriteData(0x0C);
    WriteData(0x11);
    WriteData(0x05);
    WriteData(0x03);
    WriteData(0x08);
    WriteData(0x08);
    WriteData(0x20);
    WriteData(0x04);
    WriteData(0x13);
    WriteData(0x10);
    WriteData(0x28);
    WriteData(0x30);
    WriteData(0x19);

    WriteComm(0xFF);
    WriteData(0x77);
    WriteData(0x01);
    WriteData(0x00);
    WriteData(0x00);
    WriteData(0x11);

    WriteComm(0xB0);
    WriteData(0x35);

    WriteComm(0xB1);
    WriteData(0x31);

    WriteComm(0xB2);
    WriteData(0x82);

    WriteComm(0xB3);
    WriteData(0x80);

    WriteComm(0xB5);
    WriteData(0x4E);

    WriteComm(0xB7);
    WriteData(0x85);

    WriteComm(0xB8);
    WriteData(0x20);

    WriteComm(0xB9);
    WriteData(0x10);

    WriteComm(0xC1);
    WriteData(0x78);

    WriteComm(0xC2);
    WriteData(0x78);

    WriteComm(0xD0);
    WriteData(0x88);
    msleep(100);

    WriteComm(0xE0);
    WriteData(0x00);
    WriteData(0x00);
    WriteData(0x02);
    WriteComm(0xE1);
    WriteData(0x05);
    WriteData(0xA0);
    WriteData(0x00);
    WriteData(0x00);
    WriteData(0x04);
    WriteData(0xA0);
    WriteData(0x00);
    WriteData(0x00);
    WriteData(0x00);
    WriteData(0x20);
    WriteData(0x20);
    WriteComm(0xE2);
    WriteData(0x00);
    WriteData(0x00);
    WriteData(0x00);
    WriteData(0x00);
    WriteData(0x00);
    WriteData(0x00);
    WriteData(0x00);
    WriteData(0x00);
    WriteData(0x00);
    WriteData(0x00);
    WriteData(0x00);
    WriteData(0x00);
    WriteData(0x00);
    WriteComm(0xE3);
    WriteData(0x00);
    WriteData(0x00);
    WriteData(0x33);
    WriteData(0x00);
    WriteComm(0xE4);
    WriteData(0x22);
    WriteData(0x00);
    WriteComm(0xE5);
    WriteData(0x08);
    WriteData(0x34);
    WriteData(0xA0);
    WriteData(0xA0);
    WriteData(0x06);
    WriteData(0x34);
    WriteData(0xA0);
    WriteData(0xA0);
    WriteData(0x00);
    WriteData(0x00);
    WriteData(0x00);
    WriteData(0x00);
    WriteData(0x00);
    WriteData(0x00);
    WriteData(0x00);
    WriteData(0x00);
    WriteComm(0xE6);
    WriteData(0x00);
    WriteData(0x00);
    WriteData(0x33);
    WriteData(0x00);
    WriteComm(0xE7);
    WriteData(0x22);
    WriteData(0x00);
    WriteComm(0xE8);
    WriteData(0x07);
    WriteData(0x34);
    WriteData(0xA0);
    WriteData(0xA0);
    WriteData(0x05);
    WriteData(0x34);
    WriteData(0xA0);
    WriteData(0xA0);
    WriteData(0x00);
    WriteData(0x00);
    WriteData(0x00);
    WriteData(0x00);
    WriteData(0x00);
    WriteData(0x00);
    WriteData(0x00);
    WriteData(0x00);
    WriteComm(0xEB);
    WriteData(0x02);
    WriteData(0x00);
    WriteData(0x10);
    WriteData(0x10);
    WriteData(0x00);
    WriteData(0x00);
    WriteData(0x00);
    WriteComm(0xEC);
    WriteData(0x02);
    WriteData(0x00);
    WriteComm(0xED);
    WriteData(0xAA);
    WriteData(0x54);
    WriteData(0x0B);
    WriteData(0xBF);
    WriteData(0xFF);
    WriteData(0xFF);
    WriteData(0xFF);
    WriteData(0xFF);
    WriteData(0xFF);
    WriteData(0xFF);
    WriteData(0xFF);
    WriteData(0xFF);
    WriteData(0xFB);
    WriteData(0xB0);
    WriteData(0x45);
    WriteData(0xAA);
    WriteComm(0xFF);
    WriteData(0x77);
    WriteData(0x01);
    WriteData(0x00);
    WriteData(0x00);
    WriteData(0x00);
    WriteComm(0x11);
    msleep(120);
    WriteComm(0x29);
    msleep(20);
}

static void lcd_init_over_spi(void)
{
	//Init SPI IO
    fpioa_set_function(CONFIG_LCD_SPI_PIN_CS, FUNC_GPIOHS0 + CONFIG_LCD_SPI_GPIOHS_CS);
    fpioa_set_function(CONFIG_LCD_SPI_PIN_MOSI, FUNC_GPIOHS0 + CONFIG_LCD_SPI_GPIOHS_MOSI);
    fpioa_set_function(CONFIG_LCD_SPI_PIN_SCLK, FUNC_GPIOHS0 + CONFIG_LCD_SPI_GPIOHS_SCLK);
    fpioa_set_function(CONFIG_LCD_SPI_PIN_RST, FUNC_GPIOHS0 + CONFIG_LCD_SPI_GPIOHS_RST);
	
    //cs
    gpiohs_set_drive_mode(CONFIG_LCD_SPI_GPIOHS_CS, GPIO_DM_OUTPUT);
    gpiohs_set_pin(CONFIG_LCD_SPI_GPIOHS_CS, 1);

    //sclk
    gpiohs_set_drive_mode(CONFIG_LCD_SPI_GPIOHS_SCLK, GPIO_DM_OUTPUT);
    gpiohs_set_pin(CONFIG_LCD_SPI_GPIOHS_SCLK, 1);

    //mosi
    gpiohs_set_drive_mode(CONFIG_LCD_SPI_GPIOHS_MOSI, GPIO_DM_OUTPUT);
    gpiohs_set_pin(CONFIG_LCD_SPI_GPIOHS_MOSI, 1);

    //rst
    gpiohs_set_drive_mode(CONFIG_LCD_SPI_GPIOHS_RST, GPIO_DM_OUTPUT);
    // gpiohs_set_pin(CONFIG_LCD_SPI_GPIOHS_RST, 1);

    GPIOHS_OUT_LOWX(CONFIG_LCD_SPI_GPIOHS_RST);
    msleep(50);
    GPIOHS_OUT_HIGH(CONFIG_LCD_SPI_GPIOHS_RST);

	lcd_st7701s_480_854_true_ips_init();

	return;
}

static void lcd_fpage_send_cmd(uint8_t CMDData)
{
    gpiohs_set_pin(ES_LCD_DCX_HS_NUM, GPIO_PV_HIGH);

    mf_spi_init(ES_LCD_FPGA_SPI_DEV, SPI_WORK_MODE_0, SPI_FF_OCTAL, 8, 0);
    mf_spi_init_non_standard(ES_LCD_FPGA_SPI_DEV, 8 /*instrction length*/, 0 /*address length*/, 0 /*wait cycles*/,
                          SPI_AITM_AS_FRAME_FORMAT /*spi address trans mode*/);
    mf_spi_send_data_normal(ES_LCD_FPGA_SPI_DEV, ES_LCD_FPGA_SPI_SS, (uint8_t *)(&CMDData), 1);
}

static void lcd_fpga_send_cmd(uint8_t CMDData)
{
    gpiohs_set_pin(ES_LCD_DCX_HS_NUM, GPIO_PV_HIGH);

    mf_spi_init(ES_LCD_FPGA_SPI_DEV, SPI_WORK_MODE_0, SPI_FF_OCTAL, 8, 0);
    mf_spi_init_non_standard(ES_LCD_FPGA_SPI_DEV, 8 /*instrction length*/, 0 /*address length*/, 0 /*wait cycles*/,
                          SPI_AITM_AS_FRAME_FORMAT /*spi address trans mode*/);
    mf_spi_send_data_normal(ES_LCD_FPGA_SPI_DEV, ES_LCD_FPGA_SPI_SS, (uint8_t *)(&CMDData), 1);
}


static void lcd_fpga_send_dat(uint8_t *DataBuf, uint32_t Length)
{
    sipeed_mf_spi_send_data_dma(ES_LCD_FPGA_SPI_DEV, ES_LCD_FPGA_SPI_SS, DMAC_CHANNEL2,\
	(const uint8_t *)_IOMEM_PADDR(DataBuf), Length);
}

static uint8_t cal_div_reg(uint8_t enable, uint8_t div_xy, uint8_t mula_M, uint8_t mula_N, uint8_t mulb_M, uint8_t mulb_N)
{
    if(mula_M > 2 || mulb_M > 2){
        printk("mul_M should = 1 or 2\r\n");
    }
    mula_N -= (mula_M - 1);
    mulb_N -= (mulb_M - 1);
    return (enable << 7) | (div_xy << 6) | ((mula_M - 1) << 5) | ((mula_N - 1) << 3) | ((mulb_M - 1) << 2) | ((mulb_N - 1) << 0);
}

static void lcd_fpage_init_seq(void)
{
    lcd_fpage_send_cmd(LCD_WRITE_REG | LCD_ADDR_VBP);
    lcd_fpage_send_cmd((lcd_fpga_vbp + 2));
    lcd_fpage_send_cmd(LCD_WRITE_REG | LCD_ADDR_H);
    lcd_fpage_send_cmd(lcd_fpga_h); //多了2行
    lcd_fpage_send_cmd(LCD_WRITE_REG | LCD_ADDR_VFP);
    lcd_fpage_send_cmd(lcd_fpga_vfp);
    lcd_fpage_send_cmd(LCD_WRITE_REG | LCD_ADDR_HBP);
    lcd_fpage_send_cmd(lcd_fpga_hbp);
    lcd_fpage_send_cmd(LCD_WRITE_REG | LCD_ADDR_W);
    lcd_fpage_send_cmd(lcd_fpga_w);
    lcd_fpage_send_cmd(LCD_WRITE_REG | LCD_ADDR_HFP);
    lcd_fpage_send_cmd(lcd_fpga_hfp);

    //b7:0 dis,1 en; b6: 0x,1y; b[5:3]: mul part1; b[2:0]: mul part2
    lcd_fpage_send_cmd(LCD_WRITE_REG | LCD_ADDR_DIVCFG);
    // lcd_fpage_send_cmd((LCD_SCALE_ENABLE << 7) | (LCD_SCALE_Y << 6) | (LCD_SCALE_NONE << 3) | (LCD_SCALE_NONE << 0));
    lcd_fpage_send_cmd(cal_div_reg(lcd_fpga_enable, lcd_fpga_div_xy, \
		lcd_fpga_mula_M, lcd_fpga_mula_N, lcd_fpga_mulb_M, lcd_fpga_mulb_N)); 

    lcd_fpage_send_cmd(LCD_WRITE_REG | LCD_ADDR_POS);
    lcd_fpage_send_cmd(lcd_fpga_div_pos);

    lcd_fpage_send_cmd(LCD_WRITE_REG | LCD_ADDR_PHASE);
    lcd_fpage_send_cmd((lcd_fpga_phase_de << 3) | (lcd_fpga_phase_vsync << 2) | (lcd_fpga_phase_hsync << 1) | (lcd_fpga_phase_pclk << 0)); 

    lcd_fpage_send_cmd(LCD_WRITE_REG | LCD_ADDR_INITDONE);
    lcd_fpage_send_cmd(0x01);
}

static void lcd_480854_irq_rs_sync(void)
{
    static uint64_t last_time = 0;
    uint64_t now_time;

    int32_t tmp = PageCount;
    PageCount -=  lcd_fpga_vbp;

    if(lcd_firstinit == 1){
        now_time = sysctl_get_time_us();
        if (last_time && ((now_time -last_time) > 10 *1000)) {
            printk("xxxxxxxxxxxxx lcd_480854_irq_rs_sync timeout.\r\n");
            sysctl_reset(SYSCTL_RESET_SOC);
        }
        last_time = now_time;
    }

    if(PageCount >= 0)
    {
        if(PageCount < 640)
        {
            dis_flag = 1;
            // disp_buf[240*320]
            // lcd_fpga_send_dat(&disp_buf[(PageCount / 2) * 240 * 2], 240 * 2+8);
            // PageCount = 0, lcd_fpga_send_dat(&disp_buf[0], 240 * 2+8);
            // PageCount = 1, lcd_fpga_send_dat(&disp_buf[0], 240 * 2+8);
            // PageCount = 2, lcd_fpga_send_dat(&disp_buf[240*2], 240 * 2+8);
            // PageCount = 3, lcd_fpga_send_dat(&disp_buf[240*2], 240 * 2+8);
            lcd_fpga_send_dat(&disp_buf[(PageCount / 2) * fpga_lcd_w * 2], fpga_lcd_w * 2+8);
        } else if(PageCount < 854)
        {
            dis_flag = 1;
            if (mf_brd.cfg.lcdswapline == 0) {
                uint16_t tt = (PageCount - 640);
                tt = (tt % 2) ? (tt - 1) : (tt + 1);
                // lcd_fpga_send_dat(&disp_banner_buf[tt * 480 * 2], 480 * 2+8);
            } else {
                // lcd_fpga_send_dat(&disp_banner_buf[(PageCount - 640) * 480 * 2], 480 * 2 + 8);
            }
        } else if((PageCount >= (856 +  lcd_fpga_vfp - 1))) /* 初始化配置的是856 */
        {
            dis_flag = 0;
        }
    }

    PageCount = tmp;
    PageCount++;
    return;
}

static void lcd_sipeed_display(void)
{
    gpiohs_irq_disable(ES_LCD_DCX_HS_NUM);
    PageCount = ES_LCD_INIT_LINE;

    mf_spi_init(ES_LCD_FPGA_SPI_DEV, SPI_WORK_MODE_0, SPI_FF_OCTAL, 32, 0);
    lcd_fpga_send_cmd(LCD_WRITE_REG | LCD_ADDR_START);
    lcd_fpga_send_cmd(0x0d);

    mf_spi_init(ES_LCD_FPGA_SPI_DEV, SPI_WORK_MODE_0, SPI_FF_OCTAL, 32, 1);
    mf_spi_init_non_standard(ES_LCD_FPGA_SPI_DEV, 0, 32, 0, SPI_AITM_AS_FRAME_FORMAT);

    gpiohs_set_drive_mode(ES_LCD_DCX_HS_NUM, GPIO_DM_INPUT);
    gpiohs_set_pin_edge(ES_LCD_DCX_HS_NUM, GPIO_PE_RISING);

    gpiohs_set_irq(ES_LCD_DCX_HS_NUM, 3, lcd_irq_rs_sync);
	return;
}

static int timer_callback(void *ctx)
{
    dis_flag = 1;
    lcd_sipeed_display();
    return 0;
}

ES_S32 es_hal_lcd_init(ES_VOID)
{

#if lcd_fpga_div_xy
    //              480 * 1 / 2 = 240
    fpga_lcd_w = ES_LCD_WIDTH * lcd_fpga_mula_M / lcd_fpga_mula_N;
    //              (640/4) * 4 * 1 / 2 = 320
    fpga_lcd_h = lcd_fpga_div_pos*4 * lcd_fpga_mula_M / lcd_fpga_mula_N;
#else
    fpga_lcd_w = lcd_fpga_div_pos * lcd_fpga_mula_M / lcd_fpga_mula_N; 
    fpga_lcd_h = ES_LCD_HEIGHT * lcd_fpga_mula_M / lcd_fpga_mula_N;
#endif

    disp_buf = (uint8_t *)malloc(fpga_lcd_w * (fpga_lcd_h*2) * 2);
    if (ES_NULL == disp_buf) {
        return ES_RET_FAILURE;
    }
	
    /* LCD IO*/
    fpioa_set_function(ES_LCD_RST_PIN, FUNC_GPIOHS0 + ES_LCD_RST_HS_NUM);
    fpioa_set_function(ES_LCD_DCX_PIN, FUNC_GPIOHS0 + ES_LCD_DCX_HS_NUM);
    fpioa_set_function(ES_LCD_CS_PIN, FUNC_SPI0_SS3);
    fpioa_set_function(ES_LCD_SCK_PIN, FUNC_SPI0_SCLK);
	
	fpioa_set_io_driving(ES_LCD_RST_PIN, FPIOA_DRIVING_15);
	fpioa_set_io_driving(ES_LCD_DCX_PIN, FPIOA_DRIVING_15);
	fpioa_set_io_driving(ES_LCD_CS_PIN, FPIOA_DRIVING_15);
    fpioa_set_io_driving(ES_LCD_SCK_PIN, FPIOA_DRIVING_15);

    gpiohs_set_drive_mode(ES_LCD_DCX_HS_NUM, GPIO_DM_OUTPUT);
    gpiohs_set_pin(ES_LCD_DCX_HS_NUM, GPIO_PV_HIGH);

    gpiohs_set_drive_mode(ES_LCD_RST_HS_NUM, GPIO_DM_OUTPUT);
    // gpiohs_set_pin(ES_LCD_RST_HS_NUM, GPIO_PV_LOW);
    // msleep(100);
    gpiohs_set_pin(ES_LCD_RST_HS_NUM, GPIO_PV_HIGH);

    mf_spi_init(ES_LCD_FPGA_SPI_DEV, SPI_WORK_MODE_0, SPI_FF_OCTAL, 32, 0);
    mf_spi_set_clk_rate(ES_LCD_FPGA_SPI_DEV, lcd_fpga_spi_speed*1000000);

    //480x272:40/50M测试可用,搭配100M，9M时钟
    //800x480:50/60/70M 配166~125/25M 或 40M 配111/100/83 :16.66
    //似乎FIFO最高速率到不了200M，只能使用6分频166M
	lcd_fpage_init_seq();

    lcd_init_over_spi();

    timer_init(1);
    timer_set_interval(1, 1, lcd_fpga_refresh_time * 1000 * 1000);
    //SPI传输时间(dis_flag=1)约20ms，需要留出10~20ms给缓冲区准备(dis_flag=0)
    //如果图像中断处理时间久，可以调慢FPGA端的像素时钟，但是注意不能比刷屏中断慢(否则就会垂直滚动画面)。
    //33M->18ms  25M->23ms  20M->29ms
	lcd_irq_rs_sync = lcd_480854_irq_rs_sync;

    timer_irq_register(1, 1, 0, 1, timer_callback, NULL); //4th pri
    timer_set_enable(1, 1, 1);
	
    return ES_RET_SUCCESS;
}

ES_S32 es_hal_lcd_deinit(ES_VOID)
{
    return ES_RET_SUCCESS;
}

ES_S32 es_hal_lcd_set_area(ES_U16 x, ES_U16 y, ES_U16 w, ES_U16 h)
{
    return ES_RET_SUCCESS;
}

ES_S32 es_hal_lcd_clear(ES_U16 rgb565_color)
{
    for(int i = 0; i < fpga_lcd_h*2; i++)
        for(int j = 0; j < fpga_lcd_w; j++)
            disp_buf[i * fpga_lcd_w + j] = rgb565_color;

    return ES_RET_SUCCESS;
}

ES_S32 es_hal_lcd_draw_picture(ES_U16 x, ES_U16 y, ES_U16 w, ES_U16 h, const ES_BYTE *img)
{
    // x:0, y:0, w:240, h:10 // y * 240 *
    // x:0, y:10, w:240, h:10
    // x:0, y:20, w:240, h:10
    // x:0, y:30, w:240, h:10
    // x:0, y:40, w:240, h:10
    // x:0, y:50, w:240, h:10
    // x:0, y:60, w:240, h:10
    // x:0, y:70, w:240, h:10
    // x:0, y:80, w:240, h:10
    // x:0, y:90, w:240, h:10

    memcpy(disp_buf+(y*w*2), img, w*h*2);
    return ES_RET_SUCCESS;
}

ES_U32 es_hal_lcd_get_width(ES_VOID)
{
    return ES_LCD_WIDTH;
}

ES_U32 es_hal_lcd_get_height(ES_VOID)
{
    return ES_LCD_HEIGHT;
}

ES_S32 es_hal_lcd_on(ES_VOID)
{
    return ES_RET_SUCCESS;
}

ES_S32 es_hal_lcd_standby(ES_VOID)
{
    return ES_RET_SUCCESS;
}

#endif
