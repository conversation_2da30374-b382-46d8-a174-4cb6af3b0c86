#include "es_inc.h"

ES_S32 es_hal_ir_init(ES_VOID)
{
    fpioa_set_function(ES_IR_PIN, FUNC_GPIOHS0 + ES_IR_HS_NUM);
    gpiohs_set_drive_mode(ES_IR_HS_NUM, GPIO_DM_OUTPUT);
    gpiohs_set_pin(ES_IR_HS_NUM, !ES_IR_IO_OPEN_VAL);

    return ES_RET_SUCCESS;
}

ES_S32 es_hal_ir_open(ES_VOID)
{
    // printk("ir open\r\n");
    gpiohs_set_pin(ES_IR_HS_NUM, ES_IR_IO_OPEN_VAL);
    return ES_RET_SUCCESS;
}

ES_S32 es_hal_ir_close(ES_VOID)
{
    // printk("ir close\r\n");
#if ES_IR_IO_OPEN_VAL
    gpiohs_set_pin(ES_IR_HS_NUM, 0);
#else
    gpiohs_set_pin(ES_IR_HS_NUM, 1);
#endif
    return ES_RET_SUCCESS;
}