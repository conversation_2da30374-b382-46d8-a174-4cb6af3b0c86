/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_serv_proto.c
** bef: define the interface for server protocol.
** auth: lines<<EMAIL>>
** create on 2022.01.11 
*/

#include "es_inc.h"
#include "facelib_inc.h"

#define ES_SERV_PROTO_DEBUG
#ifdef ES_SERV_PROTO_DEBUG
#define es_serv_proto_debug es_log_info
#define es_serv_proto_error es_log_error
#else
#define es_serv_proto_debug(...)
#define es_serv_proto_error(...)
#endif

#define ES_PROTO_JSON_KEY_CMD                   ("cmd")
#define ES_PROTO_JSON_KEY_RET                   ("ret")
#define ES_PROTO_JSON_KEY_CARD                  ("card")
#define ES_PROTO_JSON_KEY_VALUE                 ("value")
#define ES_PROTO_JSON_KEY_DES                   ("des")
#define ES_PROTO_JSON_KEY_START_TIME            ("start_time")
#define ES_PROTO_JSON_KEY_END_TIME              ("end_time")

#define ES_PROTO_JSON_VAL_GET_POWER             ("get_power")
#define ES_PROTO_JSON_VAL_DEL_USER              ("del_user")
#define ES_PROTO_JSON_VAL_FREEZE_USER           ("freeze_user")
#define ES_PROTO_JSON_VAL_BLE_PAYLOAD           ("ble_payload")
#define ES_PROTO_JSON_VAL_CMD_SNAPSHOT          ("snapshot")

#define ES_PROTO_JSON_VAL_CFG_WGD               ("wdg")
#define ES_PROTO_JSON_VAL_CFG_NFC               ("nfc")
#define ES_PROTO_JSON_VAL_CFG_BLE               ("ble")
#define ES_PROTO_JSON_VAL_CFG_433               ("433")
#define ES_PROTO_JSON_KEY_VAL                   ("val")


static es_serv_proto_msg_event_cb event_cb_list[ES_SERV_MSG_EVENT_CB_COUNT] = {ES_NULL};

static ES_S32 es_serv_proto_play_voice_by_queue(es_voice_type_t type)
{
    es_task_param_t task_param;

    task_param.type = ES_TASK_VOICE_PLAY;
    task_param.param = (ES_VOID *)type;
    task_param.timeout = 0;
    return es_task_queue_push_wait(&task_param); 
}

static ES_S32 es_serv_proto_parse_car_info(const cJSON *msg)
{
    cJSON *tmp = ES_NULL;
    ES_S32 ret = ES_RET_SUCCESS;
    es_task_param_t task_param;

    tmp = cJSON_GetObjectItem_Type(msg, "car_num", cJSON_String);
    if(ES_NULL == tmp) {
        es_serv_proto_debug("get \'car_num\' failed");
        ret = ES_RET_FAILURE;
        goto FUNC_END;
    }
    // strncpy((char *)mf_brd.cfg.car_num, tmp->valuestring, ES_CAR_NUM_LEN);

    tmp = cJSON_GetObjectItem_Type(msg, "expire", cJSON_Number);
    if (ES_NULL == tmp) {
        es_serv_proto_debug("get \'car_num\' failed");
        ret = ES_RET_FAILURE;
        goto FUNC_END;
    }
    mf_brd.cfg.car_expire = tmp->valueint;


    task_param.type = ES_TASK_SAVE_BRD_CFG;
    task_param.param = ES_NULL;
    task_param.timeout = 0;
    es_task_queue_push_wait(&task_param);

    tmp = cJSON_GetObjectItem_Type(msg, "voice", cJSON_Number);
    if (ES_NULL == tmp) {
        es_serv_proto_debug("get \'voice\' failed");
        ret = ES_RET_FAILURE;
        goto FUNC_END;
    }
    if (1 == tmp->valueint) {
        es_serv_proto_play_voice_by_queue(ES_VOICE_08_SET_INFO_OK);
    }

FUNC_END:
    return ret;
}


static ES_S32 es_serv_proto_parse_car_cfg_resp(const cJSON *msg)
{
    cJSON *tmp = ES_NULL;
    es_task_param_t task_param;

    tmp = cJSON_GetObjectItem_Type(msg, "bind", cJSON_Number);
    if (ES_NULL != tmp) {
        if (0 == tmp->valueint) {
            // unbind device by server
            if (0 == mf_brd.cfg.factory_flag) {
                task_param.type = ES_TASK_DEL_FACEDB_ALL;
                task_param.param = ES_NULL;
                task_param.timeout = 0;
                es_task_queue_push_wait(&task_param);

                task_param.type = ES_TASK_RESET_DEVICE;
                task_param.param = ES_NULL;
                task_param.timeout = 0;
                return es_task_queue_push_wait(&task_param);
            }
            return ES_RET_FAILURE;
        } else {
            // bing device by server
            // to do active
            if (1 == mf_brd.cfg.factory_flag) {
                task_param.type = ES_TASK_ACTIVE_DEVICE;
                task_param.param = ES_NULL;
                task_param.timeout = 0;
                return es_task_queue_push_wait(&task_param);
            }
        }
    }

    tmp = cJSON_GetObjectItem_Type(msg, "md5", cJSON_String);
    if (tmp != ES_NULL) {
        es_ota_dl_set_md5((const ES_CHAR *)tmp->valuestring);
    }

    tmp = cJSON_GetObjectItem_Type(msg, "url", cJSON_String);
    if (tmp != ES_NULL) {
        es_ota_dl_set_url((const ES_CHAR *)tmp->valuestring);
    }

    tmp = cJSON_GetObjectItem_Type(msg, "fmver", cJSON_Number);
    if (tmp != ES_NULL) {
        es_ota_dl_set_version_with_number(tmp->valueint);
    }

    return ES_RET_SUCCESS;
}

static ES_S32 es_serv_proto_parse_ctl_lock(const cJSON *msg)
{
    cJSON *tmp = ES_NULL;
    ES_S32 ret = ES_RET_SUCCESS;
    es_task_param_t task_param;

    tmp = cJSON_GetObjectItem_Type(msg, "expire", cJSON_Number);
    if (ES_NULL == tmp) {
        ret = ES_RET_FAILURE;
        goto FUNC_END;
    }
    mf_brd.cfg.car_expire = tmp->valueint;


    tmp = cJSON_GetObjectItem_Type(msg, "lock", cJSON_Number);
    if (ES_NULL == tmp) {
        ret = ES_RET_FAILURE;
        goto FUNC_END;
    }
    mf_brd.cfg.lock = tmp->valueint;

    task_param.type = ES_TASK_SAVE_BRD_CFG;
    task_param.param = ES_NULL;
    task_param.timeout = 0;
    es_task_queue_push_wait(&task_param);

    tmp = cJSON_GetObjectItem_Type(msg, "voice", cJSON_Number);
    if (ES_NULL == tmp) {
        ret = ES_RET_FAILURE;
        goto FUNC_END;
    }
    if (0 == tmp->valueint) {
        es_serv_proto_play_voice_by_queue(ES_VOICE_13_CAR_EXPIRE_AFTER_MONTH);
    } else if (1 == tmp->valueint) {
        if (1== mf_brd.cfg.lock) {
            es_serv_proto_play_voice_by_queue(ES_VOICE_11_LOCK_CAR_OK);
        } else if (2 == mf_brd.cfg.lock) {
            es_serv_proto_play_voice_by_queue(ES_VOICE_14_EXPIRE_AND_LOCKED);
        }
    } else if (2 == tmp->valueint) {
        es_serv_proto_play_voice_by_queue(ES_VOICE_10_UNLOCK_CAR_OK);
    }

FUNC_END:
    return ret;
}



static ES_S32 serv_proto_sync_time(const ES_CHAR *str_time)
{
    ES_U32 timestamp = 0;
    es_time_t time;

    timestamp = (ES_U32)es_atoi(str_time);    
    es_memset(&time, 0x00, sizeof(time));
    time.timestamp = timestamp;
    es_serv_proto_debug("sync time:%d", timestamp);
    return es_time_sync(&time);
}

// static ES_S32 serv_proto_parse_config(const cJSON *msg)
// {
//     cJSON *tmp = ES_NULL;
//     es_task_param_t task_param;

//     tmp = cJSON_GetObjectItem_Type(msg, "bind", cJSON_Number);
//     if (ES_NULL == tmp) {
//         return ES_RET_FAILURE;
//     }

//     if (0 == tmp->valueint) {
//         // unbind device by server
//         if (0 == mf_brd.cfg.factory_flag) {
//             task_param.type = ES_TASK_DEL_FACEDB_ALL;
//             task_param.param = ES_NULL;
//             task_param.timeout = 0;
//             es_task_queue_push_wait(&task_param);

//             task_param.type = ES_TASK_RESET_DEVICE;
//             task_param.param = ES_NULL;
//             task_param.timeout = 0;
//             return es_task_queue_push_wait(&task_param);
//         }
//         return ES_RET_FAILURE;
//     } else {
//         // bing device by server
//         // to do active
//         if (1 == mf_brd.cfg.factory_flag) {
//             task_param.type = ES_TASK_ACTIVE_DEVICE;
//             task_param.param = ES_NULL;
//             task_param.timeout = 0;
//             return es_task_queue_push_wait(&task_param);
//         }
//     }

//     return ES_RET_SUCCESS;
// }

static ES_S32 serv_proto_parse_update_resource(const cJSON *msg)
{
    cJSON *tmp = ES_NULL;

    tmp = cJSON_GetObjectItem_Type(msg, "is_fm", cJSON_Number);
    if (ES_NULL == tmp) {
        return ES_RET_FAILURE;
    }

#if ES_OTA_DL_ENABLE
    // firmware file
    if ((tmp->valueint & 0x1) == 1) {
        tmp = cJSON_GetObjectItem_Type(msg, "url", cJSON_String);
        if (ES_NULL != tmp) {
            es_ota_dl_set_url(tmp->valuestring);
        }

        tmp = cJSON_GetObjectItem_Type(msg, "md5", cJSON_String);
        if (ES_NULL != tmp) {
            es_ota_dl_set_md5(tmp->valuestring);
        }

        tmp = cJSON_GetObjectItem_Type(msg, "version", cJSON_Number);
        if (ES_NULL != tmp) {
            es_ota_dl_set_version_with_number(tmp->valueint);
        }
    }
#endif
    return ES_RET_SUCCESS;
}

static ES_S32 serv_proto_parse_add_face_fea(const cJSON *msg)
{
    es_task_param_t task_param;
    cJSON *tmp = ES_NULL;

    task_param.type = ES_TASK_PARSE_MQTT_FACE_FEA;
    task_param.param = (ES_VOID *)msg;
    task_param.timeout = 0;
    es_task_queue_push_wait(&task_param);

    // tmp = cJSON_GetObjectItem_Type(msg, "expire", cJSON_Number);
    // if (ES_NULL != tmp) {
    //     mf_brd.cfg.car_expire = tmp->valueint;
    // }

    tmp = cJSON_GetObjectItem_Type(msg, "status", cJSON_Number);
    if (ES_NULL != tmp) {
        if (2 == tmp->valueint) {
            es_serv_proto_play_voice_by_queue(ES_VOICE_12_DRIVER_LICENSE_EXPIRE);
        }
    }

    tmp = cJSON_GetObjectItem_Type(msg, "voice", cJSON_Number);
    if (ES_NULL != tmp) {
        if (1 == tmp->valueint) {
            es_serv_proto_play_voice_by_queue(ES_VOICE_08_SET_INFO_OK);
        }
    }

    return ES_RET_SUCCESS;
}


static ES_S32 serv_proto_parse_del_face_fea(const cJSON *msg)
{
    es_task_param_t task_param;
    cJSON *tmp = ES_NULL;

    task_param.type = ES_TASK_PARSE_MQTT_FACE_FEA;
    task_param.param = (ES_VOID *)msg;
    task_param.timeout = 0;
    es_task_queue_push_wait(&task_param);

    tmp = cJSON_GetObjectItem_Type(msg, "voice", cJSON_Number);
    if (ES_NULL != tmp) {
        if (1 == tmp->valueint) {
            es_serv_proto_play_voice_by_queue(ES_VOICE_09_RESET_INFO_OK);
        } else if (2 == tmp->valueint) {
            es_serv_proto_play_voice_by_queue(ES_VOICE_09_RESET_INFO_OK);
        }
    }

    return ES_RET_SUCCESS;
}


static ES_S32 serv_proto_parse_update_face_fea(const cJSON *msg)
{
    es_task_param_t task_param;
    cJSON *tmp = ES_NULL;

    task_param.type = ES_TASK_PARSE_MQTT_FACE_FEA;
    task_param.param = (ES_VOID *)msg;
    task_param.timeout = 0;
    es_task_queue_push_wait(&task_param);

    tmp = cJSON_GetObjectItem_Type(msg, "voice", cJSON_Number);
    if (ES_NULL != tmp) {
        if (1 == tmp->valueint) {
            es_serv_proto_play_voice_by_queue(ES_VOICE_08_SET_INFO_OK);
        }
    }

    return ES_RET_SUCCESS;
}

static ES_S32 serv_proto_parse_image_capture(const cJSON *msg)
{
#if ES_CAPTURE_ENABLE
    cJSON *tmp = ES_NULL;

    tmp = cJSON_GetObjectItem_Type(msg, "open", cJSON_Number);
    if (ES_NULL == tmp) {
        return ES_RET_FAILURE;
    }

    if (0 == tmp->valueint) {
        // disable capture and return
        return es_captrue_close();
    }

    // start capture
    tmp = cJSON_GetObjectItem_Type(msg, "interval", cJSON_Number);
    if (ES_NULL == tmp) {
        return ES_RET_FAILURE;
    }

    return es_captrue_open(tmp->valueint);
#else
    return ES_RET_FAILURE;
#endif
}

static ES_S32 serv_proto_parse_dynamic_qr_code(const cJSON *msg)
{
    cJSON *tmp = ES_NULL;
    ES_U32 tmp_len = 0 ;
    ES_BYTE code_str[ES_FLASH_USER_QRCODE_LEN];
    ES_CHAR lte_mqtt_id[ES_MAC_COLON_STR_LEN+1] = {0};

    tmp = cJSON_GetObjectItem_Type(msg, "type", cJSON_Number);
    if (ES_NULL == tmp) {
        return ES_RET_FAILURE;
    }

    tmp = cJSON_GetObjectItem_Type(msg, "length", cJSON_Number);
    if (ES_NULL == tmp) {
        return ES_RET_FAILURE;
    }
    tmp_len = tmp->valueint;

    tmp = cJSON_GetObjectItem_Type(msg, "url", cJSON_String);
    if (ES_NULL == tmp) {
        return ES_RET_FAILURE;
    }

    if (tmp_len != strlen(tmp->valuestring)) {
        return ES_RET_FAILURE;
    }

    es_hal_lte_get_mqtt_id(lte_mqtt_id);
    es_snprintf((char *)code_str, ES_FLASH_USER_QRCODE_LEN, "%s%s", tmp->valuestring, lte_mqtt_id);

    return es_dev_cfg_update_qrcode((const ES_CHAR *)code_str);
}


static ES_S32 serv_proto_parse_play_voice(const cJSON *msg)
{
    cJSON *tmp = ES_NULL;

    tmp = cJSON_GetObjectItem_Type(msg, "voice", cJSON_Number);
    if (ES_NULL == tmp) {
        return ES_RET_FAILURE;
    }

    if (1 == tmp->valueint) {
        // play don't use phone
        es_serv_proto_play_voice_by_queue(ES_VOICE_17_DO_NOT_USE_PHONE);
    } else if (2 == tmp->valueint) {
        // play no smoking
        es_serv_proto_play_voice_by_queue(ES_VOICE_18_NO_SMOKING);
    } else if (3 == tmp->valueint) {
        // play fatigue driving
        es_serv_proto_play_voice_by_queue(ES_VOICE_19_FATIGUE_DRIVING);
    } else if (4 == tmp->valueint) {
        // play over speed
        es_serv_proto_play_voice_by_queue(ES_VOICE_21_OVER_SPEED);
    }

    return ES_RET_SUCCESS;
}


static ES_S32 serv_proto_parse_emergency_control(const cJSON *msg)
{
    cJSON *tmp = ES_NULL;
    es_task_param_t task_param;

    tmp = cJSON_GetObjectItem_Type(msg, "type", cJSON_Number);
    if (ES_NULL == tmp) {
        return ES_RET_FAILURE;
    }

    mf_brd.cfg.emergency = tmp->valueint;
    task_param.type = ES_TASK_SAVE_BRD_CFG;
    task_param.param = ES_NULL;
    task_param.timeout = 0;
    es_task_queue_push_wait(&task_param);

    tmp = cJSON_GetObjectItem_Type(msg, "voice", cJSON_Number);
    if (ES_NULL == tmp) {
        return ES_RET_FAILURE;
    }

    if (1 == tmp->valueint) {
        switch (mf_brd.cfg.emergency) {
            case 0:
                es_serv_proto_play_voice_by_queue(ES_VOICE_04_LOOK_AND_CHECK);
                break;
            case 1:
                es_serv_proto_play_voice_by_queue(ES_VOICE_01_FACE_PASS);
                break;
        }
    } else if (2 == tmp->valueint) {
        es_serv_proto_debug("Don't play Voice");
    }

    return ES_RET_SUCCESS;
}



// static ES_S32 serv_proto_parse_ctrl_cmd(const cJSON *msg)
// {
//     cJSON *tmp = ES_NULL;

//     tmp = cJSON_GetObjectItem(msg, ES_PROTO_JSON_KEY_RET);
//     if (ES_NULL != tmp) {
//         return ES_RET_FAILURE;
//     }

//     tmp = cJSON_GetObjectItem_Type(msg, ES_PROTO_JSON_KEY_CMD, cJSON_String);
//     if (ES_NULL == tmp) {
//         return ES_RET_FAILURE;
//     }

// #if ES_BLE_MODULE_ENABLE
//     if (0 == es_strncmp(tmp->valuestring, ES_PROTO_JSON_VAL_BLE_PAYLOAD, es_strlen(ES_PROTO_JSON_VAL_BLE_PAYLOAD))) {
//         return es_ble_send_payload_from_serv(msg);
//     }
// #endif

//     return ES_RET_SUCCESS;
// }

// static ES_S32 serv_proto_parse_perip_cfg(const cJSON *msg)
// {
//     cJSON *tmp = ES_NULL;
// #if ES_BLE_MODULE_ENABLE
//     es_task_param_t task_param;
// #endif
//     tmp = cJSON_GetObjectItem(msg, ES_PROTO_JSON_KEY_RET);
//     if (ES_NULL != tmp) {
//         return ES_RET_FAILURE;
//     }

//     tmp = cJSON_GetObjectItem_Type(msg, ES_PROTO_JSON_KEY_CMD, cJSON_String);
//     if (ES_NULL == tmp) {
//         return ES_RET_FAILURE;
//     }

// #if ES_BLE_MODULE_ENABLE
//     if (0 == es_strncmp(tmp->valuestring, ES_PROTO_JSON_VAL_CFG_BLE, es_strlen(ES_PROTO_JSON_VAL_CFG_BLE))) {
//         tmp = cJSON_GetObjectItem_Type(msg, ES_PROTO_JSON_KEY_VAL, cJSON_Array);
//         if (ES_NULL == tmp) {
//             return ES_RET_FAILURE;
//         }
//         task_param.type = ES_TASK_PARSE_PERIP_BLE_CFG;
//         task_param.param = (ES_VOID *)tmp;
//         task_param.timeout = 0;
//         es_task_queue_push_wait(&task_param);
//     }
// #endif

//     return ES_RET_SUCCESS;
// }

static ES_S32 serv_proto_set_brd_cfg(const cJSON *msg)
{
    cJSON *tmp = ES_NULL;
    ES_BOOL have_new_cfg = ES_FALSE;
    es_task_param_t task_param;

    tmp = cJSON_GetObjectItem_Type(msg, "relay_open_s", cJSON_Number);
    if (ES_NULL != tmp) {
        if (tmp->valueint > 0 && tmp->valueint < 121) {
            mf_brd.cfg.relay.opent = (uint8_t)(tmp->valueint * 10);
            have_new_cfg = ES_TRUE;
        }
    }

    tmp = cJSON_GetObjectItem_Type(msg, "relay_open_vol", cJSON_Number);
    if (ES_NULL != tmp) {
            mf_brd.cfg.relay.pol = (uint8_t)(tmp->valueint & 0xFF);
            have_new_cfg = ES_TRUE;
    }

    tmp = cJSON_GetObjectItem_Type(msg, "safety_detect", cJSON_Number);
    if (ES_NULL != tmp) {
            mf_brd.cfg.safetybelt_detect = (uint8_t)(tmp->valueint & 0xFF);
            have_new_cfg = ES_TRUE;
    }

    tmp = cJSON_GetObjectItem_Type(msg, "face_detect", cJSON_Number);
    if (ES_NULL != tmp) {
        mf_brd.cfg.working_face_detect = (uint8_t)(tmp->valueint & 0xFF);
        have_new_cfg = ES_TRUE;
    }

    tmp = cJSON_GetObjectItem_Type(msg, "position_detect", cJSON_Number);
    if (ES_NULL != tmp) {
        mf_brd.cfg.posture_detect = (uint8_t)(tmp->valueint & 0xFF);
        have_new_cfg = ES_TRUE;
    }

    tmp = cJSON_GetObjectItem_Type(msg, "face_threshold", cJSON_Number);
    if (ES_NULL != tmp) {
        mf_brd.cfg.model.fe_gate = ((float)tmp->valueint / 100.0f);
        mf_model.fe_gate = ((float)tmp->valueint / 100.0f);
        have_new_cfg = ES_TRUE;
    }

    tmp = cJSON_GetObjectItem_Type(msg, "living_threshold", cJSON_Number);
    if (ES_NULL != tmp) {
        mf_brd.cfg.model.live_gate = ((float)tmp->valueint / 100.0f);
        mf_model.live_gate = ((float)tmp->valueint / 100.0f);
        have_new_cfg = ES_TRUE;
    }

    tmp = cJSON_GetObjectItem_Type(msg, "led_bright", cJSON_Number);
    if (ES_NULL != tmp) {
            mf_brd.cfg.led.high = ((float)tmp->valueint / 100.0f);
            have_new_cfg = ES_TRUE;
    }

    tmp = cJSON_GetObjectItem_Type(msg, "led_bright_low", cJSON_Number);
    if (ES_NULL != tmp) {
            mf_brd.cfg.led.low = ((float)tmp->valueint / 100.0f);
            have_new_cfg = ES_TRUE;
    }

    tmp = cJSON_GetObjectItem_Type(msg, "chk_face_pose", cJSON_Number);
    if (ES_NULL != tmp) {
            mf_brd.cfg.model.checkpose = (uint8_t)(tmp->valueint & 0xFF);
            have_new_cfg = ES_TRUE;
    }

    if (have_new_cfg) {
        task_param.type = ES_TASK_SAVE_BRD_CFG;
        task_param.param = ES_NULL;
        task_param.timeout = 0;
        return es_task_queue_push_wait(&task_param);
    }

    return ES_RET_SUCCESS;
}

static ES_S32 serv_proto_parse_relay_open_vol(const cJSON *msg)
{
    cJSON *tmp = ES_NULL;
    ES_S32 open = 0;
    es_task_param_t task_param;

    tmp = cJSON_GetObjectItem_Type(msg, "type", cJSON_Number);
    if (ES_NULL == tmp) {
        return ES_RET_FAILURE;
    }

    mf_brd.cfg.relay.pol = (uint8_t)(tmp->valueint & 0xFF);
    task_param.type = ES_TASK_SAVE_BRD_CFG;
    task_param.param = ES_NULL;
    task_param.timeout = 0;
    es_task_queue_push_wait(&task_param);

    tmp = cJSON_GetObjectItem_Type(msg, "voice", cJSON_Number);
    if (ES_NULL == tmp) {
        return ES_RET_FAILURE;
    }

    if (1 == tmp->valueint) {
        es_serv_proto_play_voice_by_queue(ES_VOICE_08_SET_INFO_OK);
    }

    return ES_RET_SUCCESS;
}

static ES_S32 serv_proto_parse_safety_detect(const cJSON *msg)
{
    cJSON *tmp = ES_NULL;
    ES_S32 open = 0;
    es_task_param_t task_param;

    tmp = cJSON_GetObjectItem_Type(msg, "type", cJSON_Number);
    if (ES_NULL == tmp) {
        return ES_RET_FAILURE;
    }

    mf_brd.cfg.safetybelt_detect = (uint8_t)(tmp->valueint & 0xFF);
    task_param.type = ES_TASK_SAVE_BRD_CFG;
    task_param.param = ES_NULL;
    task_param.timeout = 0;
    es_task_queue_push_wait(&task_param);

    tmp = cJSON_GetObjectItem_Type(msg, "voice", cJSON_Number);
    if (ES_NULL == tmp) {
        return ES_RET_FAILURE;
    }

    if (1 == tmp->valueint) {
        es_serv_proto_play_voice_by_queue(ES_VOICE_08_SET_INFO_OK);
    }

    return ES_RET_SUCCESS;
}

static ES_S32 serv_proto_parse_speed_detect(const cJSON *msg)
{
    cJSON *tmp = ES_NULL;
    es_task_param_t task_param;
    ES_S32 speed = 0;

    tmp = cJSON_GetObjectItem_Type(msg, "type", cJSON_Number);
    if (ES_NULL == tmp) {
        return ES_RET_FAILURE;
    }

    if (0 == tmp->valueint) {
        speed = 0;
    }

    tmp = cJSON_GetObjectItem_Type(msg, "speed", cJSON_Number);
    if (ES_NULL != tmp) {
        speed = tmp->valueint;
    }

    mf_brd.cfg.limit_speed = speed;
    task_param.type = ES_TASK_SAVE_BRD_CFG;
    task_param.param = ES_NULL;
    task_param.timeout = 0;
    es_task_queue_push_wait(&task_param);

    tmp = cJSON_GetObjectItem_Type(msg, "voice", cJSON_Number);
    if (ES_NULL == tmp) {
        return ES_RET_FAILURE;
    }
    if (1 == tmp->valueint) {
        es_serv_proto_play_voice_by_queue(ES_VOICE_08_SET_INFO_OK);
    }

    return ES_RET_SUCCESS;
}

static ES_S32 serv_proto_parse_working_face_detect(const cJSON *msg)
{
    cJSON *tmp = ES_NULL;
    ES_S32 open = 0;
    es_task_param_t task_param;

    tmp = cJSON_GetObjectItem_Type(msg, "type", cJSON_Number);
    if (ES_NULL == tmp) {
        return ES_RET_FAILURE;
    }

    open = tmp->valueint;
    mf_brd.cfg.working_face_detect = tmp->valueint;
    task_param.type = ES_TASK_SAVE_BRD_CFG;
    task_param.param = ES_NULL;
    task_param.timeout = 0;
    es_task_queue_push_wait(&task_param);

    tmp = cJSON_GetObjectItem_Type(msg, "voice", cJSON_Number);
    if (ES_NULL == tmp) {
        return ES_RET_FAILURE;
    }

    if (1 == tmp->valueint) {
        es_serv_proto_play_voice_by_queue(ES_VOICE_08_SET_INFO_OK);
    }

    return ES_RET_SUCCESS;
}

static ES_S32 serv_proto_parse_posture_detect(const cJSON *msg)
{
    cJSON *tmp = ES_NULL;
    es_task_param_t task_param;

    tmp = cJSON_GetObjectItem_Type(msg, "type", cJSON_Number);
    if (ES_NULL == tmp) {
        return ES_RET_FAILURE;
    }

    mf_brd.cfg.posture_detect = tmp->valueint;
    task_param.type = ES_TASK_SAVE_BRD_CFG;
    task_param.param = ES_NULL;
    task_param.timeout = 0;
    es_task_queue_push_wait(&task_param);

    tmp = cJSON_GetObjectItem_Type(msg, "voice", cJSON_Number);
    if (ES_NULL == tmp) {
        return ES_RET_FAILURE;
    }
    
    if (1 == tmp->valueint) {
        es_serv_proto_play_voice_by_queue(ES_VOICE_08_SET_INFO_OK);
    }

    return ES_RET_SUCCESS;
}

// static ES_S32 serv_proto_parse_server_rm_open(ES_VOID)
// {
//     return es_relay_open((ES_U16)(mf_brd.cfg.relay.opent*100));
// }

static ES_S32 serv_proto_parse_upload_offline_log(const cJSON *msg)
{
    cJSON *tmp = ES_NULL;
    es_task_param_t task_param;

    tmp = cJSON_GetObjectItem(msg, ES_PROTO_JSON_KEY_RET);
    if (ES_NULL == tmp) {
        return ES_RET_FAILURE;
    }
    
    task_param.type = ES_TASK_PASSLOG_UPLOAD_RESULT;
    task_param.param = (ES_VOID *)(ES_TRUE);
    if (0 != tmp->valueint) {
        task_param.param = (ES_VOID *)(ES_FALSE);
    }
    task_param.timeout = 0;
    es_task_queue_push_wait((const es_task_param_t *)&task_param);

    return ES_RET_SUCCESS;
}

ES_S32 es_serv_proto_init(ES_VOID)
{
    return ES_RET_SUCCESS;
}

// return cmd, mi
ES_S32 es_serv_proto_parse(const ES_CHAR *json_str, ES_U32 *mi)
{
    cJSON *root = NULL;
    cJSON *tmp = NULL;
    cJSON *msg = NULL;
    ES_S32 ret = ES_RET_SUCCESS;
    ES_S32 cmd = ES_PROTO_SERV_DEV_MAX;
    ES_U32 i;

    es_serv_proto_debug("serv proto parse start");
    es_serv_proto_debug("%s", json_str);
    root = cJSON_Parse((char*)json_str);
    if (ES_NULL == root) {
        es_serv_proto_debug("serv proto json parse fail");
        return ES_RET_FAILURE;
    }

    msg = cJSON_GetObjectItem_Type(root, "m", cJSON_Object);
    if(NULL == msg) {
        es_serv_proto_debug("get obj \'m\'failed");
        ret = ES_RET_FAILURE;
        goto FUNC_END;
    }

    tmp = cJSON_GetObjectItem_Type(root, "c", cJSON_Number);
    if((NULL == tmp) || (tmp->valueint >=  ES_PROTO_SERV_DEV_MAX)) {
        es_serv_proto_debug("error cmd value get");
        ret = ES_RET_FAILURE;
        goto FUNC_END;
    }

    cmd = tmp->valueint;
    switch(cmd) {
        case ES_PROTO_SERV_PUSH_CAR_INFO:
            ret = es_serv_proto_parse_car_info((const cJSON *)msg);
            break;
        
        case ES_PROTO_DEV_SYNC_TIME:
            tmp = cJSON_GetObjectItem_Type(root, "mi", cJSON_String);
            if (ES_NULL == tmp) {
                es_serv_proto_error("get \'mi\' failed");
                ret = ES_RET_FAILURE;
                goto FUNC_END;
            }
            serv_proto_sync_time((const ES_CHAR *)tmp->valuestring);
            break;
        case ES_PROTO_SERV_PUSH_ADD_DRIVER_INFO:
            ret = serv_proto_parse_add_face_fea((const cJSON *)msg);
            break;
        case ES_PROTO_SERV_PUSH_DEL_DRIVER_INFO:
            ret = serv_proto_parse_del_face_fea((const cJSON *)msg);
            break;

        case ES_PROTO_SERV_PUSH_CTL_LOCK:
            ret = es_serv_proto_parse_ctl_lock((const cJSON *)msg);
            break;

        case ES_PROTO_SERV_PUSH_UPDATE_DRIVER_INFO:
            ret = serv_proto_parse_update_face_fea((const cJSON *)msg);
            break;
        
        case ES_PROTO_SERV_PUSH_WORK_FACE_DETECT:
            ret = serv_proto_parse_working_face_detect((const cJSON *)msg);
            break;

        case ES_PROTO_SERV_PUSH_SPEED_DETECT:
            ret = serv_proto_parse_speed_detect((const cJSON *)msg);
            break;

        case ES_PROTO_SERV_PUSH_POSTURE_DETECT:
            ret = serv_proto_parse_posture_detect((const cJSON *)msg);
            break;

        case ES_PROTO_SERV_PUSH_IMAGE_CAPTRUE:
            ret = serv_proto_parse_image_capture((const cJSON *)msg);
            break;

        case ES_PROTO_SERV_PUSH_DYNAMIC_QR_CODE:
            ret = serv_proto_parse_dynamic_qr_code((const cJSON *)msg);
            break;

        case ES_PROTO_SERV_PUSH_EMERHENCY_CONTROL:
            ret = serv_proto_parse_emergency_control((const cJSON *)msg);
            break;
        case ES_PROTO_SERV_UPDATE_FLASH_RESOURCE:
            ret = serv_proto_parse_update_resource((const cJSON *)msg);
            break;

        case ES_PROTO_SERV_PUSH_RELAY_OPEN_VOL:
            ret = serv_proto_parse_relay_open_vol((const cJSON *)msg);
            break;

        case ES_PROTO_SERV_PUSH_SAFETY_DETECT:
            ret = serv_proto_parse_safety_detect((const cJSON *)msg);
            break;

        case ES_PROTO_DEV_UPLOAD_STATUS:
        case ES_PROTO_DEV_UPLOAD_DRIVER_INFO:
            ret = ES_RET_SUCCESS;
            break;
        case ES_PROTO_DEV_UPLOAD_DEVICE_INFO:
            ret = es_serv_proto_parse_car_cfg_resp((const cJSON *)msg);
            ret = serv_proto_set_brd_cfg((const cJSON *)msg);
            break;
        case ES_PROTO_DEV_UPLOAD_OFFLINE_RECORDS:
            ret = serv_proto_parse_upload_offline_log((const cJSON *)msg);
            break;
        case ES_PROTO_SERV_PLAY_VOICE:
            ret = serv_proto_parse_play_voice((const cJSON *)msg);
            break;
        case ES_PROTO_DEV_UPLOAD_AI_EVENT:
            ret = ES_RET_SUCCESS;
            break;

        default: {
            ret = ES_RET_FAILURE;
            es_serv_proto_debug("serv proto type not support, fail");
            goto FUNC_END;
        } break;
    }

    if (ES_NULL != mi) {
        tmp = cJSON_GetObjectItem_Type(root, "mi", cJSON_String);
        if (ES_NULL == tmp) {
            ret = ES_RET_FAILURE;
            es_serv_proto_error("get \'mi\' failed");
            goto FUNC_END;
        }
        *mi = (ES_U32) es_atoi((const ES_CHAR *)tmp->valuestring);
    }

FUNC_END:
    if (root) {
        cJSON_Delete(root);
    }

    for (i = 0; i < ES_SERV_MSG_EVENT_CB_COUNT; i++) {
        if (ES_NULL != event_cb_list[i]) {
            event_cb_list[i](cmd, ret);
        }
    }

    es_serv_proto_debug("serv proto parse ret:%d", ret);
    if (ret != ES_RET_SUCCESS) {
        return ES_PROTO_SERV_DEV_MAX;
    }

    return cmd;
}


ES_S32 es_serv_proto_reg_msg_event(es_serv_proto_msg_event_cb event_cb)
{
    ES_U32 i = 0;

    for (i = 0; i < ES_SERV_MSG_EVENT_CB_COUNT; i++) {
        if (ES_NULL == event_cb_list[i]) {
            event_cb_list[i] = event_cb;
            return ES_RET_SUCCESS;
        }
    }

    return ES_RET_FAILURE;
}

#if ES_MODEL_ACTIVE_ENABLE
ES_S32 es_serv_proto_get_model_active(const ES_BYTE *cpu_id, const ES_BYTE *mac, ES_CHAR **data)
{
    ES_CHAR json_str[ES_MODEL_ACTIVE_PROTO_DATA_LEN];
    ES_U32 json_len;
    ES_BYTE aes_data[ES_MODEL_ACTIVE_PROTO_DATA_LEN];
    ES_U32 aes_out_len;
    ES_U32 base64_len = 0;

    json_len = snprintf(json_str, ES_MODEL_ACTIVE_PROTO_DATA_LEN, "{\"cpu_id\":\"%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X\"",
        cpu_id[0], cpu_id[1], cpu_id[2], cpu_id[3], cpu_id[4], cpu_id[5], cpu_id[6], cpu_id[7],
        cpu_id[8], cpu_id[9], cpu_id[10], cpu_id[11], cpu_id[12], cpu_id[13], cpu_id[14], cpu_id[15]);
    json_len += snprintf(json_str+json_len, ES_MODEL_ACTIVE_PROTO_DATA_LEN - json_len, ",\"mac\":\"%02X%02X%02X%02X%02X%02X\"}",
        mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);

    if (ES_RET_SUCCESS != es_aes_cbc128_encrypt(aes_data, &aes_out_len, (ES_BYTE *)json_str, json_len)) {
        return ES_RET_FAILURE;
    }

    *data = (ES_CHAR *)base64_encode((const ES_BYTE *)aes_data, aes_out_len, &base64_len);

    return ES_RET_SUCCESS;
}

ES_S32 es_serv_proto_model_active_decode(const ES_BYTE *data, ES_U32 data_len)
{
    ES_BYTE *aes_data = NULL;
    ES_U32 aes_len = 0;
    ES_BYTE json_str[ES_MODEL_ACTIVE_PROTO_DATA_LEN];
    ES_BYTE model_act_key[ES_MODEL_ACTIVE_KEY_LEN];
    ES_U32 json_len = 0;
    ES_S32 ret = ES_RET_SUCCESS;
    cJSON *root = NULL;
    cJSON *json_tmp = NULL;

    if (data_len > (ES_MODEL_ACTIVE_PROTO_DATA_LEN<<1)) {
        return ES_RET_FAILURE;
    }

    aes_data = base64_decode(data, data_len, &aes_len);
    if (NULL == aes_data) {
        ret = ES_RET_FAILURE;
        es_serv_proto_error("base64 decode fail");
        goto FUNC_END;
    }

    if (aes_len > ES_MODEL_ACTIVE_PROTO_DATA_LEN) {
        ret = ES_RET_FAILURE;
        es_serv_proto_error("aes_len:%d error",  aes_len);
        goto FUNC_END;
    }
    es_serv_proto_debug("aes_len:%d",  aes_len);

    es_aes_cbc128_decrypt(json_str, &json_len, aes_data, aes_len);
    if (json_str[0] != '{') {
        es_serv_proto_error("json_str error\r\n");
        ret = ES_RET_FAILURE;
        goto FUNC_END;
    }
    // es_serv_proto_debug("json:%s", json_str);

    root = cJSON_Parse((char*)json_str);
    if (NULL == root) {
        es_serv_proto_error("cJSON_Parse error\r\n");
        ret = ES_RET_FAILURE;
        goto FUNC_END;
    }

    json_tmp = cJSON_GetObjectItem_Type(root, "code", cJSON_Number);
    if(NULL == json_tmp) {
        es_serv_proto_error("code field error\r\n");
        ret = ES_RET_FAILURE;
        goto FUNC_END;
    } else {
        if(json_tmp->valueint != 0) {
            es_serv_proto_error("code value error\r\n");
            ret = ES_RET_FAILURE;
            goto FUNC_END;
        }
    }

    json_tmp = cJSON_GetObjectItem_Type(root, "key", cJSON_String);
    if(NULL == json_tmp) {
        es_serv_proto_error("key field error\r\n");
        ret = ES_RET_FAILURE;
        goto FUNC_END;
    } else {
        if(strlen(json_tmp->valuestring) != (ES_MODEL_ACTIVE_KEY_LEN<<1)) {
            es_serv_proto_error("code value error\r\n");
            ret = ES_RET_FAILURE;
            goto FUNC_END;
        }
    }
    es_serv_proto_debug("key string:%s\r\n", json_tmp->valuestring);
    es_utils_hex_str_to_bytes(json_tmp->valuestring, model_act_key, ES_MODEL_ACTIVE_KEY_LEN);
    es_model_active(model_act_key);

FUNC_END:
    if (aes_data) {
        free(aes_data);
    }

    if (root) {
        cJSON_Delete(root);
    }
    return ret;
}
#endif