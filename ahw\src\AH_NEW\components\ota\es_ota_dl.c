/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_ota_dl.c
** bef: define the interface for ota download.
** auth: lines<<EMAIL>>
** create on 2022.01.12 
*/

#include "es_inc.h"

#if ES_OTA_DL_ENABLE

// #define ES_OTA_DL_DEBUG
#ifdef ES_OTA_DL_DEBUG
#define es_ota_dl_debug es_log_info
#define es_ota_dl_error es_log_error
#else
#define es_ota_dl_debug(...)
#define es_ota_dl_error(...)
#endif

#define ES_OTA_DL_URL_LEN               (128)


static ES_CHAR ota_http_url[ES_OTA_DL_URL_LEN] = {0};
static ES_BYTE ota_file_md5[ES_MD5_HEX_LEN] = {0};
static ES_BOOL found_new_version = 0;


static ES_U32 es_ota_dl_version2dec(const ES_CHAR *version)
{
    ES_U32 dec_version = 0;
    const ES_CHAR *p = ES_NULL;
    ES_U32 i = 0;

    p = version;
    for (i = 0; i < 3; i++) {
        dec_version *= 100;
        dec_version += es_atoi(p);
        while (*p != '.' && 0 != *p) {
            p++;
        }
        p++;
    }

    return dec_version;
}

ES_S32 es_ota_dl_init(ES_VOID)
{
    return ES_RET_SUCCESS;
}

ES_S32 es_ota_dl_set_url(const ES_CHAR *url)
{
    if (es_strlen(url) >= ES_OTA_DL_URL_LEN) {
        return ES_RET_FAILURE;
    }

    es_memset(ota_http_url, 0x00, sizeof(ota_http_url));
    es_strncpy(ota_http_url, url, ES_OTA_DL_URL_LEN);
    return ES_RET_SUCCESS;
}

ES_S32 es_ota_dl_set_md5(const ES_CHAR *md5)
{
    if (ES_MD5_STR_LEN != es_strlen(md5)) {
        return ES_RET_FAILURE;
    }

    if (ES_MD5_HEX_LEN != es_string_str2hex(md5, ota_file_md5, ES_MD5_HEX_LEN)) {
        return ES_RET_FAILURE;
    }

    return ES_RET_SUCCESS;
}

ES_S32 es_ota_dl_set_version(const ES_CHAR *version)
{
    ES_U32 version_val = 0;

    version_val = es_ota_dl_version2dec(version);
    if (version_val > ES_SYS_SVER_VAL) {
        found_new_version = ES_TRUE;
    }
    
    return ES_RET_SUCCESS;
}

ES_S32 es_ota_dl_set_version_with_number(ES_U32 version)
{
    if (version > ES_SYS_SVER_VAL) {
        found_new_version = ES_TRUE;
    }
    
    return ES_RET_SUCCESS;
}

ES_VOID es_ota_dl_run(ES_VOID)
{
    static ES_U32 last_time = 0;

    if (!found_new_version) {
        return;
    }

    if (0 == ota_http_url[0]) {
        return;
    }


    if (ES_NETWORK_CONNECTED != es_network_get_status()) {
        return;
    }

    // if (ES_RET_SUCCESS != es_time_check_timeout_ms(&last_time, 10*60*1000)) {
    if (ES_RET_SUCCESS != es_time_check_timeout_ms(&last_time, 60*1000)) {
        return;
    }

    if (ES_RET_SUCCESS != es_network_httpc_get_file(ota_http_url)) {
        last_time = 0;
        return;
    }
}

#endif

