#include "es_inc.h"
#include "es_lcd_tft_spi.h"

#include "gpiohs.h"
#include "spi.h"
#include "unistd.h"
#include "sleep.h"


/* clang-format off */
#define SPI_CHANNEL             0
#define SPI_SLAVE_SELECT        3

#if (ES_LCD_DRIVER_NT35510 == ES_LCD_DRIVER_TYPE)
#define SPI_MODE_A              SPI_WORK_MODE_0
#define SPI_MODE_B              SPI_WORK_MODE_0
#define SPI_CS_HIGH() do { \
        usleep(1); \
        gpiohs_set_pin(ES_LCD_SPI_CS_HS_NUM, 1); \
        usleep(1); \
    } while(0)
#define SPI_CS_LOW() do { \
        usleep(1); \
        gpiohs_set_pin(ES_LCD_SPI_CS_HS_NUM, 0); \
        usleep(1); \
    } while(0)
#else
#define SPI_MODE_A              SPI_WORK_MODE_0
#define SPI_MODE_B              SPI_WORK_MODE_0
#define SPI_CS_HIGH()
#define SPI_CS_LOW()
#endif
/* clang-format on */

static void init_dcx(void)
{
    gpiohs_set_drive_mode(ES_LCD_DCX_HS_NUM, GPIO_DM_OUTPUT);
    gpiohs_set_pin(ES_LCD_DCX_HS_NUM, GPIO_PV_HIGH);
}

static void set_dcx_control(void)
{
    gpiohs_set_pin(ES_LCD_DCX_HS_NUM, GPIO_PV_LOW);
}

static void set_dcx_data(void)
{
    gpiohs_set_pin(ES_LCD_DCX_HS_NUM, GPIO_PV_HIGH);
    SPI_CS_LOW();
}

static void init_rst(void)
{
    gpiohs_set_drive_mode(ES_LCD_RST_HS_NUM, GPIO_DM_OUTPUT);
    gpiohs_set_pin(ES_LCD_RST_HS_NUM, GPIO_PV_LOW);
    msleep(10);
    gpiohs_set_pin(ES_LCD_RST_HS_NUM, GPIO_PV_HIGH);
    msleep(200);
}

void es_tft_hard_init(uint8_t freqMhz)
{
    init_dcx();
    spi_init(SPI_CHANNEL, SPI_MODE_B, SPI_FF_OCTAL, 8, 0);

    init_rst();
    spi_set_clk_rate(SPI_CHANNEL, freqMhz * 1000 * 1000);
}

static inline void tft_write_data(uint8_t *data, uint32_t len)
{
    spi_send_data_standard(SPI_CHANNEL, SPI_SLAVE_SELECT, NULL, 0, data, len);
}

void es_tft_write_command(uint8_t cmd)
{
    set_dcx_control();
    SPI_CS_LOW();
    spi_init(SPI_CHANNEL, SPI_MODE_A, SPI_FF_OCTAL, 8, 0);

    spi_init_non_standard(SPI_CHANNEL, 8 /*instrction length*/, 0 /*address length*/, 0 /*wait cycles*/,
                          SPI_AITM_AS_FRAME_FORMAT /*spi address trans mode*/);
    // spi_send_data_normal_dma(DMAC_CHANNEL0, SPI_CHANNEL, SPI_SLAVE_SELECT, (uint8_t *)(&cmd), 1, SPI_TRANS_CHAR);

    tft_write_data(&cmd, 1);

    set_dcx_data();
    SPI_CS_HIGH();
}

void es_tft_write_command_word(uint8_t *cmd_buf)
{
    set_dcx_control();
    SPI_CS_LOW();
    spi_init(SPI_CHANNEL, SPI_MODE_B, SPI_FF_OCTAL, 8, 0);

    spi_init_non_standard(SPI_CHANNEL, 8 /*instrction length*/, 0 /*address length*/, 0 /*wait cycles*/,
                          SPI_AITM_AS_FRAME_FORMAT /*spi address trans mode*/);
    // spi_send_data_normal_dma(DMAC_CHANNEL0, SPI_CHANNEL, SPI_SLAVE_SELECT, (uint8_t *)(&cmd), 1, SPI_TRANS_CHAR);

    tft_write_data(cmd_buf, 2);

    // set_dcx_data();
    // SPI_CS_HIGH();
}

void es_tft_write_byte_word(uint8_t *data_buf)
{
    set_dcx_data();
    SPI_CS_LOW();
    spi_init(SPI_CHANNEL, SPI_MODE_B, SPI_FF_OCTAL, 8, 0);

    spi_init_non_standard(SPI_CHANNEL, 8 /*instrction length*/, 0 /*address length*/, 0 /*wait cycles*/,
                          SPI_AITM_AS_FRAME_FORMAT /*spi address trans mode*/);
    // spi_send_data_normal_dma(DMAC_CHANNEL0, SPI_CHANNEL, SPI_SLAVE_SELECT, data_buf, length, SPI_TRANS_CHAR);

    tft_write_data(data_buf, 2);
    SPI_CS_HIGH();
}

void es_tft_write_byte(uint8_t *data_buf, uint32_t length)
{
    set_dcx_data();
    SPI_CS_LOW();
    spi_init(SPI_CHANNEL, SPI_MODE_A, SPI_FF_OCTAL, 8, 0);

    spi_init_non_standard(SPI_CHANNEL, 8 /*instrction length*/, 0 /*address length*/, 0 /*wait cycles*/,
                          SPI_AITM_AS_FRAME_FORMAT /*spi address trans mode*/);
    // spi_send_data_normal_dma(DMAC_CHANNEL0, SPI_CHANNEL, SPI_SLAVE_SELECT, data_buf, length, SPI_TRANS_CHAR);

    tft_write_data(data_buf, length);
    SPI_CS_HIGH();
}

void es_tft_write_onebyte(uint8_t data)
{
    es_tft_write_byte(&data, 1);
	return;
}

// void tft_write_half(uint16_t *data_buf, uint32_t length)
// {
//     set_dcx_data();
//     spi_init(SPI_CHANNEL, SPI_MODE_A, SPI_FF_OCTAL, 16, 0);
//     spi_init_non_standard(SPI_CHANNEL, 16 /*instrction length*/, 0 /*address length*/, 0 /*wait cycles*/,
//                           SPI_AITM_AS_FRAME_FORMAT /*spi address trans mode*/);
//     spi_send_data_normal_dma(DMAC_CHANNEL0, SPI_CHANNEL, SPI_SLAVE_SELECT, data_buf, length, SPI_TRANS_SHORT);
// }

void es_tft_write_word(uint32_t *data_buf, uint32_t length, uint32_t flag)
{
    set_dcx_data();
    SPI_CS_LOW();
    spi_init(SPI_CHANNEL, SPI_MODE_A, SPI_FF_OCTAL, 32, 0);

    spi_init_non_standard(SPI_CHANNEL, 0 /*instrction length*/, 32 /*address length*/, 0 /*wait cycles*/,
                          SPI_AITM_AS_FRAME_FORMAT /*spi address trans mode*/);
    spi_send_data_normal_dma(DMAC_CHANNEL0, SPI_CHANNEL, SPI_SLAVE_SELECT, data_buf, length, SPI_TRANS_INT);
    SPI_CS_HIGH();
}

void es_tft_fill_data(uint32_t *data_buf, uint32_t length)
{
    set_dcx_data();
    SPI_CS_LOW();
    spi_init(SPI_CHANNEL, SPI_MODE_A, SPI_FF_OCTAL, 32, 0);
    spi_init_non_standard(SPI_CHANNEL, 0 /*instrction length*/, 32 /*address length*/, 0 /*wait cycles*/,
                          SPI_AITM_AS_FRAME_FORMAT /*spi address trans mode*/);
    spi_fill_data_dma(DMAC_CHANNEL0, SPI_CHANNEL, SPI_SLAVE_SELECT, data_buf, length);
    SPI_CS_HIGH();
}
