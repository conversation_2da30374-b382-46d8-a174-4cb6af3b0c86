/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_facedb_wrapper.h
** bef: define the interface for facedb wrapper. 
** auth: lines<<EMAIL>>
** create on 2020.11.24
*/

#ifndef _ES_FACEDB_WRAPPER_H_
#define _ES_FACEDB_WRAPPER_H_

#ifdef __cplusplus 
extern "C" { 
#endif

#include "es_types.h"

ES_S32 es_facedb_file_parse(const ES_BYTE *file_data);

ES_S32 es_facedb_parse_mqtt_face_fea(const ES_VOID *msg_json_obj);

ES_S32 es_facedb_tuya_add(const ES_VOID *json_data);

ES_S32 es_facedb_tuya_del(const ES_VOID *json_data);

ES_BOOL es_facedb_runing(ES_VOID);

#ifdef __cplusplus 
}
#endif

#endif