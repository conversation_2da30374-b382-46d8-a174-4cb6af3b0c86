#include "facelib_inc.h"

mf_flow_t mf_flow;
/*****************************************************************************/
// Private Func 局部函数
/*****************************************************************************/



volatile uint8_t face_lib_flow_debug_en = 0;
extern mf_flow_t mf_flow_vis2vis;


/*****************************************************************************/
// Public Func 全局函数
/*****************************************************************************/
mf_err_t mf_flow_choose(mf_flow_type_t type)
{
	mf_err_t err = MF_ERR_NONE;
	switch(type) 
	{
	case MF_FLOW_DUALCAM_VIS2VIS:
		memcpy(&mf_flow, &mf_flow_vis2vis, sizeof(mf_flow_t));
		break;
	default:
		err = MF_ERR_FLOW_TYPE;
		break;
	}
	return err;
}

