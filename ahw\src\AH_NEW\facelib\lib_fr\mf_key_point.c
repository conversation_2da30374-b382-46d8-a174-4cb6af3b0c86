#include "facelib_inc.h"
#include "mf_kpu_v1.h"
/* key_point.c */
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////


// 64x128
static const int8_t key_point_fc_1_w[] ={
    0.07699552*116, 0.122422315*116, -0.24020718*116, 0.0524498*116, 0.25608224*116, 0.1231027*116, -0.015539356*116, -0.0582908*116, -0.06838728*116, 0.18018034*116, -0.10231962*116, -0.041622467*116, -0.15446115*116, -0.101237915*116, -0.105062455*116, 0.09588476*116, 0.2815472*116, -0.0334811*116, -0.39399675*116, 0.23934028*116, 0.15829968*116, 0.11854504*116, -0.1440899*116, 0.1118722*116, 0.09571979*116, -0.087245345*116, -0.037842553*116, -0.20636953*116, 0.08249469*116, -0.51552695*116, 0.07393276*116, -0.015240014*116, -0.15701857*116, 0.00055588724*116, 0.12800238*116, 0.06500255*116, 0.023471223*116, -0.098691426*116, -0.01667687*116, -0.044131696*116, -0.033473752*116, 0.2392079*116, 0.21044771*116, -0.01735861*116, 0.10175696*116, 0.015696978*116, -0.019483957*116, 0.2983836*116, -0.11477337*116, 0.24739376*116, -0.061535235*116, 0.39731893*116, -0.14294472*116, -0.057225093*116, -0.136937*116, 0.13179196*116, 0.07964027*116, -0.27310905*116, -0.015504954*116, 0.12283894*116, 0.24186961*116, -0.26767322*116, 0.023842178*116, -0.0053248256*116, 0.09652466*116, -0.5264454*116, 0.036402546*116, -0.17134395*116, -0.04537604*116, 0.033583596*116, -0.066112734*116, 0.07472204*116, -0.33197227*116, 0.0735111*116, -0.031100629*116, 0.0043190266*116, -0.106147744*116, 0.06640585*116, 0.20565422*116, 0.082679294*116, -0.15278773*116, 0.07234931*116, 0.20359975*116, 0.13704541*116, -0.14677098*116, 0.24872692*116, 0.041489698*116, -0.10901799*116, -0.021256594*116, -0.42641848*116, 0.023158725*116, -0.050133888*116, -0.061347473*116, 0.007773153*116, 0.016274525*116, 0.036548276*116, 0.21533436*116, 0.36941805*116, 0.15242295*116, 0.17504936*116, 0.3067449*116, 0.24691823*116, 0.22741091*116, 0.0019082092*116, 0.00039644327*116, -0.03067431*116, -0.17713495*116, -0.33108115*116, 0.07985381*116, 0.04024525*116, -0.16614269*116, -0.19157456*116, 0.4281145*116, -0.079535425*116, -0.3953565*116, 0.09837052*116, -0.07795867*116, -0.045474906*116, 0.02216078*116, -0.04540457*116, -0.033537887*116, 0.099019185*116, -0.16296361*116, 0.005586583*116, -0.25683582*116, 0.03657447*116, 0.12268971*116, -0.12816629*116, -0.005735163*116, 0.12423461*116, -0.0066505726*116, -0.39905983*116, 0.08316617*116, 0.14421037*116, 0.050357442*116, -0.020839255*116, 0.18151481*116, 0.13197334*116, -0.117651284*116, -0.0012979388*116, 0.1937054*116, -0.0436476*116, -0.20398873*116, 0.09282092*116, 0.03698792*116, 0.038128495*116, -0.10598803*116, -0.40715003*116, -0.09192862*116, -0.2601714*116, -0.006769204*116, -0.042294376*116, -0.09682314*116, 0.17581972*116, 0.24015321*116, 0.002639736*116, 0.17188947*116, 0.0637776*116, 0.07552437*116, -0.44605517*116, 0.05138554*116, -0.07302127*116, -0.11448678*116, -0.1117072*116, -0.16807902*116, -0.021871114*116, -0.18610497*116, -0.0732797*116, 0.17256986*116, -0.07986893*116, -0.06452283*116, -0.065583125*116, -0.0027538803*116, 0.08542687*116, 0.26466537*116, -0.12190689*116, -0.26729003*116, -0.25523978*116, 0.12630078*116, 0.0811426*116, -0.038903933*116, -0.0046662716*116, 0.15596022*116, 0.12169541*116, 0.32457447*116, 0.22741692*116, -0.20071353*116, -0.23557746*116, 0.16353779*116, -0.07775176*116, 0.050184842*116, 0.05765013*116, 0.023495616*116, 0.11356687*116, 0.14333071*116, 0.035685994*116, -0.19401062*116, -0.13334447*116, -0.046396565*116, -0.19325027*116, 0.07834571*116, -0.09276602*116, -0.116659276*116, -0.27917203*116, 0.024904344*116, -0.04019051*116, 0.17741685*116, -0.19886476*116, -0.26666743*116, -0.31237167*116, -6.330727e-05*116, 0.012538414*116, 0.22809544*116, 0.07613722*116, 0.010676715*116, 0.031996913*116, 0.22286806*116, -0.17661609*116, 0.00046483573*116, 0.18993437*116, -0.1312924*116, 0.21447542*116, -0.10101575*116, -0.09282897*116, 0.2047889*116, 0.25902772*116, 0.014717216*116, -0.22194666*116, 0.09665966*116, -0.02301957*116, -0.06117978*116, 0.028535226*116, 0.10954439*116, 0.030603617*116, 0.34583932*116, -0.08056551*116, -0.11850879*116, -0.13760008*116, -0.023087675*116, 0.07648423*116, -0.027810361*116, -0.059936445*116, 0.086436845*116, -0.1550073*116, 0.018973226*116, 0.037293483*116, 0.18426204*116, 0.2139027*116, 0.31316563*116, -0.2201771*116, -0.14647904*116, -0.17702161*116, 0.39806798*116, 0.29625124*116, 0.09291979*116, 0.046910428*116, -0.16530877*116, 0.37598112*116, 0.2546715*116, 0.12300171*116, 0.08309448*116, -0.048013765*116, 0.10636341*116, -0.029978001*116, 0.22780283*116, -0.16305305*116, -0.13810903*116, 0.11645112*116, 0.016003612*116, -0.050030716*116, -0.121592134*116, 0.1953836*116, 0.1843893*116, -0.03493592*116, 0.2704428*116, -0.15539376*116, 0.12000878*116, -0.1892845*116, -0.23842207*116, -0.044365328*116, -0.15234256*116, 0.29038444*116, -0.34948617*116, -0.051033095*116, 0.32271406*116, 0.031173898*116, 0.12553434*116, 0.13045935*116, 0.13048959*116, -0.24902356*116, -0.047981948*116, -0.16390714*116, 0.0019079042*116, -0.010112981*116, -0.17082012*116, -0.06824564*116, -0.18669221*116, -0.19913119*116, -0.061779235*116, 0.15855739*116, -0.06944269*116, -0.075958766*116, 0.033081304*116, -0.2376026*116, -0.10458015*116, 0.08670049*116, 0.17987825*116, -0.14394425*116, 0.18819156*116, 0.023919504*116, -0.3202483*116, 0.10542736*116, -0.16717264*116, -0.112529516*116, 0.019820528*116, -0.099083975*116, -0.18002288*116, -0.013114151*116, 0.1205692*116, -0.14626466*116, 0.05924807*116, 0.0739977*116, -0.044115815*116, 0.064464346*116, -0.0806966*116, -0.056534212*116, 0.10980859*116, 0.044854905*116, -0.06687186*116, -0.21632946*116, 0.24370834*116, -0.15453161*116, 0.006712542*116, -0.0942215*116, -0.06056454*116, -0.0818935*116, 0.08778236*116, 0.036669843*116, -0.04166704*116, 0.008575366*116, 0.17606327*116, 0.085183166*116, 0.35644767*116, 0.14507376*116, -0.077218786*116, -0.07568422*116, 0.15054712*116, 0.10141483*116, -0.02962017*116, -0.008300154*116, -0.2952292*116, -0.11957705*116, 0.008285279*116, -0.059405997*116, -0.032250486*116, -0.15658228*116, -0.37012932*116, 0.027495619*116, 0.10500266*116, -0.2573941*116, -0.036729638*116, 0.21065947*116, 0.008716546*116, -0.017000295*116, -0.18198851*116, -0.24927565*116, 0.08221375*116, -0.00080476253*116, 0.040610433*116, 0.17955849*116, -0.058429614*116, -0.13813493*116, 0.08563861*116, -0.0976471*116, 0.06916131*116, -0.31771258*116, 0.0020376467*116, 0.0120029785*116, 0.039418098*116, 0.079834536*116, 0.081927665*116, 0.09726729*116, -0.007330127*116, -0.01077159*116, -0.1892645*116, -0.013238894*116, -0.15915665*116, 0.23661886*116, 0.07370473*116, -0.15318176*116, -0.31156301*116, 0.057545897*116, 0.039613154*116, 0.077646606*116, 0.09250904*116, 0.06563723*116, 0.24777491*116, 0.43090528*116, 0.16552353*116, 0.10314734*116, 0.13032936*116, -0.0586249*116, 0.056200914*116, -0.05268061*116, -0.25538418*116, -0.22005966*116, -0.032331184*116, 0.030031817*116, -0.1898215*116, -0.053341545*116, -0.05450598*116, 0.0922965*116, 0.29809958*116, -0.19428313*116, -0.115308665*116, -0.074610636*116, -0.24247836*116, 0.08599956*116, 0.064653896*116, 0.086182624*116, -0.09466515*116, 0.21235812*116, -0.21320175*116, 1.0940557*116, 0.10992386*116, -0.07492009*116, -0.11416356*116, 0.058615986*116, 0.14501788*116, 0.4536854*116, -0.33285162*116, 0.06493587*116, -0.060576055*116, -0.16647236*116, 0.063348316*116, 0.058080345*116, 0.023184303*116, -0.0129790995*116, 0.05700139*116, -0.017502744*116, -0.07667928*116, -0.037720315*116, -0.10926677*116, 0.05961218*116, 0.03215595*116, -0.07863508*116, 0.11989385*116, 0.19968148*116, 0.050737362*116, 0.11415756*116, -0.17710188*116, -0.07198123*116, 0.122759335*116, 0.2258466*116, -0.12407446*116, 0.040776722*116, -0.08343041*116, -0.19083548*116, -0.16994944*116, -0.06887528*116, 0.17304547*116, -0.19140409*116, -0.020339604*116, 0.18929343*116, -0.03466095*116, 0.27915478*116, -0.03411518*116, -0.12890774*116, -0.2286893*116, -0.057115704*116, -0.14395112*116, -0.07073608*116, 0.031354897*116, 0.0065053655*116, 0.14005712*116, -0.20391895*116, 0.2254961*116, -0.080328956*116, 0.12642935*116, 0.036655005*116, -0.036813214*116, -0.16219868*116, 0.12157735*116, -0.031411003*116, 0.08457526*116, 0.25203207*116, -0.10003479*116, -0.17385569*116, 0.09073467*116, -0.020006837*116, -0.041633513*116, -0.17835876*116, 0.13992554*116, 0.08518911*116, -0.0032221032*116, 0.1054117*116, -0.14571421*116, -0.2087099*116, -0.15477642*116, 0.14802453*116, -0.0011377664*116, 0.0883539*116, 0.056282606*116, 0.054476667*116, -0.08404693*116, -0.0807956*116, 0.22437985*116, 0.043362055*116, 0.041707695*116, -0.09657866*116, 0.044808332*116, 0.19336575*116, 0.16689152*116, 0.19415808*116, -0.11709512*116, 0.05090831*116, 0.17704268*116, 0.051551238*116, -0.019237485*116, -0.10258497*116, -0.07752505*116, 0.17002864*116, 0.14800073*116, -0.021987153*116, -0.091596134*116, -0.027227392*116, 0.17146946*116, -0.04740877*116, 0.22002326*116, -0.031055275*116, -0.21175213*116, 0.0864005*116, -0.05742649*116, 0.018794857*116, -0.1817675*116, -0.14101988*116, 0.011640236*116, -0.054838225*116, 0.20563307*116, 0.075042285*116, -0.18544419*116, -0.48879445*116, 0.035700127*116, -0.062332857*116, -0.100504994*116, 0.2652906*116, 0.15883814*116, -0.22676288*116, -0.046763077*116, -0.15002722*116, 0.16895111*116, -0.004915428*116, 0.14682432*116, -0.022991799*116, 0.042990316*116, -0.10448038*116, 0.054548997*116, -0.016273577*116, -0.060402144*116, 0.39368096*116, -0.17725217*116, 0.279709*116, -0.0186734*116, 0.30661798*116, -0.1039992*116, -0.3075812*116, 0.09439949*116, 0.02903295*116, 0.32503712*116, -0.27106434*116, -0.11281512*116, 0.035949294*116, 0.18247198*116, -0.323127*116, 0.10076526*116, 0.09280245*116, 0.20455144*116, -0.008278615*116, -0.047145046*116, -0.13084602*116, -0.0106095355*116, 0.1661175*116, 0.07274025*116, -0.058046848*116, -0.08165756*116, 0.1317786*116, -0.25623286*116, -0.06647419*116, -0.049457077*116, 0.1158701*116, -0.32571417*116, 0.21107723*116, -0.15067749*116, 0.2090406*116, -0.078778625*116, 0.0300502*116, -0.10746309*116, 0.18868026*116, -0.19582222*116, -0.18098928*116, 0.091504514*116, -0.17566857*116, 0.06135219*116, -0.32272616*116, -0.15298605*116, -0.09487123*116, 0.25028977*116, 0.08073212*116, 0.079741605*116, 0.13014443*116, 0.09526379*116, -0.155005*116, 0.5722052*116, 0.17653865*116, 0.15417823*116, 0.04815926*116, -0.19439659*116, -0.23777735*116, -0.2766693*116, -0.06907801*116, 0.255303*116, -0.111504726*116, 0.19215076*116, -0.052624557*116, -0.0007451408*116, 0.19127129*116, -0.15223916*116, 0.189351*116, -0.16729744*116, -0.08647858*116, 0.14748669*116, 0.17516026*116, 0.22869788*116, -0.09579047*116, 0.033171333*116, -0.045804046*116, 0.2695898*116, -0.11427318*116, 0.090103485*116, 0.10507716*116, 0.069994606*116, -0.13177693*116, -0.08798575*116, 0.20217915*116, -0.14327264*116, -0.12257286*116, 0.04154486*116, -0.1205098*116, 0.04508789*116, 0.056405637*116, -0.05472533*116, -0.1457486*116, -0.120242484*116, 0.0050805244*116, -0.18159246*116, 0.08803315*116, -0.21026441*116, -0.108769774*116, -0.08126219*116, 0.1713864*116, 0.17401448*116, 0.2583829*116, 0.108951986*116, 0.24503425*116, -0.1600145*116, -0.12472113*116, 0.15223718*116, 0.04624404*116, -0.23183943*116, -0.15165697*116, -0.30908594*116, 0.09876768*116, -0.08807037*116, -0.058093917*116, -0.16159314*116, 0.17161103*116, 0.09915423*116, -0.1450991*116, 0.15221389*116, -0.016592257*116, -0.056986563*116, 0.12127803*116, 0.26549423*116, -0.08621683*116, -0.09552026*116, 0.032336794*116, 0.15482004*116, 0.31699112*116, -0.13962638*116, 0.1675273*116, 0.105634674*116, 0.101170644*116, -0.13002776*116, -0.013036051*116, -0.018896056*116, 0.07126389*116, 0.04833202*116, -0.09117625*116, 0.051701378*116, -0.17474863*116, 0.3539154*116, 0.15438229*116, -0.036684837*116, -0.07193559*116, 0.16397561*116, -0.17623812*116, 0.106898144*116, -0.15539946*116, -0.105246365*116, -0.18455514*116, 0.17410307*116, 0.035516966*116, -0.04397236*116, 0.20835787*116, 0.19215836*116, 0.1999634*116, -0.28180024*116, 0.18853845*116, -0.16755077*116, 0.14391126*116, -0.0618193*116, -0.051387217*116, 0.021878572*116, 0.30521226*116, 0.1999145*116, 0.05490879*116, -0.14069614*116, 0.1831107*116, -0.007149553*116, 0.042953175*116, -0.11346372*116, 0.017679816*116, -0.113554284*116, 0.17362258*116, -0.15235873*116, -0.116298646*116, -0.14765568*116, 0.14143364*116, 0.112036854*116, 0.31082317*116, 0.117819935*116, -0.021683177*116, 0.34745008*116, 0.08303154*116, -0.32116875*116, -0.016314588*116, 0.00835685*116, 0.06444743*116, -0.16738015*116, 0.11873806*116, -0.10463401*116, -0.121745005*116, 0.12426975*116, 0.025469381*116, 0.037766356*116, 0.061458234*116, -0.28791717*116, 0.12739979*116, -0.1581565*116, -0.037511867*116, -0.04848514*116, 0.005090002*116, -0.035022292*116, -0.027615929*116, -0.15394355*116, -0.14133535*116, 0.086193554*116, -0.07623261*116, 0.021603277*116, -0.09273964*116, -0.0038410383*116, -0.04433898*116, -0.18509924*116, 0.112143636*116, -0.06043332*116, -0.18077022*116, 0.016364235*116, -0.054726094*116, -0.18963522*116, 0.027155764*116, 0.12715876*116, 0.09072639*116, -0.1278966*116, -0.07561025*116, -0.10725471*116, 0.25477242*116, 0.28003088*116, -0.12487499*116, 0.01549079*116, -0.24404947*116, 0.02746107*116, -0.0974923*116, 0.054315623*116, -0.2596193*116, -0.003215217*116, -0.06835243*116, -0.17492928*116, 0.2833723*116, -0.03598534*116, -0.020586131*116, 0.048283476*116, 0.18741976*116, 0.15058303*116, 0.09219053*116, 0.020526348*116, 0.2542217*116, 0.060219433*116, -0.006835281*116, -0.10709486*116, 0.058386102*116, -0.025644058*116, 0.1116786*116, -0.07803832*116, -0.021832487*116, -0.20179367*116, 0.015664281*116, 0.09889296*116, -0.09838574*116, 0.088045575*116, -0.20340009*116, 0.1452955*116, 0.13451687*116, -0.12717809*116, 0.07406585*116, -0.22774172*116, -0.022538025*116, -0.08921177*116, 0.09264849*116, 0.038762257*116, 0.34273958*116, 0.07164061*116, -0.040298898*116, -0.1412903*116, -0.012972052*116, 0.019781347*116, 0.09433793*116, -0.12223792*116, 0.09354837*116, -0.179233*116, 0.02107923*116, -0.17972632*116, -0.15445475*116, -0.105302535*116, -0.13099296*116, 0.31741107*116, -0.17656581*116, 0.2573834*116, 0.03481798*116, -0.13409376*116, 0.028061066*116, 0.0936092*116, -0.23933095*116, -0.07292978*116, -0.027668335*116, -0.0037863052*116, -0.1592789*116, 0.09677565*116, 0.05085709*116, 0.12625311*116, 0.053333197*116, 0.12597069*116, 0.1247429*116, 0.17750488*116, 0.21141382*116, 0.24960983*116, -0.16076444*116, -0.11685088*116, -0.23709235*116, 0.037388235*116, 0.22734366*116, -0.14366598*116, -0.08898821*116, 0.28411764*116, -0.11269131*116, -0.11890494*116, -0.05873827*116, -0.102537856*116, 0.06875687*116, 0.025687966*116, 0.11999767*116, 0.11095406*116, -0.1351738*116, -0.16004013*116, -0.18795435*116, 0.062254664*116, -0.055821057*116, 0.2017833*116, 0.11751622*116, -0.1010612*116, -0.20433015*116, 0.2309856*116, -0.026833633*116, 0.15405595*116, -0.076388516*116, 0.055752207*116, 0.14030561*116, 0.0334105*116, -0.17623934*116, -0.08096098*116, -0.15270549*116, 0.016141351*116, 0.14640644*116, -0.012347365*116, 0.086325064*116, -0.009957208*116, 0.040182605*116, -0.14681286*116, -0.05443188*116, 0.17333636*116, 0.13023923*116, -0.25413767*116, 0.14882265*116, -0.102256045*116, 0.22981355*116, -0.033474945*116, -0.21052761*116, -0.15571737*116, -0.060334772*116, -0.046159223*116, -0.19531523*116, -0.010520424*116, -0.0146350125*116, -0.27916217*116, -0.03596489*116, 0.030256253*116, 0.19490975*116, 0.17307274*116, 0.15809315*116, -0.06618593*116, -0.014397639*116, -0.07614836*116, 0.21723936*116, -0.019771632*116, 0.2643516*116, 0.111164406*116, 0.008123514*116, -0.21082324*116, -0.19406018*116, -0.31047916*116, 0.15669543*116, 0.20235229*116, 0.06841101*116, -0.042952992*116, -0.28710207*116, -0.001734861*116, 0.082049526*116, 0.010760252*116, -0.13043015*116, 0.117507026*116, 0.26513797*116, -0.0057776025*116, -0.09672728*116, -0.03331785*116, 0.13741012*116, 0.016129546*116, 0.05223358*116, -0.25844344*116, 0.20324646*116, 0.16492723*116, -0.1025175*116, 0.007819072*116, -0.022795128*116, 0.0793362*116, 0.044821236*116, -0.06847911*116, 0.14913744*116, -0.21803759*116, 0.11902505*116, 0.2109428*116, 0.15305297*116, -0.17001715*116, 0.06510429*116, 0.22684021*116, 0.2973295*116, -0.03276906*116, -0.04995035*116, 0.03158674*116, 0.110922694*116, 0.04387325*116, -0.010493345*116, -0.17650981*116, 0.03786907*116, -0.23329277*116, -0.2523508*116, 0.101036586*116, 0.16091113*116, -0.067499064*116, -0.14375871*116, 0.15882106*116, -0.12814173*116, 0.013622244*116, -0.013108906*116, -0.11929776*116, 0.14985615*116, -0.118326105*116, 0.072519675*116, 0.11082231*116, 0.29237187*116, -0.09267483*116, 0.62487364*116, 0.07176668*116, -0.14012423*116, 0.06926088*116, -0.048010815*116, -0.019679517*116, 0.1525881*116, -0.19215198*116, 0.07555933*116, -0.11343706*116, -0.24580571*116, -0.12417253*116, 0.057870004*116, 0.03285441*116, -0.17893486*116, -0.02194228*116, 0.18437828*116, 0.22820352*116, -0.08403268*116, -0.28755778*116, -0.01681844*116, 0.116324924*116, -0.055824067*116, 0.117215395*116, -0.016811348*116, 0.26828253*116, -0.1430447*116, 0.08848241*116, -0.046933874*116, 0.08830055*116, -0.17332783*116, 0.060080357*116, -0.04013307*116, 0.073085606*116, 0.20017825*116, 0.12402338*116, 0.029027563*116, -0.11296743*116, 0.068295695*116, -0.19464524*116, 0.10955698*116, -0.06957362*116, 0.014755605*116, -0.05309036*116, 0.18298148*116, 0.16011314*116, 0.21236168*116, 0.033188865*116, 0.16692783*116, -0.04182343*116, 0.12555218*116, -0.17140728*116, 0.12093797*116, -0.24165963*116, -0.1078868*116, -0.14493892*116, -0.2799351*116, -0.05078486*116, 0.32155648*116, -0.13335475*116, 0.14186454*116, 0.12486106*116, -0.077561215*116, 0.2501183*116, 0.21679467*116, -0.32647547*116, 0.19915062*116, -0.16599748*116, -0.112723514*116, -0.05377997*116, 0.014444933*116, -0.08520952*116, -0.10762669*116, -0.110200725*116, 0.13766868*116, 0.08678903*116, -0.07258739*116, -0.15326153*116, 0.17518617*116, 0.0071598273*116, -0.18138309*116, -0.1390192*116, 0.18750131*116, -0.021908097*116, -0.026880402*116, -0.19467501*116, -0.10383544*116, -0.1377705*116, -0.17912097*116, -0.011001696*116, -0.0524177*116, -0.14290342*116, 0.2880257*116, -0.29967365*116, 0.07378388*116, -0.09514262*116, -0.22013314*116, -0.06400815*116, 0.07452352*116, -0.13234094*116, -0.15764649*116, -0.014419441*116, 0.09818146*116, -0.055549573*116, 0.065032765*116, -0.13258015*116, -0.3432007*116, -0.002899436*116, 0.23046948*116, 0.023574345*116, 0.011327028*116, 0.12862761*116, 0.11166785*116, -0.0029956654*116, -0.13893825*116, 0.06430033*116, -0.08981627*116, 0.02879893*116, 0.1154398*116, -0.10735587*116, -0.039501667*116, -0.19268912*116, 0.23209053*116, -0.16230893*116, 0.06340105*116, -0.09342269*116, -0.11711428*116, -0.07107365*116, -0.0339359*116, 0.1670705*116, 0.28062373*116, 0.40930712*116, 0.010874858*116, 0.08100726*116, 0.01875911*116, 0.038789365*116, 0.29867062*116, -0.163645*116, 0.045641936*116, 0.13192843*116, -0.005078496*116, -0.04814741*116, -0.16710374*116, 0.110744216*116, 0.16174659*116, 0.1319078*116, -0.21666975*116, -0.053051952*116, -0.26537433*116, 0.2780249*116, 0.0535132*116, 0.020720547*116, 0.18208201*116, 0.09269225*116, -0.019567631*116, -0.10793439*116, -0.29003853*116, 0.03457988*116, 0.1133658*116, 0.032332018*116, -0.075420365*116, 0.031961642*116, 0.08859472*116, 0.027330315*116, -0.064994745*116, 0.04072468*116, -0.00383979*116, -0.17072111*116, -0.09663522*116, -0.03691734*116, -0.008241263*116, -0.13600154*116, -0.43677825*116, 0.016865352*116, -0.23411782*116, 0.20966049*116, -0.054252975*116, -0.02755907*116, -0.099504516*116, 0.22404905*116, -0.016547859*116, -0.037314482*116, 0.14032745*116, 0.051681913*116, -0.3318995*116, -0.15288617*116, 0.13242292*116, -0.047972847*116, -0.010092901*116, -0.04898957*116, -0.18451354*116, -0.11493673*116, 0.03378199*116, 0.16837001*116, -0.12136443*116, 0.10338485*116, 0.072049946*116, 0.10993263*116, 0.009106476*116, 0.29618734*116, 0.0064378954*116, -0.3196766*116, -0.100627854*116, -0.14268641*116, 0.007881639*116, -0.06086213*116, 0.12912516*116, 0.15285532*116, 0.047563687*116, 0.25637683*116, 0.09384317*116, -0.0061304215*116, -0.08843078*116, 0.2526458*116, 0.16788915*116, 0.045138907*116, 0.05797296*116, -0.2009028*116, 0.011607862*116, 0.07232403*116, 0.29638678*116, -0.2841859*116, 0.0134124*116, -0.09934485*116, -0.08448362*116, -0.22795995*116, 0.016632695*116, -0.1116356*116, -0.029847793*116, -0.03212083*116, 0.11563892*116, -0.15556009*116, 0.22662438*116, -0.2551767*116, -0.10579893*116, -0.061472844*116, 0.13199387*116, 0.11988639*116, -0.0386996*116, -0.022780037*116, 0.12958482*116, -0.22873914*116, -0.116803326*116, 0.053578094*116, -0.20524544*116, -0.1334851*116, -0.05427182*116, -0.19272418*116, 0.08579901*116, -0.031835344*116, 0.17021018*116, -0.25171155*116, -0.04898197*116, 0.1627512*116, 0.0327937*116, 0.04014091*116, 0.013208694*116, -0.05742515*116, 0.16751088*116, 0.3821514*116, 0.047093946*116, -0.04272365*116, -0.1252531*116, 0.18953529*116, 0.13630608*116, -0.118332654*116, 0.181122*116, -0.13298905*116, 0.05568065*116, 0.13572526*116, -0.061046217*116, -0.03244996*116, 0.17821823*116, 0.10298705*116, 0.09062654*116, -0.030277122*116, -0.23816553*116, 0.19984296*116, 0.13876426*116, 0.0062630665*116, -0.012699683*116, -0.25464413*116, 0.06854798*116, -0.26574406*116, 0.012511222*116, -0.09893827*116, 0.06657128*116, 0.09073717*116, -0.1033157*116, -0.06507225*116, -0.11540182*116, -0.0072446023*116, -0.057522573*116, 0.124939956*116, -0.16132617*116, 0.03482025*116, 0.15682887*116, -0.13819525*116, -0.040296696*116, -0.12500492*116, -0.07133014*116, -0.13019344*116, 0.07626455*116, 0.12682883*116, -0.1724776*116, 0.108431876*116, -0.09439027*116, 0.054351002*116, -0.22717449*116, 0.30244833*116, 0.10272699*116, -0.050898444*116, -0.10245003*116, 0.05361507*116, -0.14259025*116, -0.08323074*116, -0.11465635*116, -0.037359204*116, 0.16910483*116, -0.20903087*116, -0.013433047*116, 0.16801277*116, -0.17652594*116, 0.1954431*116, -0.21927485*116, 0.2881323*116, 0.08352881*116, 0.1500873*116, 0.06695341*116, -0.2133848*116, 0.010794702*116, -0.2151719*116, 0.043625347*116, 0.25278553*116, -0.17836975*116, 0.26338747*116, -0.029407766*116, 0.33813357*116, -0.14390418*116, 0.023965249*116, 0.0021178415*116, 0.27666304*116, -0.064851*116, -0.026872415*116, 0.0625742*116, 0.12311672*116, 0.08428436*116, -0.3290855*116, -0.30234286*116, -0.050323498*116, -0.004226755*116, -0.19699164*116, -0.384305*116, 0.10855218*116, 0.15921883*116, -0.0051606027*116, -0.08341853*116, -0.019805385*116, 0.38057572*116, -0.1087761*116, 0.119348556*116, -0.084856525*116, -0.06931206*116, 0.14633778*116, -0.18791455*116, -0.007574609*116, -0.029576821*116, 0.034437038*116, -0.22345065*116, 0.05364954*116, -0.16150767*116, 0.13741222*116, 0.1000554*116, -0.085338965*116, -0.034446765*116, 0.12112698*116, -0.28828615*116, -0.10590777*116, 0.31226182*116, 0.053304117*116, -0.094375975*116, 0.03690632*116, 0.02722714*116, 0.1260034*116, 0.3745822*116, 0.22622249*116, -0.1346054*116, 0.041221317*116, -0.01313183*116, 0.11286814*116, 0.025816975*116, -0.1740394*116, -0.0020622218*116, -0.15075366*116, -0.19467247*116, -0.046981663*116, 0.13715641*116, 0.053205613*116, -0.01999558*116, 0.29036978*116, 0.2053611*116, 0.4121989*116, 0.05212749*116, -0.5908579*116, -0.08299574*116, 0.16975075*116, -0.039693173*116, 0.30973494*116, -0.1677575*116, 0.176748*116, 0.009662226*116, 0.27504715*116, 0.08223303*116, -0.040024735*116, 0.07305494*116, 0.15226534*116, 0.10337993*116, -0.20149833*116, -0.08887715*116, -0.07571674*116, -0.063209414*116, -0.050240718*116, -0.40626228*116, -0.22930996*116, 0.0018569236*116, 0.06434468*116, 0.011816082*116, 0.0045421836*116, -0.072159275*116, 0.08542696*116, 0.3134729*116, -0.03115204*116, -0.0035207602*116, 0.26018667*116, -0.12152367*116, 0.19125295*116, -0.20677902*116, -0.07602731*116, -0.054246224*116, 0.003869406*116, 0.10684101*116, 0.23927271*116, 0.04288436*116, -0.03241803*116, -0.2458569*116, 0.30413997*116, -0.07204872*116, 0.20661058*116, -1.0590402*116, -0.15064043*116, 0.058774024*116, -0.2716406*116, 0.045365646*116, -0.0018384884*116, -0.46961585*116, 0.32678914*116, -0.032775715*116, 0.04241443*116, 0.20657219*116, -0.11237866*116, -0.079399146*116, -0.27802855*116, 0.020148203*116, -0.08833268*116, 0.15357587*116, 0.12011546*116, 0.07719475*116, 0.08867835*116, 0.020533944*116, -0.040720113*116, -0.060297687*116, -0.0027232238*116, -0.013896688*116, 0.10283176*116, 0.19578722*116, -0.06475556*116, 0.14599875*116, -0.1583893*116, 0.084799476*116, -0.059855055*116, -0.036246635*116, -0.13419442*116, 0.026537418*116, -0.0071523995*116, -0.052254852*116, -0.032604422*116, 0.06662383*116, -0.11570938*116, 0.037723683*116, -0.11644217*116, -0.058879446*116, -0.31477693*116, -0.003293527*116, -0.031399284*116, 0.09575297*116, 0.09885415*116, 0.115417816*116, 0.049898446*116, -0.25556758*116, 0.257176*116, -0.097548276*116, -0.058983292*116, -0.008867759*116, -0.11094332*116, 0.04993275*116, 0.037297368*116, 0.22127065*116, -0.10192491*116, -0.15764807*116, -0.022816164*116, -0.1131502*116, -0.0412091*116, -0.03127263*116, 0.18355846*116, 0.21892735*116, 0.39939967*116, -0.12133023*116, -0.029767143*116, -0.104244635*116, 0.14214301*116, 0.31761634*116, -0.22575325*116, -0.026792891*116, -0.13903083*116, 0.16019179*116, 0.052338768*116, -0.20913942*116, 0.20365119*116, 0.034976766*116, 0.2972432*116, -0.29817274*116, -0.013087416*116, -0.22542311*116, 0.44749737*116, -0.09741674*116, -0.07955283*116, 0.20723604*116, -0.05735903*116, -0.18479745*116, -0.26599243*116, 0.34716785*116, -0.05497844*116, 0.07033868*116, -0.053867783*116, 0.109521925*116, 0.015746515*116, -0.052411832*116, 0.09743685*116, 0.357265*116, 0.16698824*116, 0.20003018*116, -0.046616975*116, 0.23008311*116, 0.25324517*116, 0.244373*116, -0.14193283*116, -0.024596533*116, -0.12703931*116, -0.2520717*116, 0.20022447*116, -0.02111656*116, -0.4591031*116, -0.00853526*116, -0.22083114*116, -0.20155148*116, 0.19902292*116, -0.13664776*116, -0.110959314*116, 0.4331004*116, -0.4948069*116, -0.074948534*116, -0.11598688*116, 0.45815912*116, -0.28012136*116, 0.14283562*116, -0.16995354*116, 0.08896813*116, -0.22457366*116, 0.18573791*116, 0.47555304*116, -0.16879717*116, 0.3586008*116, 0.28196746*116, -0.13149415*116, -0.06926933*116, 0.075859256*116, 0.016801782*116, -0.029531958*116, 0.33896458*116, -0.0609632*116, -0.17045084*116, -0.12349615*116, -0.5041344*116, -0.26517972*116, 0.17230336*116, 0.29229516*116, 0.0076904236*116, -0.27375245*116, -0.1004029*116, -0.13524932*116, 0.16734488*116, 0.041345257*116, 0.12900403*116, -0.013706222*116, 0.23468812*116, -0.13037935*116, 0.0740649*116, 0.1742663*116, -0.25156513*116, 0.006371334*116, -0.09233004*116, 0.00880529*116, -0.2100686*116, 0.045230657*116, -0.03345281*116, -0.13469549*116, 0.116502576*116, -0.11624581*116, 0.0006959617*116, -0.31547526*116, -0.0070059844*116, 0.23746435*116, 0.09651955*116, -0.23049311*116, 0.11986147*116, -0.051551115*116, -0.29713964*116, -0.13770147*116, -0.24037887*116, -0.0941856*116, 0.06602678*116, -0.15998113*116, 0.023995709*116, 0.16596988*116, 0.30767426*116, 0.041926958*116, 0.03178321*116, 0.16164188*116, -0.21991299*116, 0.082517974*116, -0.07078089*116, -0.14054811*116, 0.23945627*116, 0.22839883*116, -0.01356985*116, -0.027039997*116, -0.074699186*116, 0.10055681*116, 0.33813947*116, -0.29609627*116, 0.18754703*116, -0.20054239*116, -0.024173513*116, 0.031021984*116, -0.19711377*116, -0.09950105*116, 0.48311839*116, 0.0041518253*116, -0.218375*116, -0.048813783*116, 0.030544791*116, 0.35474265*116, 0.035502084*116, 0.10172835*116, 0.15980883*116, 0.052488893*116, 0.16860406*116, 0.080111295*116, 0.17330466*116, 0.12672225*116, -0.087736666*116, 0.20714231*116, -0.086360425*116, 0.10446849*116, -0.09795051*116, -0.11703444*116, -0.10506288*116, -0.2226276*116, 0.12866656*116, -0.13336548*116, 0.1239464*116, 0.033373825*116, -0.15936537*116, 0.05154812*116, 0.23161839*116, 0.1259785*116, 0.17785282*116, -0.10855111*116, 0.18818381*116, -0.15754366*116, -0.072268926*116, -0.19298552*116, -0.12919718*116, -0.1437668*116, -0.09717187*116, -0.03697639*116, 0.24814564*116, 0.070470944*116, -0.0056029772*116, 0.20351624*116, -0.11155671*116, 0.20847581*116, -0.20452371*116, 0.1900843*116, 0.0077334153*116, -0.22517519*116, 0.1204092*116, 0.2414414*116, 0.0073544113*116, -0.20188932*116, 0.0066002365*116, -0.22733018*116, 0.20402056*116, -0.07359332*116, 0.30680037*116, 0.19399512*116, 0.06480572*116, -0.09931632*116, -0.07325644*116, -0.24215443*116, 0.1410375*116, 0.07566291*116, -0.15293567*116, 0.042128496*116, -0.08676096*116, -0.025878005*116, -0.111991785*116, 0.14195281*116, 0.08489239*116, 0.15964924*116, 0.1205121*116, 0.1307555*116, 0.12544689*116, 0.04431717*116, -0.17315623*116, 0.087264195*116, 0.06342937*116, 0.19828789*116, 0.059196167*116, -0.06309908*116, 0.1258182*116, -0.10803998*116, 0.20509954*116, -0.11369744*116, -0.033357974*116, 0.13731016*116, 0.05332397*116, -0.08126692*116, 0.19143991*116, 0.086307034*116, 0.21870005*116, -0.18771781*116, -0.007117113*116, -0.11258181*116, 0.11298923*116, 0.03361045*116, 0.1837657*116, -0.19122839*116, -0.038168192*116, -0.0011348396*116, -0.01693708*116, -0.12150482*116, 0.056299422*116, 0.008188132*116, 0.2447546*116, -0.042445138*116, -0.08500408*116, 0.26959768*116, 0.12333474*116, -0.27776945*116, -0.006568294*116, 0.106152475*116, 0.0404053*116, -0.044294067*116, -0.07005718*116, 0.021877104*116, -0.045115557*116, -0.12689254*116, 0.22434491*116, -0.01724809*116, -0.10488671*116, -0.108488455*116, 0.0671391*116, -0.20143466*116, 0.004346772*116, 0.012482483*116, 0.042444155*116, -0.21900518*116, -0.0025609504*116, -0.1140566*116, -0.13394748*116, 0.0700245*116, 0.011962489*116, 0.08506195*116, -0.052277997*116, 0.06281433*116, -0.31635496*116, -0.06688106*116, -0.23716585*116, 0.20850232*116, -0.11947649*116, 0.061509408*116, 0.2504488*116, -0.073452756*116, 0.24756296*116, -0.17655951*116, 0.17517817*116, -0.11029104*116, 0.014247981*116, -0.0040007527*116, 0.09046686*116, 0.18163492*116, 0.104198486*116, -0.03248904*116, 0.15361264*116, -0.008561629*116, 0.28070882*116, -0.13903072*116, -0.015490468*116, -0.049587674*116, 0.05349082*116, -0.06957408*116, 0.15257967*116, -0.10975436*116, -0.16845743*116, 0.14017408*116, 0.24789315*116, -0.11368522*116, 0.17854819*116, 0.1520082*116, -0.19552979*116, 0.31135106*116, 0.1991374*116, -0.40719056*116, -0.07172308*116, -0.14276402*116, 0.059599396*116, -0.07891586*116, -0.12641235*116, -0.067772225*116, -0.1639058*116, 0.11276073*116, -0.0077249687*116, 0.13472636*116, -0.052996874*116, -0.31015843*116, 0.31694376*116, -0.22649747*116, 0.027658727*116, -0.03403287*116, 0.29753253*116, -0.25960353*116, 0.026600769*116, -0.26310676*116, 0.051013958*116, -0.07492256*116, -0.26920816*116, -0.05562583*116, -0.12738311*116, -0.13090307*116, -0.3592752*116, -0.11531557*116, 0.0831234*116, -0.120807424*116, 0.09922801*116, 0.013038218*116, 0.18478593*116, -0.05236839*116, 0.1743138*116, 0.051883403*116, -0.022737525*116, -0.3722952*116, 0.31642434*116, 0.07055215*116, 0.22338274*116, 0.16772692*116, -0.2263859*116, -0.13852364*116, -0.016411614*116, -0.118231475*116, 0.11028384*116, -0.26892397*116, 0.21283312*116, -0.15262249*116, -0.14612213*116, 0.111628205*116, 0.19853488*116, 0.002181381*116, 0.067841694*116, -0.038458686*116, -0.059568614*116, 0.0052195224*116, 0.20714584*116, 0.229932*116, 0.021094332*116, 0.012898167*116, -0.10444177*116, -0.06276046*116, -0.2802324*116, -0.32799104*116, -0.020054026*116, 0.05659156*116, 0.05120218*116, 0.24334703*116, -0.3182509*116, -0.07241388*116, -0.2117253*116, 0.14459532*116, 0.06960496*116, -0.11330919*116, 0.1613982*116, -0.036509767*116, -0.1407457*116, 0.03970047*116, 0.22707124*116, -0.026487496*116, -0.021616014*116, -0.08821986*116, 0.0315165*116, 0.081857584*116, -0.16651124*116, -0.17388938*116, -0.031552084*116, 0.07367315*116, -0.06985758*116, -0.03929223*116, 0.094589286*116, -0.012249244*116, 0.027428325*116, 0.16463107*116, -0.032064177*116, 0.22349887*116, 0.22565068*116, 0.09000431*116, 0.01727892*116, 0.08002498*116, -0.1701425*116, 0.14808479*116, 0.13071175*116, -0.021894228*116, 0.023158662*116, -0.1782117*116, -0.23391677*116, 0.13695402*116, 0.07121468*116, -0.12316211*116, 0.061374865*116, -0.13744207*116, 0.188121*116, 0.21727417*116, 0.19226372*116, 0.0055118953*116, 0.04743964*116, 0.12831207*116, -0.27176178*116, 0.19070874*116, 0.101219065*116, -0.26381913*116, 0.17503208*116, -0.21444368*116, 0.113152206*116, -0.011118086*116, -0.16142842*116, -0.39373264*116, 0.006964899*116, -0.118946746*116, 0.015358379*116, -0.2863498*116, -0.13703188*116, 0.21901096*116, -0.13386314*116, 0.0360395*116, -0.13411987*116, 0.2591982*116, 0.0024023193*116, -0.31649637*116, -0.16466725*116, -0.31534413*116, 0.10639767*116, 0.028427914*116, 0.3342591*116, -0.3348893*116, 0.12455871*116, -0.29253742*116, -0.0029720485*116, -0.00571676*116, -0.08317521*116, 0.0990031*116, -0.033967476*116, -0.02406414*116, -0.23078011*116, -0.019434692*116, 0.09293624*116, -0.09837925*116, -0.011612068*116, 0.070779905*116, 0.06332826*116, 0.008444317*116, 0.2058711*116, -0.050023098*116, -0.16658731*116, 0.1545053*116, 0.010182477*116, 0.099594794*116, 0.063396715*116, 0.1951333*116, 0.15845028*116, 0.2823473*116, 0.08777438*116, -0.024206912*116, -0.15689789*116, 0.07745619*116, 0.22280478*116, -0.055650532*116, 0.11288788*116, -0.19642447*116, -0.071959056*116, 0.11894853*116, 0.039570365*116, -0.1365327*116, 0.10570944*116, -0.045990866*116, -0.0030909444*116, -0.028206324*116, 0.15499687*116, -0.22442561*116, 0.09856955*116, 0.14965746*116, -0.017658386*116, -0.18952654*116, -0.1444406*116, -0.05385421*116, 0.016670303*116, 0.1963128*116, 0.10891742*116, 0.123315744*116, -0.021044556*116, -0.23865625*116, 0.075393*116, 0.030749233*116, -0.0903604*116, -0.16159976*116, -0.04141442*116, -0.26474902*116, 0.056177694*116, 0.11722372*116, 0.0561892*116, -0.0520646*116, -0.12168552*116, 0.13515586*116, -0.20251858*116, -0.022112504*116, 0.091334045*116, -0.032083474*116, -0.16105169*116, -0.067213826*116, -0.024765212*116, -0.044447504*116, 0.05046784*116, -0.0096298205*116, 0.11295918*116, -0.11165129*116, 0.20432828*116, 0.006578725*116, 0.0660536*116, -0.009178082*116, -0.022883004*116, 0.121718384*116, 0.45362467*116, 0.21132158*116, 0.26100352*116, 0.1465062*116, -0.05087862*116, 0.04034686*116, -0.08109111*116, -0.11773636*116, 0.020602815*116, -0.20287381*116, 0.063540086*116, -0.20733884*116, 0.07468195*116, -0.050009754*116, 0.22039123*116, -0.08341689*116, 0.28327218*116, 0.20305952*116, -0.04938619*116, 0.21788922*116, 0.05468176*116, -0.28948137*116, 0.33647388*116, 0.06799988*116, -0.10820517*116, -0.13373815*116, -0.080196925*116, -0.21409085*116, -0.10229332*116, 0.2689621*116, 0.13605589*116, 0.07180871*116, 0.049976178*116, -0.07392403*116, -0.045910474*116, -0.061069958*116, -0.0870677*116, -0.2654056*116, -0.14701231*116, -0.10426384*116, 0.19983564*116, -0.25963295*116, 0.01516441*116, -0.008783381*116, -0.027396858*116, 0.1440635*116, -0.10534161*116, -0.09606573*116, -0.054107927*116, 0.23711272*116, 0.016692512*116, 0.1042609*116, 0.13581839*116, -0.008772632*116, 0.15455253*116, -0.20288192*116, -0.09207037*116, 0.06150726*116, -0.04293591*116, -0.17570135*116, 0.30769518*116, 0.058365475*116, 0.11533179*116, -0.11962424*116, 0.036596037*116, -0.067324184*116, 0.050214157*116, -0.34674343*116, -0.20292117*116, 0.016585857*116, 0.021998115*116, 0.05998299*116, -0.12868948*116, -0.065898955*116, -0.24488929*116, 0.14564186*116, 0.08936582*116, -0.115682155*116, -0.02603768*116, 0.06321265*116, 0.105994895*116, 0.1781971*116, 0.04751632*116, 0.23795469*116, -0.07040563*116, 0.03398196*116, -0.18071184*116, -0.14156647*116, 0.06238736*116, 0.13995723*116, 0.024143444*116, 0.068041325*116, 0.031058952*116, -0.19122137*116, 0.09054179*116, -0.067582116*116, 0.39564785*116, -0.06933413*116, -0.013987353*116, -0.1530329*116, 0.11449443*116, 0.009684118*116, -0.1308573*116, -0.07041299*116, 0.15781444*116, 0.14568268*116, -0.23701258*116, 0.0751733*116, 0.13362202*116, -0.01157053*116, -0.035048056*116, -0.05670982*116, 0.34078714*116, -0.23607874*116, -0.03240781*116, 0.114782535*116, 0.08953391*116, -0.062293265*116, -0.12105807*116, -0.2238098*116, 0.034924712*116, 0.16387591*116, 0.15662679*116, -0.047746576*116, -0.029822536*116, 0.030098198*116, -0.42200354*116, 0.019621762*116, 0.06782407*116, -0.06950474*116, 0.06014777*116, 0.28220102*116, 0.19325483*116, -0.18553172*116, -0.0316592*116, 0.089826465*116, -0.062637165*116, -0.11149006*116, -0.03253613*116, -0.19930378*116, 0.0074030356*116, -0.23603876*116, -0.03633174*116, -0.31017303*116, 0.118555844*116, 0.047146883*116, -0.18412773*116, -0.108415544*116, 0.044234324*116, 0.076785855*116, 0.023223197*116, 0.42171308*116, -0.059515808*116, 0.24940886*116, -0.054404702*116, 0.08475308*116, 0.08317315*116, -0.09993805*116, 0.034142338*116, -0.047076527*116, 0.011873098*116, -0.024710035*116, -0.17689109*116, 0.21133824*116, -0.011248264*116, -0.03550418*116, 0.10970854*116, 0.17626029*116, -0.35914528*116, 0.122814745*116, 0.115588665*116, 0.061406355*116, 0.020669274*116, 0.07527042*116, -0.06817893*116, -0.1626576*116, 0.074306816*116, -0.08437801*116, 0.04636975*116, -0.26611045*116, -0.16834421*116, 0.11254724*116, 0.056516867*116, -0.026038593*116, -0.18316348*116, 0.14390253*116, 0.17002976*116, -0.1816219*116, 0.076190524*116, 0.08614767*116, 0.11283142*116, 0.07973836*116, 0.17899229*116, -0.021109954*116, 0.007453591*116, -0.35019904*116, -0.106181756*116, 0.036963586*116, -0.073910825*116, 0.05872362*116, -0.22692981*116, 0.12902822*116, -0.24564248*116, 0.16015807*116, -0.0720423*116, 0.0435372*116, -0.00922671*116, -0.05456824*116, 0.13901573*116, 0.37730002*116, 0.063301526*116, -0.06278725*116, 0.55402493*116, 0.03275682*116, -0.09682348*116, -0.21561103*116, -0.077359706*116, 0.20851839*116, 0.117397845*116, 0.018670201*116, -0.11063818*116, 0.05478582*116, 0.044793133*116, 0.03128943*116, 0.065233275*116, 0.14066164*116, 0.03978121*116, -0.13025665*116, 0.34545568*116, 0.17616373*116, -0.032440174*116, -0.14251857*116, 0.02103511*116, -0.14585671*116, 0.014953354*116, 0.13629223*116, -0.023962079*116, -0.014317464*116, -0.6882449*116, -0.091151476*116, 0.0026000605*116, -0.16125752*116, 0.030110104*116, -0.1473109*116, -0.009934809*116, -0.11429511*116, -0.13338503*116, 0.15735638*116, 0.1441854*116, 0.12157255*116, -0.04545883*116, 0.2793022*116, -0.05473475*116, -0.119236134*116, -0.062412817*116, 0.07196675*116, -0.090974644*116, -0.31390193*116, 0.20909758*116, 0.026172157*116, -0.19485752*116, -0.013598383*116, -0.17588294*116, 0.20020448*116, -0.06590742*116, 0.1621397*116, -0.023549251*116, 0.083243266*116, -0.085688464*116, 0.095621206*116, 0.25324813*116, -0.14822902*116, 0.18736997*116, 0.02193455*116, 0.01888789*116, 0.16377789*116, -0.064782366*116, 0.113454066*116, -0.060293872*116, 0.20078725*116, -0.3292392*116, 0.15989515*116, -0.11774247*116, 0.067129694*116, -0.12192484*116, 0.19068071*116, 0.09729956*116, -0.4083744*116, 0.10505451*116, 0.1047865*116, -0.14673686*116, 0.09812076*116, -0.011996785*116, -0.01482886*116, 0.009897951*116, -0.09510329*116, 0.10315915*116, 0.055315766*116, 0.01837923*116, -0.25633973*116, 0.028619159*116, -0.15504359*116, 0.09458034*116, 0.016809002*116, 0.019331833*116, 0.05620121*116, 0.15817149*116, -0.09012898*116, 0.004833431*116, -0.08324643*116, -0.1543917*116, 0.049755927*116, -0.09437768*116, 0.21513906*116, -0.07244416*116, 0.22813305*116, 0.12802899*116, -0.07527842*116, -0.0035750568*116, 0.16783929*116, 0.017786885*116, -0.20438868*116, -0.024486324*116, 0.09023786*116, -0.21065098*116, 0.017831832*116, 0.0676832*116, -0.044472765*116, -0.062433865*116, 0.19315468*116, -0.18069263*116, -0.10058965*116, 0.10466731*116, 0.12603658*116, -0.21475224*116, -0.091462135*116, 0.029530587*116, -0.17787695*116, 0.1417738*116, -0.17956041*116, -0.10925609*116, 0.2914432*116, -0.008952746*116, -0.18923819*116, -0.15135811*116, 0.1440722*116, 0.08378685*116, -0.13282321*116, 0.07651645*116, 0.030315273*116, -0.2723958*116, -0.18452677*116, -0.025299238*116, 0.07521591*116, -0.031556148*116, -0.0018829321*116, 0.01257068*116, 0.14285176*116, -0.09889187*116, 0.14492312*116, -0.12068807*116, 0.101287425*116, 0.100805506*116, 0.056414574*116, 0.08860654*116, 0.0008825993*116, -0.08779185*116, -0.17938954*116, -0.11167058*116, -0.07520696*116, 0.18649118*116, 0.16081998*116, -0.1299976*116, 0.062788844*116, -0.07287325*116, -0.092661016*116, -0.15104234*116, -0.019481737*116, 0.019207057*116, 0.018247606*116, -0.014561831*116, 0.09187124*116, 0.06721315*116, -0.25621107*116, 0.07192036*116, -0.13432847*116, 0.09125021*116, 0.03932414*116, 0.08168648*116, -0.06863558*116, -0.24160546*116, 0.09560009*116, -0.123478524*116, -0.050314046*116, -0.19617781*116, 0.07997274*116, -0.036807034*116, -0.1520631*116, 0.28172418*116, 0.124308616*116, -0.11813125*116, 0.05215153*116, -0.2192169*116, 0.055761952*116, 0.03924252*116, 0.19890639*116, 0.24056351*116, 0.35953838*116, -0.053891644*116, -0.006803776*116, -0.10987875*116, 0.14307438*116, 0.39906904*116, -0.07401391*116, 0.21030147*116, 0.012559946*116, 0.025747646*116, -0.050899778*116, -0.22998157*116, 0.28920093*116, 0.025015356*116, 0.22029914*116, -0.16634881*116, -0.12599*116, 0.043532833*116, 0.18915771*116, -0.20281081*116, 0.04691049*116, 0.12704231*116, 0.07397067*116, -0.09015135*116, 0.08677341*116, -0.14050116*116, 0.002585925*116, -0.099361345*116, -0.14673641*116, 0.15705065*116, -0.15839115*116, -0.11690511*116, 0.083883874*116, 0.120491914*116, 0.071799405*116, 0.30358618*116, 0.1726248*116, -0.08266743*116, -0.15826467*116, 0.12562783*116, 0.5037448*116, -0.26511776*116, -0.19464858*116, -0.18948534*116, 0.48509282*116, -0.1551149*116, -0.0018847043*116, 0.06975043*116, 0.03484569*116, 0.3226139*116, 0.07633098*116, 0.4681082*116, -0.1249776*116, -0.12744546*116, -0.064634226*116, -0.13474672*116, -0.1408021*116, 0.1280046*116, -0.2962227*116, 0.1736429*116, 0.09002839*116, 0.050816435*116, -0.08044465*116, -0.10549997*116, -0.1445349*116, 0.087861896*116, -0.101543516*116, 0.14468805*116, -0.22442621*116, -0.48902386*116, 0.15988523*116, -0.2702704*116, 0.090062775*116, -0.08704234*116, 0.009325087*116, 0.11984701*116, -0.029406507*116, -0.2907008*116, -0.022439523*116, 0.28926086*116, -0.18675688*116, -0.2309978*116, -0.19755149*116, 0.20447661*116, -0.1070909*116, -0.31092444*116, 0.12874337*116, 0.050417285*116, -0.06613224*116, 0.09473943*116, 0.039652508*116, 0.04767017*116, -0.1298084*116, -0.18269385*116, 0.14051971*116, 0.008077815*116, -0.17538647*116, -0.06025537*116, 0.07804881*116, 0.24845*116, -0.07262328*116, -0.04126505*116, -0.10733873*116, 0.16843386*116, -0.05943265*116, 0.032710567*116, -0.18121798*116, 0.10982873*116, -0.18164146*116, -0.25423717*116, 0.071337014*116, 0.11856373*116, 0.12015175*116, 0.07127541*116, 0.012551329*116, -0.19491413*116, 0.038049527*116, -0.054602727*116, 0.008735005*116, -0.040268112*116, -0.10007035*116, -0.074078776*116, 0.25864485*116, 0.048908766*116, -0.08353107*116, 0.6728367*116, -0.099458516*116, -0.10009568*116, -0.0021586888*116, -0.066831976*116, 0.1917072*116, 0.06390419*116, -0.15760419*116, -0.01524996*116, -0.16920038*116, -0.024095973*116, 0.088670835*116, 0.19236457*116, 0.07549094*116, 0.07961057*116, -0.06118017*116, 0.20055796*116, 0.19924334*116, -0.044726532*116, -0.27456653*116, -0.03985287*116, 0.16407463*116, -0.1321457*116, 0.026469959*116, -0.028661603*116, 0.08208573*116, -0.0017641948*116, 0.10158433*116, -0.13798837*116, -0.211835*116, -0.07714868*116, -0.25284323*116, 0.0054595373*116, -0.030461356*116, -0.06619117*116, -0.2166341*116, -0.13625787*116, 0.12793933*116, 0.076407604*116, -0.054914117*116, 0.15481144*116, -0.104742646*116, 0.050083824*116, 0.21558735*116, -0.10984894*116, -0.032489408*116, 0.08778482*116, -0.091342196*116, 0.006911205*116, 0.07888321*116, -0.17315215*116, 0.018047784*116, -0.10191084*116, -0.19718067*116, 0.42180035*116, 0.012758406*116, 0.017280104*116, 0.08895085*116, 0.09586776*116, -0.33702013*116, 0.1744302*116, 0.21232878*116, -0.018627651*116, 0.009688024*116, -0.079572216*116, -0.14458175*116, -0.12700771*116, 0.013169172*116, -0.015505177*116, 0.17179546*116, 0.004590247*116, 0.19801398*116, -0.18255606*116, 0.044377528*116, -0.008773512*116, -0.06943856*116, -0.22425583*116, 0.033527695*116, 0.19942658*116, -0.04539203*116, -0.048764955*116, 0.19110388*116, 0.31006387*116, 0.038596652*116, -0.17397186*116, 0.23990442*116, 0.3706763*116, 0.1734853*116, -0.20789345*116, 0.003995158*116, -0.948603*116, -0.14059572*116, 0.23629968*116, 0.005610352*116, 0.064643376*116, -0.17206357*116, -0.014392053*116, -0.33836824*116, -0.016879227*116, -0.039615117*116, 0.1013558*116, 0.11699319*116, 0.07420329*116, 0.23165293*116, 0.012695801*116, -0.03226844*116, 0.12734923*116, -0.059198827*116, -0.09268437*116, -0.2483974*116, 0.31285706*116, 0.2441376*116, -0.21380334*116, -0.1959612*116, -0.15455607*116, 0.11590599*116, 0.088979736*116, -0.004164397*116, -0.013095691*116, 0.04391745*116, 0.012596817*116, 0.026793515*116, 0.2541076*116, -0.18128195*116, 0.25015286*116, 0.08307483*116, 0.057434764*116, -0.08411525*116, 0.012617335*116, 0.03482525*116, 0.16659816*116, 0.053539477*116, -0.35874185*116, 0.14285354*116, 0.042666353*116, 0.19058959*116, -0.011259017*116, 0.20092255*116, 0.060743544*116, -0.5406083*116, 0.07819866*116, -0.045852125*116, -0.075251065*116, 0.06687889*116, -0.096021354*116, -0.15025227*116, 0.095691256*116, -0.00089312496*116, -0.11632415*116, -0.035844047*116, 0.0013970347*116, -0.08948836*116, 0.11335034*116, -0.07855052*116, -0.0688217*116, -0.18272454*116, -0.41314048*116, -0.025479458*116, 0.3260049*116, 0.20368984*116, 0.17581114*116, 0.16608371*116, -0.08919725*116, 0.010889798*116, 0.08995384*116, -0.02342488*116, 0.035528857*116, -0.3174359*116, 0.06239261*116, -0.12472999*116, 0.3355502*116, -0.180821*116, -0.28988102*116, -0.123253316*116, -0.25453368*116, -0.08595795*116, 0.101777144*116, -0.11777753*116, -0.21561289*116, 0.036235493*116, -0.21232551*116, -0.09725238*116, 0.09472666*116, -0.05747782*116, -0.19228035*116, -0.1524867*116, 0.14554426*116, -0.0017740784*116, 0.1058596*116, 0.08741611*116, -0.15145892*116, 0.18984477*116, 0.030779656*116, 0.18136711*116, -0.2591158*116, -0.22145551*116, 0.046979737*116, 0.15095139*116, -0.013440439*116, 0.22981954*116, 0.058509734*116, -0.17481938*116, 0.16366702*116, 0.07984271*116, -0.102082565*116, -0.04376499*116, 0.20981371*116, 0.034715623*116, -0.069546394*116, 0.32796493*116, 0.11604919*116, -0.09818357*116, -0.13910198*116, 0.06468389*116, 0.062237956*116, 0.12642474*116, 0.029167749*116, -0.12710974*116, -0.7460742*116, -0.18760285*116, 0.06896039*116, 0.04681731*116, -0.097220354*116, 0.029231144*116, 0.09429863*116, -0.31874087*116, 0.07404684*116, -0.024718847*116, -0.03607746*116, -0.10341053*116, 0.09454206*116, 0.03340312*116, -0.14312346*116, -0.1259054*116, 0.06796752*116, 0.039591007*116, -0.19197972*116, -0.21001063*116, 0.01583694*116, 0.20087118*116, -0.006722076*116, 0.07179476*116, -0.16452928*116, 0.06279742*116, -0.1440791*116, -0.06466061*116, -0.01078958*116, -0.10087545*116, -0.16790119*116, 0.06533122*116, 0.21535335*116, -0.018598568*116, 0.020080967*116, 0.040747717*116, 0.08575123*116, 0.21026729*116, -0.057694893*116, 0.13934597*116, -0.04350374*116, 0.10738409*116, -0.18593763*116, 0.016734255*116, -0.042252142*116, 0.03605685*116, -0.07479437*116, 0.120693155*116, -0.077879615*116, -0.3032186*116, 0.17077635*116, 0.12204674*116, -0.0030147973*116, 0.33423996*116, -0.0008919053*116, 0.056163836*116, 0.019576967*116, -0.027949419*116, -0.024297604*116, 0.15141341*116, 0.027420156*116, -0.11228538*116, 0.19393127*116, -0.009267525*116, -0.06610581*116, -0.17495063*116, -0.20333076*116, -0.14438729*116, 0.09514033*116, -0.072466284*116, -0.023351165*116, 0.034829464*116, 0.21857154*116, -0.25036266*116, -0.0063504907*116, 0.19212237*116, 0.3049542*116, -0.117562525*116, 0.22181217*116, 0.07658891*116, -0.0082426425*116, 0.2011554*116, -0.17919706*116, -0.16646068*116, 0.051530987*116, -0.2314529*116, -0.19641879*116, -0.12055933*116, 0.10196969*116, 0.033612005*116, -0.20964326*116, -0.02934587*116, -0.12497305*116, 0.15403903*116, 0.028449219*116, 0.07143164*116, 0.19741626*116, -0.17273717*116, 0.014490273*116, 0.11473259*116, 0.011990912*116, -0.049150042*116, 0.38316566*116, 0.14888847*116, 0.011225432*116, -0.3207381*116, 0.013547885*116, 0.15504555*116, 0.014757656*116, -0.13891232*116, 0.024435958*116, 0.122750975*116, -0.076457605*116, -0.049985368*116, 0.040658403*116, 0.12826017*116, -0.027270902*116, 0.11965058*116, 0.14106432*116, 0.002988322*116, 0.005615391*116, -0.281529*116, 0.16977854*116, -0.051784173*116, 0.10640754*116, 0.079011194*116, 0.008366301*116, 0.2835486*116, -0.24201353*116, -0.053197574*116, -0.1701092*116, -0.09135158*116, -0.10164422*116, -0.05009268*116, 0.091465496*116, 0.08926565*116, 0.21031892*116, 0.098088585*116, 0.1465929*116, -0.10970931*116, 0.1822646*116, -0.18088865*116, 0.15343493*116, 0.039523695*116, -0.10012181*116, 0.047743633*116, 0.10243994*116, 0.2214087*116, 0.0342363*116, 0.14503007*116, 0.21272056*116, 0.04094657*116, -0.12468213*116, -0.16718832*116, 0.11639047*116, -0.23836926*116, -0.10863007*116, -0.21254629*116, -0.04974533*116, -0.11119816*116, 0.25541726*116, -0.13059795*116, 0.18050109*116, 0.1460344*116, -0.21444924*116, 0.2778576*116, 0.06074263*116, -0.25177842*116, 0.0641124*116, -0.11848681*116, -0.09335278*116, 0.040560294*116, 0.020764401*116, 0.08941271*116, -0.00521315*116, 0.17224395*116, 0.15227342*116, 0.071848676*116, -0.10586227*116, -0.22616342*116, -0.0010396944*116, -0.24997921*116, -0.09740062*116, -0.13822775*116, 0.09493107*116, -0.07185744*116, 0.07343289*116, 0.06748082*116, 0.041020945*116, 0.027080033*116, -0.025876952*116, 0.0329806*116, 0.0729798*116, -0.13638842*116, 0.12287663*116, 0.21430072*116, -0.069959596*116, -0.014860963*116, -0.05959427*116, 0.016074521*116, 0.050418943*116, -0.05239767*116, 0.038016107*116, 0.04668204*116, -0.11087755*116, 0.10857553*116, 0.09001636*116, -0.089351095*116, 0.11999462*116, 0.035985503*116, -0.1510329*116, -0.109213345*116, 0.10039726*116, -0.27714115*116, -0.25644737*116, 0.17428544*116, 0.23080087*116, -0.11599921*116, -0.026799487*116, 0.06478988*116, -0.00045876257*116, 0.15100887*116, -0.115055814*116, 0.11428519*116, -0.13151096*116, 0.23813666*116, -0.08167129*116, -0.057126846*116, 0.23938479*116, -0.19991615*116, 0.005997783*116, 0.18556634*116, -0.173849*116, -0.16114166*116, -0.02023011*116, -0.045702342*116, 0.20204395*116, -0.23451237*116, 0.10838912*116, -0.06326601*116, -0.11531503*116, -0.10244887*116, -0.10615454*116, 0.21322922*116, -0.13042569*116, 0.063836865*116, -0.0833215*116, 0.041537203*116, 0.03275697*116, -0.009855629*116, 0.121806294*116, -0.15854698*116, 0.109301314*116, 0.011519428*116, -0.027180053*116, 0.08983426*116, -0.03457522*116, 0.22853735*116, 0.20274901*116, -0.09908597*116, -0.08753264*116, 0.088227615*116, 0.06666232*116, -0.035638794*116, -0.19040947*116, -0.040216576*116, -0.13758333*116, -0.15493181*116, 0.045046456*116, -0.03867831*116, -0.07104146*116, 0.10352929*116, 0.02086251*116, 0.016571032*116, 0.33969697*116, 0.13007331*116, 0.24603894*116, -0.03127299*116, 0.13881983*116, -0.0047459314*116, 0.14761317*116, -0.14864525*116, 0.1003804*116, 0.090070754*116, -0.10747615*116, 0.0515488*116, 0.43786535*116, -0.08890731*116, 0.038549047*116, 0.088045776*116, 0.06470194*116, 0.08604847*116, -0.111172505*116, -0.0034962127*116, -0.08018851*116, -0.26459858*116, 0.15085492*116, 0.093507476*116, 0.17312215*116, -0.2691992*116, -0.10915277*116, -0.22050473*116, 0.09934946*116, 0.11060548*116, -0.08758788*116, 0.1674902*116, -0.17211533*116, 0.06944288*116, 0.052747898*116, -0.072921686*116, 0.045192078*116, -0.26609126*116, -0.023663975*116, -0.019756258*116, 0.20741947*116, -0.2745696*116, 0.088914365*116, -0.015619681*116, -0.1179662*116, 0.1885496*116, 0.21557702*116, -0.1906565*116, 0.07159736*116, 0.12729752*116, -0.03249059*116, 0.02227461*116, -0.22123523*116, -0.10763367*116, 0.20554417*116, -0.0900366*116, -0.09288174*116, -0.17133868*116, -0.074449405*116, 0.13800985*116, -0.05881592*116, 0.174827*116, -0.06868886*116, -0.41301772*116, 0.09956118*116, 0.04404395*116, 0.13626115*116, -0.09306348*116, 0.14241572*116, 0.1019612*116, -0.12686929*116, 0.28369957*116, -0.19451955*116, -0.14839284*116, -0.30433697*116, -0.062661916*116, -0.16765508*116, 0.1173547*116, 0.18445835*116, 0.080596924*116, -0.152559*116, 0.02946782*116, -0.332165*116, 0.18500914*116, -0.005263956*116, 0.0084316125*116, 0.26934567*116, 0.1875009*116, 0.061271448*116, 0.12605897*116, 0.07894123*116, 0.01082483*116, 0.544812*116, -0.17296208*116, 0.061817568*116, -0.17025456*116, 0.16029385*116, -0.22626616*116, -0.0695871*116, -0.0032846334*116, 0.031430338*116, 0.3278205*116, -0.13775685*116, -0.095142655*116, 0.049671125*116, 0.49270868*116, -0.2359791*116, 0.14711376*116, -0.013000825*116, 0.04719583*116, 0.1124652*116, -0.05040828*116, -0.10763196*116, 0.1570181*116, -0.020974932*116, -0.0013506287*116, -0.24291553*116, 0.18833268*116, 0.19429451*116, 0.24675672*116, 0.037591737*116, -0.16193701*116, 0.040701207*116, 0.20254606*116, -0.1288062*116, 0.30237722*116, 0.0012795016*116, -0.24405578*116, 0.012744007*116, 0.1492071*116, -0.26006374*116, -0.14852935*116, -0.041831926*116, 0.102013886*116, -0.2545153*116, 0.20851025*116, 0.005831692*116, -0.08946877*116, -0.47884822*116, -0.021004576*116, -0.0001366095*116, -0.15710396*116, 0.36546168*116, 0.3936714*116, -0.1472849*116, -0.12409375*116, -0.078915015*116, 0.022633953*116, -0.0849471*116, 0.3797528*116, 0.17367177*116, 0.024737844*116, -0.13359174*116, -0.06854574*116, 0.10290917*116, -0.20402591*116, 0.55149806*116, -0.10631334*116, 0.17568894*116, -0.15145268*116, 0.14264455*116, -0.079656534*116, -0.09825357*116, 0.07092646*116, -0.020847976*116, 0.15703592*116, -0.32112795*116, -0.15048651*116, 0.021597622*116, 0.24575499*116, -0.1307239*116, 0.0010873926*116, 0.067996345*116, -0.039170627*116, 0.18062319*116, 0.067318015*116, -0.15789545*116, -0.17678086*116, 0.19603366*116, 0.19523357*116, 0.017457627*116, -0.03296177*116, -0.11929744*116, 0.053464837*116, -0.102928996*116, -0.009732935*116, -0.22385232*116, -0.04427362*116, -0.05783129*116, -0.015279266*116, 0.122007854*116, -0.26982892*116, 0.014550151*116, 0.15603876*116, 0.11729454*116, 0.058320403*116, -0.04951858*116, 0.13147397*116, -0.13493565*116, -0.0392454*116, -0.0011383123*116, -0.041264422*116, -0.24456911*116, 0.20903529*116, 0.07827917*116, 0.008957511*116, 0.07129397*116, 0.20987792*116, -0.0389453*116, 0.050448373*116, -0.3758976*116, -0.17757925*116, -0.1455769*116, 0.026102524*116, 0.061709348*116, 0.041566435*116, 0.073646486*116, -0.29217678*116, -0.20045614*116, 0.09675975*116, 0.31560555*116, -0.26204887*116, 0.3909813*116, -0.09690568*116, -0.22111738*116, 0.05889892*116, 0.041952953*116, -0.22562611*116, 0.27734756*116, 0.074970916*116, -0.13410516*116, 0.21775798*116, 0.13988116*116, 0.2791325*116, 0.10169914*116, -0.047543433*116, 0.12381937*116, -0.20687255*116, 0.13458604*116, -0.050613195*116, 0.07780613*116, -0.06640935*116, 0.021006694*116, 0.07718281*116, 0.0023485452*116, 0.04164907*116, -0.1878691*116, 0.27553242*116, 0.26243237*116, -0.16266033*116, 0.101700716*116, 0.22642662*116, -0.33585626*116, 0.23474883*116, 0.0032822837*116, 0.13663304*116, 0.034796752*116, 0.051287595*116, -0.15164286*116, 0.21285181*116, 0.14259985*116, -0.3205526*116, 0.06124032*116, -0.05672408*116, 0.13824555*116, 0.19174701*116, 0.06075072*116, -0.16774917*116, -0.057611667*116, -0.15011786*116, -0.02218674*116, 0.128105*116, 0.11913554*116, -0.5642495*116, -0.015702736*116, -0.10822057*116, -0.16297174*116, 0.13320497*116, 0.122368224*116, 0.21573028*116, 0.13265008*116, -0.24247077*116, 0.22004706*116, -0.37441528*116, -0.11665254*116, 0.06983987*116, -0.104612656*116, -0.007121977*116, -0.023069752*116, 0.012669961*116, 0.12818469*116, -0.12744519*116, -0.5764287*116, -0.09091122*116, -0.022212936*116, 0.18942569*116, -0.062063675*116, -0.17846227*116, -0.07108532*116, -0.045264147*116, -0.06073022*116, -0.072245605*116, 0.31343663*116, 0.12327311*116, 0.12625617*116, 0.107255474*116, -0.009234973*116, -0.049319234*116, -0.12503079*116, 0.29778475*116, 0.08797696*116, -0.13400929*116, -0.07269173*116, 0.0002312781*116, 0.11821808*116, -0.12790051*116, 0.17434844*116, 0.09980958*116, -0.10609281*116, 0.11246078*116, 0.035813738*116, 0.11621631*116, -0.040839523*116, -0.10960055*116, 0.23638424*116, -0.15085886*116, 0.19292095*116, -0.3385799*116, 0.001748938*116, -0.10827299*116, 0.017305825*116, 0.045653794*116, 0.2709399*116, -0.07909933*116, 0.018940045*116, -0.07827103*116, 0.11456662*116, -0.19815294*116, 0.044719245*116, -0.08736258*116, 0.007926187*116, -0.2147956*116, 0.15719204*116, -0.19405766*116, 0.30793512*116, -0.10617077*116, 0.03830869*116, -0.06376171*116, -0.0042715427*116, 0.039667737*116, -0.04396114*116, 0.24554457*116, -0.07590754*116, 0.011819016*116, -0.033459157*116, -0.29335192*116, -0.044318933*116, 0.01787934*116, 0.11491291*116, 0.024091927*116, -0.045442384*116, -0.09665373*116, 0.11164101*116, -0.0927888*116, -0.11332183*116, -0.1245874*116, 0.1475763*116, 0.20183682*116, -0.05149441*116, -0.1121281*116, 0.09892039*116, 0.22315474*116, 0.097291626*116, 0.03214238*116, -0.1089861*116, -0.25967833*116, 0.09175274*116, 0.022098044*116, -0.3068939*116, -0.3383717*116, -0.049308747*116, -0.26840425*116, 0.14799528*116, 0.19267102*116, -0.29337612*116, -0.027482815*116, -0.0085921455*116, 0.07758802*116, 0.04042296*116, 0.09494524*116, 0.09960757*116, -0.21010467*116, -0.031129269*116, 0.012008378*116, 0.452434*116, 0.16722883*116, 0.080643766*116, 0.1084068*116, -0.03361559*116, -0.09023379*116, -0.079821795*116, 0.20150647*116, -0.04717667*116, -0.18615015*116, -0.20054072*116, -0.14398876*116, -0.17793824*116, -0.16498896*116, 0.05294754*116, -0.038395558*116, 0.015561124*116, 0.4220567*116, -0.36780316*116, -0.13688542*116, -0.065651245*116, 0.059083614*116, -0.15033227*116, 0.035646766*116, 0.21174212*116, 0.018776728*116, 0.20772302*116, -0.10993861*116, 0.036397647*116, 0.28258342*116, -0.21034358*116, -0.11051965*116, 0.23622774*116, 0.018171078*116, -0.10769675*116, -0.045444243*116, 0.17827632*116, -0.17337845*116, 0.117370136*116, -0.15479307*116, 0.2627564*116, 0.09558544*116, 0.025001712*116, -0.06300095*116, -0.24747868*116, -0.18871467*116, 0.010314335*116, 0.16591378*116, -0.10618913*116, 0.15883724*116, 0.1442081*116, -0.077452034*116, -0.101975575*116, -0.010084557*116, -0.24052896*116, 0.038403187*116, 0.033193678*116, 0.033195958*116, -0.16425246*116, -0.093158916*116, -0.17172758*116, -0.045434125*116, -0.27385482*116, 0.24612592*116, -0.06029728*116, -0.049537055*116, -0.09382415*116, -0.11934417*116, 0.0008383081*116, -0.04572496*116, -0.11664635*116, -0.05291147*116, 0.007711652*116, -0.15776964*116, -0.11783941*116, 0.2388195*116, 0.1127691*116, -0.03178648*116, -0.025358811*116, 0.09984067*116, 0.13850717*116, 0.20565963*116, 0.21548976*116, -0.24519023*116, 0.044776123*116, -0.052377984*116, 0.13750803*116, 0.17757837*116, -0.24626122*116, 0.25243366*116, 0.24790813*116, 0.033280052*116, -0.29540718*116, 0.17400247*116, 0.10308723*116, 0.21109296*116, -0.044917013*116, -0.07099389*116, 0.08268515*116, 0.02111736*116, -0.07798145*116, 0.07081146*116, 0.060785584*116, 0.13691707*116, -0.19585156*116, -0.009650808*116, -0.026412135*116, -0.11190947*116, 0.12102938*116, 0.12106122*116, -0.043731052*116, -0.13943087*116, 0.090307906*116, -0.21673216*116, 0.043391142*116, -0.041343346*116, 0.05768864*116, -0.03784474*116, -0.08857392*116, 0.018743062*116, 0.2030782*116, -0.050347548*116, 0.21020624*116, 0.0895435*116, -0.1152827*116, 0.060080018*116, -0.036892883*116, -0.12155716*116, 0.089581095*116, 0.03126206*116, 0.064261556*116, 0.15186137*116, 0.056468096*116, -0.10970526*116, -0.027826605*116, -0.07092515*116, -0.24374378*116, 0.09687477*116, 0.04415035*116, -0.2616601*116, 0.19435012*116, 0.19283284*116, 0.13078068*116, -0.063089624*116, -0.29133022*116, 0.20960575*116, -0.02554017*116, 0.12181189*116, 0.29902202*116, 0.0210253*116, 0.039949637*116, -0.23554249*116, -0.06882952*116, -0.037969373*116, 0.036762156*116, -0.0535206*116, -0.056358885*116, -0.16369067*116, -0.10162578*116, -0.019913368*116, -0.118658476*116, -0.12428356*116, 0.07910224*116, 0.16202456*116, -0.15314758*116, 0.14017734*116, -0.059288863*116, 0.09034861*116, -0.026747443*116, 0.12156082*116, 0.1568513*116, 0.09758704*116, 0.2718038*116, 0.22509299*116, 0.13944444*116, -0.14359044*116, 0.14165819*116, -0.15994881*116, -0.007572958*116, 0.0638783*116, -0.084297076*116, -0.15722746*116, 0.25966725*116, 0.2640678*116, -0.026336309*116, -0.02161444*116, 0.011625978*116, -0.07375831*116, 0.11907011*116, -0.09378034*116, 0.04972607*116, -0.18625659*116, 0.087004654*116, 0.08375145*116, -0.078662336*116, -0.02297675*116, 0.104943395*116, 0.060219727*116, -0.0031264427*116, 0.027855199*116, -0.120274745*116, 0.11642419*116, 0.09181559*116, -0.29111475*116, 0.034116026*116, 0.012823041*116, 0.079756856*116, -0.14368205*116, -0.0037273092*116, -0.08351893*116, -0.09348351*116, 0.120948926*116, 0.04788655*116, 0.058490776*116, 0.12345936*116, -0.12036805*116, -0.0359979*116, -0.21547186*116, -0.001242154*116, -0.20064545*116, -0.011456349*116, -0.14910749*116, -0.05442314*116, -0.20301597*116, -0.13305293*116, 0.018346379*116, -0.12645261*116, 0.04916387*116, -0.056531493*116, 0.030872263*116, -0.1401812*116, 0.14823304*116, -0.069939815*116, -0.05996602*116, 0.23759201*116, 0.15326633*116, 0.093662366*116, -0.043823656*116, 0.040152628*116, 0.20495383*116, -0.06501504*116, 0.07078813*116, -0.15286411*116, 0.2119301*116, 0.18364006*116, 0.23784*116, -0.12782443*116, -0.1484949*116, -0.17041838*116, 0.008631744*116, -0.2310542*116, 0.01169329*116, 0.15364757*116, -0.21384194*116, 0.17192996*116, 0.2655927*116, 0.029776376*116, 0.14841156*116, 0.12914503*116, 0.13074665*116, -0.1535914*116, 0.21956784*116, 0.12926923*116, -0.22534901*116, 0.18881123*116, 0.0028719367*116, 0.06689777*116, 0.15861757*116, -0.11521517*116, -0.25926754*116, -0.047364727*116, -0.15040553*116, 0.046969336*116, -0.32305908*116, -0.19730893*116, -0.016122177*116, -0.2452113*116, 0.10961037*116, -0.037927166*116, 0.024147507*116, -0.06328117*116, -0.12312939*116, -0.14360265*116, -0.19110414*116, 0.028963897*116, 0.012637891*116, 0.27255952*116, -0.34817755*116, 0.0036200832*116, -0.046323773*116, 0.054552745*116, -0.060182314*116, 0.015045988*116, -0.0036050086*116, -0.09945463*116, -0.14541572*116, 0.08566904*116, 0.20720507*116, -0.1570451*116, 0.0030623958*116, -0.016756983*116, -0.1374003*116, 0.011881778*116, -0.024878526*116, 0.11331574*116, -0.2506986*116, -0.02528465*116, -0.07092252*116, 0.17654029*116, 0.013368204*116, -0.06188692*116, 0.14323068*116, -0.27049577*116, 0.122144565*116, 0.15296905*116, 0.031629425*116, -0.08001925*116, 0.087078154*116, -0.0325749*116, -0.08618889*116, 0.102675565*116, -0.23689973*116, -0.08239599*116, -0.1838741*116, 0.037246704*116, 0.109676555*116, 0.1222271*116, -0.2096595*116, -0.13506718*116, -0.10242624*116, -0.028363017*116, -0.020126179*116, 0.043108027*116, 0.02427655*116, 0.105080575*116, -0.10909062*116, 0.14406028*116, 0.10837556*116, 0.24955642*116, -0.268288*116, 0.17351136*116, 0.08346976*116, -0.012924003*116, -0.047431335*116, 0.085932106*116, 0.053555977*116, 0.005694737*116, 0.22371219*116, 0.058068316*116, 0.09670693*116, -0.14484547*116, 0.3081638*116, 0.15122867*116, 0.058190756*116, -0.13897333*116, -0.012595727*116, -0.09167135*116, -0.07171358*116, -0.035832155*116, -0.077747844*116, 0.10327612*116, 0.13612837*116, -0.019215817*116, 0.170103*116, 0.17915176*116, -0.22019762*116, -0.14444289*116, 0.026693452*116, -0.0032809058*116, -0.2603859*116, 0.083188996*116, 0.012138654*116, 0.20135716*116, 0.036760796*116, -0.16417275*116, -0.15071408*116, 0.062689155*116, -0.15683107*116, -0.09034824*116, 0.08771415*116, -0.05531803*116, -0.01661312*116, -0.1848026*116, -0.027546557*116, -0.18304704*116, -0.030862601*116, -0.058789432*116, 0.061579417*116, 0.0054719993*116, -0.19452707*116, 0.043510497*116, 0.27894723*116, 0.06975341*116, -0.008740712*116, 0.0065675173*116, -0.05186726*116, -0.10310732*116, -0.15111537*116, -0.082601264*116, 0.05870904*116, -0.07032124*116, 0.14666766*116, 0.10792975*116, -0.13962959*116, 0.2102532*116, 0.1045648*116, -0.042657986*116, 0.03751225*116, -0.12432647*116, 0.2046492*116, 0.3432783*116, 0.22026256*116, -0.061555333*116, 0.04654886*116, -0.096157305*116, 0.3587139*116, -0.02923582*116, -0.030787284*116, 0.08827096*116, -0.11867054*116, -0.0273*116, 0.070340835*116, 0.2970404*116, 0.04594811*116, -0.28877458*116, -0.11426051*116, 0.002986987*116, -0.11363286*116, 0.05615367*116, 0.13556415*116, -0.11394936*116, 0.10724669*116, 0.11576181*116, 0.22816475*116, -0.18052785*116, 0.16903615*116, -0.28699973*116, 0.117366046*116, 0.16276075*116, 0.101519205*116, -0.06833*116, 0.22551286*116, 0.019018695*116, 0.16865745*116, 0.07685669*116, -0.06036971*116, 0.22206713*116, -0.051709604*116, -0.08449608*116, -0.24064492*116, -0.15753162*116, 0.01647461*116, -0.11564736*116, -0.00017287215*116, -0.19170366*116, -0.1992706*116, 0.04118395*116, 0.025918953*116, 0.14723288*116, 0.08859028*116, 0.23921719*116, 0.093175046*116, -0.060569264*116, -0.13151021*116, 0.002173837*116, -0.32211718*116, 0.02566123*116, 0.1582392*116, -0.1385944*116, 0.14810184*116, 0.052555993*116, 0.24397753*116, -0.09083953*116, 0.19082993*116, -0.025511341*116, 0.09028583*116, -0.059648234*116, -0.17908755*116, -0.09740901*116, -0.15238936*116, 0.033495232*116, -0.085313715*116, -0.009266752*116, 0.094448686*116, -0.002348778*116, 0.020039368*116, 0.21621007*116, 0.05986415*116, 0.06766827*116, -0.27781492*116, -0.020312345*116, 0.052306682*116, -0.026512573*116, -0.2485475*116, -0.13847184*116, 0.03178895*116, 0.2970908*116, -0.13276082*116, 0.062010836*116, -0.011311493*116, 0.08536578*116, -0.054105867*116, -0.019200226*116, 0.049381472*116, -0.0786946*116, -0.31971022*116, -0.031723868*116, 0.058389418*116, 0.073923156*116, 0.10160593*116, -0.0469691*116, 0.016777545*116, -0.1725141*116, 0.076033324*116, 0.012286249*116, -0.20739828*116, 0.13931413*116, -0.04284775*116, -0.008305725*116, 0.14951186*116, 0.027345033*116, -0.065050304*116, 0.57177263*116, 0.10819809*116, 0.0077062566*116, 0.015877659*116, -0.1387114*116, 0.19836909*116, 0.18298942*116, -0.0889322*116, 0.12632512*116, -0.121440694*116, 0.03611771*116, 0.05823439*116, 0.13982104*116, 0.06906287*116, -0.19824664*116, 0.06186832*116, 0.2635453*116, 0.022769464*116, 0.031183628*116, -0.15711793*116, -0.062284056*116, -0.056944784*116, -0.1920595*116, 0.011367573*116, 0.22232923*116, 0.07846328*116, -0.10766965*116, -0.12495994*116, -0.3212511*116, -0.024337435*116, 0.14479198*116, -0.12374947*116, -0.1613751*116, -0.029000774*116, 0.098819435*116, -0.124413304*116, -0.08826264*116, -0.03411783*116, 0.17408414*116, -0.029204058*116, 0.13597928*116, -0.3711622*116, 0.04673104*116, 0.07578801*116, -0.14336614*116, -0.05070373*116, -0.040274452*116, 0.13370395*116, 0.12296583*116, 0.11913449*116, -0.25861678*116, 0.18980953*116, -0.091194384*116, -0.21051052*116, 0.33268827*116, -0.304998*116, -0.2779774*116, -0.033788227*116, 0.46682882*116, -0.21241233*116, -0.003618725*116, 0.098872304*116, 0.10892992*116, 0.14801626*116, 0.013329744*116, 0.040903743*116, -0.022413757*116, 0.21790852*116, 0.057840932*116, 0.11829655*116, 0.0017170344*116, 0.1097659*116, -0.24498257*116, 0.11226635*116, -0.18780892*116, -0.05987283*116, -0.021021727*116, -0.094018616*116, -0.0148815755*116, 0.29358536*116, -0.07865371*116, 0.10728764*116, 0.2224377*116, -0.31693876*116, -0.21150464*116, 0.36458763*116, 0.3206643*116, 0.21003594*116, -0.11956995*116, -0.0962686*116, 0.12800604*116, -0.052583247*116, -0.14741626*116, -0.006859467*116, 0.16812794*116, 0.17199296*116, -0.09383574*116, 0.013682321*116, -0.14501908*116, 0.24479215*116, 0.053368162*116, 0.103011005*116, 0.09947377*116, 0.29518363*116, -0.28757915*116, -0.120928116*116, -0.08377407*116, -0.003990203*116, -0.13878332*116, 0.1083888*116, 0.046231072*116, 0.38494784*116, -0.043716136*116, 0.03331472*116, -0.06742473*116, 0.073620774*116, 0.19936849*116, 0.05167276*116, -0.07023399*116, -0.20544942*116, -0.110701434*116, 0.08653872*116, 0.14931092*116, 0.050044633*116, 0.011886755*116, -0.3546776*116, -0.03774849*116, -0.14056917*116, -0.007113966*116, 0.21255223*116, 0.20686644*116, -0.0019102072*116, -0.17959078*116, -0.18147397*116, -0.081490606*116, -0.11530752*116, 0.024432408*116, 0.01038721*116, 0.096550174*116, 0.12194617*116, -0.1960126*116, -0.06773173*116, 0.061327714*116, 0.24195892*116, -0.20496793*116, -0.07129881*116, -0.11026859*116, -0.102255054*116, 0.07500953*116, -0.062037524*116, -0.050201062*116, -0.08338017*116, 0.019721238*116, 0.058566123*116, -0.052950222*116, -0.08958779*116, 0.07361526*116, -0.23402771*116, -0.24232483*116, -0.028683348*116, -0.22349697*116, 0.16264124*116, 0.113858975*116, 0.021823239*116, -0.1525391*116, -0.086867675*116, -0.009283938*116, -0.115619294*116, 0.26878977*116, 0.023612626*116, -0.29673904*116, 0.059641868*116, 0.20331852*116, -0.030398974*116, 0.14390856*116, 0.0046793385*116, -0.14907154*116, -0.12891805*116, 0.14605798*116, 0.013984494*116, -0.0754033*116, 0.16676*116, -0.13083138*116, 0.18841773*116, -0.08905821*116, -0.22363469*116, -0.16554292*116, -0.25224796*116, 0.17383002*116, 0.17971607*116, 0.17410155*116, -0.0176714*116, 0.20317629*116, -0.18558247*116, 0.09468764*116, 0.23124532*116, 0.086860016*116, 0.20436029*116, 0.22870938*116, -0.1863113*116, -0.014053667*116, 0.049587995*116, -0.1497119*116, -0.12126215*116, 0.14326893*116, 0.21796812*116, -0.2292273*116, -0.10549298*116, 0.17176491*116, 0.092760935*116, -0.09605094*116, 0.06391487*116, 0.078760006*116, 0.13273056*116, 0.058205843*116, 0.0005532614*116, -0.00011200946*116, 0.10101435*116, -0.041951716*116, 0.09380339*116, 0.037230335*116, 0.10043303*116, 0.050256897*116, 0.107455686*116, 0.033458546*116, -0.03194126*116, 0.14420615*116, 0.22640428*116, 0.037023082*116, -0.25360465*116, 0.13886033*116, -0.21978818*116, -0.012782665*116, 0.024050176*116, -0.14816561*116, -0.099648006*116, 0.24715231*116, 0.040808253*116, 0.15848656*116, 0.05912039*116, 0.18058462*116, 0.046558227*116, -0.011842459*116, -0.13720073*116, 0.2074721*116, -0.20057428*116, -0.09049072*116, -0.20037459*116, -0.022228321*116, -0.032637566*116, -0.023447787*116, -0.16768017*116, 0.08168035*116, 0.029034896*116, -0.11812815*116, 0.20828982*116, 0.050776612*116, -0.324658*116, 0.08923136*116, 0.0047283224*116, 0.0017330862*116, -0.1767247*116, -0.04656255*116, 0.07422766*116, 0.1109301*116, -0.13126881*116, -0.016628297*116, 0.179265*116, -0.017206322*116, -0.016378406*116, -0.041591305*116, -0.10782494*116, 0.017238652*116, -0.077852406*116, 0.21741216*116, -0.252089*116, 0.1045682*116, -0.037152216*116, -0.16662274*116, 0.06177835*116, -0.09402985*116, 0.27119616*116, -0.017100975*116, -0.23003021*116, 0.003748648*116, 0.12871502*116, 0.08045374*116, 0.18582177*116, -0.0815226*116, -0.03868882*116, 0.10346123*116, 0.057643138*116, 0.067176655*116, 0.0678957*116, 0.067073815*116, -0.07685936*116, 0.03824131*116, 0.031155078*116, 0.17775269*116, -0.36593914*116, -0.12269277*116, -0.15577932*116, 0.08474076*116, -0.040412333*116, -0.076687634*116, -0.0125243375*116, -0.0845369*116, 0.21934254*116, -0.08508296*116, 0.064791046*116, -0.40607285*116, 0.11338299*116, 0.03956037*116, -0.08779583*116, 0.32929036*116, 0.16182701*116, 0.069902964*116, -0.17549594*116, -0.040903393*116, 0.09721326*116, 0.002436589*116, 0.041201923*116, 0.0034588613*116, -0.26689756*116, -0.024150833*116, 0.09120295*116, -0.015182623*116, 0.010883437*116, 0.21578881*116, -0.075857796*116, 0.12945662*116, -0.056516346*116, 0.21165772*116, -0.19618845*116, 0.06602767*116, 0.013514664*116, 0.07475721*116, 0.17480217*116, -0.17219466*116, -0.10706685*116, 0.091193415*116, 0.04603447*116, -0.27574426*116, -0.020997126*116, 0.18295264*116, -0.058155023*116, 0.14720093*116, -0.018613433*116, -0.05054749*116, -0.13642536*116, -0.03568914*116, -0.16082908*116, -0.13989352*116, -0.17283578*116, 0.0023180386*116, 0.16239296*116, -0.000811109*116, -0.09634898*116, 0.14414601*116, 0.055903357*116, -0.101036176*116, -0.23095644*116, -0.28422132*116, 0.24547815*116, 0.0979262*116, 0.117363326*116, 0.009323492*116, 0.39825824*116, 0.093242995*116, 0.15468775*116, 0.036088318*116, 0.2159536*116, 0.10694216*116, -0.17913416*116, -0.004489797*116, -0.16695811*116, -0.03961527*116, 0.057417907*116, 0.015176739*116, 0.12877189*116, -0.21237342*116, -0.19281924*116, 0.1460107*116, -0.13861935*116, 0.04778344*116, 0.17503181*116, 0.04306995*116, 0.27980617*116, -0.04130575*116, -0.052677836*116, -0.10603113*116, -0.12286833*116, 0.15038759*116, 0.0017252781*116, -0.13629134*116, -0.06289897*116, -0.058601063*116, 0.035067614*116, 0.0023227518*116, 0.26519075*116, -0.06195779*116, -0.09941172*116, 0.12327481*116, -0.13552722*116, -0.21099922*116, -0.0647749*116, 0.17867847*116, -0.07247221*116, -0.13744767*116, -0.13270931*116, 0.12753956*116, -0.15816644*116, 0.07108464*116, -0.10396649*116, 0.06359798*116, -0.049214054*116, -0.08239889*116, 0.11475494*116, 0.05007711*116, 0.09930024*116, 0.14673221*116, -0.0063678846*116, -0.10058199*116, -0.31826*116, 0.109297365*116, -0.016010504*116, 0.31293365*116, 0.13340136*116, 0.09810525*116, 0.103314295*116, -0.25355718*116, -0.29812923*116, 0.06704087*116, -0.2102828*116, -0.07014773*116, -0.22809222*116, -0.05619492*116, 0.12590113*116, 0.052049246*116, 0.13826552*116, 0.2068643*116, 0.071913734*116, -0.025347319*116, 0.23277272*116, 0.2899591*116, 0.08039525*116, -0.07622317*116, 0.035438865*116, -0.10093669*116, 0.0052632703*116, -0.12186091*116, -0.08949041*116, 0.11134348*116, -0.12413267*116, 0.086933225*116, -0.06865945*116, 0.05192547*116, -0.0841803*116, -0.08695834*116, 0.10510938*116, -0.13843283*116, 0.14023536*116, 0.1300493*116, -0.33597296*116, 0.11869535*116, -0.25425932*116, -0.11560675*116, 0.023809502*116, 0.09662809*116, -0.15225351*116, 0.16858257*116, 0.045420792*116, -0.07557104*116, -0.27035993*116, -0.057028648*116, -0.20548004*116, -0.039690316*116, -0.049280338*116, 0.21279776*116, -0.023655338*116, 0.03120221*116, -0.14035477*116, -0.16828772*116, -0.085366175*116, -0.08054121*116, 0.33622047*116, -0.22160065*116, -0.031573284*116, 0.08732482*116, -0.14376736*116, 0.021424705*116, 0.011854932*116, -0.35948935*116, -0.08691996*116, -0.17909083*116, 0.11773448*116, 0.017579693*116, 0.022243084*116, -0.09316605*116, 0.063108355*116, -0.26423454*116, 0.20618205*116, 0.1568086*116, -0.061053645*116, 0.056092292*116, -0.07422148*116, -0.23302814*116, -0.124294706*116, -0.12035827*116, 0.084991254*116, -0.11805485*116, -0.05570101*116, -0.008357558*116, 0.14040846*116, -0.0074953823*116, -0.115915425*116, -0.14526393*116, 0.17470624*116, 0.009241544*116, 0.29413474*116, -0.044091027*116, -0.082654685*116, -0.11531484*116, 0.05255316*116, -0.15697*116, 0.06723011*116, -0.092012025*116, 0.19435503*116, 0.023846867*116, 0.33989078*116, 0.21513689*116, 0.27876744*116, -0.1097353*116, 0.052404013*116, 0.10986312*116, 0.053336293*116, 0.14058723*116, 0.12486597*116, -0.0010435644*116, 0.08746395*116, 0.04799594*116, 0.021659812*116, -0.13079691*116, 0.23738053*116, 0.013762228*116, -0.04960887*116, 0.114223875*116, 0.024188956*116, 0.12512812*116, -0.14829724*116, 0.20001929*116, -0.2170872*116, -0.0072709196*116, 0.10801349*116, -0.1134664*116, -0.1963534*116, -0.06134237*116, 0.09776294*116, 0.11932442*116, 0.24081679*116, 0.061518293*116, 0.027620468*116, 0.011482669*116, -0.029627467*116, 0.05021197*116, -0.22730204*116, -0.017306644*116, -0.15788837*116, -0.07480945*116, -0.09120174*116, -0.0009886882*116, 0.005593013*116, -0.0747114*116, -0.108076155*116, 0.06442003*116, 0.21854384*116, 0.022927346*116, -0.28352284*116, 0.099855214*116, 0.16027753*116, 0.011570793*116, 0.092766725*116, -0.0729598*116, 0.18621819*116, 0.049555775*116, -0.07194184*116, 0.13923126*116, -0.068602666*116, -0.06671237*116, -0.18283588*116, 0.11854823*116, 0.015047413*116, -0.2395933*116, 0.055365253*116, 0.042487226*116, -0.0466869*116, -0.10773581*116, 0.1386434*116, -0.16653985*116, -0.0009604014*116, 0.042588245*116, 0.0042856075*116, 0.08250658*116, -0.0045378106*116, 0.15957847*116, -0.086358786*116, 0.24522538*116, -0.19268556*116, 0.0056660497*116, 0.023021165*116, -0.24423471*116, -0.06877421*116, 0.055680707*116, 0.107392766*116, -0.22009225*116, 0.051819455*116, 0.0035291594*116, 0.030086895*116, 0.059052147*116, 0.09409881*116, -0.1975026*116, 0.030429482*116, -0.12046226*116, -0.04164798*116, -0.21384622*116, -0.0074181324*116, 0.09067122*116, 0.2329995*116, -0.12733671*116, 0.0977074*116, -0.12901992*116, -0.034155954*116, 0.075922534*116, 0.0017167592*116, -0.21116996*116, 0.031986423*116, -0.25724006*116, -0.120297335*116, 0.21724205*116, -0.22010241*116, -0.09227209*116, 0.29343274*116, 0.022802675*116, -0.06580825*116, -0.030214261*116, 0.19331582*116, 0.062994964*116, 0.14416963*116, 0.08819867*116, -0.07775813*116, -0.21342815*116, -0.19554101*116, -0.03970272*116, 0.21322457*116, -0.19294043*116, 0.07818607*116, -0.0070911627*116, -0.11171215*116, 0.07853175*116, 0.07625727*116, 0.063917354*116, 0.10477714*116, -0.09397182*116, 0.1381693*116, 0.05335456*116, 0.01691472*116, 0.058161534*116, 0.20560043*116, -0.0717991*116, 0.21313848*116, -0.12555474*116, -0.019031245*116, 0.27130845*116, -0.11189608*116, 0.09180623*116, -0.11594328*116, -0.20527503*116, 0.012467345*116, -0.1563234*116, 0.093827374*116, -0.060375214*116, 0.04585282*116, -0.2621353*116, 0.07068883*116, 0.06370711*116, -0.12525843*116, 0.08457178*116, -0.17518409*116, 0.004531326*116, 0.2550852*116, 0.20343837*116, 0.07574377*116, 0.04390643*116, 0.053828903*116, 0.036978517*116, -0.04564449*116, -0.15018731*116, 0.2112537*116, -0.18331474*116, -0.2964785*116, -0.2906568*116, 0.31570262*116, 0.01763492*116, -0.062610716*116, -0.12731645*116, 0.064165846*116, -0.07015214*116, -0.13373984*116, -0.042459648*116, 0.1468792*116, -0.027551796*116, 0.09233584*116, -0.11226877*116, 0.15030527*116, 0.059306964*116, 0.13850403*116, 0.07916054*116, -0.08616286*116, 0.1092822*116, 0.06505339*116, -0.049806047*116, 0.20569324*116, 0.1068704*116, -0.10735509*116, -0.052078355*116, -0.2019292*116, -0.15170924*116, 0.0608995*116, -0.079107575*116, 0.08505018*116, -0.47177118*116, -0.2889665*116, -0.1793787*116, 0.09105332*116, -0.101326145*116, -0.027191527*116, 0.17786585*116, -0.2579326*116, 0.27775425*116, -0.15126677*116, 0.10403943*116, -0.09146683*116, 0.18514714*116, 0.09368784*116, 0.21279353*116, -0.09346892*116, 0.21345139*116, 0.12295397*116, -0.27266645*116, -0.19283909*116, 0.07735251*116, 0.17694962*116, -0.17302276*116, 0.02046451*116, -0.16726361*116, 0.2793333*116, -0.12115362*116, -0.07297586*116, 0.024256198*116, 0.066077895*116, -0.29632849*116, -0.16785337*116, 0.30346513*116, -0.120323725*116, 0.22456132*116, -0.032142173*116, 0.26434165*116, 0.20616181*116, 0.036283743*116, -0.071357146*116, -0.13515413*116, -0.20655426*116, -0.20510867*116, 0.36583975*116, 0.011255843*116, 0.17142203*116, -0.1988417*116, 0.09741316*116, -0.013299123*116, -0.10626445*116, 0.20259576*116, -0.14830832*116, 0.2279309*116, 0.35391507*116, -0.2728475*116, 0.17781961*116, 0.26779085*116, -0.06293203*116, -0.1977454*116, -0.12124893*116, -0.02751193*116, 0.13674052*116, -0.17306128*116, 0.06260413*116, -0.056855552*116, 0.06626609*116, -0.1365531*116, -0.21280481*116, -0.11557721*116, -0.3225026*116, 0.086692154*116, -0.085295364*116, -0.08803154*116, 0.008369153*116, -0.05334103*116, 0.08322147*116, 0.12147922*116, -0.13517414*116, 0.055221133*116, -0.31800193*116, 0.081337556*116, 0.34722087*116, -0.12509468*116, 0.08263238*116, 0.006038024*116, -0.063952416*116, -0.043063927*116, 0.13269639*116, -0.034415536*116, -0.022466775*116, 0.16993578*116, -0.087110616*116, 0.2881077*116, 0.13289654*116, -0.123383954*116, 0.29824224*116, -0.011739338*116, 0.12293089*116, -0.084062904*116, 0.110101365*116, 0.25410524*116, 0.052003276*116, 0.029996866*116, 0.13721165*116, -0.22440442*116, -0.3299517*116, 0.10000209*116, 0.07488614*116, -0.11159775*116, -0.1487574*116, -0.32748312*116, -0.009781446*116, -0.098776445*116, 0.15818486*116, -0.31141922*116, 0.12406872*116, 0.044542*116, -0.029308079*116, 0.15316455*116, -0.09839523*116, 0.20869704*116, -0.22473855*116, 0.103219084*116, -0.26405436*116, 0.10563946*116, 0.02978316*116, -0.15762752*116, 0.18983465*116, -0.09777088*116, 0.06339509*116, -0.10606748*116, 0.078915946*116, -0.22010216*116, -0.07274118*116, 0.029350312*116, -0.025452401*116, 0.20568638*116, 0.16040803*116, -0.028301168*116, -0.3081977*116, 0.23732601*116, -0.20563069*116, 0.08629181*116, -0.16254479*116, -0.01969703*116, -0.06140638*116, 0.3090081*116, 0.18602018*116, 0.13805352*116, 0.014174885*116, 0.18570416*116, -0.018834729*116, -0.10422892*116, -0.090480566*116, 0.14749406*116, -0.2636518*116, -0.12683378*116, -0.19061925*116, -0.1946979*116, 0.084912024*116, -0.004529718*116, -0.010634142*116, 0.12120818*116, 0.039643027*116, -0.106139146*116, 0.37484246*116, 0.18761882*116, -0.3153386*116, 0.15964533*116, 0.20601615*116, 0.096896626*116, -0.11426126*116, 0.14144741*116, 0.06765619*116, -0.0019350965*116, 0.15756063*116, 0.10473005*116, -0.086525686*116, 0.022021262*116, -0.18510726*116, 0.057351485*116, -0.014531665*116, -0.16097623*116, -0.21670829*116, 0.10817011*116, -0.16609809*116, 0.08843513*116, 0.0664554*116, -0.06496166*116, 0.02680838*116, -0.2145906*116, -0.21464379*116, 0.06396914*116, -0.1414982*116, 0.13247545*116, 0.02183821*116, 0.147203*116, 0.07252547*116, -0.082456805*116, 0.08278965*116, 0.010721427*116, -0.023275796*116, -0.22046113*116, 0.25963655*116, -0.26964286*116, 0.13252735*116, 0.10196194*116, 0.08264155*116, -0.017486349*116, 0.0030144067*116, -0.054939784*116, -0.20029515*116, -0.13665101*116, -0.09106437*116, -0.21250081*116, 0.15214755*116, -0.09750485*116, 0.027644053*116, -0.1340541*116, 0.025990425*116, -0.2643823*116, 0.14570071*116, 0.19108924*116, -0.085954465*116, -0.17257188*116, 0.07805211*116, 0.032045785*116, 0.12265133*116, 0.07554203*116, -0.2116527*116, -0.14167665*116, 0.23564254*116, -0.14230444*116, 0.0695875*116, -0.1661036*116, 0.085665196*116, -0.018307496*116, -0.09625882*116, 0.26169434*116, -0.13081332*116, 0.016303014*116, -0.16127917*116, 0.017668692*116, 0.266104*116, -0.17037246*116, 0.11233897*116, 0.039533462*116, 0.02938076*116, -0.15484564*116, 0.23747434*116, 0.053138472*116, 0.21973531*116, 0.10218862*116, -0.018036928*116, 0.20015122*116, 0.10869334*116, -0.05605689*116, 0.24619281*116, 0.14020294*116, -0.015247612*116, -0.006858236*116, 0.029452397*116, -0.105633125*116, -0.03613658*116, -0.075086825*116, 0.22383179*116, -0.07590209*116, -0.26560044*116, 0.09898565*116, -0.14825143*116, 0.0080411965*116, 0.095562086*116, -0.035322335*116, 0.10071893*116, 0.3495254*116, 0.23370346*116, 0.10144252*116, -0.0252192*116, -0.06455328*116, -0.020289065*116, -0.08027587*116, -0.24045938*116, 0.086350456*116, -0.21803439*116, -0.038844347*116, 0.05610381*116, 0.21885236*116, 0.028252967*116, -0.021257816*116, -0.007844021*116, 0.17645228*116, 0.17311274*116, -0.03005916*116, -0.04344052*116, 0.10900594*116, -0.15001138*116, 0.29106826*116, -0.13001299*116, 0.21834481*116, -0.120886974*116, -0.027839996*116, -0.17569326*116, -0.06550229*116, 0.04332173*116, 0.19543386*116, 0.23176105*116, -0.0082353605*116, -0.00045614125*116, -0.1370232*116, -0.21783654*116, 0.12704515*116, -0.28727907*116, -0.083218746*116, 0.011387932*116, 0.25043067*116, -0.1977309*116, -0.15308158*116, 0.038579464*116, 0.03311355*116, 0.13743253*116, -0.15707718*116, 0.006591428*116, -0.044045508*116, 0.028802678*116, -0.07376067*116, 0.28162283*116, -0.04574724*116, -0.036525976*116, 0.16132319*116, -0.00072734*116, 0.087251596*116, -0.1525045*116, 0.005615529*116, -0.267941*116, 0.15357025*116, -0.15074806*116, 0.027390735*116, -0.14507946*116, -0.020483175*116, 0.18303566*116, -0.02178324*116, -0.09953057*116, 0.065034844*116, 0.11836679*116, -0.07144504*116, 0.13514644*116, -0.120630614*116, -0.11181735*116, 0.12370878*116, 0.059634566*116, 0.10461116*116, -0.02016622*116, 0.10457977*116, 0.033164974*116, -0.018316839*116, 0.09868475*116, -0.22082786*116, 0.24327108*116, -0.2249903*116, -0.25022176*116, 0.11041817*116, -0.03407717*116, 0.14861093*116, -0.15926048*116, -0.25607374*116, 0.16020648*116, 0.01663109*116, -0.009948249*116, 0.22341102*116, 0.062326662*116, -0.060068563*116, -0.07989382*116, 0.09245823*116, -0.09492967*116, -0.108897895*116, -0.1574298*116, 0.09631679*116, -0.13306512*116, -0.02193049*116, 0.16607527*116, 0.07154398*116, 0.020086585*116, -0.10664431*116, 0.047230706*116, 0.026603991*116, -0.15866324*116, -0.03579383*116, 0.19617164*116, -0.089859635*116, -0.3006167*116, -0.008613708*116, 0.0891397*116, 0.107077934*116, -0.1359618*116, 0.03318658*116, 0.22253832*116, 0.119904086*116, 0.16831227*116, -0.05043841*116, 0.1562379*116, 0.19770442*116, 0.01681254*116, -0.083018385*116, -0.05034861*116, -0.11658048*116, -0.13374197*116, -0.094996735*116, -0.06315412*116, -0.005842567*116, -0.07662344*116, 0.07474377*116, 0.24411115*116, -0.02288446*116, 0.1581503*116, 0.067628846*116, 0.14972055*116, 0.07481747*116, 0.11280392*116, 0.025953736*116, -0.097953804*116, 0.22733179*116, -0.061755557*116, 0.21435396*116, 0.038061652*116, -0.1281723*116, -0.43708193*116, -0.017060028*116, 0.04658338*116, -0.0915027*116, -0.25534564*116, -0.05475504*116, 0.117607094*116, -0.075860284*116, 0.060900256*116, -0.13639696*116, 0.20727642*116, -0.07885413*116, -0.018682104*116, 0.14452149*116, -0.16486971*116, -0.05234074*116, -0.2858676*116, 0.24430843*116, -0.28461394*116, 0.11719859*116, -0.0082916645*116, 0.12967697*116, 0.1856506*116, 0.10173409*116, 0.14111778*116, -0.05557073*116, 0.08038287*116, 0.1417914*116, -0.09156354*116, 0.19360596*116, -0.11768545*116, -0.09337955*116, 0.18222964*116, -0.18143198*116, 0.09098499*116, -0.38080466*116, 0.073865056*116, -0.24336326*116, -0.005830227*116, -0.038270537*116, -0.19452137*116, -0.08809417*116, 0.05955508*116, 0.019362789*116, 0.119459406*116, 0.10224499*116, 0.13944891*116, -0.17766924*116, 0.13151105*116, 0.066873826*116, -0.12563626*116, -0.16281003*116, 0.02348829*116, 0.03125271*116, 0.16248408*116, 0.11747624*116, -0.034493458*116, -0.16430102*116, 0.18649745*116, -0.06955076*116, 0.10844789*116, -0.7131848*116, -0.013279155*116, -0.153794*116, -0.19901782*116, -0.07095632*116, -0.20305513*116, -0.44257805*116, 0.28603405*116, -0.10331117*116, 0.07934592*116, 0.1424484*116, -0.004018009*116, -0.09791992*116, 0.07038338*116, 0.023180868*116, 0.20832327*116, 0.08752909*116, -0.11842532*116, 0.10690875*116, 0.188715*116, 0.022124264*116, -0.028417228*116, -0.20091967*116, -0.20858012*116, 0.21290818*116, -0.049908433*116, -0.11862743*116, 0.028230393*116, 0.40344697*116, 0.07167945*116, -0.22656131*116, -0.014146818*116, -0.1447943*116, -0.11244714*116, 0.05333274*116, 0.23927452*116, -0.07971844*116, -0.04392139*116, 0.06845287*116, 0.33343422*116, 0.009386722*116, 0.006164321*116, -0.26584372*116, 0.074512586*116, 0.37368268*116, 0.15528396*116, 0.18940191*116, -0.10174432*116, 0.041670214*116, -0.09603928*116, -0.040963586*116, -0.053107046*116, 0.1777517*116, -0.108145654*116, -0.2736643*116, -0.04690357*116, 0.022528531*116, 0.19974844*116, -0.023876961*116, 0.19861257*116, 0.100123525*116, 0.038313977*116, -0.055018727*116, 0.018061077*116, 0.041678302*116, 0.023237858*116, 0.35660484*116, 0.22358385*116, -0.21982335*116, -0.2723245*116, -0.011347951*116, -0.3576183*116, 0.29926792*116, 0.15620103*116, -0.039153155*116, -0.1164373*116, 0.19226868*116, 0.028017556*116, -0.20440216*116, 0.067393154*116, -0.1913091*116, -0.2625653*116, -0.27755058*116, 0.13054657*116, 0.15344708*116, 0.05549413*116, -0.20045589*116, -0.15345201*116, 0.050765194*116, -0.08097586*116, -0.02182448*116, 0.25749075*116, 0.1185572*116, -0.08153568*116, -0.0812824*116, -0.16094945*116, 0.13043097*116, 0.05002664*116, -0.115506485*116, 0.23573036*116, 0.1430075*116, -0.017489698*116, -0.119340725*116, 0.22576976*116, -0.3160771*116, 0.14819907*116, 0.038477756*116, 0.20206141*116, -0.050373465*116, 0.032842904*116, 0.042467218*116, 0.2518283*116, 0.1788351*116, 0.047308825*116, 0.020160746*116, 0.024464808*116, 0.11658302*116, -0.09183498*116, 0.110334925*116, -0.053219844*116, 0.17993903*116, 0.17158318*116, 0.06365341*116, 0.16935222*116, 0.08152343*116, -0.33103308*116, -0.09195557*116, 0.047115453*116, 0.010857296*116, 0.053136934*116, 0.05320031*116, -0.13131449*116, -0.10435667*116, -0.06618064*116, 0.08267412*116, -0.15125309*116, -0.3329972*116, 0.20039877*116, -0.20140208*116, -0.044357534*116, -0.12536569*116, 0.08989597*116, 0.034398597*116, -0.13741402*116, -0.2180593*116, -0.3274023*116, -0.10110906*116, 0.061305907*116, 0.2148588*116, -0.24857576*116, 0.17709722*116, -0.07748808*116, -0.018114839*116, -0.1106955*116, -0.40219653*116, 0.040356528*116, 0.041763198*116, 0.025602931*116, 0.02841416*116, -0.16954674*116, 0.14634852*116, -0.13742125*116, 0.04182709*116, -0.052254513*116, 0.0715907*116, -0.0108618885*116, -0.009749256*116, 0.1366766*116, 0.05009011*116, -0.15337819*116, -0.13411163*116, 0.14774257*116, -0.0951442*116, -0.30773455*116, -0.071598835*116, 0.21359308*116, 0.02657581*116, 0.111446224*116, -0.22704369*116, 0.23418444*116, -0.057426043*116, 0.10895835*116, 0.041423507*116, 0.14725749*116, -0.15131666*116, 0.10198552*116, -0.11179889*116, -0.16494548*116, -0.034428827*116, -0.09376893*116, 0.11898852*116, -0.20765874*116, -0.123769954*116, 0.08180236*116, -0.1855683*116, 0.006236148*116, -0.26503265*116, 0.2427676*116, -0.1182784*116, 0.16180678*116, 0.071798764*116, 0.19838777*116, -0.2118109*116, -0.38808224*116, 0.22167487*116, 0.1232128*116, -0.07471114*116, 0.12345299*116, -0.09815394*116, -0.03396423*116, -0.0018717829*116, 0.10031123*116, -0.10641572*116, 0.20384558*116, 0.05454719*116, 0.04868659*116, 0.0038991065*116, 0.10817958*116, 0.019883139*116, -0.030811332*116, -0.35862967*116, 0.11241915*116, -0.08106336*116, -0.17538664*116, 0.05828512*116, 0.16112815*116, 0.12905157*116, 0.12889278*116, 0.032789703*116, -0.12419134*116, -0.19195008*116, -0.14823651*116, 0.075100675*116, 0.08523868*116, 0.42535284*116, 0.048810385*116, -0.18624003*116, 0.1087921*116, -0.23038898*116, -0.33474663*116, 0.069997706*116, 0.10277905*116, -0.0096197445*116, -0.24003196*116, 0.068498075*116, 0.061561983*116, 0.13379976*116, 0.22280063*116, 0.06685813*116, 0.15876137*116, -0.0880232*116, 0.23065488*116, -0.05396058*116, 0.09764697*116, -0.056314*116, 0.12306635*116, 0.064213336*116, -0.10174251*116, -0.22006686*116, -0.48482603*116, -0.07981592*116, -0.15449084*116, -0.0675128*116, -0.038681354*116, -0.06301702*116, 0.09776124*116, 0.006206468*116, 0.1083403*116, -0.053626105*116, -0.0448608*116, 0.16144599*116, -0.10536788*116, 0.13320446*116, -0.1904758*116, 0.04639096*116, 0.01012378*116, 0.29846373*116, -0.3420461*116, 0.009359068*116, 0.10929954*116, -0.02791155*116, 0.012153965*116, -0.12736626*116, 0.14527288*116, -0.1765776*116, 0.062848546*116, -0.20436555*116, 0.031557087*116, -0.15140153*116, 0.18514758*116, 0.18959308*116, 0.18459311*116, 0.07020661*116, -0.28169173*116, 0.17370512*116, -0.1337895*116, -0.06347836*116, 0.11231155*116, -0.14594616*116, 0.1432328*116, 0.2853049*116, 0.07321581*116, 0.09793318*116, 0.1032706*116, 0.06431601*116, 0.023140969*116, -0.09406209*116, 0.041190144*116, 0.020011947*116, -0.11982475*116, 0.17416629*116, -0.24848157*116, -0.16462968*116, 0.12047956*116, 0.10673648*116, -0.050337873*116, 0.17461486*116, 0.119816884*116, 0.06377135*116, 0.12742859*116, 0.093753085*116, -0.25583342*116, 0.22299749*116, -0.002293414*116, 0.100804485*116, -0.09503105*116, 0.021450326*116, -0.021416219*116, 0.015337827*116, -0.074486226*116, -0.051866803*116, 0.19013302*116, -0.060991794*116, -0.10537519*116, -0.021798704*116, -0.29200643*116, 0.044947572*116, -0.24653332*116, 0.049730744*116, -0.22547226*116, 0.13784045*116, -0.10263731*116, 0.004883903*116, 0.123076804*116, -0.23736116*116, 0.020255866*116, -0.07120794*116, 0.13409923*116, -0.1571865*116, -0.29310253*116, -0.002925074*116, 0.089960665*116, -0.058995638*116, 0.10545158*116, -0.15622391*116, -0.12887505*116, 0.047270615*116, 0.13130939*116, 0.12307842*116, -0.1908866*116, 0.13096303*116, -0.119078055*116, 0.15629908*116, 0.25318587*116, -0.17738296*116, -0.14788455*116, -0.034757324*116, 0.16329779*116, -0.048769113*116, 0.036147032*116, -0.1732541*116, -0.2331127*116, -0.11844195*116, 0.08162295*116, 0.4849351*116, -0.022477852*116, -0.02798886*116, 0.015829736*116, -0.011975198*116, -0.09630535*116, 0.13941312*116, -0.063051306*116, 0.036778938*116, 0.015652908*116, -0.098911315*116, -0.33440048*116, -0.03302145*116, -0.16001418*116, 0.1957919*116, 0.09922739*116, 0.14989765*116, 0.1682693*116, -0.38714227*116, 0.009018155*116, -0.30386204*116, 0.0085696345*116, -0.1737836*116, 0.11475821*116, 0.22236925*116, 0.015717285*116, 0.05652661*116, 0.08083431*116, 0.11990893*116, -0.03344403*116, -0.10109514*116, 0.14435259*116, 0.45515814*116, -0.017232867*116, -0.19365576*116, 0.2479277*116, 0.0869016*116, 0.08118829*116, -0.08660948*116, -0.08159728*116, -0.28299206*116, 0.14120346*116, 0.07167935*116, -0.055418238*116, 0.20511454*116, -0.10507759*116, 0.05069214*116, -0.13621165*116, 0.1018148*116, -0.017452046*116, -0.11347268*116, -0.123903535*116, 0.018025467*116, 0.108593635*116, 0.20639218*116, 0.042903867*116, 0.079064794*116, 0.1313311*116, 0.10675455*116, 0.071283005*116, -0.043715686*116, 0.00833489*116, 0.20388077*116, -0.28872487*116, 0.11853148*116, -0.15406317*116, -0.27146128*116, 0.09810602*116, 0.21268447*116, -0.17977887*116, 0.17054023*116, -0.04626449*116, -0.038740586*116, 0.24272496*116, 0.09864083*116, -0.08839305*116, 0.108765155*116, 0.06648569*116, -0.017118707*116, -0.049965873*116, 0.07447322*116, -0.04997925*116, -0.048469655*116, -0.090314634*116, 0.17996217*116, 0.045946427*116, -0.06299889*116, -0.12047542*116, 0.090083495*116, -0.01796094*116, -0.0755181*116, 0.11511006*116, 0.091049574*116, -0.22448586*116, -0.050723042*116, -0.03139614*116, 0.0015831261*116, -0.11613767*116, -0.14818639*116, 0.19182663*116, -0.17198901*116, -0.01207031*116, -0.026481537*116, 0.08545957*116, -0.22856851*116, 0.0698036*116, 0.060671277*116, 0.083088525*116, 0.23023446*116, 0.16840172*116, -0.026101237*116, -0.013624341*116, 0.20029439*116, -0.13435416*116, 0.051859945*116, -0.03294876*116, 0.0026523876*116, 0.050217785*116, 0.11897624*116, -0.016777916*116, -0.011480637*116, -0.05824107*116, 0.0047345436*116, -0.15653783*116, -0.096659355*116, -0.12413759*116, 0.008341504*116, -0.22265412*116, -0.084741816*116, -0.049052805*116, -0.10943685*116, 0.015849313*116, 0.15728374*116, 0.019623732*116, 0.03968261*116, 0.12137071*116, 0.04801433*116, 0.17361487*116, 0.0375894*116, -0.2880193*116, 0.23835266*116, -0.12525313*116, -0.03802259*116, 0.048765503*116, 0.16580069*116, -0.034263246*116, -0.042345356*116, 0.21693188*116, 0.0160256*116, 0.22338378*116, 0.08801989*116, -0.09912503*116, -0.12958637*116, -0.16876239*116, -0.119154476*116, -0.06458652*116, 0.13559924*116, -0.08274482*116, 0.09851591*116, -0.13950281*116, -0.08128978*116, -0.07134519*116, -0.06348143*116, 0.07731527*116, 0.8280366*116, -0.0052982206*116, -0.13149832*116, 0.044492595*116, -0.011585426*116, -0.025728377*116, -0.11657101*116, 0.33901116*116, 0.26238385*116, 0.040091164*116, 0.0069680647*116, -0.07552947*116, -0.17357907*116, -0.24296172*116, 0.020829068*116, -0.043665383*116, -0.22338052*116, 0.015621769*116, -0.17853606*116, 0.17728288*116, -0.29505402*116, -0.025503175*116, -0.07851867*116, 0.22452815*116, 0.35851127*116, 0.04793816*116, 0.30195788*116, -0.033786636*116, -0.08329632*116, 0.04386895*116, -0.0604825*116, -0.04106979*116, -0.0970791*116, 0.02890425*116, -0.27355868*116, -0.30892164*116, -0.050025415*116, 0.051394224*116, 0.029625721*116, 0.22651966*116, -0.09385346*116, 0.091123864*116, 0.29258752*116, -0.017623784*116, -0.06437612*116, 0.12376437*116, -0.04804968*116, -0.2738284*116, -0.048880357*116, 0.49358988*116, -0.11804319*116, 0.11794742*116, -0.09757697*116, -0.16289848*116, 0.1674699*116, 0.2491782*116, -0.09354803*116, -0.12968026*116, -0.18074937*116, 0.047699768*116, -0.04925385*116, 0.10451781*116, -0.0860195*116, -0.2947399*116, 0.099013805*116, 0.014356946*116, 0.016442439*116, -0.104442514*116, 0.28262013*116, 0.084576376*116, -0.07170916*116, 0.06730355*116, -0.09363791*116, -0.090837546*116, -0.06900049*116, 0.17235193*116, -0.05387173*116, -0.04022042*116, -0.14842543*116, 0.09172464*116, 0.15214087*116, -0.114173785*116, -0.31281304*116, -0.13934258*116, -0.23472688*116, 0.028988674*116, 0.041655973*116, -0.084807776*116, -0.07998778*116, -0.0020996314*116, -0.105367176*116, 0.12847185*116, -0.036877487*116, 0.08797664*116, 0.21941166*116, 0.048040316*116, -0.21971028*116, 0.1403861*116, -0.19761778*116, -0.1772033*116, 0.07097089*116, -0.0906795*116, -0.0012016009*116, 0.14546606*116, -0.16743821*116, 0.02910293*116, -0.027321115*116, 0.113381214*116, 0.10415426*116, 0.0077863308*116, 0.14755127*116, 0.0758921*116, -0.107782185*116, -0.2180801*116, -0.0029856646*116, 0.23887387*116, -0.059026387*116, 0.083202444*116, -0.102109335*116, 0.26125103*116, -0.18883845*116, 0.32701877*116, -0.05854063*116, 0.030744476*116, 0.060542926*116, -0.15563425*116, 0.21365932*116, 0.16811651*116, -0.1539785*116, 0.0060056006*116, -0.13011768*116, 0.11512094*116, -0.01616142*116, 0.18545856*116, -0.06943576*116, 0.16988988*116, 0.17190841*116, 0.17943119*116, 0.027053073*116, -0.33456832*116, 0.22187084*116, -0.1599899*116, -0.0007103954*116, 0.11979643*116, -0.023170989*116, 0.07291632*116, 0.34254375*116, 0.07731041*116, 0.028997138*116, -0.23691364*116, 0.06594546*116, -0.1793266*116, 0.063761905*116, -0.13588502*116, 0.09827516*116, -0.025957515*116, -0.21703084*116, -0.17717946*116, -0.24826092*116, -0.051142547*116, 0.23926984*116, -0.011865829*116, 0.1197209*116, 0.012579382*116, -0.09762748*116, 0.20540415*116, 0.09432887*116, -0.24820045*116, 0.09815941*116, 0.0953708*116, 0.054034542*116, -0.2564139*116, 0.080424614*116, -0.13179159*116, 0.17827833*116, 0.05085734*116, 0.25690216*116, 0.1838417*116, 0.17692699*116, -0.2869892*116, -0.0060810484*116, -0.18535866*116, -0.16468222*116, -0.068774305*116, -0.10608456*116, -0.20146582*116, 0.038750738*116, 0.040724173*116, -0.112324074*116, -0.12476345*116, -0.064113766*116, -0.28252*116, 0.08313103*116, -0.11339639*116, 0.21596256*116, -0.08958196*116, 0.28259006*116, -0.14769061*116, 0.033774577*116, 0.04332696*116, -0.2740367*116, -0.19442382*116, -0.014206208*116, 0.2034179*116, -0.124048255*116, 0.19053634*116, -0.09202896*116, -0.040349454*116, -0.12356522*116, 0.07238267*116, -0.16361497*116, -0.28146803*116, -0.006780825*116, 0.20285301*116, -0.24802816*116, -0.00614236*116, -0.13513815*116, -0.02439135*116, -0.045290463*116, 0.34074035*116, 0.011430777*116, -0.02819297*116, 0.19733678*116, -0.052078653*116, -0.07713819*116, 0.09142869*116, -0.0853608*116, -0.036110952*116, 0.18744758*116, -0.37616217*116, -0.13939881*116, 0.20057927*116, 0.09981935*116, 0.06757579*116, -0.08294938*116, 0.0012222475*116, -0.032138348*116, 0.06708932*116, -0.024812773*116, -0.02269876*116, -0.1550917*116, -0.10796631*116, 0.085546*116, 0.17008747*116, -0.1619468*116, 0.31193498*116, 0.16464321*116, -0.04418035*116, -0.23130503*116, 0.017088305*116, 0.10402318*116, -0.008818055*116, 0.10982997*116, 0.12081705*116, 0.2968502*116, -0.024664205*116, 0.14488357*116, -0.045923203*116, 0.037035987*116, 0.14956824*116, 0.052968316*116, 0.18219148*116, -0.1856882*116, 0.08196633*116, 0.12701318*116, 0.17902072*116, 0.10705486*116, -0.1506262*116, 0.06533659*116, 0.07169946*116, -0.04429789*116, 0.25293508*116, 0.14312991*116, -0.3269432*116, 0.06561909*116, 0.11255808*116, 0.033808645*116, 0.0013163063*116, 0.10883458*116, -0.19523966*116, -0.07400549*116, 0.13570647*116, 0.03529069*116, -0.24513762*116, -0.21422611*116, -0.14858976*116, 0.010077806*116, -0.11919819*116, 0.06777479*116, 0.2312263*116, -0.10999554*116, -0.2909872*116, -0.27197558*116, 0.15243445*116, 0.115065835*116, 0.14106485*116, -0.020063695*116, 0.1324707*116, -0.15370747*116, -0.03639656*116, 0.11950782*116, -0.14658584*116, 0.4975122*116, -0.28639045*116, 0.22010948*116, -0.16222207*116, 0.046106998*116, -0.011771402*116, -0.06603548*116, -0.21814077*116, 0.0013639763*116, 0.10283733*116, -0.21881676*116, 0.123836294*116, 0.12038011*116, -0.024595696*116, -0.15525815*116, -0.046002965*116, 0.122238465*116, -0.13550787*116, 0.0186061*116, -0.20389137*116, 0.07833766*116, -0.03659704*116, 0.18350202*116, 0.0069917175*116, 0.08232946*116, -0.06482292*116, -0.15902106*116, -0.27328262*116, -0.12255478*116, 0.025923438*116, 0.045660395*116, -0.07441448*116, 0.16825083*116, -0.13341744*116, 0.023831582*116, 0.061031435*116, -0.07632138*116, -0.021407621*116, -0.15193488*116, -0.13658023*116, 0.034671586*116, 0.01525949*116, 0.019782756*116, 0.17027092*116, -0.17826128*116, 0.0412991*116, 0.10556323*116, 0.13638009*116, -0.06659696*116, 0.10783231*116, -0.11596031*116, 0.09258666*116, 0.06845249*116, 0.09695611*116, 0.016963426*116, -0.14901866*116, -0.102535754*116, 0.3822919*116, -0.20109195*116, -0.1300498*116, -0.047988024*116, 0.34787732*116, 0.20807385*116, 0.0062658563*116, -0.104138315*116, -0.24642928*116, -0.25441855*116, 0.13056426*116, 0.13664941*116, -0.028806895*116, -0.11271389*116, 0.022132006*116, 0.3440761*116, 0.06250615*116, -0.1543593*116, 0.18216214*116, -0.011818232*116, 0.059234418*116, -0.0031463504*116, 0.18646033*116, 0.12645689*116, 0.16355313*116, 0.09293369*116, -0.064422734*116, -0.24735157*116, 0.13730288*116, 0.07111044*116, -0.094545215*116, 0.07435609*116, 0.15199597*116, 0.1559699*116, -0.17215453*116, -0.015482662*116, -0.106428586*116, -0.101328045*116, -0.2818378*116, 0.011500881*116, 0.011607031*116, 0.3456074*116, 0.054429237*116, -0.15013814*116, -0.072165176*116, -0.1316176*116, -0.09215108*116, -0.06401705*116, -0.05470018*116, -0.17071687*116, 0.11612276*116, -0.17115362*116, -0.08386609*116, 0.08446*116, 0.17461751*116, -0.21944842*116, 0.016591068*116, 0.19342448*116, -0.011339216*116, 0.08944442*116, 0.094735496*116, 0.0722237*116, 0.13846393*116, -0.05581913*116, 0.17142293*116, -0.21733609*116, -0.32631534*116, 0.017694728*116, 0.082749285*116, 0.132562*116, 0.11983708*116, 0.038064387*116, -0.27369037*116, 0.038788904*116, -0.070834346*116, 0.09804202*116, -0.14010724*116, 0.119645886*116, -0.006219569*116, 0.033842888*116, 0.23941624*116, 0.100891*116, -0.14551096*116, -0.027057175*116, 0.13437086*116, 0.014597796*116, 0.14618148*116, -0.17001061*116, -0.026705237*116, -0.13085073*116, 0.06995763*116, -0.07316468*116, -0.08777117*116, 0.07470633*116, 0.08282851*116, -0.035116147*116, -0.10294778*116, 0.056559052*116, 0.17396952*116, -0.013374727*116, -0.21727516*116, -0.15894413*116, -0.18561839*116, 0.09703011*116, 0.0727241*116, -0.15986401*116, -0.08875459*116, -0.0493837*116, 0.08726478*116, 0.18936035*116, 0.26762867*116, 0.25220624*116, -0.10227412*116, -0.16325004*116, 0.041970514*116, 0.15962203*116, -0.24921744*116, 0.10217798*116, -0.24202572*116, 0.08502299*116, 0.06929912*116, -0.0622391*116, 0.09863908*116, 0.10964072*116, -0.09222261*116, -0.059327383*116, 0.2670462*116, -0.37020582*116, -0.15557525*116, 0.15829*116, -0.020123089*116, -0.02376087*116, -0.17915241*116, -0.16348246*116, 0.07718554*116, -0.03861307*116, 0.17384036*116, 0.28659782*116, -0.03285029*116, 0.020995542*116, -0.15379879*116, -0.06443796*116, -0.080540955*116, -0.20529395*116, -0.112571254*116, 0.057033982*116, 0.21975854*116, 0.020832473*116, -0.113652304*116, 0.068490155*116, -0.16868877*116, 0.05636523*116, 0.10145199*116, -0.117186025*116, 0.15440923*116, -0.34543163*116, 0.20547387*116, 0.050379686*116, -0.07537513*116, 0.23492007*116, 0.14859922*116, 0.006805674*116, -0.10297409*116, 0.10409527*116, -0.12332598*116, -0.29696727*116, -0.17345858*116, 0.22828278*116, -0.17689945*116, 0.20619309*116, -0.03149208*116, -0.32625508*116, 0.09411195*116, -0.029254999*116, -0.052293777*116, 0.0952092*116, -0.04590893*116, 0.15845609*116, -0.27912793*116, 0.028300377*116, 0.14764339*116, 0.23150241*116, 0.09945416*116, 0.08188039*116, -0.04255949*116, -0.19597162*116, -0.018026391*116, 0.10282558*116, 0.2310576*116, 0.038130783*116, 0.15017013*116, 0.014065159*116, -0.24689046*116, -0.29592228*116, -0.19789483*116, 0.16624951*116, -0.012627667*116, -0.038005304*116, 0.1423015*116, -0.22701056*116, -0.030494554*116, 0.04992247*116, -0.030869467*116, -0.19912918*116, -0.029653296*116, 0.17897472*116, -0.102799796*116, -0.09161437*116, -0.30101818*116, 0.25719264*116, 0.15645666*116, 0.01686676*116, -0.26071087*116, 0.07239904*116, 0.11950495*116, -0.2669974*116, -0.05283948*116, -0.044542756*116, -0.034826458*116, -0.07447585*116, 0.015922958*116, -0.019828027*116, 0.3010939*116, -0.08526047*116, -0.18944193*116, -0.062195353*116, 0.001490199*116, 0.024983764*116, -0.26311702*116, -0.3489376*116, 0.015324487*116, -0.06661262*116, 0.009127537*116, 0.044109702*116, -0.080478296*116, 0.11690308*116, 0.2173571*116, 0.17790583*116, 0.034544244*116, 0.015028342*116, 0.044302385*116, 0.048755806*116, -0.019794513*116, 0.054733265*116, -0.09607846*116, 0.23372093*116, -0.07492475*116, -0.018963179*116, -0.02306656*116, 0.036811765*116, 0.07285965*116, -0.11162559*116, -0.05615093*116, -0.2801535*116, 0.22394282*116, -0.985744*116, -0.12910226*116, 0.01923344*116, 0.22934401*116, 0.13681698*116, -0.28262672*116, -0.18466298*116, 0.22560509*116, -0.124027506*116, -0.12809591*116, 0.2932412*116, -0.07200173*116, -0.12950552*116, 0.033361547*116, 0.18408443*116, 0.08756928*116, -0.06742942*116, -0.18508394*116, -0.003397723*116, 0.29711217*116, -0.030486066*116, 0.064864226*116, 0.08520226*116, -0.18614273*116, -0.08449196*116, 0.1748595*116, -0.11734179*116, -0.13623655*116, -0.3807504*116, 0.05616472*116, 0.10287729*116, 0.12375327*116, 0.03289438*116, 0.08599665*116, -0.054405*116, 0.0034258342*116, 0.23305187*116, -0.022959815*116, 0.10613997*116, -0.040709298*116, -0.09536158*116, -0.09686814*116, 0.42664707*116, 0.15310156*116, -0.19518991*116, -0.09096338*116, -0.06362961*116, -0.12485045*116, -0.2178994*116, -0.028825147*116, -0.10796918*116, 0.115097225*116, 0.07486105*116, 0.0151352305*116, -0.01970418*116, 0.09340016*116, 0.109610625*116, 0.09221001*116, -0.05302054*116, 0.2462278*116, 0.14955576*116, -0.06435694*116, 0.17428148*116, 0.11683607*116, 0.023448292*116, -0.15187985*116, -0.1265612*116, -0.29379433*116, 0.06484908*116, 0.021933408*116, -0.07896188*116, 0.027991375*116, -0.15001494*116, -0.07317963*116, -0.038197704*116, 0.090640575*116, -0.09689775*116, -0.076502286*116, 0.21305412*116, -0.0630967*116, -0.038623836*116, -0.1371157*116, 0.16940263*116, -0.101364225*116, 0.26040366*116, -0.09037852*116, -0.010373892*116, 0.014966537*116, 0.021084394*116, -0.03133848*116, -0.039567918*116, 0.19393326*116, 0.025768645*116, 0.027218206*116, 0.04159561*116, -0.1910176*116, 0.26527435*116, 0.04858406*116, -0.12103549*116, -0.22637492*116, 0.104716055*116, 0.17291434*116, 0.0061954907*116, -0.1958817*116, 0.07638329*116, 0.09502008*116, 0.11787959*116, 0.09790919*116, 0.016241038*116, -0.050506804*116, 0.06164209*116, -0.18237633*116, 0.05848084*116, -0.09878724*116, 0.13056053*116, -0.30947065*116, 0.14615594*116, 0.20417349*116, 0.09690704*116, 0.1386085*116, 0.0072860364*116, 0.024861714*116, -0.29153857*116, 0.098363094*116, -0.036049265*116, 0.14016882*116, 0.2320685*116, -0.176955*116, -0.027030958*116, -0.102797605*116, -0.13294071*116, -0.28609535*116, -0.052705318*116, -0.15053616*116, -0.0087256795*116, -0.116626255*116, -0.3284756*116, 0.0055109826*116, -0.009694615*116, 0.14688067*116, -0.20851134*116, 0.17795616*116, 0.00017628314*116, -0.105186656*116, 0.06955316*116, -0.22796883*116, 0.15034893*116, -0.09279041*116, 0.14651355*116, -0.24856295*116, 0.1857243*116, -0.08106365*116, 0.0012609457*116, -0.19308585*116, 0.090213925*116, -0.061102606*116, 0.27418578*116, -0.057887483*116, -0.1848829*116, 0.04715534*116, -0.112133175*116, -0.023872435*116, -0.029059753*116, -0.08394387*116, 0.0130831*116, -0.019860856*116, 0.10723802*116, 0.024947926*116, -0.031219637*116, -0.08561294*116, -0.22230898*116, -0.0029558428*116, -0.06044061*116, 0.11461461*116, 0.08570835*116, 0.14246634*116, 0.116004355*116, -0.102480605*116, 0.15712264*116, 0.031977016*116, -0.037924256*116, -0.1634074*116, 0.254092*116, -0.107544936*116, -0.11639801*116, -0.13018243*116, 0.07505147*116, -0.3397479*116, -0.028594533*116, -0.040605854*116, -0.19256957*116, 0.12539086*116, 0.023031428*116, -0.14950682*116, -0.012083957*116, 0.42602557*116, 0.11066037*116, 0.1289177*116, 0.05893631*116, 0.1976298*116, 0.018635688*116, 0.024127908*116, -0.032019652*116, -0.058816798*116, -0.1118879*116, 0.026310686*116, -0.039167974*116, 0.028002815*116, 0.07440581*116, 0.19408727*116, 0.05746198*116, 0.09282821*116, -0.45077306*116, 0.4308505*116, 0.27747095*116, -0.009126241*116, -0.26439312*116, 0.048964053*116, -0.011288185*116, -0.118360095*116, -0.15127799*116, -0.19276188*116, 0.17402418*116, -0.025310844*116, -0.13960528*116, 0.021385062*116, -0.31656432*116, 0.19206601*116, 0.26628706*116, 0.099570155*116, 0.07328856*116, 0.08164057*116, -0.17422879*116, -0.06988841*116, 0.4029068*116, 0.25785777*116, -0.05268537*116, -0.115724914*116, -0.13886417*116, 0.108077675*116, 0.054030094*116, -0.10390518*116, -0.046284664*116, 0.07399739*116, 0.08553891*116, 0.036463667*116, 0.19753091*116, 0.06404117*116, 0.0036079746*116, -0.19054486*116, -0.021931078*116, 0.20173463*116, 0.25503585*116, -0.3309682*116, 0.17389193*116, -0.10708898*116, 0.07169373*116, 0.14703366*116, 0.022595065*116, -0.21735238*116, 0.07237804*116, -0.17584985*116, 0.06349415*116, 0.05804331*116, -0.12876594*116, 0.012512973*116, -0.053588994*116, 0.020455547*116, -0.19794798*116, 0.08964927*116, 0.14070138*116, -0.1549377*116, -0.31523913*116, -0.22939114*116, 0.123844795*116, 0.27223086*116, 0.047859598*116, -0.27525225*116, 0.2716673*116, -0.21365373*116, -0.14149229*116, -0.12226513*116, -0.2190573*116, -0.1820415*116, 0.0046743653*116, -0.0023485767*116, 0.08756735*116, -0.1463978*116, -0.026959108*116, -0.09242074*116, -0.21411249*116, 0.07064765*116, 0.013838699*116, 0.30724612*116, 0.054866053*116, 0.20912325*116, 0.12252013*116, -0.022019012*116, 0.1338595*116, 0.037240613*116, -0.06275885*116, -0.28476533*116, -0.10571622*116, 0.08518946*116, -0.07840649*116, -0.07967211*116, -0.07266941*116, 0.08140416*116, 0.010040325*116, 0.2571349*116, 0.13339318*116, 0.024524326*116, 0.11715235*116, -0.07210037*116, -0.0782924*116, -0.01483534*116, -0.063824326*116, 0.033492383*116, 0.06841891*116, -0.19763958*116, -0.080669634*116, 0.18058713*116, -0.0673672*116, -0.025387753*116, -0.25436836*116, 0.24820127*116, 0.01392847*116, -0.092084944*116, -0.19179384*116, 0.016865535*116, -0.26907447*116, -0.22404978*116, 0.12550105*116, 0.12028001*116, -0.099222235*116, 0.1807234*116, -0.19183823*116, 0.14299679*116, 0.06929543*116, -0.013009767*116, -0.060884554*116, -0.16753966*116, 0.16354835*116, 0.114761375*116, -0.028625766*116, 0.07876972*116, 0.2343202*116, 0.06845436*116, -0.06841035*116, 0.21925211*116, -0.19163439*116, -0.1364578*116, 0.0597787*116, -0.057270847*116, 0.32278797*116, -0.08849325*116, -0.1432322*116, -0.0016841716*116, -0.11075198*116, 0.08507601*116, -0.16900404*116, 0.17933951*116, -0.2706311*116, -0.051163603*116, -0.0064171115*116, -0.03577601*116, -0.1360447*116, -0.017090004*116, -0.01788767*116, 0.43423462*116, 0.31487826*116, 0.08984946*116, 0.20524836*116, 0.155733*116, -0.2549216*116, 0.16849624*116, -0.3742941*116, 0.3296416*116, -0.13405155*116, -0.08046972*116, -0.50493574*116, -0.0934858*116, -0.17538801*116, 0.089533016*116, -0.026846185*116, 0.29087943*116, -0.2061303*116, -0.12700729*116, 0.14375564*116, 0.031004017*116, -0.1258669*116, 0.13668074*116, -0.07745023*116, -0.18828078*116, -0.07587115*116, 0.061179638*116, 0.04157418*116, -0.10212291*116, 0.09099856*116, 0.018471185*116, 0.25060683*116, 0.41457853*116, 0.07960436*116, -0.13253823*116, -0.14304858*116, -0.09650831*116, -0.45529774*116, 0.04426225*116, -0.20875533*116, -0.1099721*116, 0.04566922*116, 0.31354225*116, 0.4068707*116, -0.04826597*116, -0.21102852*116, -0.19121884*116, 0.12529665*116, -0.015098768*116, -0.19549201*116, -0.12830718*116, -0.020049997*116, 0.044782825*116, -0.006911764*116, 0.22945078*116, -0.10773029*116, -0.007421897*116, -0.32664764*116, 0.25663954*116, 0.11825782*116, -0.13145372*116, 0.24326156*116, 0.050536823*116, 0.06748154*116, 0.14406705*116, -0.04696456*116, 0.024240434*116, 0.107389204*116, 0.038954563*116, 0.007079051*116, 0.099587*116, 0.14789233*116, 0.09236868*116, -0.058214802*116, -0.03266838*116, -0.1604875*116, 0.18344773*116, 0.060033575*116, 0.01027773*116, -0.13954115*116, -0.15617484*116, 0.021251354*116, 0.06308005*116, -0.05842876*116, -0.04007486*116, 0.0039982623*116, -0.1559916*116, -0.022111783*116, 0.27678934*116, -0.15729372*116, -0.14106417*116, -0.21144873*116, 0.11272907*116, -0.08064718*116, 0.1317594*116, -0.024498602*116, -0.2698319*116, 0.08229889*116, -0.12625143*116, 0.1368939*116, 0.003455004*116, 0.048807237*116, 0.03829899*116, -0.0937626*116, -0.04583761*116, -0.13445596*116, 0.10864594*116, -0.28716153*116, -0.0025552015*116, -0.0904665*116, -0.020999843*116, 0.2591089*116, 0.12131464*116, 0.025138825*116, -0.12781811*116, -0.007379857*116, 0.2744127*116, -0.06442562*116, 0.01919537*116, -0.06666864*116, 0.010210617*116, 0.019809155*116, 0.21260247*116, -0.29349267*116, 0.04446011*116, -0.1449109*116, 0.17056327*116, -0.09822643*116, -0.07918772*116, 0.18540446*116, -0.18674968*116, 0.060696457*116, 0.19323857*116, 0.37463722*116, 0.15554431*116, -0.12451839*116, 0.11810772*116, -0.20492022*116, -0.12302484*116, 0.06303723*116, 0.0721875*116, 0.17748463*116, -0.15018743*116, 0.00055294967*116, -0.09545962*116, -0.1820535*116, -0.20985214*116, 0.12729807*116, -0.008803509*116, 0.06233212*116, 0.078907065*116, -0.4458931*116, 0.11443796*116, -0.37358528*116, 0.112572476*116, -0.15544702*116, 0.01963087*116, 0.19595096*116, -0.0013764852*116, -0.12199997*116, -0.17111194*116, 0.40158916*116, 0.034388993*116, -0.12772506*116, -0.14593832*116, 0.25897947*116, -0.045330554*116, -0.32598147*116, -0.006594084*116, -0.090772726*116, 0.11844245*116, -0.3029777*116, -0.016815923*116, 0.034984197*116, -0.17201921*116, 0.16515897*116, -0.05322517*116, 0.027632575*116, -0.05698579*116, 0.15530787*116, -0.009511882*116, 0.15283409*116, 0.025627205*116, -0.04325262*116, 0.04470898*116, 0.021302404*116, 0.3038313*116, -0.066904776*116, -0.091970734*116, -0.2440357*116, 0.14901999*116, -0.10088464*116, 0.15857221*116, 0.06340526*116, -0.11604547*116, 0.024512894*116, 0.059651338*116, 0.36578432*116, 0.12781884*116, -0.13423797*116, 0.20346685*116, 0.009482512*116, -0.011920539*116, 0.100357346*116, -0.06922565*116, 0.28274155*116, 0.10421727*116, 0.045371015*116, 0.0005611957*116, -0.07898859*116, -0.38670686*116, 0.002213526*116, 0.15280248*116, -0.007197188*116, -0.22495228*116, -0.4314828*116, 0.12527359*116, -0.30761564*116, 0.16240078*116, 0.017324215*116, -0.031632725*116, 0.047380585*116, -0.107502334*116, 0.0143527035*116, -0.030717034*116, 0.12856781*116, -0.16306588*116, 0.08888439*116, -0.18410686*116, -0.002396204*116, -0.039901514*116, -0.12448579*116, 0.08800649*116, -0.016522402*116, -0.12499365*116, -0.27028832*116, -0.06921326*116, 0.12693745*116, 0.1772033*116, 0.10554935*116, 0.13005666*116, 0.16505177*116, -0.23968259*116, 0.07970958*116, 0.0008147449*116, -0.059235226*116, -0.35217655*116, 0.24638249*116, -0.030533578*116, 0.29167047*116, 0.119313*116, -0.16502881*116, 0.080212146*116, -0.06826077*116, -0.14751421*116, 0.15312321*116, -0.055788808*116, 0.10345566*116, -0.06444704*116, -0.06298565*116, -0.1375894*116, 0.09220825*116, -0.12581713*116, 0.015585932*116, 0.07069656*116, 0.002113153*116, -0.066592336*116, -0.015376682*116, 0.025390424*116, 0.060157184*116, 0.0059475754*116, 0.039659955*116, 0.060015973*116, -0.24900469*116, -0.11632136*116, -0.03022932*116, -0.08419427*116, -0.13890547*116, 0.14035031*116, -0.06518771*116, -0.20108224*116, 0.24433835*116, 0.09347191*116, 0.07941313*116, -0.2447118*116, 0.084823176*116, -0.1956664*116, -0.10266355*116, 0.19357327*116, 0.23240793*116, 0.13140975*116, -0.10316179*116, -0.06032794*116, 0.0050379*116, 0.053951483*116, -0.16367632*116, 0.18935587*116, -0.06774194*116, -0.014802445*116, 0.0008350403*116, -0.105031446*116, -0.20689008*116, 0.070149094*116, 0.12620725*116, -0.08304098*116, 0.1543388*116, 0.14554998*116, 0.11428369*116, 0.042889684*116, -0.17441566*116, 0.072830826*116, -0.19796364*116, 0.21249002*116, -0.049382046*116, -0.17389557*116, -0.023221547*116, 0.14488377*116, -0.010594082*116, 0.13000193*116, 0.19341987*116, 0.048369355*116, -0.06378005*116, -0.007129887*116, 0.09935901*116, 0.04932831*116, 0.19598*116, -0.19755247*116, 0.10616436*116, -0.06200788*116, 0.12858467*116, -0.06829934*116, -0.03757281*116, -0.15698285*116, -0.26788482*116, 0.25586152*116, -0.094519936*116, -0.16726227*116, 0.14396492*116, 0.025547234*116, 0.029683627*116, -0.09472451*116, -0.09933552*116, 0.01024292*116, 0.0074631195*116, 0.20749488*116, 0.039849564*116, -0.05643797*116, -0.11336118*116, -0.23882702*116, 0.019796034*116, 0.0016982561*116, -0.2728555*116, 0.039987396*116, 0.24307464*116, -0.09748058*116, 0.064244464*116, -0.12815593*116, 0.037642*116, -0.10406342*116, -0.19502197*116, -0.18836367*116, 0.28575295*116, 0.092043675*116, -0.12020435*116, -0.08229852*116, 0.053434346*116, -0.12964205*116, -0.3105289*116, 0.080906466*116, 0.0013863698*116, 0.095448256*116, -0.03878001*116, -0.013719884*116, 0.118774116*116, -0.21621339*116, -0.17585486*116, -0.2069127*116, 0.20480923*116, 0.27916056*116, -0.20631543*116, 0.182852*116, -0.32337618*116, 0.15093942*116, 0.011894354*116, 0.20203742*116, -0.17441998*116, -0.15002857*116, 0.14676127*116, -0.115748405*116, 0.24217017*116, 0.08262262*116, -0.22564481*116, 0.14139126*116, 0.10186977*116, 0.32122433*116, -0.1433979*116, -0.1723597*116, -0.17144099*116, -0.15625167*116, 0.009299754*116, 0.15362598*116, 0.067775816*116, -0.1642106*116, 0.056628395*116, -0.092956826*116, -0.018301625*116, -0.041385487*116, 0.17021222*116, -0.2792117*116, -0.0906409*116, 0.17141263*116, -0.32310146*116, 0.2531806*116, -0.008412564*116, -0.30922636*116, 0.2698887*116, 0.061553936*116, -0.07994713*116, -0.17949648*116, 0.12671371*116, 0.11898851*116, 0.21665508*116, -0.02929004*116, -0.26856086*116, -0.13113149*116, -0.032722753*116, 0.007532282*116, 0.2121104*116, 0.010455154*116, -0.119184114*116, 0.07881422*116, 0.0014533376*116, 0.015436139*116, 0.03318639*116, 0.1408773*116, -0.08103935*116, -0.20311195*116, 0.04675188*116, 0.1576871*116, -0.017048651*116, 0.15085326*116, -0.063475266*116, 0.041940875*116, 0.36008713*116, 0.12937364*116, 0.23931058*116, -0.33793002*116, 0.053289294*116, 0.015092221*116, -0.03287915*116, -0.10770671*116, 0.12631804*116, -0.075561844*116, -0.22845244*116, -0.043771002*116, 0.3039201*116, 0.03342554*116, -0.013706769*116, -0.009514856*116, -0.11564545*116, 0.2179875*116, 0.011456639*116, 0.000618257*116, -0.079393014*116, -0.046363205*116, 0.20494623*116, 0.13814677*116, 0.026100826*116, -0.09089179*116, -0.004002544*116, -0.12322662*116, 0.09117457*116, 0.29757383*116, -0.102190726*116, 0.06137876*116, 0.102813385*116, 0.013294586*116, -0.030382367*116, -0.16465658*116, -0.086355515*116, -0.31799278*116, -0.16026703*116, -0.1046635*116, 0.3108371*116, -0.016031096*116, -0.10032822*116, 0.01486066*116, 0.12698932*116, -0.086736575*116, -0.35792962*116, -0.2810554*116, -0.20461373*116, -0.09966237*116, -0.06635781*116, -0.042357214*116, 0.06817182*116, -0.12368499*116, 0.19269566*116, 0.11523449*116, 0.093094975*116, 0.13760212*116, -0.0050844206*116, 0.22251879*116, 0.024346106*116, -0.053995788*116, -0.05956339*116, 0.17371431*116, -0.20196944*116, -0.16192964*116, 0.062100355*116, 0.11450387*116, -0.21539411*116, 0.025982779*116, -0.09629115*116, 0.31876174*116, -0.12710893*116, 0.119614355*116, 0.0051020547*116, -0.09923047*116, -0.30157867*116, -0.07634582*116, 0.019637741*116, -0.17871986*116, 0.12573273*116, -0.12751588*116, -0.006205644*116, -0.11038973*116, 0.05129587*116, 0.08840472*116, -0.16902314*116, -0.082481764*116, -0.18987095*116, 0.26078236*116, -0.07684504*116, 0.27895182*116, -0.07897728*116, -0.028536778*116, -0.016445078*116, -0.19491401*116, 0.09009222*116, -0.14188243*116, 0.027302284*116, 0.22066061*116, -0.073197976*116, 0.31456527*116, 0.26131764*116, -0.06331041*116, -0.07115779*116, 0.19825813*116, 0.016114099*116, -0.17552885*116, 0.16768286*116, 0.16071114*116, -0.29194075*116, 0.14791243*116, 0.04654555*116, -0.013858114*116, -0.039024733*116, -0.17028154*116, -0.07198986*116, -0.25116128*116, 0.025213534*116, 0.19331184*116, -0.13179007*116, -0.05715049*116, 0.08180076*116, 0.14610742*116, -0.19879964*116, -0.010794456*116, 0.006149355*116, 0.05367875*116, 0.27144104*116, -0.092216104*116, 0.072805*116, -0.05946799*116, -0.080742344*116, 0.1534923*116, -0.29347306*116, 0.12829232*116, 0.21200329*116, -0.03598418*116, -0.02960482*116, 0.15202098*116, -0.09055104*116, 0.3041372*116, 0.1744338*116, 0.2781348*116, 0.105651505*116, 0.21240753*116, 0.09940778*116, -0.00867601*116, 0.112619326*116, 0.14903294*116, -0.10117162*116, -0.29814583*116, -0.21274458*116, -0.17238337*116, -0.2820328*116, -0.26813424*116, -0.013282676*116, 0.3404541*116, -0.19491701*116, 0.003995205*116, -0.01909613*116, 0.1479451*116, -0.19233441*116, 0.18886264*116, -0.0830549*116, -0.17106193*116, -0.05764294*116, -0.15414387*116, 0.16471718*116, -0.07699382*116, 0.08781656*116, -0.021221299*116, 0.02427495*116, -0.1079623*116, 0.32203308*116, 0.027169824*116, 0.24219386*116, 0.06549597*116, -0.036911797*116, -0.025034431*116, -0.15032488*116, 0.03208875*116, -0.056684643*116, -0.14810877*116, -0.19461866*116, -0.09913723*116, -0.06516272*116, 0.115041815*116, -0.07313753*116, 0.08805657*116, -0.058525722*116, 0.015545826*116, 0.32320076*116, -0.048784886*116, 0.28107563*116, -0.28567055*116, 0.18162198*116, 0.09853206*116, 0.089856334*116, -0.23054232*116, 0.010031682*116, 0.0040023774*116, -0.115931295*116, 0.00030246936*116, 0.121674016*116, 0.04455199*116, -0.092573054*116, 0.24860719*116, -0.20691586*116, 0.2359098*116, 0.013546704*116, 0.09570028*116, 0.021920133*116, -0.22603019*116, 0.185779*116, 0.06369126*116, 0.22849256*116, -0.12624893*116, -0.17127556*116, -0.19398524*116, 0.079756066*116, -0.07217791*116, 0.18616833*116, 0.19824994*116, -0.11778632*116, 0.0030153093*116, -0.18970443*116, -0.17059602*116, 0.019286849*116, 0.015344915*116, -0.25786868*116, 0.07668422*116, 0.10532826*116, -0.16138114*116, -0.14313404*116, 0.19653107*116, 0.1637474*116, -0.04570485*116, -0.69731176*116, -0.061714422*116, 0.2076678*116, 0.027298387*116, 0.15802957*116, -0.12099642*116, -0.0048600454*116, -0.22468941*116, -0.08796492*116, 0.09111624*116, 0.035653375*116, 0.0010765888*116, 0.024544146*116, 0.1639324*116, -0.04639113*116, -0.047186106*116, 0.029232362*116, -0.00075924076*116, -0.02716401*116, -0.33720216*116, 0.032124132*116, 0.05631061*116, 0.010886167*116, 0.07564431*116, -0.20307486*116, 0.24112727*116, -0.05716067*116, 0.1548566*116, 0.0077807847*116, -0.046431966*116, 0.044104062*116, -0.063834295*116, 0.100082844*116, -0.18650913*116, 0.04481869*116, 0.18340854*116, -0.10809293*116, 0.11140287*116, -0.104357384*116, 0.02425862*116, 0.012612522*116, 0.12036239*116, -0.31984678*116, -0.024346378*116, -0.12434188*116, 0.0860462*116, 0.09727766*116, 0.10292318*116, -0.060845986*116, -0.28410676*116, 0.05853942*116, 0.014791051*116, -0.09366086*116, 0.17212243*116, -0.18638459*116, -0.10702777*116, 0.097982444*116, 0.013552232*116, 0.06570246*116, -0.067844786*116, -0.020537164*116, -0.01889493*116, 0.18025868*116, 0.2592968*116, -0.021256069*116, 0.1973698*116, -0.19241947*116, 0.08227162*116, -0.0623961*116, 0.17762008*116, -0.085184604*116, 0.07006337*116, 0.19904126*116, -0.024488153*116, 0.026741283*116, -0.22300926*116, 0.120275185*116, -0.2706815*116, -0.11300622*116, -0.0041443096*116, 0.07011434*116, 0.010821731*116, 0.15786779*116, 0.14254409*116, 0.18265569*116, 0.067827195*116, 0.09696018*116, -0.07376302*116, 0.080948405*116, -0.1932941*116, 0.07596587*116, -0.17257065*116, 0.20067237*116, -0.10923919*116, -0.08355018*116, 0.14472657*116, 0.031507567*116, -0.13472286*116, 0.1960226*116, 0.03241037*116, -0.06851481*116, 0.18196519*116, 0.035873607*116, -0.28620216*116, -0.030822197*116, -0.2007512*116, 0.0015974883*116, -0.15542658*116, -0.16889171*116, 0.119988814*116, -0.15601072*116, 0.04191751*116, 0.076345794*116, 0.1878378*116, -0.13473037*116, -0.1585308*116, 0.1398071*116, -0.20914298*116, 0.004866398*116, -0.13116269*116, 0.22146155*116, -0.23907378*116, 0.048827812*116, -0.08017252*116, 0.044650756*116, -0.008384064*116, -0.18938583*116, -0.05927894*116, 0.18884948*116, -0.006295201*116, -0.07482157*116, -0.10707136*116, 0.102290355*116, -0.17535247*116, 0.18621315*116, 0.020948315*116, 0.01939506*116, -0.15487005*116, -0.15275094*116, 0.15580912*116, -0.075169325*116, -0.08245737*116, 0.01527786*116, -0.03575766*116, 0.22374138*116, 0.06768467*116, -0.11137074*116, 0.08425488*116, -0.10882671*116, -0.12176097*116, -0.2665374*116, 0.022744343*116, 0.1771858*116, 0.19001049*116, -0.04539147*116, 0.06136615*116, -0.11057381*116, 0.039801337*116, -0.068686396*116, 0.12100771*116, -0.07299577*116, -0.018494673*116, -0.12234663*116, 0.12649105*116, 0.024780009*116, -0.18681695*116, 0.011995355*116, 0.06528714*116, -0.111862764*116, -0.256969*116, -0.10127364*116, 0.039747234*116, 0.18010625*116, -0.16810371*116, -0.16579825*116, 0.11116892*116, -0.19604655*116, -0.118696354*116, 0.010104336*116, 0.285551*116, -0.047446698*116, 0.071538605*116, 0.19887139*116, 0.21277878*116, -0.040348083*116, -0.020569151*116, 0.016910085*116, -0.1069541*116, 0.03107042*116, 0.12035835*116, 0.15991923*116, 0.10896145*116, -0.013492795*116, 0.0034267525*116, 0.028250488*116, -0.06703756*116, -0.13579415*116, -0.020111324*116, 0.04705372*116, 0.23073058*116, -0.053063035*116, -0.09016661*116, 0.023209747*116, 0.12301106*116, 0.29084924*116, 0.21244475*116, 0.022543436*116, 0.19904295*116, -0.034141008*116, 0.14033626*116, -0.0966481*116, 0.042695034*116, -0.23881371*116, -0.06761773*116, -0.13023531*116, -0.084895745*116, 0.14164627*116, 0.06671069*116, 0.08933816*116, 0.092563994*116, -0.09640117*116, 0.065673195*116, -0.14309576*116, -0.22597344*116, 0.0781987*116, 0.006089236*116, 0.12826148*116, -0.17179626*116, 0.1261669*116, -0.27801046*116, 0.9730418*116, 0.23689005*116, -0.02557007*116, -0.12109558*116, -0.10239245*116, -0.045308072*116, 0.4009235*116, -0.2235042*116, 0.10380118*116, 0.039038282*116, -0.15927112*116, 0.116878875*116, 0.11810046*116, 0.13380696*116, -0.109471224*116, -0.046537478*116, -0.13759416*116, 0.15750894*116, -0.12718678*116, -0.15521151*116, -0.009975052*116, -0.21369241*116, -0.06480569*116, 0.11039461*116, 0.131386*116, 0.0018941723*116, -0.062397458*116, -0.11381105*116, 0.13883732*116, -0.26365635*116, -0.057552673*116, 0.2510508*116, -0.16107944*116, 0.09728813*116, -0.11179043*116, 0.052470893*116, -0.12500377*116, 0.14813212*116, -0.032336157*116, -0.04158962*116, -0.07923276*116, 0.02206865*116, -0.14041999*116, -0.13736004*116, 0.18254615*116, 0.006343456*116, 0.2011516*116, 0.03931967*116, -0.06639519*116, -0.02854241*116, -0.22960967*116, 0.07603837*116, -0.17449915*116, -0.02552854*116, -0.107039995*116, -0.19685781*116, 0.08416617*116, 0.00053577183*116, 0.11335757*116, -0.04713981*116, -0.04340905*116, -0.06777233*116, -0.11437707*116, 0.12604043*116, 0.0624486*116, 0.13420555*116, 0.009551679*116, 0.34344327*116, -0.1292365*116, 0.0715093*116, 0.05354079*116, 0.1007184*116, 0.32491243*116, -0.22232649*116, 0.14914331*116, 0.036561415*116, 0.06559667*116, -0.13065095*116, -0.16438526*116, 0.041624714*116, 0.20779614*116, 0.24474965*116, -0.03974948*116, 0.1433016*116, -0.13045028*116, 0.43714634*116, -0.106283896*116, -0.03861404*116, 0.0031841965*116, 0.12640612*116, -0.13832101*116, -0.018352596*116, -0.06662614*116, 0.30596042*116, 0.15910815*116, -0.11242729*116, -0.015843224*116, 0.003843365*116, 0.07278694*116, -0.012284583*116, 0.22554584*116, -0.1281829*116, -0.08625304*116, -0.057282824*116, -0.086725935*116, 0.26952222*116, 0.40512723*116, 0.19625421*116, -0.03904468*116, 0.012511069*116, -0.10425577*116, -0.0014158423*116, 0.042810567*116, -0.06790584*116, 0.010971839*116, -0.07771016*116, 0.10472138*116, 0.20576587*116, 0.09019256*116, -0.001569345*116, 0.1480219*116, 0.030945195*116, -0.23982547*116, 0.22958177*116, 0.16713417*116, -0.16236134*116, 0.03577525*116, 0.038841415*116, 0.008934698*116, -0.23166555*116, -0.1728625*116, -0.34508863*116, 0.0659328*116, -0.20680709*116, 0.19389516*116, -0.16904038*116, -0.21758755*116, 0.09333476*116, -0.12629451*116, 0.10687034*116, -0.06249467*116, 0.0467268*116, 0.023825312*116, -0.2803893*116, -0.18198228*116, -0.3223955*116, -0.06522453*116, 0.0049303262*116, 0.42672205*116, -0.22793753*116, 0.050773744*116, -0.041232824*116, -0.14697027*116, -0.2975792*116, 0.13631223*116, -0.13895309*116, 0.1928437*116, -0.1155587*116, 0.09083343*116, -0.087556034*116, 0.03801281*116, -0.05968739*116, -0.15608431*116, -0.16628617*116, -0.017459717*116, 0.15035892*116, -0.1525085*116, 0.11996358*116, -0.14118987*116, 0.09258919*116, -0.03463298*116, 0.08969081*116, -0.063079186*116, -0.10037251*116, -0.14788981*116, 0.19895926*116, -0.23345543*116, 0.052065358*116, -0.019242939*116, 0.20002672*116, -0.1120181*116, 0.3363321*116, -0.0288291*116, 0.064460605*116, 0.25764126*116, 0.022164108*116, -0.25311962*116, 0.17027633*116, -0.26548478*116, -0.13719298*116, 0.23871042*116, -0.3504262*116, -0.21556078*116, 0.3447193*116, -0.08794932*116, 0.023286862*116, 0.06103484*116, -0.009612113*116, -0.04833337*116, 0.05420876*116, 0.15129478*116, -0.070443586*116, -0.13997047*116, -0.00662343*116, 0.017227598*116, 0.30107817*116, -0.14590353*116, 0.037813332*116, 0.009062514*116, -0.057829253*116, -0.22558331*116, 0.25078228*116, 0.013854455*116, 0.08792763*116, 0.046733305*116, 0.113375925*116, 0.272577*116, 0.095645614*116, 0.018981388*116, -0.15266746*116, 0.13636746*116, 0.019244201*116, -0.031877715*116, -0.18644334*116, 0.038040143*116, 0.2275061*116, 0.09070536*116, -0.078031845*116, -0.11399594*116, -0.011781385*116, 0.22037601*116, 0.19998033*116, -0.101280816*116, -0.03311988*116, -0.11764863*116, 0.02269148*116, -0.04020851*116, -0.029388217*116, 0.012845435*116, -0.12305292*116, -0.16055591*116, -0.031668123*116, -0.07416495*116, 0.12632233*116, 0.12950009*116, 0.08888572*116, -0.20061302*116, 0.21927284*116, -0.107071444*116, -0.10016861*116, -0.017746832*116, 0.21994393*116, -0.015952839*116, -0.08347752*116, 0.2289575*116, -0.17068057*116, 0.50983727*116, 0.114938736*116, -0.090677395*116, 0.019866113*116, -0.121970996*116, 0.23190972*116, 0.15659623*116, -0.12511618*116, 0.013740651*116, 0.11825636*116, -0.2605114*116, 0.16484308*116, 0.09914746*116, 0.139571*116, -0.06944538*116, 0.008882376*116, 0.052273877*116, 0.15790202*116, -0.20207316*116, -0.20721065*116, 0.016079837*116, -0.056065537*116, 0.035909377*116, 0.24041758*116, 0.11283084*116, -0.18286687*116, 0.10654038*116, -0.0509719*116, -0.06452586*116, -0.06059062*116, 0.09041825*116, 0.21707928*116, 0.013055704*116, 0.03853786*116, -0.005349672*116, 0.10438961*116, 0.12788144*116, -0.1257959*116, 0.115073115*116, 0.1857549*116, -0.23602395*116, 0.13669245*116, 0.016914776*116, 0.09589911*116, -0.08062423*116, 0.13818996*116, 0.031575214*116, 0.33544585*116, 0.24122776*116, -0.0286574*116, 0.10609075*116, -0.090270326*116, 0.045382693*116, -0.058601245*116, -0.06279977*116, -0.26623178*116, -0.00969276*116, -0.06585328*116, -0.142716*116, -0.035133123*116, -0.16299933*116, -0.43198556*116, -0.15798056*116, 0.028667368*116, -0.15613069*116, 0.10452501*116, 0.08528433*116, 0.299922*116, -0.03823417*116, -0.13935632*116, 0.06451641*116, 0.069903284*116, 0.09378329*116, 0.11967177*116, 0.11952348*116, -0.08683715*116, -0.11390089*116, -0.12663601*116, -0.13986479*116, 0.08242939*116, -0.45841032*116, -0.14604847*116, -0.12844406*116, 0.087534435*116, -0.17882663*116, -0.12586986*116, 0.1420984*116, -0.24287859*116, -0.039691474*116, 0.16000451*116, -0.08367999*116, 0.07568976*116, -0.21795283*116, -0.079231836*116, -0.25289974*116, -0.06111382*116, 0.12328356*116, -0.037060022*116, 0.112538405*116, 0.2225323*116, -0.034384344*116, -0.14454341*116, 0.2600626*116, -0.11178283*116, 0.0116291195*116, -0.007321702*116, 0.009696989*116, 0.03836042*116, 0.021263551*116, 0.03266453*116, 0.024404045*116, 0.15229946*116, 0.20612885*116, 0.030842036*116, 0.042055096*116, -0.09510711*116, 0.14180745*116, -0.0384318*116, 0.13011916*116, -0.1470301*116, -0.097358786*116, -0.09929235*116, 0.05825796*116, -0.038733613*116, 0.2778711*116, 0.09522766*116, -0.11773661*116, 0.04353124*116, -0.04644192*116, -0.24130675*116, 0.24266905*116, -0.081556566*116, 0.029074427*116, -0.15198126*116, -0.051669743*116, 0.047617476*116, 0.0009789503*116, 0.04128747*116, 0.071786396*116, 0.17758894*116, 0.049063243*116, -0.08812498*116, -0.009497259*116, -0.24271119*116, -0.16651693*116, -0.0629213*116, 0.01892928*116, -0.18287954*116, 0.13822189*116, -0.18761626*116, 0.08831548*116, -0.0631842*116, -0.23310477*116, 0.09298975*116, -0.17160647*116, 0.17382868*116, 0.022349961*116, -0.08985177*116, 0.047862444*116, 0.27220976*116, 0.077536464*116, -0.02359428*116, -0.03828719*116, 0.104947835*116, 0.044947296*116, -0.24894765*116, -0.000830266*116, 0.026518222*116, 0.03269754*116, 0.101650804*116, 0.04544409*116, 0.029981136*116, -0.13960452*116, 0.16684838*116, 0.04875329*116, 0.041974243*116, 0.25832132*116, -0.13004716*116, -0.026965197*116, -0.17132841*116, 0.18566193*116, -0.17195708*116, 0.04692997*116, -0.19232714*116, -0.097301625*116, 0.032712463*116, 0.09167285*116, -0.17407703*116, 0.020030659*116, -0.17190112*116, -0.004702077*116, 0.018779662*116, -0.0805728*116, -0.35927153*116, 0.0022403894*116, -0.07543052*116, 0.14690077*116, -0.19408856*116, -0.22572945*116, 0.10281466*116, 0.04469793*116, 0.20612286*116, 0.25363326*116, -0.12507631*116, 0.034817725*116, -0.26221025*116, 0.049768038*116, -0.08747687*116, -0.30818808*116, -0.043966442*116, 0.15858655*116, -0.0011771108*116, -0.057962365*116, 0.0035159846*116, -0.08040768*116, -0.1212767*116, 0.05137385*116, -0.05435501*116, 0.09229244*116, -0.22728497*116, -0.30666712*116, 0.1635341*116, 0.06841106*116, 0.1587459*116, 0.025312021*116, 0.0902232*116, 0.19398947*116, -0.11800068*116, 0.038526174*116, -0.1582077*116, -0.06778878*116, -0.17830904*116, 0.24928164*116, -0.05349797*116, 0.072332315*116, -0.13819745*116, -0.28354853*116, 0.018097842*116, 0.083243765*116, -0.2335971*116, 0.08946873*116, 0.014942429*116, 0.12781271*116, 0.03164742*116, -0.19200999*116, -0.1571731*116, -0.15487595*116, -0.063723326*116, -0.27445543*116, -0.07498874*116, -0.12855524*116, -0.08827533*116, 0.02840678*116, -0.02751718*116, -0.12091094*116, 0.17587215*116, -0.034071896*116, 0.02943424*116, 0.025107952*116, -0.13969989*116, 0.14952716*116, 0.1519881*116, 0.031505927*116, 0.21450168*116, -0.07679063*116, -0.23509227*116, 0.09452492*116, -0.04500257*116, 0.11578711*116, -0.26062942*116, 0.2319868*116, 0.23531011*116, 0.13758941*116, 0.32043368*116, 0.18199456*116, -0.048555583*116, -0.2199872*116, 0.20751286*116, -0.22012337*116, 0.11560435*116, -0.11470208*116

};

// 128
static const float key_point_fc_1_b[] ={
    0.02918073f, 0.058965314f, -0.0036470564f, -0.086789414f, 0.04419562f, -0.052453607f, -0.0031668774f, -0.0020767737f, 0.034498405f, 0.027095972f, -0.05772032f, 0.013791756f, 0.010346374f, -0.014532606f, 0.041147344f, 0.047011446f, 0.0035746875f, -0.084342785f, -0.026955964f, -0.042909674f, -0.020498024f, 0.054423787f, -0.01864773f, -0.058597233f, 0.0037462232f, -0.027714336f, -0.029916026f, 0.009770835f, 0.019425025f, 0.06688819f, 0.04630796f, -0.022457069f, 0.021336067f, 0.07488727f, -0.062021498f, -0.004328712f, -0.026139222f, -0.010611393f, -0.04485887f, 0.016410835f, 0.019054828f, -0.035780918f, -0.0070163077f, -0.0068516266f, -0.012662557f, 0.053337224f, 0.00602679f, 0.04030839f, -0.018329062f, -0.01954713f, 0.01128357f, -0.003961587f, 0.008925929f, 0.0113175055f, -0.019711986f, -0.0047825924f, -0.010246458f, 0.03695805f, 0.086188056f, -0.051925812f, -0.021124123f, -0.05580939f, 0.0151028875f, -0.05132877f, -0.03853031f, -0.013998395f, -0.005559736f, 0.030633362f, -0.06477506f, 0.027356464f, -0.040944878f, -0.012573698f, 0.00872812f, -0.051062226f, 0.003295112f, 0.052281637f, 0.023319425f, -0.013093434f, 0.02058873f, -0.009379124f, 0.04758649f, -0.011473593f, -0.01815659f, -0.010829602f, -0.026357457f, 0.08516586f, 0.027385725f, -0.027486455f, -0.028038915f, 0.03931998f, -0.005005477f, -0.027985027f, 0.012836505f, -0.010661875f, 0.018673295f, 0.022554442f, 0.044490267f, -0.010774705f, 0.034937862f, -0.014886185f, 0.03266976f, 0.042212803f, -0.08697724f, -0.025487075f, -0.03117971f, 0.048802897f, -0.019247103f, 0.018207006f, 0.061395228f, 0.0192284f, -0.00525908f, 0.05242351f, -0.007143753f, -0.02167142f, 0.040379923f, -0.003826517f, -0.051492814f, 0.036567494f, 0.051123954f, 0.028078547f, -0.022652535f, 0.0731458f, -0.013418258f, 0.0123560745f, -0.029151855f, 0.024965191f, -0.025527248f, 0.028986137f
};

// 128x10
static const int8_t key_point_fc_2_w[] ={
    -0.026576031*116, 0.24196357*116, -0.021770772*116, -0.0050464007*116, -0.18272316*116, -0.01726506*116, 0.029389568*116, 0.057845376*116, 0.015438934*116, 0.008850207*116, -0.057660244*116, -0.065249*116, -0.022388391*116, 0.07627227*116, -0.01877186*116, 0.018468529*116, -0.062126055*116, -0.018410567*116, 0.001439076*116, -0.033320565*116, 0.068942584*116, 0.026719144*116, -0.023128206*116, 0.020326527*116, 0.036303055*116, -0.053159893*116, -0.11060024*116, -0.100799076*116, -0.05744838*116, -0.044528984*116, 0.0070987632*116, -0.03515595*116, -0.020648593*116, -0.013683762*116, 0.0020958646*116, 0.014336404*116, 0.27536428*116, 0.08007652*116, 0.028954264*116, 0.037539244*116, 0.0075615323*116, -0.0006803127*116, 0.34373307*116, -0.018130857*116, 0.29316452*116, 0.041718572*116, -0.057742957*116, 0.060182594*116, -0.06924056*116, -0.0011879242*116, 0.014170417*116, -0.06913964*116, -0.0012812549*116, -0.19005404*116, -0.07023105*116, 0.044086177*116, -0.053980477*116, -0.072020076*116, 0.01873775*116, -0.10533103*116, -0.014322749*116, 0.039365854*116, 0.019182546*116, 0.08158301*116, 0.057681475*116, -0.01316679*116, -0.06547474*116, 0.017309248*116, 0.065768585*116, -0.17963044*116, 0.053391557*116, -0.06340017*116, 0.055315886*116, 0.034601275*116, 0.16515996*116, -0.013676118*116, -0.04856678*116, 0.057178766*116, -0.08016287*116, -0.07600031*116, 0.034235597*116, 0.0010192661*116, 0.043808497*116, 0.044578243*116, 0.18816717*116, -0.04418266*116, -0.06850952*116, -0.036814015*116, -0.058463648*116, -0.058517575*116, -0.31301612*116, 0.057694*116, -0.06930153*116, 0.082407385*116, -0.02494191*116, 0.04069746*116, -0.026370903*116, -0.02612703*116, -0.049900383*116, -0.027949085*116, -0.0014337328*116, -0.06165229*116, -0.026789626*116, 0.028290885*116, 0.1273108*116, -0.05837292*116, 0.05279472*116, -0.005014344*116, 0.057379838*116, -0.09890116*116, -0.03012713*116, -0.103264004*116, -0.023828503*116, 0.15928361*116, 0.11813301*116, -0.15698671*116, 0.31281382*116, -0.068005554*116, 0.045387987*116, 0.024997106*116, -0.0013501722*116, 0.009530082*116, 0.052210618*116, 0.049010254*116, -0.002548799*116, -0.054862484*116, -0.039892808*116, -0.042489182*116, -0.12516142*116, 0.0028665976*116, 0.020433512*116, -0.005938771*116, 0.03734486*116, -0.00021243146*116, -0.010790671*116, -0.004624409*116, -0.1446266*116, 0.011383337*116, 0.005231135*116, 0.0063963137*116, 0.18428275*116, -0.003991932*116, 0.16476691*116, -0.0052551874*116, -0.0049683684*116, -0.0016859164*116, 0.04521315*116, 0.024830906*116, -0.0007244955*116, -0.014410113*116, 0.008578782*116, -0.033693317*116, 0.08000993*116, 0.0004006189*116, -0.024070933*116, -0.005540129*116, 0.017377205*116, 0.08828042*116, 0.030832252*116, -0.00082023384*116, -0.012555716*116, -0.03604102*116, 0.020363258*116, 0.003265717*116, -0.0008173128*116, -0.0042401217*116, -0.0703973*116, 0.03973516*116, -0.025582476*116, 0.15390086*116, 0.0024513297*116, 0.04833859*116, -0.0052173138*116, 0.015671302*116, 0.012548207*116, -0.01818641*116, -0.015216812*116, -0.1046736*116, -0.09399538*116, -0.010912699*116, -0.006818141*116, 0.020077575*116, 0.10055422*116, -0.004049719*116, -0.0020835355*116, -0.00677111*116, 0.006139191*116, 0.067833945*116, 0.020900052*116, -0.050237462*116, -0.020485248*116, 0.09167484*116, -0.043051615*116, 0.011266922*116, -0.013341279*116, -0.066857465*116, -0.032056205*116, 0.08485282*116, 0.021109158*116, -0.00165944*116, -0.003775602*116, -0.072819114*116, 0.022197956*116, 0.09374005*116, -0.0072034136*116, -0.007899685*116, -0.010523796*116, 0.017493391*116, -0.003588669*116, -0.023810185*116, -0.07267102*116, 0.0042652157*116, 0.0031554801*116, 0.023236753*116, 0.0017374652*116, 0.2348136*116, 0.0009187715*116, 0.001106355*116, -0.00707364*116, 0.0075593456*116, -0.015962739*116, -0.0015003812*116, -0.08242473*116, 0.004492122*116, 0.033028297*116, 0.014682926*116, 0.05971857*116, 0.004725494*116, 0.032954358*116, 0.07470709*116, 0.04004714*116, 0.0689286*116, 0.010376016*116, -0.15484597*116, -0.018676853*116, 0.086779274*116, 0.038053136*116, 0.054001316*116, 0.009448955*116, 0.03360212*116, -0.047568914*116, 0.018546047*116, -0.031515744*116, -0.05717924*116, -0.012071547*116, 0.013239705*116, 0.0034888647*116, -0.017877618*116, -0.006520185*116, 0.010874803*116, -0.005732495*116, -0.026721198*116, -0.015159781*116, 0.005759396*116, 0.011377695*116, 0.007316498*116, -0.018573327*116, 0.03124454*116, -0.0009971821*116, 0.023773598*116, -0.00076851377*116, -0.12622724*116, 0.32203946*116, 0.033443626*116, 0.019600704*116, -0.043458626*116, -0.054552417*116, -0.0416769*116, 0.04074386*116, 0.041698277*116, -0.007055834*116, 0.19601047*116, -0.042266686*116, 0.01817677*116, -0.035803463*116, -0.022064513*116, 0.032586522*116, 0.17073117*116, -0.27305928*116, -0.0073533445*116, 0.09214722*116, -0.058491115*116, -0.040855773*116, -0.0373803*116, -0.04032388*116, -0.03609873*116, 0.039304413*116, -0.049276408*116, -0.016560847*116, -0.024780175*116, 0.017783657*116, 0.15093045*116, 0.034085244*116, 0.06183214*116, -0.013128972*116, 0.026236175*116, 0.19086486*116, -0.047379468*116, 0.028176932*116, -0.051659834*116, 0.028363043*116, 0.17856567*116, -0.049761217*116, 0.09901599*116, -0.0013286261*116, -0.019061742*116, 0.033512972*116, -0.13929817*116, 0.027077192*116, -0.026328761*116, -0.109585285*116, 0.026568403*116, -0.09369252*116, -0.03666636*116, 0.0528971*116, -0.083921805*116, -0.023694102*116, 0.089768894*116, 0.1654627*116, -0.020967614*116, -0.0022057178*116, 0.11528687*116, -0.043011308*116, -0.037494037*116, 0.066616796*116, -0.030143863*116, 0.0155608645*116, -0.08429767*116, 0.051528197*116, -0.052993376*116, 0.008539505*116, 0.018369991*116, -0.038625*116, 0.046598196*116, -0.029369043*116, -0.124742895*116, 0.11657102*116, -0.28683975*116, 0.04503084*116, 0.014471371*116, 0.039324325*116, -0.032025553*116, -0.03454072*116, -0.03593333*116, -0.06334681*116, -0.037955582*116, -0.026505778*116, 0.045834497*116, -0.030018682*116, 0.054446265*116, 0.005540348*116, 0.040890783*116, -0.051531963*116, -0.14003013*116, -0.05751315*116, -0.28033814*116, -0.03742933*116, 0.03415578*116, 0.0045513012*116, -0.007953368*116, 0.06289684*116, -0.006327898*116, 0.05911669*116, -0.00046032065*116, 0.05232361*116, -0.10314963*116, -0.08447604*116, 0.035517957*116, 0.011987383*116, 0.014311045*116, 0.056370907*116, -0.016223423*116, 0.039117098*116, -0.0534062*116, 0.076115765*116, 0.2931492*116, -0.009911738*116, 0.0057500796*116, 0.04415023*116, 0.23675413*116, -0.055317786*116, -0.042316224*116, -0.09886318*116, -0.059066374*116, -0.17824462*116, 0.04495441*116, -0.01217429*116, 0.082755186*116, 0.0028151548*116, 0.008655075*116, 0.00151269*116, -0.013948729*116, -0.13290623*116, -0.04440063*116, -0.03204761*116, -0.00526756*116, 0.19620058*116, 0.021414086*116, 0.038974788*116, -0.002272644*116, -0.009345498*116, -0.004547106*116, -0.016422475*116, -0.011548031*116, -0.0032705646*116, 0.019286783*116, -0.010980489*116, -0.04448695*116, -0.14751841*116, -0.043877408*116, -0.025921432*116, -0.022388138*116, 0.0073771374*116, -0.018965486*116, 0.041353814*116, 0.02681004*116, 0.008539763*116, -0.12727924*116, 0.0032086598*116, -0.016566949*116, 0.012229815*116, 0.008200169*116, -0.09234613*116, 0.08401999*116, -0.009580361*116, 0.094174646*116, 0.0068179388*116, -0.032824453*116, 0.023023397*116, -0.00030128742*116, -0.00497557*116, 0.022134965*116, 0.0132273035*116, -0.10126545*116, -0.07462411*116, -0.06336232*116, 0.015553072*116, -0.009851014*116, 0.03782836*116, -0.018696597*116, -0.00622759*116, -0.007994161*116, 0.03158628*116, -0.034181565*116, -0.04888882*116, 0.060338106*116, -0.01587461*116, 0.10578436*116, 0.032021247*116, -0.018376013*116, -0.0041236673*116, -0.1463653*116, 0.11727575*116, -0.023610687*116, 0.004517517*116, -0.020970006*116, -0.0011468749*116, 0.07191*116, 0.0054623303*116, 0.19174607*116, 0.0015453296*116, -0.017250124*116, -0.0031019498*116, -0.009434272*116, 0.030845469*116, -0.011897685*116, 0.0007408964*116, 0.054463293*116, 0.021976555*116, 0.0024448915*116, 0.012992144*116, 0.124448426*116, 0.015408579*116, -0.0064534307*116, -0.013136666*116, -0.010155799*116, -0.018860005*116, 0.009434718*116, -0.1235431*116, -0.0018170835*116, -0.02932728*116, -0.0046720547*116, 0.0025854313*116, -0.004704795*116, 0.004177432*116, 0.07875158*116, 0.023740817*116, 0.038400285*116, 0.02491838*116, 0.01064616*116, 0.06909089*116, 0.086738415*116, 0.09176832*116, -0.033300962*116, 0.00719677*116, 0.09911172*116, 0.0013535349*116, -0.00020356265*116, 0.06186198*116, -0.016022926*116, 0.019374797*116, 0.0063252267*116, 0.00924631*116, 0.0037358452*116, -0.06608081*116, 0.0115662115*116, 0.004626163*116, 0.012858651*116, 0.01574317*116, 0.0070414366*116, -0.0054499805*116, -0.06148955*116, -0.0062141796*116, -0.020046763*116, 0.025947921*116, 0.006120317*116, 0.05936614*116, -0.034516662*116, 0.02450155*116, 0.10697474*116, -0.024245627*116, -0.017190726*116, -0.056630295*116, -0.14952482*116, -0.016332958*116, 0.14683028*116, -0.010043884*116, 0.030181227*116, -0.15811539*116, 0.00031912726*116, 0.017043093*116, -0.008417104*116, 0.10130011*116, -0.035337064*116, -0.033204414*116, 0.015985088*116, 0.0036053536*116, -0.0070095886*116, -0.06433883*116, -0.13971798*116, -0.13729915*116, -0.023242133*116, 0.028733462*116, -0.13162777*116, -0.12303678*116, -0.022597067*116, 0.0067983065*116, 0.0280193*116, 0.04224639*116, 0.12178057*116, 0.0062494087*116, -0.024526753*116, 0.012061996*116, -0.028745541*116, 0.024641097*116, -0.008646996*116, 0.0388703*116, 0.028577024*116, -0.15379575*116, 0.12107157*116, -0.037173815*116, -0.021806767*116, -0.00462637*116, 0.041364692*116, 0.0038059677*116, -0.0644428*116, 0.040223315*116, 0.106698364*116, -0.121809386*116, -0.12375282*116, 0.026817769*116, -0.015111198*116, 0.029357146*116, -0.0005283821*116, 0.03633575*116, -0.11563886*116, 0.0076038833*116, 0.0313975*116, -0.13458107*116, -0.059243593*116, 0.0021827586*116, 0.012234504*116, 0.096864365*116, -0.14328632*116, 0.13151695*116, -0.029788425*116, -0.018616462*116, -0.03364388*116, -0.13512719*116, 0.13539362*116, -0.08888456*116, -0.12362588*116, 0.03856615*116, -0.0191766*116, -0.0034274394*116, 0.0008479562*116, 0.075868145*116, -0.022052536*116, -0.13003138*116, -0.021343077*116, -0.16283715*116, -0.15695411*116, -0.022634728*116, 0.16058943*116, -0.13002689*116, 0.1454438*116, 0.004091343*116, 0.10013749*116, -0.028939191*116, -0.07014951*116, -0.0036435563*116, -0.01733195*116, -0.03772977*116, 0.025238274*116, 0.004094485*116, 0.029755723*116, 0.09108431*116, -0.012886667*116, 0.016705465*116, 0.009773977*116, -0.0062948614*116, -0.056249272*116, -0.10848866*116, 0.035287205*116, -0.053515792*116, 0.034740243*116, -0.005771852*116, -0.027893767*116, 0.051897265*116, -0.1162942*116, 0.10270566*116, 0.033164736*116, 0.01776773*116, 0.0035489004*116, 0.16168673*116, 0.050102416*116, -0.014315531*116, -0.12111999*116, -0.09998017*116, -0.020616088*116, -0.08368417*116, -0.012385285*116, 0.035274167*116, -0.09456737*116, 0.09351462*116, -0.007078731*116, 0.007532883*116, 0.0044510816*116, -0.11285508*116, -0.021046346*116, -0.05050073*116, 0.0086784*116, -0.18245985*116, -0.00045938394*116, 0.10114388*116, -0.008541308*116, 0.014083263*116, -0.075061254*116, -0.013564143*116, -0.04169985*116, -0.005936006*116, -0.061662216*116, 0.0006617698*116, -0.110364914*116, 0.10583821*116, -0.11690743*116, -0.031794164*116, -0.0021118673*116, 0.012388415*116, 0.09326039*116, 0.040268276*116, -0.0045200754*116, -0.015020686*116, 0.013977191*116, -0.0031879619*116, -0.01868982*116, -0.009639819*116, 0.0055263718*116, -0.14056699*116, 0.07124467*116, -0.010263932*116, 0.012175776*116, -0.0019458683*116, -0.015238234*116, 0.0023787771*116, -0.011628916*116, -0.006476949*116, 0.017164685*116, -0.0093349805*116, -0.113007955*116, -0.13039142*116, -0.13460717*116, 0.022259098*116, 0.012867679*116, 0.057929818*116, -0.02283979*116, 0.0009596574*116, 0.00765914*116, 0.06153876*116, -0.10034702*116, -0.03229566*116, 0.012779903*116, -0.011140806*116, -0.033062488*116, -0.04563514*116, 0.0152186155*116, -0.005724339*116, -0.08063448*116, -0.053320758*116, -0.060186844*116, 0.014967139*116, 0.005800319*116, 0.00043036047*116, 0.20409864*116, 0.036519077*116, 0.03830874*116, -0.0034076455*116, -0.025210008*116, -0.016850665*116, -0.0069597308*116, 0.011274897*116, -0.004059365*116, -0.0388354*116, 0.01878878*116, 0.0072350125*116, 0.024673421*116, -0.010424864*116, 0.0451836*116, -0.007896163*116, 0.008512651*116, 0.0043848176*116, -0.008007566*116, -0.007174539*116, -0.01215838*116, -0.13329221*116, -0.00065334915*116, -0.008892196*116, 0.0019609176*116, 0.09364836*116, 0.000991908*116, 0.002728471*116, 0.11998526*116, 0.010065641*116, -0.006243164*116, 0.018261524*116, 0.11096899*116, 0.015679786*116, 0.12872395*116, 0.080928415*116, 0.027045889*116, 0.020162765*116, -0.15033601*116, -0.017216355*116, 0.021856628*116, -0.034540556*116, -0.017216206*116, -0.00032939465*116, 0.010501334*116, 0.014535417*116, 0.0027516566*116, -0.11310588*116, 0.04137756*116, -0.007397607*116, -0.015248361*116, 0.011492703*116, 0.009372925*116, 0.012806581*116, 0.028205073*116, 0.023641152*116, -0.03800144*116, 0.24166743*116, -0.03791329*116, -0.06394141*116, -0.016632447*116, 0.006792389*116, 0.0664558*116, 0.061985835*116, 0.04313337*116, -0.053060442*116, -0.06096205*116, -0.028404664*116, 0.02671563*116, 0.05431679*116, 0.020007132*116, -0.047776572*116, -0.01436311*116, 0.28479433*116, -0.043275487*116, 0.06378152*116, 0.09463569*116, -0.01423502*116, 0.032455396*116, -0.06675552*116, -0.021350082*116, -0.1198096*116, -0.05744771*116, -0.030827677*116, 0.076537974*116, -0.03606087*116, -0.06249626*116, -0.14219351*116, 0.056206837*116, -0.034961056*116, 0.0060683824*116, 0.02234468*116, 0.045643058*116, 0.05613298*116, 0.0069428664*116, 0.013953338*116, 0.0228254*116, 0.0020834405*116, 0.16963741*116, 0.06207718*116, 0.03720784*116, -0.050653737*116, 0.051402852*116, -0.22653024*116, 0.05293689*116, 0.056633536*116, 0.02102295*116, -0.024187183*116, -0.091951296*116, 0.034133732*116, 0.0524978*116, -0.038897894*116, -0.06000346*116, 0.01024885*116, 0.14850144*116, 0.09065553*116, -0.07997761*116, 0.013899364*116, 0.1484578*116, 0.036942128*116, -0.06618931*116, -0.06936571*116, 0.10729853*116, -0.029898502*116, -0.05271612*116, 0.07900029*116, -0.026601229*116, 0.054909516*116, -0.037684157*116, 0.1361097*116, -0.015209223*116, -0.049356464*116, 0.032833718*116, -0.13369568*116, -0.03259177*116, 0.01739091*116, -0.0055247853*116, -0.1470593*116, -0.06880425*116, 0.054346234*116, 0.026946805*116, -0.06400989*116, 0.039387476*116, -0.031988695*116, -0.0683157*116, 0.06143876*116, 0.060262933*116, -0.043080352*116, 0.022330053*116, 0.05887468*116, 0.06311737*116, 0.17511398*116, -0.041695304*116, 0.0281875*116, -0.012534609*116, 0.07158174*116, -0.07263462*116, -0.00669034*116, 0.044881735*116, 0.045191996*116, 0.1703948*116, -0.15583716*116, 0.030632522*116, 0.042095233*116, 0.07959089*116, -0.03851061*116, 0.075898394*116, -0.2164607*116, 0.17397232*116, -0.06780306*116, -0.20090285*116, -0.002566697*116, -0.034988347*116, 0.048850145*116, 0.013347038*116, 0.019357879*116, -0.0008954322*116, 0.02561357*116, 0.014424182*116, -0.008184568*116, -0.05914502*116, -0.03588019*116, 0.17481893*116, -0.0043664216*116, 0.018233037*116, 0.022854025*116, -0.1848774*116, 0.03791089*116, -0.026883248*116, -0.00158177*116, 0.03299304*116, -0.027018933*116, 0.027633462*116, -0.07594303*116, 0.010661461*116, 0.102252945*116, -0.014943795*116, 0.14135715*116, -0.0019484058*116, 0.016156022*116, -0.13071205*116, 0.021923924*116, -0.08677549*116, -0.01949955*116, -0.03757291*116, 0.005221301*116, -0.10147056*116, 0.10655446*116, -0.04358873*116, -0.02918912*116, 0.008716649*116, 0.01874974*116, 0.09629436*116, 0.09029542*116, -0.017194595*116, -0.0028989746*116, 0.0926221*116, -0.13707617*116, 0.0034157163*116, -0.010694136*116, -0.004536225*116, -0.04748109*116, 0.01925694*116, -0.005319619*116, -0.060779776*116, 0.0071880436*116, 0.07322793*116, -0.007890929*116, 0.016665608*116, 0.007449758*116, 0.050945133*116, -0.021210838*116, -0.063734405*116, -0.0062308167*116, 0.02036099*116, 0.015715403*116, -0.008229999*116, 0.040165912*116, -0.011422227*116, -0.007636244*116, 0.014692768*116, 0.10231529*116, -0.020039672*116, 0.0009397805*116, -0.10410367*116, -0.0062152287*116, -0.054639503*116, 0.001401026*116, -0.089500785*116, -0.011020036*116, 0.07789555*116, -0.14406042*116, 0.15468861*116, -0.0013439456*116, -0.024948684*116, 0.0057090386*116, -0.098750085*116, 0.02958283*116, 0.054216243*116, -0.0064230864*116, -0.012459666*116, 0.0048986967*116, 0.016725982*116, 0.104565345*116, 0.02066349*116, -0.05399238*116, 0.020623095*116, -0.024643421*116, 0.144127*116, -0.0004201814*116, 0.003617457*116, -0.021888468*116, 0.0120980255*116, -0.019092735*116, -0.0019849853*116, 0.0103908805*116, -0.011952141*116, 0.030390814*116, 0.0054546525*116, 0.086191244*116, -0.0010176427*116, 0.109401934*116, 0.010312752*116, 0.15697177*116, 0.10414028*116, -0.15816101*116, 0.1232108*116, 0.004242974*116, -0.090967834*116, -0.087330356*116, 0.0473788*116, 0.061984356*116, 0.076812945*116, -0.008241954*116, 0.033030234*116, -0.026653314*116, 0.003790236*116, -0.00061803235*116, -0.02993434*116, -0.0072624777*116, 0.024581665*116, 0.019766035*116, -0.0073225955*116, -0.11488488*116, 0.14147113*116, -0.011540819*116, -0.02214294*116, -0.01943773*116, 0.0025195493*116, 0.0061633484*116, 0.033885762*116, 0.03633246*116, 0.011370019*116, 0.03189915*116, 0.016055673*116, 0.0756992*116, -0.22840199*116, 0.012082461*116, 0.05999957*116, 0.0032609086*116, 0.23175569*116, 0.012857892*116, -0.038123034*116, 0.0559967*116, 0.038677163*116, 0.08054077*116, 0.13081463*116, -0.03170426*116, 0.012882106*116, 0.017270228*116, 0.058680613*116, 0.034496326*116, 0.064757496*116, 0.039480135*116, 0.014020222*116, -0.13569535*116, 0.05713723*116, 0.0036328281*116, -0.019981889*116, -0.025511898*116, 0.078426495*116, -0.09850465*116, -0.098258905*116, -0.037584055*116, 0.20709673*116, 0.015895853*116, 0.22082703*116, 0.008013149*116, 0.024646355*116, 0.0053943885*116, -0.075613596*116, 0.18330419*116, 0.13239117*116, 0.007207632*116, 0.04626185*116, 0.007895849*116, 0.18017438*116, -0.033373497*116, 0.06561721*116, -0.03311361*116, 0.02241038*116, -0.043178555*116, -0.0063851066*116, -0.2506204*116, -0.022295456*116, 0.08499791*116, 0.05021011*116, -0.051413845*116, -0.044865113*116, -0.03476876*116, -0.036028758*116, 0.198876*116, -0.099328*116, 0.15521294*116, 0.024223493*116, -0.20993201*116, 0.09673804*116, -0.053804904*116, -0.023147896*116, -0.16114008*116, 0.0600779*116, 0.04039026*116, -0.07647459*116, 0.04901367*116, 0.10899504*116, 0.0155377025*116, 0.045201894*116, -0.041267205*116, 0.0603561*116, -0.025861122*116, -0.022993*116, 0.03154441*116, -0.057110433*116, -0.059403714*116, -0.14973827*116, 0.010684656*116, 0.014066621*116, -0.03745475*116, 0.04437398*116, -0.056962*116, -0.033844993*116, -0.014269965*116, 0.0259461*116, -0.035321858*116, 0.031587087*116, -0.0809186*116, 0.073737696*116, 0.08954668*116, -0.10651631*116, 0.05494677*116, -0.07926128*116, -0.015158801*116, 0.07605427*116, 0.01408903*116, -0.14102538*116, 0.017257268*116, 0.012528202*116, -0.0039859735*116, 0.010343043*116, -0.015539218*116, 0.04236325*116, -0.119729154*116, -0.046747394*116, -0.026125733*116, 0.013616408*116, -0.028814351*116, 0.021419378*116, 0.011210656*116, -0.028190844*116, 0.08911785*116, 0.008597199*116, 0.026110254*116, -0.038017668*116, 0.026669953*116, 0.05191931*116, -0.31703416*116, -0.057086293*116, -0.08325286*116, 0.06466186*116, -0.058284763*116, 0.052532293*116, -0.011565462*116, -0.071286306*116, -0.0005062179*116, -0.0012356985*116, 0.0043255663*116, 0.0012856128*116, 0.008955471*116, -0.02520423*116, -0.121993385*116, 0.00085997034*116, 0.059325818*116, 0.006240965*116, 0.08114494*116, -0.001267152*116, 0.004537444*116, -0.12949412*116, -0.032247033*116, -0.14067954*116, -0.024067428*116, 0.016193897*116, 0.002207034*116, -0.11244275*116, -0.13972169*116, -0.085492834*116, -0.050385755*116, -0.0047742897*116, 0.01044694*116, 0.0057573863*116, 0.124148816*116, 0.008000844*116, 0.011615278*116, 0.009692255*116, -0.1682704*116, 0.002087316*116, 0.018120434*116, 0.0129346065*116, -0.08575113*116, 0.051992387*116, -0.0022155237*116, -0.07501859*116, 0.002212826*116, -0.015991835*116, 0.0033499356*116, 0.036166187*116, 0.0027856408*116, 0.075326525*116, 0.002949854*116, -0.061748285*116, 0.022514146*116, -0.017155915*116, 0.023064017*116, -0.018655626*116, -0.033385027*116, -0.02903737*116, -0.005111816*116, 0.015487273*116, 0.13464803*116, -0.12825334*116, -0.058380947*116, 0.014806866*116, -0.015510845*116, -0.08147616*116, 0.065899536*116, -0.07912857*116, 0.0013667851*116, 0.052924447*116, -0.020219214*116, 0.034930635*116, -0.021166129*116, -0.038545325*116, 0.0037384382*116, 0.02843095*116, 0.010024361*116, 0.13017087*116, -0.000983239*116, -0.016073087*116, 0.01741937*116, -0.009429704*116, 0.14688139*116, 0.04716223*116, 0.0057845097*116, 0.09884245*116, -0.0020608027*116, 0.13428144*116, 0.00753965*116, -0.030369848*116, -0.010214638*116, 0.011343509*116, -0.00080025994*116, -0.009883469*116, 0.003503122*116, -0.0060671046*116, -0.004810038*116, -0.014649211*116, 0.017426733*116, -0.0011488482*116, 0.029544326*116, -0.012918086*116, 0.11024204*116, 0.073591076*116, -0.17876057*116, 0.07550213*116, 0.011847647*116, 0.1023701*116, 0.0039572986*116, 0.037315316*116, 0.12187481*116, -0.023619875*116, 0.015659463*116, 0.084530726*116, 0.0141025465*116, 0.008978656*116, 0.10383026*116, -0.023806684*116, 0.007683682*116, 0.018816885*116, 0.0293595*116, 0.018197577*116, -0.12957525*116, 0.12673691*116, -0.007296636*116, 0.026447745*116, -0.012781528*116, 0.01046123*116, 0.0034810475*116, -0.04211428*116
};

// 10
static const float key_point_fc_2_b[] ={
    -1.0791304f, -0.46425956f, 0.9955152f, -0.49356773f, -0.013303851f, 0.24845023f, -0.9145272f, 0.7907584f, 0.9210353f, 0.76585186f
};


static void matrix_mul(const int8_t *matrix1, uint32_t row1, uint32_t col1, float *matrix2, uint32_t row2, uint32_t col2, float *matrix_result)
{
    for (uint32_t i = 0; i < row1; i++)
    {
        for (uint32_t j = 0; j < col2; j++)
        {
            *(matrix_result + i * col2 + j) = 0;
            for (uint32_t k = 0; k < col1; k++)
                *(matrix_result + i * col2 + j) += *(matrix1 + i * col1 + k)/116.0 * *(matrix2 + k * col2 + j);
        }
    }
}

static float sigmoid(float x)
{
    return 1.0 / (1.0 + expf(-x));
}

void key_point_last_handle(mf_kpu_task_t *task, key_point_t *key_point)
{
    float scale = task->output_scale;
    float bais = task->output_bias;
    uint8_t *input = (uint8_t *)task->dst;
    float output[1024];
    float matrix_result1[128][1];
    float matrix_result2[10][1];

    for (uint32_t i = 0; i < 1024; i++)
        output[i] = input[i] * scale + bais;
    
    for (uint32_t i = 0; i < 64; i++)
    {
        float sum = 0;
        uint32_t index = 16 * i;
        for (uint32_t j = 0; j < 16; j++)
        {
            sum += output[index + j];
        }
        sum /= 16;
        output[i] = sum;
    }

    matrix_mul(key_point_fc_1_w, 128, 64, output, 64, 1, (float *)matrix_result1);
    for (uint32_t i = 0; i < 128; i++)
        matrix_result1[i][0] += key_point_fc_1_b[i];

    matrix_mul(key_point_fc_2_w, 10, 128, (float *)matrix_result1, 128, 1, (float *)matrix_result2);

    for (uint32_t i = 0; i < 10; i++)
        matrix_result2[i][0] = sigmoid(matrix_result2[i][0] + key_point_fc_2_b[i]);
    
    for (uint32_t i = 0; i < 5; i++)
    {
        key_point->point[i].x = matrix_result2[2 * i][0] * key_point->width;
        key_point->point[i].y = matrix_result2[2 * i + 1][0] * key_point->height;
    }
}
