<mxfile host="75acfe92-c69d-41e4-b5eb-eab6fae32da8" modified="2020-08-10T14:00:40.008Z" agent="5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.47.3 Chrome/78.0.3904.130 Electron/7.3.2 Safari/537.36" etag="bojNA0YfNJkUxjlsuToV" version="13.1.3" pages="2">
    <diagram id="6hGFLwfOUW9BJ-s0fimq" name="时序图">
        <mxGraphModel dx="685" dy="400" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="3" value="设备" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="100" y="230" width="130" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="4" value="服务器" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="383" y="230" width="130" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="5" value="" style="endArrow=none;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" target="3">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="165" y="520" as="sourcePoint"/>
                        <mxPoint x="170" y="290" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="6" value="" style="endArrow=none;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="448" y="520" as="sourcePoint"/>
                        <mxPoint x="447.58" y="250" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="10" value="" style="endArrow=classic;html=1;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="170" y="365" as="sourcePoint"/>
                        <mxPoint x="440" y="365" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="11" value="3 连接服务器，订阅主题(MQTT)" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;" vertex="1" parent="1">
                    <mxGeometry x="187" y="345" width="190" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="CjPbGc6zD1O4qIW_degH-19" value="" style="endArrow=classic;html=1;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="210" y="330" as="sourcePoint"/>
                        <mxPoint x="170" y="330" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="CjPbGc6zD1O4qIW_degH-20" value="" style="endArrow=none;html=1;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="210" y="300" as="sourcePoint"/>
                        <mxPoint x="210" y="330" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="CjPbGc6zD1O4qIW_degH-21" value="" style="endArrow=none;html=1;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="170" y="300" as="sourcePoint"/>
                        <mxPoint x="210" y="300" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="CjPbGc6zD1O4qIW_degH-22" value="2 联网&lt;br&gt;(WiFi/4G)" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;" vertex="1" parent="1">
                    <mxGeometry x="208" y="302" width="70" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="CjPbGc6zD1O4qIW_degH-23" value="" style="endArrow=classic;html=1;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="440" y="400" as="sourcePoint"/>
                        <mxPoint x="170" y="400" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="CjPbGc6zD1O4qIW_degH-24" value="4 连接成功" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;" vertex="1" parent="1">
                    <mxGeometry x="190" y="380" width="70" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="CjPbGc6zD1O4qIW_degH-25" value="" style="endArrow=classic;html=1;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="170" y="430" as="sourcePoint"/>
                        <mxPoint x="440" y="430" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="CjPbGc6zD1O4qIW_degH-26" value="5 同步操作(时间、人脸特征值、配置）" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;" vertex="1" parent="1">
                    <mxGeometry x="190" y="410" width="220" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="CjPbGc6zD1O4qIW_degH-27" value="" style="endArrow=classic;html=1;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="440" y="460" as="sourcePoint"/>
                        <mxPoint x="170" y="460" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="CjPbGc6zD1O4qIW_degH-28" value="6 返回数据(时间、人脸特征值、配置）" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;" vertex="1" parent="1">
                    <mxGeometry x="190" y="440" width="220" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="CjPbGc6zD1O4qIW_degH-29" value="" style="html=1;points=[];perimeter=orthogonalPerimeter;" vertex="1" parent="1">
                    <mxGeometry x="160" y="277" width="10" height="223" as="geometry"/>
                </mxCell>
                <mxCell id="CjPbGc6zD1O4qIW_degH-30" value="1 上电" style="html=1;verticalAlign=bottom;startArrow=oval;endArrow=block;startSize=8;" edge="1" target="CjPbGc6zD1O4qIW_degH-29" parent="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="100" y="277" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="CjPbGc6zD1O4qIW_degH-31" value="" style="html=1;points=[];perimeter=orthogonalPerimeter;" vertex="1" parent="1">
                    <mxGeometry x="443" y="355" width="10" height="135" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
    <diagram id="P-LVZqYmFN7wNhp5GBy_" name="Page-2">
        <mxGraphModel dx="822" dy="480" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1654" pageHeight="2336" math="0" shadow="0">
            <root>
                <mxCell id="Kn9X4tFIYklmbTUgauKP-0"/>
                <mxCell id="Kn9X4tFIYklmbTUgauKP-1" parent="Kn9X4tFIYklmbTUgauKP-0"/>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>