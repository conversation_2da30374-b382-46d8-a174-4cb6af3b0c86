#ifndef _ES_HAL_I2C_H_
#define _ES_HAL_I2C_H_

#ifdef __cplusplus 
extern "C" { 
#endif

#include "es_types.h"

typedef struct {
    ES_U32 speed;
    ES_U8 i2c_id;
    ES_U8 addr;
} es_hal_i2c_cfg_t;


ES_S32 es_hal_i2c_init(ES_VOID);

ES_S32 es_hal_i2c_read_byte(const es_hal_i2c_cfg_t *cfg, ES_U8 reg, ES_BYTE *data);

ES_S32 es_hal_i2c_write_byte(const es_hal_i2c_cfg_t *cfg, ES_U8 reg, ES_BYTE data);


#ifdef __cplusplus 
}
#endif
#endif