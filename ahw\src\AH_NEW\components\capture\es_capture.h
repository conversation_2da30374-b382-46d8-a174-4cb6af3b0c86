/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_capture.h
** bef: define the interface image capture. 
** auth: lines<<EMAIL>>
** create on 2023.01.11
*/

#ifndef _ES_CAPTRUE_H_
#define _ES_CAPTRUE_H_

#ifdef __cplusplus 
extern "C" { 
#endif

#include "es_types.h"

ES_S32 es_capture_init(ES_VOID);

ES_S32 es_capture_run(ES_VOID);

ES_S32 es_captrue_open(ES_U32 interval_sec);

ES_S32 es_captrue_close(ES_VOID);

ES_S32 es_capture_upload_pic(ES_U32 timestamp, ES_U8 type);

#ifdef __cplusplus 
}
#endif

#endif