/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_tuya_dp.h
** bef: define the interface for tuya data point. 
** auth: lines<<EMAIL>>
** create on 2021.09.28
*/

#ifndef _ES_DP_DATA_H_
#define _ES_DP_DATA_H_

#ifdef __cplusplus 
extern "C" { 
#endif

#include "es_types.h"

typedef enum {
    ES_DP_FACE_DATA_PASS,
    ES_DP_FACE_DATA_STRANGER,
    ES_DP_FACE_DATA_DETECT,
    ES_DP_FACE_DATA_FAKE,
    ES_DP_FACE_DATA_LIVING
} es_dp_face_data_type_e;

typedef enum {
    ES_DP_PASS_BY_CARD      = 1,
    ES_DP_PASS_BY_PWD       = 2,
    ES_DP_PASS_BY_QRCODE    = 3,
    ES_DP_PASS_BY_FACE      = 4,
    ES_DP_PASS_BY_FINGER    = 5,
    ES_DP_PASS_BY_KEY       = 6,
    ES_DP_PASS_BY_ID        = 7,
    ES_DP_PASS_BY_IRIS      = 8,
    ES_DP_PASS_BY_REMOTE    = 9,
} es_dp_pass_event_e;

typedef struct {
    unsigned char *uid; // string, end with '\0'
    unsigned char *data;
    unsigned char data_len;
    unsigned char type;
    unsigned int timestamp;
} es_dp_pass_data_t;

int es_dp_recv_sync_time_ret(const unsigned char value[], unsigned short length);

int es_dp_set_pass_info(unsigned char *value, unsigned short length);

//{"gateway":false,"sn":1499006367409635394,"uid":"hbay16462118592214it8"}
int es_dp_open_door(const unsigned char *value, unsigned short length);

int es_dp_send_dev_info(void);

int es_dp_send_pass_data(const es_dp_pass_data_t *data);

int es_dp_upload_face_data(unsigned char *uid, unsigned int timestamp, es_dp_face_data_type_e type);

int es_dp_set_recognition_threshold(unsigned int value);

unsigned int es_dp_get_recognition_threshold(void);

int es_dp_set_face_threshold(unsigned int value);

unsigned int es_dp_get_face_threshold(void);

int es_dp_set_living_threshold(unsigned int value);

unsigned int es_dp_get_living_threshold(void);

int es_dp_set_led_brightness(unsigned int value);

unsigned int es_dp_get_led_brightness(void);

int es_dp_set_led_brightness_offset(unsigned int value);

unsigned int es_dp_get_led_brightness_offset(void);

int es_dp_set_door_opening_duration(unsigned int value);

unsigned int es_dp_get_door_opening_duration(void);

#ifdef __cplusplus 
}
#endif
#endif

