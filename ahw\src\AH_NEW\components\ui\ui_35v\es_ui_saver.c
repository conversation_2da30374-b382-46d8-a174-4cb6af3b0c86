#include "es_inc.h"

#if (ES_UI_TYPE == ES_UI_TYPE_K35V_320_480)
#include "es_ui_img_res.h"

// #define ES_UI_SAVER_DEBUG
#ifdef ES_UI_SAVER_DEBUG
#define es_ui_saver_debug es_log_info
#define es_ui_saver_error es_log_error
#else
#define es_ui_saver_debug(...)
#define es_ui_saver_error(...)
#endif

static es_time_t ui_date_time;

static lv_obj_t *lv_saver_bg = ES_NULL;
static lv_timer_t *saver_timer = NULL;
static lv_obj_t *label_time = ES_NULL;

static ES_CHAR time_str_buf[32] = {0};

static ES_VOID es_ui_saver_update_datetime(ES_VOID)
{
    es_time_t now;
    
    if (ES_RET_SUCCESS != es_time_get_now(&now)) {
        es_ui_saver_debug("get time fail");
        return;
    }

    if (0 == es_memcmp(&now, &ui_date_time, sizeof(now))) {
        return;
    }

    if (now.sec != ui_date_time.sec) {
        // update time
        es_snprintf(time_str_buf, sizeof(time_str_buf), "#ffffff %02d:%02d:%02d#", ui_date_time.hour, ui_date_time.min, ui_date_time.sec);
        lv_label_set_text_static(label_time, time_str_buf);

    }

    // if (now.mday != ui_date_time.mday) {
    //     es_snprintf(date_str_buf, sizeof(date_str_buf), "#ffffff %04d.%02d.%02d", ui_date_time.year, ui_date_time.mon, ui_date_time.mday);
    //     lv_label_set_text_static(label_date, date_str_buf);
    // }

    // if (now.wday != ui_date_time.wday) {
    //     // update date and week
    //     es_snprintf(wday_str_buf, sizeof(wday_str_buf), "#ffffff %s#", wday_str[now.wday-1]);
    //     lv_label_set_text_static(label_wday, wday_str_buf);
    // }

    memcpy(&ui_date_time, &now, sizeof(now));
}

static void saver_timer_cb(lv_timer_t *timer)
{
    es_ui_saver_update_datetime();
}

ES_S32 es_ui_saver_create_widgets(ES_VOID)
{
    lv_obj_t *obj;

    lv_saver_bg = lv_obj_create(lv_scr_act());
    lv_obj_remove_style_all(lv_saver_bg);
    lv_obj_set_size(lv_saver_bg, ES_UI_WIDTH, ES_UI_HEIGHT);
    lv_obj_align(lv_saver_bg, LV_ALIGN_TOP_LEFT, 0, 0);

    obj = lv_img_create(lv_saver_bg);
    lv_obj_align(obj, LV_ALIGN_TOP_LEFT, 0, 0);
    lv_img_set_src(obj, ES_UI_IMG_SRC_SAVER);

    label_time = lv_label_create(lv_saver_bg);
    lv_obj_add_style(label_time, (lv_style_t *)es_ui_font_get_time(), 0);
    lv_obj_align(label_time, LV_ALIGN_LEFT_MID, 100, 120);
    lv_label_set_recolor(label_time, ES_TRUE);
    es_snprintf(time_str_buf, sizeof(time_str_buf), "#ffffff %02d:%02d:%02d#", ui_date_time.hour, ui_date_time.min, ui_date_time.sec);
    lv_label_set_text_static(label_time, time_str_buf);

    return ES_RET_SUCCESS;
}

ES_S32 es_ui_saver_init(ES_VOID)
{
    memset(&ui_date_time, 0x00, sizeof(ui_date_time));
    if (ES_RET_SUCCESS != es_time_get_now(&ui_date_time)) {
        return ES_RET_FAILURE;
    }

    es_ui_saver_create_widgets();
    es_ui_saver_show(ES_FALSE);

    saver_timer = lv_timer_create(saver_timer_cb, 1000, NULL);
    if (ES_NULL == saver_timer) {
        return ES_RET_FAILURE;
    }

    return ES_RET_SUCCESS;
}

ES_S32 es_ui_saver_show(ES_BOOL show)
{
    if (show) {
        lv_timer_resume(saver_timer);
        lv_obj_clear_flag(lv_saver_bg, LV_OBJ_FLAG_HIDDEN);
    } else {
        lv_timer_pause(saver_timer);
        lv_obj_add_flag(lv_saver_bg, LV_OBJ_FLAG_HIDDEN);
    }

    return ES_RET_SUCCESS;
}

#endif