/*
*---------------------------------------------------------------
*                        Lvgl Font Tool                         
*                                                               
* 注:使用unicode编码                                              
* 注:本字体文件由Lvgl Font Tool V0.2 生成                          
* 作者:阿里(qq:617622104)                                         
*---------------------------------------------------------------
*/


#include "../../lvgl.h"


static const uint8_t glyph_bitmap[] = {
/*   */
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....


/* . */
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....
0x3e,0xe0,  //.@@.
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....


/* 0 */
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0xaf,0xfe,0x80,0x00,  //..%@@@+..
0x0a,0xe8,0x0a,0xe8,0x00,  //.%@+.%@+.
0x3e,0xc0,0x00,0xee,0x00,  //.@%...@@.
0x8e,0xa0,0x00,0xce,0x50,  //+@%...%@+
0x8c,0x80,0x00,0xae,0x50,  //+%+...%@+
0xae,0x80,0x00,0xae,0x80,  //%@+...%@+
0xae,0x80,0x00,0xae,0x80,  //%@+...%@+
0x8c,0x80,0x00,0xae,0x50,  //+%+...%@+
0x8e,0xa0,0x00,0xce,0x50,  //+@%...%@+
0x3e,0xc0,0x00,0xee,0x00,  //.@%...@@.
0x0a,0xe8,0x0a,0xe8,0x00,  //.%@+.%@+.
0x00,0xcf,0xfe,0x80,0x00,  //..%@@@+..
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........


/* 1 */
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x8c,0x50,0x00,  //....+%+..
0x00,0x05,0xee,0x50,0x00,  //...+@@+..
0x00,0xaf,0xfe,0x50,0x00,  //..%@@@+..
0x0a,0xfc,0xae,0x50,0x00,  //.%@%%@+..
0x00,0x00,0xae,0x50,0x00,  //....%@+..
0x00,0x00,0xae,0x50,0x00,  //....%@+..
0x00,0x00,0xae,0x50,0x00,  //....%@+..
0x00,0x00,0xae,0x50,0x00,  //....%@+..
0x00,0x00,0xae,0x50,0x00,  //....%@+..
0x00,0x00,0xae,0x50,0x00,  //....%@+..
0x00,0x00,0xae,0x50,0x00,  //....%@+..
0x00,0x00,0xae,0x50,0x00,  //....%@+..
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........


/* 2 */
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x03,0xef,0xff,0xc0,0x00,  //..@@@@%..
0x3e,0xe8,0x0c,0xfe,0x00,  //.@@+.%@@.
0x8e,0xa0,0x00,0xce,0x50,  //+@%...%@+
0x00,0x00,0x00,0xae,0x80,  //......%@+
0x00,0x00,0x00,0xee,0x50,  //......@@+
0x00,0x00,0x0a,0xfc,0x00,  //.....%@%.
0x00,0x00,0xaf,0xe0,0x00,  //....%@@..
0x00,0x0a,0xfc,0x00,0x00,  //...%@%...
0x00,0xcf,0xc0,0x00,0x00,  //..%@%....
0x0c,0xfa,0x00,0x00,0x00,  //.%@%.....
0x8e,0xc0,0x00,0x00,0x00,  //+@%......
0xcf,0xff,0xff,0xfe,0x80,  //%@@@@@@@+
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........


/* 3 */
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0xcf,0xfe,0x80,0x00,  //..%@@@+..
0x0c,0xe5,0x0a,0xe8,0x00,  //.%@+.%@+.
0x5e,0xc0,0x03,0xee,0x00,  //+@%...@@.
0x00,0x00,0x03,0xee,0x00,  //......@@.
0x00,0x00,0x0c,0xe8,0x00,  //.....%@+.
0x00,0x0a,0xff,0xc0,0x00,  //...%@@%..
0x00,0x00,0x03,0xee,0x30,  //......@@.
0x00,0x00,0x00,0xae,0x80,  //......%@+
0x00,0x00,0x00,0x8e,0xa0,  //......+@%
0x8e,0xa0,0x00,0xae,0x50,  //+@%...%@+
0x0e,0xe3,0x03,0xec,0x00,  //.@@...@%.
0x00,0xef,0xff,0xa0,0x00,  //..@@@@%..
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........


/* 4 */
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x0e,0xe3,0x00,  //.....@@..
0x00,0x00,0xaf,0xe3,0x00,  //....%@@..
0x00,0x05,0xef,0xe3,0x00,  //...+@@@..
0x00,0x0c,0xee,0xe3,0x00,  //...%@@@..
0x00,0x8c,0x8e,0xe3,0x00,  //..+%+@@..
0x03,0xec,0x0e,0xe3,0x00,  //..@%.@@..
0x0c,0xe3,0x0e,0xe3,0x00,  //.%@..@@..
0x8c,0x80,0x0e,0xe3,0x00,  //+%+..@@..
0xcf,0xff,0xff,0xff,0xa0,  //%@@@@@@@%
0x00,0x00,0x0e,0xe3,0x00,  //.....@@..
0x00,0x00,0x0e,0xe3,0x00,  //.....@@..
0x00,0x00,0x0e,0xe3,0x00,  //.....@@..
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........


/* 5 */
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x05,0xef,0xff,0xfe,0x00,  //.+@@@@@@.
0x08,0xc8,0x00,0x00,0x00,  //.+%+.....
0x0a,0xe5,0x00,0x00,0x00,  //.%@+.....
0x0c,0xe0,0x00,0x00,0x00,  //.%@......
0x0e,0xcf,0xff,0xc0,0x00,  //.@%@@@%..
0x5e,0xe3,0x00,0xec,0x00,  //+@@...@%.
0x00,0x00,0x00,0xae,0x50,  //......%@+
0x00,0x00,0x00,0x8e,0xa0,  //......+@%
0x00,0x00,0x00,0x8c,0x80,  //......+%+
0x8e,0xa0,0x00,0xae,0x50,  //+@%...%@+
0x0e,0xe0,0x00,0xec,0x00,  //.@@...@%.
0x00,0xef,0xff,0xa0,0x00,  //..@@@@%..
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........


/* 6 */
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x8e,0xff,0xe0,0x00,  //..+@@@@..
0x0a,0xe3,0x05,0xee,0x00,  //.%@..+@@.
0x3e,0xc0,0x00,0xce,0x50,  //.@%...%@+
0x8c,0x80,0x00,0x00,0x00,  //+%+......
0xae,0x6e,0xff,0xc0,0x00,  //%@+@@@%..
0xcf,0xe5,0x03,0xee,0x00,  //%@@+..@@.
0xce,0x80,0x00,0xae,0x50,  //%@+...%@+
0xce,0x50,0x00,0x8e,0xa0,  //%@+...+@%
0xae,0x50,0x00,0x8c,0x80,  //%@+...+%+
0x5e,0xa0,0x00,0xae,0x50,  //+@%...%@+
0x0c,0xe3,0x03,0xec,0x00,  //.%@...@%.
0x00,0xaf,0xff,0xc0,0x00,  //..%@@@%..
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........


/* 7 */
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0xaf,0xff,0xff,0xff,0xa0,  //%@@@@@@@%
0x00,0x00,0x05,0xee,0x00,  //.....+@@.
0x00,0x00,0x0c,0xe5,0x00,  //.....%@+.
0x00,0x00,0x8e,0xc0,0x00,  //....+@%..
0x00,0x00,0xee,0x50,0x00,  //....@@+..
0x00,0x05,0xec,0x00,0x00,  //...+@%...
0x00,0x0a,0xe8,0x00,0x00,  //...%@+...
0x00,0x0e,0xe3,0x00,0x00,  //...@@....
0x00,0x5e,0xc0,0x00,0x00,  //..+@%....
0x00,0x8e,0xa0,0x00,0x00,  //..+@%....
0x00,0xae,0x80,0x00,0x00,  //..%@+....
0x00,0xce,0x50,0x00,0x00,  //..%@+....
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........


/* 8 */
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0xcf,0xff,0xa0,0x00,  //..%@@@%..
0x0c,0xe8,0x0a,0xfa,0x00,  //.%@+.%@%.
0x3e,0xe0,0x00,0xee,0x00,  //.@@...@@.
0x3e,0xe0,0x00,0xee,0x00,  //.@@...@@.
0x0c,0xe8,0x0a,0xfa,0x00,  //.%@+.%@%.
0x00,0xaf,0xfe,0x80,0x00,  //..%@@@+..
0x0e,0xe5,0x08,0xec,0x00,  //.@@+.+@%.
0x8e,0xa0,0x00,0xce,0x50,  //+@%...%@+
0xae,0x80,0x00,0xae,0x80,  //%@+...%@+
0x8e,0xa0,0x00,0xae,0x50,  //+@%...%@+
0x0e,0xe5,0x08,0xec,0x00,  //.@@+.+@%.
0x00,0xef,0xff,0xc0,0x00,  //..@@@@%..
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........


/* 9 */
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0xcf,0xfe,0x80,0x00,  //..%@@@+..
0x0e,0xe8,0x08,0xea,0x00,  //.@@+.+@%.
0x5e,0xc0,0x00,0xce,0x30,  //+@%...%@.
0xae,0x80,0x00,0xae,0x80,  //%@+...%@+
0xae,0x80,0x00,0xaf,0xa0,  //%@+...%@%
0x8e,0xa0,0x00,0xcf,0xa0,  //+@%...%@%
0x0e,0xe8,0x08,0xef,0xa0,  //.@@+.+@@%
0x00,0xef,0xfe,0x6c,0x80,  //..@@@@+%+
0x00,0x00,0x00,0x8c,0x50,  //......+%+
0x00,0x00,0x00,0xce,0x30,  //......%@.
0x5e,0xe0,0x0a,0xe8,0x00,  //+@@..%@+.
0x05,0xef,0xfe,0x80,0x00,  //.+@@@@+..
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........


/* : */
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....
0x3e,0xe0,  //.@@.
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....
0x3e,0xe0,  //.@@.
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....


/* A */
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0xef,0xc0,0x00,0x00,  //....@@%....
0x00,0x08,0xcc,0xe5,0x00,0x00,  //...+%%@+...
0x00,0x0c,0xe8,0xea,0x00,0x00,  //...%@+@%...
0x00,0x3e,0xe3,0xee,0x00,0x00,  //...@@.@@...
0x00,0xae,0x80,0xce,0x80,0x00,  //..%@+.%@+..
0x00,0xee,0x30,0x5e,0xc0,0x00,  //..@@..+@%..
0x08,0xec,0x00,0x0e,0xe5,0x00,  //.+@%...@@+.
0x0c,0xff,0xff,0xff,0xfa,0x00,  //.%@@@@@@@%.
0x3e,0xe0,0x00,0x05,0xee,0x00,  //.@@....+@@.
0xae,0x80,0x00,0x00,0xce,0x80,  //%@+.....%@+
0xee,0x30,0x00,0x00,0x8e,0xc0,  //@@......+@%
0xea,0x00,0x00,0x00,0x0e,0xe0,  //@%.......@@
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........


/* B */
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x0e,0xff,0xff,0xfe,0x80,0x00,  //.@@@@@@@+..
0x0e,0xe3,0x00,0x0c,0xe8,0x00,  //.@@....%@+.
0x0e,0xe3,0x00,0x05,0xec,0x00,  //.@@....+@%.
0x0e,0xe3,0x00,0x05,0xea,0x00,  //.@@....+@%.
0x0e,0xe3,0x00,0x0c,0xe0,0x00,  //.@@....%@..
0x0e,0xff,0xff,0xff,0xe3,0x00,  //.@@@@@@@@..
0x0e,0xe3,0x00,0x0a,0xfe,0x00,  //.@@....%@@.
0x0e,0xe3,0x00,0x00,0xee,0x50,  //.@@.....@@+
0x0e,0xe3,0x00,0x00,0xce,0x50,  //.@@.....%@+
0x0e,0xe3,0x00,0x00,0xee,0x30,  //.@@.....@@.
0x0e,0xe3,0x00,0x0a,0xfa,0x00,  //.@@....%@%.
0x0e,0xff,0xff,0xfe,0x80,0x00,  //.@@@@@@@+..
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........


/* C */
0x00,0x00,0x00,0x00,0x00,0x00,  //............
0x00,0x00,0x00,0x00,0x00,0x00,  //............
0x00,0x00,0x00,0x00,0x00,0x00,  //............
0x00,0x03,0xef,0xff,0xe3,0x00,  //....@@@@@...
0x00,0xaf,0xc0,0x00,0xee,0x80,  //..%@%...@@+.
0x08,0xea,0x00,0x00,0x3e,0xe3,  //.+@%.....@@.
0x0c,0xe3,0x00,0x00,0x05,0x50,  //.%@......++.
0x3e,0xe0,0x00,0x00,0x00,0x00,  //.@@.........
0x5e,0xc0,0x00,0x00,0x00,0x00,  //+@%.........
0x5e,0xc0,0x00,0x00,0x00,0x00,  //+@%.........
0x3e,0xe0,0x00,0x00,0x00,0x00,  //.@@.........
0x0c,0xe3,0x00,0x00,0x0a,0xfa,  //.%@......%@%
0x08,0xea,0x00,0x00,0x3e,0xe3,  //.+@%.....@@.
0x00,0xaf,0xc0,0x03,0xee,0x80,  //..%@%...@@+.
0x00,0x05,0xef,0xff,0xe3,0x00,  //...+@@@@@...
0x00,0x00,0x00,0x00,0x00,0x00,  //............
0x00,0x00,0x00,0x00,0x00,0x00,  //............
0x00,0x00,0x00,0x00,0x00,0x00,  //............


/* D */
0x00,0x00,0x00,0x00,0x00,0x00,  //............
0x00,0x00,0x00,0x00,0x00,0x00,  //............
0x00,0x00,0x00,0x00,0x00,0x00,  //............
0x0e,0xff,0xff,0xff,0xa0,0x00,  //.@@@@@@@%...
0x0e,0xe3,0x00,0x08,0xee,0x00,  //.@@....+@@..
0x0e,0xe3,0x00,0x00,0xae,0x80,  //.@@.....%@+.
0x0e,0xe3,0x00,0x00,0x3e,0xe0,  //.@@......@@.
0x0e,0xe3,0x00,0x00,0x0e,0xe3,  //.@@......@@.
0x0e,0xe3,0x00,0x00,0x0c,0xe5,  //.@@......%@+
0x0e,0xe3,0x00,0x00,0x0c,0xe5,  //.@@......%@+
0x0e,0xe3,0x00,0x00,0x0e,0xe3,  //.@@......@@.
0x0e,0xe3,0x00,0x00,0x3e,0xe0,  //.@@......@@.
0x0e,0xe3,0x00,0x00,0xae,0x80,  //.@@.....%@+.
0x0e,0xe3,0x00,0x08,0xec,0x00,  //.@@....+@%..
0x0e,0xff,0xff,0xff,0xa0,0x00,  //.@@@@@@@%...
0x00,0x00,0x00,0x00,0x00,0x00,  //............
0x00,0x00,0x00,0x00,0x00,0x00,  //............
0x00,0x00,0x00,0x00,0x00,0x00,  //............


/* E */
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x0e,0xff,0xff,0xff,0xfe,0x30,  //.@@@@@@@@@.
0x0e,0xe3,0x00,0x00,0x00,0x00,  //.@@........
0x0e,0xe3,0x00,0x00,0x00,0x00,  //.@@........
0x0e,0xe3,0x00,0x00,0x00,0x00,  //.@@........
0x0e,0xe3,0x00,0x00,0x00,0x00,  //.@@........
0x0e,0xff,0xff,0xff,0xfc,0x00,  //.@@@@@@@@%.
0x0e,0xe3,0x00,0x00,0x00,0x00,  //.@@........
0x0e,0xe3,0x00,0x00,0x00,0x00,  //.@@........
0x0e,0xe3,0x00,0x00,0x00,0x00,  //.@@........
0x0e,0xe3,0x00,0x00,0x00,0x00,  //.@@........
0x0e,0xe3,0x00,0x00,0x00,0x00,  //.@@........
0x0e,0xff,0xff,0xff,0xfe,0x80,  //.@@@@@@@@@+
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........


/* F */
0x00,0x00,0x00,0x00,0x00,  //..........
0x00,0x00,0x00,0x00,0x00,  //..........
0x00,0x00,0x00,0x00,0x00,  //..........
0x0e,0xff,0xff,0xff,0xfa,  //.@@@@@@@@%
0x0e,0xe3,0x00,0x00,0x00,  //.@@.......
0x0e,0xe3,0x00,0x00,0x00,  //.@@.......
0x0e,0xe3,0x00,0x00,0x00,  //.@@.......
0x0e,0xe3,0x00,0x00,0x00,  //.@@.......
0x0e,0xe3,0x00,0x00,0x00,  //.@@.......
0x0e,0xff,0xff,0xff,0xa0,  //.@@@@@@@%.
0x0e,0xe3,0x00,0x00,0x00,  //.@@.......
0x0e,0xe3,0x00,0x00,0x00,  //.@@.......
0x0e,0xe3,0x00,0x00,0x00,  //.@@.......
0x0e,0xe3,0x00,0x00,0x00,  //.@@.......
0x0e,0xe3,0x00,0x00,0x00,  //.@@.......
0x00,0x00,0x00,0x00,0x00,  //..........
0x00,0x00,0x00,0x00,0x00,  //..........
0x00,0x00,0x00,0x00,0x00,  //..........


/* G */
0x00,0x00,0x00,0x00,0x00,0x00,  //............
0x00,0x00,0x00,0x00,0x00,0x00,  //............
0x00,0x00,0x00,0x00,0x00,0x00,  //............
0x00,0x05,0xef,0xff,0xe3,0x00,  //...+@@@@@...
0x00,0xcf,0xa0,0x03,0xee,0x50,  //..%@%...@@+.
0x08,0xea,0x00,0x00,0x3e,0xe0,  //.+@%.....@@.
0x0e,0xe3,0x00,0x00,0x0c,0xa0,  //.@@......%%.
0x3e,0xc0,0x00,0x00,0x00,0x00,  //.@%.........
0x5e,0xa0,0x00,0x00,0x00,0x00,  //+@%.........
0x5e,0xa0,0x00,0xef,0xff,0xe5,  //+@%...@@@@@+
0x5e,0xc0,0x00,0x00,0x0a,0xe5,  //+@%......%@+
0x0e,0xe3,0x00,0x00,0x0a,0xe5,  //.@@......%@+
0x08,0xea,0x00,0x00,0x0a,0xe5,  //.+@%.....%@+
0x00,0xcf,0xc0,0x00,0xef,0xe0,  //..%@%...@@@.
0x00,0x05,0xef,0xff,0xe5,0x00,  //...+@@@@@+..
0x00,0x00,0x00,0x00,0x00,0x00,  //............
0x00,0x00,0x00,0x00,0x00,0x00,  //............
0x00,0x00,0x00,0x00,0x00,0x00,  //............


/* H */
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x3e,0xe0,0x00,0x00,0xee,0x30,  //.@@.....@@.
0x3e,0xe0,0x00,0x00,0xee,0x30,  //.@@.....@@.
0x3e,0xe0,0x00,0x00,0xee,0x30,  //.@@.....@@.
0x3e,0xe0,0x00,0x00,0xee,0x30,  //.@@.....@@.
0x3e,0xe0,0x00,0x00,0xee,0x30,  //.@@.....@@.
0x3e,0xff,0xff,0xff,0xfe,0x30,  //.@@@@@@@@@.
0x3e,0xe0,0x00,0x00,0xee,0x30,  //.@@.....@@.
0x3e,0xe0,0x00,0x00,0xee,0x30,  //.@@.....@@.
0x3e,0xe0,0x00,0x00,0xee,0x30,  //.@@.....@@.
0x3e,0xe0,0x00,0x00,0xee,0x30,  //.@@.....@@.
0x3e,0xe0,0x00,0x00,0xee,0x30,  //.@@.....@@.
0x3e,0xe0,0x00,0x00,0xee,0x30,  //.@@.....@@.
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........


/* I */
0x00,0x00,  //...
0x00,0x00,  //...
0x00,0x00,  //...
0xae,0x80,  //%@+
0xae,0x80,  //%@+
0xae,0x80,  //%@+
0xae,0x80,  //%@+
0xae,0x80,  //%@+
0xae,0x80,  //%@+
0xae,0x80,  //%@+
0xae,0x80,  //%@+
0xae,0x80,  //%@+
0xae,0x80,  //%@+
0xae,0x80,  //%@+
0xae,0x80,  //%@+
0x00,0x00,  //...
0x00,0x00,  //...
0x00,0x00,  //...


/* J */
0x00,0x00,0x00,0x00,  //........
0x00,0x00,0x00,0x00,  //........
0x00,0x00,0x00,0x00,  //........
0x00,0x00,0x3e,0xe0,  //.....@@.
0x00,0x00,0x3e,0xe0,  //.....@@.
0x00,0x00,0x3e,0xe0,  //.....@@.
0x00,0x00,0x3e,0xe0,  //.....@@.
0x00,0x00,0x3e,0xe0,  //.....@@.
0x00,0x00,0x3e,0xe0,  //.....@@.
0x00,0x00,0x3e,0xe0,  //.....@@.
0x00,0x00,0x3e,0xe0,  //.....@@.
0xce,0x50,0x3e,0xe0,  //%@+..@@.
0xae,0x80,0x3e,0xc0,  //%@+..@%.
0x8e,0xc0,0x8e,0xa0,  //+@%.+@%.
0x0a,0xff,0xfa,0x00,  //.%@@@%..
0x00,0x00,0x00,0x00,  //........
0x00,0x00,0x00,0x00,  //........
0x00,0x00,0x00,0x00,  //........


/* K */
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x0e,0xe3,0x00,0x00,0xaf,0xe0,  //.@@.....%@@
0x0e,0xe3,0x00,0x0a,0xfa,0x00,  //.@@....%@%.
0x0e,0xe3,0x00,0xae,0x80,0x00,  //.@@...%@+..
0x0e,0xe3,0x0a,0xe8,0x00,0x00,  //.@@..%@+...
0x0e,0xe3,0xae,0x80,0x00,0x00,  //.@@.%@+....
0x0e,0xea,0xfe,0x80,0x00,0x00,  //.@@%@@+....
0x0e,0xfe,0x8e,0xe5,0x00,0x00,  //.@@@+@@+...
0x0e,0xe8,0x05,0xee,0x00,0x00,  //.@@+.+@@...
0x0e,0xe3,0x00,0xaf,0xc0,0x00,  //.@@...%@%..
0x0e,0xe3,0x00,0x0c,0xe8,0x00,  //.@@....%@+.
0x0e,0xe3,0x00,0x03,0xee,0x50,  //.@@.....@@+
0x0e,0xe3,0x00,0x00,0x8e,0xe0,  //.@@.....+@@
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........


/* L */
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x0e,0xe3,0x00,0x00,0x00,  //.@@......
0x0e,0xe3,0x00,0x00,0x00,  //.@@......
0x0e,0xe3,0x00,0x00,0x00,  //.@@......
0x0e,0xe3,0x00,0x00,0x00,  //.@@......
0x0e,0xe3,0x00,0x00,0x00,  //.@@......
0x0e,0xe3,0x00,0x00,0x00,  //.@@......
0x0e,0xe3,0x00,0x00,0x00,  //.@@......
0x0e,0xe3,0x00,0x00,0x00,  //.@@......
0x0e,0xe3,0x00,0x00,0x00,  //.@@......
0x0e,0xe3,0x00,0x00,0x00,  //.@@......
0x0e,0xe3,0x00,0x00,0x00,  //.@@......
0x0e,0xff,0xff,0xff,0xc0,  //.@@@@@@@%
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........


/* M */
0x00,0x00,0x00,0x00,0x00,0x00,0x00,  //.............
0x00,0x00,0x00,0x00,0x00,0x00,0x00,  //.............
0x00,0x00,0x00,0x00,0x00,0x00,0x00,  //.............
0x3e,0xfc,0x00,0x00,0x0a,0xfe,0x30,  //.@@%.....%@@.
0x3e,0xfe,0x30,0x00,0x0e,0xfe,0x30,  //.@@@.....@@@.
0x3e,0xec,0x80,0x00,0x5a,0xee,0x30,  //.@@%+...+%@@.
0x3e,0xee,0xc0,0x00,0xae,0xee,0x30,  //.@@@%...%@@@.
0x3e,0xea,0xe3,0x00,0xec,0xee,0x30,  //.@@%@...@%@@.
0x3e,0xe8,0xc5,0x05,0xc8,0xee,0x30,  //.@@+%+.+%+@@.
0x3e,0xe3,0xea,0x0a,0xe3,0xee,0x30,  //.@@.@%.%@.@@.
0x3e,0xe0,0xce,0x0e,0xc0,0xee,0x30,  //.@@.%@.@%.@@.
0x3e,0xe0,0x8c,0x5c,0x80,0xee,0x30,  //.@@.+%+%+.@@.
0x3e,0xe0,0x3e,0xac,0x50,0xee,0x30,  //.@@..@%%+.@@.
0x3e,0xe0,0x0e,0xfe,0x00,0xee,0x30,  //.@@..@@@..@@.
0x3e,0xe0,0x0a,0xfa,0x00,0xee,0x30,  //.@@..%@%..@@.
0x00,0x00,0x00,0x00,0x00,0x00,0x00,  //.............
0x00,0x00,0x00,0x00,0x00,0x00,0x00,  //.............
0x00,0x00,0x00,0x00,0x00,0x00,0x00,  //.............


/* N */
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x3e,0xe5,0x00,0x00,0xee,0x30,  //.@@+....@@.
0x3e,0xfe,0x00,0x00,0xee,0x30,  //.@@@....@@.
0x3e,0xec,0x80,0x00,0xee,0x30,  //.@@%+...@@.
0x3e,0xec,0xe0,0x00,0xee,0x30,  //.@@%@...@@.
0x3e,0xe5,0xea,0x00,0xee,0x30,  //.@@+@%..@@.
0x3e,0xe0,0xce,0x30,0xee,0x30,  //.@@.%@..@@.
0x3e,0xe0,0x3e,0xc0,0xee,0x30,  //.@@..@%.@@.
0x3e,0xe0,0x0a,0xe5,0xee,0x30,  //.@@..%@+@@.
0x3e,0xe0,0x00,0xec,0xee,0x30,  //.@@...@%@@.
0x3e,0xe0,0x00,0x8c,0xee,0x30,  //.@@...+%@@.
0x3e,0xe0,0x00,0x0e,0xfe,0x30,  //.@@....@@@.
0x3e,0xe0,0x00,0x05,0xee,0x30,  //.@@....+@@.
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........


/* O */
0x00,0x00,0x00,0x00,0x00,0x00,  //............
0x00,0x00,0x00,0x00,0x00,0x00,  //............
0x00,0x00,0x00,0x00,0x00,0x00,  //............
0x00,0x08,0xef,0xff,0xa0,0x00,  //...+@@@@%...
0x00,0xcf,0xa0,0x08,0xee,0x00,  //..%@%..+@@..
0x08,0xc8,0x00,0x00,0x5e,0xc0,  //.+%+....+@%.
0x0e,0xe0,0x00,0x00,0x0c,0xe3,  //.@@......%@.
0x5e,0xc0,0x00,0x00,0x0a,0xe8,  //+@%......%@+
0x5e,0xa0,0x00,0x00,0x08,0xea,  //+@%......+@%
0x5e,0xa0,0x00,0x00,0x08,0xea,  //+@%......+@%
0x5e,0xc0,0x00,0x00,0x0a,0xe8,  //+@%......%@+
0x0e,0xe0,0x00,0x00,0x0c,0xe3,  //.@@......%@.
0x08,0xea,0x00,0x00,0x5e,0xc0,  //.+@%....+@%.
0x00,0xcf,0xa0,0x08,0xee,0x00,  //..%@%..+@@..
0x00,0x08,0xef,0xff,0xa0,0x00,  //...+@@@@%...
0x00,0x00,0x00,0x00,0x00,0x00,  //............
0x00,0x00,0x00,0x00,0x00,0x00,  //............
0x00,0x00,0x00,0x00,0x00,0x00,  //............


/* P */
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x0e,0xff,0xff,0xff,0xe0,0x00,  //.@@@@@@@@..
0x0e,0xe3,0x00,0x05,0xee,0x30,  //.@@....+@@.
0x0e,0xe3,0x00,0x00,0xae,0x80,  //.@@.....%@+
0x0e,0xe3,0x00,0x00,0x8e,0xa0,  //.@@.....+@%
0x0e,0xe3,0x00,0x00,0xae,0x80,  //.@@.....%@+
0x0e,0xe3,0x00,0x05,0xee,0x00,  //.@@....+@@.
0x0e,0xff,0xff,0xff,0xc0,0x00,  //.@@@@@@@%..
0x0e,0xe3,0x00,0x00,0x00,0x00,  //.@@........
0x0e,0xe3,0x00,0x00,0x00,0x00,  //.@@........
0x0e,0xe3,0x00,0x00,0x00,0x00,  //.@@........
0x0e,0xe3,0x00,0x00,0x00,0x00,  //.@@........
0x0e,0xe3,0x00,0x00,0x00,0x00,  //.@@........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........


/* Q */
0x00,0x00,0x00,0x00,0x00,0x00,  //............
0x00,0x00,0x00,0x00,0x00,0x00,  //............
0x00,0x00,0x00,0x00,0x00,0x00,  //............
0x00,0x0a,0xff,0xfe,0x80,0x00,  //...%@@@@+...
0x03,0xee,0x50,0x08,0xec,0x00,  //..@@+..+@%..
0x0c,0xe5,0x00,0x00,0x8e,0xa0,  //.%@+....+@%.
0x5e,0xc0,0x00,0x00,0x0e,0xe0,  //+@%......@@.
0x8c,0x80,0x00,0x00,0x0a,0xe5,  //+%+......%@+
0xae,0x80,0x00,0x00,0x0a,0xe8,  //%@+......%@+
0xae,0x80,0x00,0x00,0x0a,0xe8,  //%@+......%@+
0x8c,0x80,0x00,0x00,0x0a,0xe5,  //+%+......%@+
0x5e,0xc0,0x00,0x00,0x0e,0xe3,  //+@%......@@.
0x0c,0xe5,0x03,0xee,0x8e,0xc0,  //.%@+..@@+@%.
0x00,0xee,0x50,0x3e,0xfe,0x30,  //..@@+..@@@..
0x00,0x0a,0xff,0xff,0xff,0xe5,  //...%@@@@@@@+
0x00,0x00,0x00,0x00,0x00,0xc8,  //..........%+
0x00,0x00,0x00,0x00,0x00,0x00,  //............
0x00,0x00,0x00,0x00,0x00,0x00,  //............


/* R */
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x3e,0xff,0xff,0xff,0xc0,0x00,  //.@@@@@@@%..
0x3e,0xe0,0x00,0x0a,0xfa,0x00,  //.@@....%@%.
0x3e,0xe0,0x00,0x03,0xee,0x00,  //.@@.....@@.
0x3e,0xe0,0x00,0x00,0xee,0x30,  //.@@.....@@.
0x3e,0xe0,0x00,0x03,0xee,0x00,  //.@@.....@@.
0x3e,0xe0,0x00,0x0c,0xfa,0x00,  //.@@....%@%.
0x3e,0xff,0xff,0xff,0xa0,0x00,  //.@@@@@@@%..
0x3e,0xe0,0x08,0xee,0x00,0x00,  //.@@..+@@...
0x3e,0xe0,0x00,0xaf,0xc0,0x00,  //.@@...%@%..
0x3e,0xe0,0x00,0x0e,0xe8,0x00,  //.@@....@@+.
0x3e,0xe0,0x00,0x05,0xee,0x30,  //.@@....+@@.
0x3e,0xe0,0x00,0x00,0xcf,0xc0,  //.@@.....%@%
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........


/* S */
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x5e,0xff,0xfe,0x50,0x00,  //..+@@@@@+..
0x0a,0xfa,0x00,0x0e,0xe8,0x00,  //.%@%...@@+.
0x0e,0xe0,0x00,0x03,0xee,0x00,  //.@@.....@@.
0x3e,0xe0,0x00,0x00,0x00,0x00,  //.@@........
0x0a,0xfe,0x00,0x00,0x00,0x00,  //.%@@.......
0x00,0x8e,0xff,0xe5,0x00,0x00,  //..+@@@@+...
0x00,0x00,0x03,0xef,0xe8,0x00,  //......@@@+.
0x00,0x00,0x00,0x00,0xee,0x50,  //........@@+
0x8e,0xa0,0x00,0x00,0xae,0x80,  //+@%.....%@+
0x3e,0xe0,0x00,0x00,0xae,0x50,  //.@@.....%@+
0x0a,0xfe,0x30,0x0a,0xfc,0x00,  //.%@@...%@%.
0x00,0x5e,0xff,0xfe,0x80,0x00,  //..+@@@@@+..
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........


/* T */
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0xef,0xff,0xff,0xff,0xe0,  //@@@@@@@@@
0x00,0x08,0xc8,0x00,0x00,  //...+%+...
0x00,0x08,0xc8,0x00,0x00,  //...+%+...
0x00,0x08,0xc8,0x00,0x00,  //...+%+...
0x00,0x08,0xc8,0x00,0x00,  //...+%+...
0x00,0x08,0xc8,0x00,0x00,  //...+%+...
0x00,0x08,0xc8,0x00,0x00,  //...+%+...
0x00,0x08,0xc8,0x00,0x00,  //...+%+...
0x00,0x08,0xc8,0x00,0x00,  //...+%+...
0x00,0x08,0xc8,0x00,0x00,  //...+%+...
0x00,0x08,0xc8,0x00,0x00,  //...+%+...
0x00,0x08,0xc8,0x00,0x00,  //...+%+...
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........


/* U */
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x3e,0xe0,0x00,0x00,0xee,0x30,  //.@@.....@@.
0x3e,0xe0,0x00,0x00,0xee,0x30,  //.@@.....@@.
0x3e,0xe0,0x00,0x00,0xee,0x30,  //.@@.....@@.
0x3e,0xe0,0x00,0x00,0xee,0x30,  //.@@.....@@.
0x3e,0xe0,0x00,0x00,0xee,0x30,  //.@@.....@@.
0x3e,0xe0,0x00,0x00,0xee,0x30,  //.@@.....@@.
0x3e,0xe0,0x00,0x00,0xee,0x30,  //.@@.....@@.
0x3e,0xe0,0x00,0x00,0xee,0x30,  //.@@.....@@.
0x0e,0xe0,0x00,0x00,0xee,0x00,  //.@@.....@@.
0x0c,0xe5,0x00,0x05,0xec,0x00,  //.%@+...+@%.
0x05,0xee,0x00,0x0e,0xe5,0x00,  //.+@@...@@+.
0x00,0x3e,0xff,0xfe,0x30,0x00,  //...@@@@@...
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........


/* V */
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0xee,0x50,0x00,0x00,0x3e,0xe0,  //@@+......@@
0xaf,0xa0,0x00,0x00,0xaf,0xa0,  //%@%.....%@%
0x5e,0xe0,0x00,0x00,0xee,0x30,  //+@@.....@@.
0x0e,0xe5,0x00,0x05,0xec,0x00,  //.@@+...+@%.
0x08,0xea,0x00,0x0a,0xe8,0x00,  //.+@%...%@+.
0x03,0xee,0x00,0x0e,0xe3,0x00,  //..@@...@@..
0x00,0xce,0x50,0x8e,0xa0,0x00,  //..%@+.+@%..
0x00,0x5e,0xa0,0xce,0x50,0x00,  //..+@%.%@+..
0x00,0x0e,0xe3,0xee,0x00,0x00,  //...@@.@@...
0x00,0x0a,0xe8,0xc8,0x00,0x00,  //...%@+%+...
0x00,0x03,0xec,0xe3,0x00,0x00,  //....@%@....
0x00,0x00,0xcf,0xc0,0x00,0x00,  //....%@%....
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........


/* W */
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,  //...............
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,  //...............
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,  //...............
0xee,0x30,0x00,0xcf,0xc0,0x00,0x3e,0xe0,  //@@....%@%....@@
0xce,0x50,0x00,0xea,0xe3,0x00,0x5e,0xc0,  //%@+...@%@...+@%
0x8e,0xa0,0x05,0xc8,0xc8,0x00,0x8c,0x80,  //+@%..+%+%+..+%+
0x5e,0xc0,0x0a,0xe3,0xea,0x00,0xce,0x50,  //+@%..%@.@%..%@+
0x0e,0xe0,0x0c,0xe0,0xee,0x00,0xee,0x00,  //.@@..%@.@@..@@.
0x0a,0xe3,0x3e,0xa0,0xae,0x33,0xea,0x00,  //.%@..@%.%@..@%.
0x08,0xc8,0x5c,0x80,0x8c,0x88,0xc8,0x00,  //.+%++%+.+%++%+.
0x03,0xea,0xae,0x30,0x3e,0xaa,0xe3,0x00,  //..@%%@...@%%@..
0x00,0xec,0xce,0x00,0x0e,0xec,0xe0,0x00,  //..@%%@...@@%@..
0x00,0xae,0xea,0x00,0x0a,0xee,0xa0,0x00,  //..%@@%...%@@%..
0x00,0x8c,0xc8,0x00,0x08,0xee,0x80,0x00,  //..+%%+...+@@+..
0x00,0x3e,0xe3,0x00,0x03,0xee,0x30,0x00,  //...@@.....@@...
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,  //...............
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,  //...............
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,  //...............


/* X */
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x8e,0xe3,0x00,0x00,0xcf,0xa0,  //+@@.....%@%
0x0a,0xfc,0x00,0x08,0xec,0x00,  //.%@%...+@%.
0x00,0xee,0x50,0x3e,0xe0,0x00,  //..@@+..@@..
0x00,0x5e,0xe0,0xce,0x50,0x00,  //..+@@.%@+..
0x00,0x08,0xea,0xea,0x00,0x00,  //...+@%@%...
0x00,0x00,0xcf,0xc0,0x00,0x00,  //....%@%....
0x00,0x03,0xef,0xe5,0x00,0x00,  //....@@@+...
0x00,0x0e,0xe5,0xee,0x00,0x00,  //...@@+@@...
0x00,0xaf,0xa0,0xaf,0xc0,0x00,  //..%@%.%@%..
0x08,0xee,0x00,0x0e,0xe8,0x00,  //.+@@...@@+.
0x3e,0xe5,0x00,0x05,0xee,0x30,  //.@@+...+@@.
0xef,0xa0,0x00,0x00,0xaf,0xe0,  //@@%.....%@@
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........


/* Y */
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0xee,0x50,0x00,0x8e,0xe0,  //@@+...+@@
0xaf,0xc0,0x00,0xce,0x50,  //%@%...%@+
0x0e,0xe5,0x05,0xec,0x00,  //.@@+.+@%.
0x08,0xec,0x0c,0xe5,0x00,  //.+@%.%@+.
0x00,0xee,0x6e,0xc0,0x00,  //..@@+@%..
0x00,0x8e,0xfe,0x30,0x00,  //..+@@@...
0x00,0x0c,0xfa,0x00,0x00,  //...%@%...
0x00,0x0a,0xe8,0x00,0x00,  //...%@+...
0x00,0x0a,0xe8,0x00,0x00,  //...%@+...
0x00,0x0a,0xe8,0x00,0x00,  //...%@+...
0x00,0x0a,0xe8,0x00,0x00,  //...%@+...
0x00,0x0a,0xe8,0x00,0x00,  //...%@+...
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........


/* Z */
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x5e,0xff,0xff,0xff,0xc0,  //+@@@@@@@%
0x00,0x00,0x00,0xce,0x80,  //......%@+
0x00,0x00,0x08,0xec,0x00,  //.....+@%.
0x00,0x00,0x3e,0xe3,0x00,  //.....@@..
0x00,0x00,0xce,0x80,0x00,  //....%@+..
0x00,0x08,0xec,0x00,0x00,  //...+@%...
0x00,0x3e,0xe3,0x00,0x00,  //...@@....
0x00,0xce,0x80,0x00,0x00,  //..%@+....
0x05,0xee,0x00,0x00,0x00,  //.+@@.....
0x0e,0xe5,0x00,0x00,0x00,  //.@@+.....
0xaf,0xa0,0x00,0x00,0x00,  //%@%......
0xef,0xff,0xff,0xff,0xe0,  //@@@@@@@@@
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........


/* a */
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0xef,0xff,0xa0,0x00,  //..@@@@%..
0x0e,0xe0,0x0c,0xe8,0x00,  //.@@..%@+.
0x5c,0x80,0x08,0xea,0x00,  //+%+..+@%.
0x00,0x00,0xaf,0xfa,0x00,  //....%@@%.
0x0c,0xff,0xe8,0xea,0x00,  //.%@@@+@%.
0x8e,0xa0,0x08,0xea,0x00,  //+@%..+@%.
0xae,0x50,0x0a,0xfa,0x00,  //%@+..%@%.
0x8e,0xa0,0x5e,0xfc,0x00,  //+@%.+@@%.
0x0c,0xff,0xe8,0xee,0x80,  //.%@@@+@@+
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........


/* b */
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x8e,0xa0,0x00,0x00,0x00,  //+@%......
0x8e,0xa0,0x00,0x00,0x00,  //+@%......
0x8e,0xa0,0x00,0x00,0x00,  //+@%......
0x8e,0xae,0xff,0xc0,0x00,  //+@%@@@%..
0x8e,0xe8,0x08,0xec,0x00,  //+@@+.+@%.
0x8e,0xc0,0x00,0xce,0x50,  //+@%...%@+
0x8e,0xa0,0x00,0xae,0x80,  //+@%...%@+
0x8c,0x80,0x00,0x8c,0x80,  //+%+...+%+
0x8e,0xa0,0x00,0xae,0x80,  //+@%...%@+
0x8e,0xc0,0x00,0xce,0x30,  //+@%...%@.
0x8e,0xe8,0x0a,0xfa,0x00,  //+@@+.%@%.
0x8e,0xae,0xff,0xa0,0x00,  //+@%@@@%..
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........


/* c */
0x00,0x00,0x00,0x00,  //........
0x00,0x00,0x00,0x00,  //........
0x00,0x00,0x00,0x00,  //........
0x00,0x00,0x00,0x00,  //........
0x00,0x00,0x00,0x00,  //........
0x00,0x00,0x00,0x00,  //........
0x00,0xcf,0xff,0xe0,  //..%@@@@.
0x0c,0xe8,0x05,0xec,  //.%@+.+@%
0x5e,0xc0,0x00,0x00,  //+@%.....
0x8e,0xa0,0x00,0x00,  //+@%.....
0x8c,0x80,0x00,0x00,  //+%+.....
0x8e,0xa0,0x00,0x00,  //+@%.....
0x5e,0xc0,0x00,0x00,  //+@%.....
0x0c,0xe8,0x03,0xee,  //.%@+..@@
0x00,0xcf,0xff,0xe0,  //..%@@@@.
0x00,0x00,0x00,0x00,  //........
0x00,0x00,0x00,0x00,  //........
0x00,0x00,0x00,0x00,  //........


/* d */
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0xae,0x80,  //......%@+
0x00,0x00,0x00,0xae,0x80,  //......%@+
0x00,0x00,0x00,0xae,0x80,  //......%@+
0x00,0xcf,0xfe,0xae,0x80,  //..%@@@%@+
0x0e,0xe5,0x0a,0xfe,0x80,  //.@@+.%@@+
0x8e,0xa0,0x00,0xee,0x80,  //+@%...@@+
0xae,0x50,0x00,0xae,0x80,  //%@+...%@+
0xce,0x50,0x00,0xae,0x80,  //%@+...%@+
0xae,0x50,0x00,0xae,0x80,  //%@+...%@+
0x8e,0xa0,0x00,0xce,0x80,  //+@%...%@+
0x0c,0xe8,0x0a,0xfe,0x80,  //.%@+.%@@+
0x00,0xcf,0xfe,0xae,0x80,  //..%@@@%@+
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........


/* e */
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0xcf,0xff,0xc0,0x00,  //..%@@@%..
0x0c,0xe8,0x08,0xec,0x00,  //.%@+.+@%.
0x5e,0xc0,0x00,0xae,0x50,  //+@%...%@+
0xae,0x80,0x00,0x8e,0xa0,  //%@+...+@%
0xaf,0xff,0xff,0xff,0xa0,  //%@@@@@@@%
0xae,0x80,0x00,0x00,0x00,  //%@+......
0x5e,0xa0,0x00,0x00,0x00,  //+@%......
0x0c,0xe8,0x00,0xee,0x80,  //.%@+..@@+
0x00,0xcf,0xff,0xe5,0x00,  //..%@@@@+.
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........


/* f */
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....
0x0c,0xff,  //.%@@
0x5e,0xc0,  //+@%.
0x5e,0xa0,  //+@%.
0xef,0xfe,  //@@@@
0x5e,0xa0,  //+@%.
0x5e,0xa0,  //+@%.
0x5e,0xa0,  //+@%.
0x5e,0xa0,  //+@%.
0x5e,0xa0,  //+@%.
0x5e,0xa0,  //+@%.
0x5e,0xa0,  //+@%.
0x5e,0xa0,  //+@%.
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....


/* g */
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0xef,0xfe,0xae,0x80,  //..@@@@%@+
0x0e,0xe5,0x0a,0xfe,0x80,  //.@@+.%@@+
0x8e,0xa0,0x00,0xee,0x80,  //+@%...@@+
0xae,0x50,0x00,0xae,0x80,  //%@+...%@+
0xce,0x50,0x00,0xae,0x80,  //%@+...%@+
0xae,0x50,0x00,0xae,0x80,  //%@+...%@+
0x8e,0xa0,0x00,0xce,0x80,  //+@%...%@+
0x0e,0xe5,0x0a,0xfe,0x80,  //.@@+.%@@+
0x00,0xef,0xfe,0xae,0x50,  //..@@@@%@+
0x00,0x00,0x00,0xae,0x50,  //......%@+
0x8e,0xa0,0x05,0xec,0x00,  //+@%..+@%.
0x0a,0xff,0xff,0xa0,0x00,  //.%@@@@%..


/* h */
0x00,0x00,0x00,0x00,  //........
0x00,0x00,0x00,0x00,  //........
0x00,0x00,0x00,0x00,  //........
0x5e,0xa0,0x00,0x00,  //+@%.....
0x5e,0xa0,0x00,0x00,  //+@%.....
0x5e,0xa0,0x00,0x00,  //+@%.....
0x5e,0xae,0xff,0xa0,  //+@%@@@%.
0x5e,0xe8,0x0c,0xe8,  //+@@+.%@+
0x5e,0xc0,0x08,0xea,  //+@%..+@%
0x5e,0xa0,0x05,0xea,  //+@%..+@%
0x5e,0xa0,0x05,0xea,  //+@%..+@%
0x5e,0xa0,0x05,0xea,  //+@%..+@%
0x5e,0xa0,0x05,0xea,  //+@%..+@%
0x5e,0xa0,0x05,0xea,  //+@%..+@%
0x5e,0xa0,0x05,0xea,  //+@%..+@%
0x00,0x00,0x00,0x00,  //........
0x00,0x00,0x00,0x00,  //........
0x00,0x00,0x00,0x00,  //........


/* i */
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....
0x5e,0xa0,  //+@%.
0x00,0x00,  //....
0x00,0x00,  //....
0x5e,0xa0,  //+@%.
0x5e,0xa0,  //+@%.
0x5e,0xa0,  //+@%.
0x5e,0xa0,  //+@%.
0x5e,0xa0,  //+@%.
0x5e,0xa0,  //+@%.
0x5e,0xa0,  //+@%.
0x5e,0xa0,  //+@%.
0x5e,0xa0,  //+@%.
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....


/* j */
0x00,0x00,  //...
0x00,0x00,  //...
0x00,0x00,  //...
0x8e,0xa0,  //+@%
0x00,0x00,  //...
0x00,0x00,  //...
0x8e,0xa0,  //+@%
0x8e,0xa0,  //+@%
0x8e,0xa0,  //+@%
0x8e,0xa0,  //+@%
0x8e,0xa0,  //+@%
0x8e,0xa0,  //+@%
0x8e,0xa0,  //+@%
0x8e,0xa0,  //+@%
0x8e,0xa0,  //+@%
0x8e,0xa0,  //+@%
0x8c,0x80,  //+%+
0xfe,0x00,  //@@.


/* k */
0x00,0x00,0x00,0x00,  //........
0x00,0x00,0x00,0x00,  //........
0x00,0x00,0x00,0x00,  //........
0x5e,0xc0,0x00,0x00,  //+@%.....
0x5e,0xc0,0x00,0x00,  //+@%.....
0x5e,0xc0,0x00,0x00,  //+@%.....
0x5e,0xc0,0x0c,0xfc,  //+@%..%@%
0x5e,0xc0,0xcf,0xa0,  //+@%.%@%.
0x5e,0xca,0xfa,0x00,  //+@%%@%..
0x5e,0xcf,0xc0,0x00,  //+@%@%...
0x5e,0xe8,0xc8,0x00,  //+@@+%+..
0x5e,0xc0,0xee,0x30,  //+@%.@@..
0x5e,0xc0,0x8e,0xa0,  //+@%.+@%.
0x5e,0xc0,0x0e,0xe5,  //+@%..@@+
0x5e,0xc0,0x05,0xee,  //+@%..+@@
0x00,0x00,0x00,0x00,  //........
0x00,0x00,0x00,0x00,  //........
0x00,0x00,0x00,0x00,  //........


/* l */
0x00,0x00,  //...
0x00,0x00,  //...
0x00,0x00,  //...
0x5e,0xa0,  //+@%
0x5e,0xa0,  //+@%
0x5e,0xa0,  //+@%
0x5e,0xa0,  //+@%
0x5e,0xa0,  //+@%
0x5e,0xa0,  //+@%
0x5e,0xa0,  //+@%
0x5e,0xa0,  //+@%
0x5e,0xa0,  //+@%
0x5e,0xa0,  //+@%
0x5e,0xa0,  //+@%
0x5e,0xa0,  //+@%
0x00,0x00,  //...
0x00,0x00,  //...
0x00,0x00,  //...


/* m */
0x00,0x00,0x00,0x00,0x00,0x00,0x00,  //.............
0x00,0x00,0x00,0x00,0x00,0x00,0x00,  //.............
0x00,0x00,0x00,0x00,0x00,0x00,0x00,  //.............
0x00,0x00,0x00,0x00,0x00,0x00,0x00,  //.............
0x00,0x00,0x00,0x00,0x00,0x00,0x00,  //.............
0x00,0x00,0x00,0x00,0x00,0x00,0x00,  //.............
0x8e,0xae,0xff,0xc8,0xef,0xfa,0x00,  //+@%@@@%+@@@%.
0x8e,0xe8,0x0c,0xfe,0x50,0xce,0x50,  //+@@+.%@@+.%@+
0x8e,0xc0,0x08,0xec,0x00,0x8e,0xa0,  //+@%..+@%..+@%
0x8e,0xa0,0x08,0xea,0x00,0x8e,0xa0,  //+@%..+@%..+@%
0x8e,0xa0,0x08,0xea,0x00,0x8e,0xa0,  //+@%..+@%..+@%
0x8e,0xa0,0x08,0xea,0x00,0x8e,0xa0,  //+@%..+@%..+@%
0x8e,0xa0,0x08,0xea,0x00,0x8e,0xa0,  //+@%..+@%..+@%
0x8e,0xa0,0x08,0xea,0x00,0x8e,0xa0,  //+@%..+@%..+@%
0x8e,0xa0,0x08,0xea,0x00,0x8e,0xa0,  //+@%..+@%..+@%
0x00,0x00,0x00,0x00,0x00,0x00,0x00,  //.............
0x00,0x00,0x00,0x00,0x00,0x00,0x00,  //.............
0x00,0x00,0x00,0x00,0x00,0x00,0x00,  //.............


/* n */
0x00,0x00,0x00,0x00,  //........
0x00,0x00,0x00,0x00,  //........
0x00,0x00,0x00,0x00,  //........
0x00,0x00,0x00,0x00,  //........
0x00,0x00,0x00,0x00,  //........
0x00,0x00,0x00,0x00,  //........
0x5e,0xae,0xff,0xa0,  //+@%@@@%.
0x5e,0xe8,0x0c,0xe8,  //+@@+.%@+
0x5e,0xc0,0x08,0xea,  //+@%..+@%
0x5e,0xa0,0x05,0xea,  //+@%..+@%
0x5e,0xa0,0x05,0xea,  //+@%..+@%
0x5e,0xa0,0x05,0xea,  //+@%..+@%
0x5e,0xa0,0x05,0xea,  //+@%..+@%
0x5e,0xa0,0x05,0xea,  //+@%..+@%
0x5e,0xa0,0x05,0xea,  //+@%..+@%
0x00,0x00,0x00,0x00,  //........
0x00,0x00,0x00,0x00,  //........
0x00,0x00,0x00,0x00,  //........


/* o */
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0xaf,0xff,0xc0,0x00,  //..%@@@%..
0x0a,0xfa,0x05,0xee,0x00,  //.%@%.+@@.
0x3e,0xc0,0x00,0xae,0x80,  //.@%...%@+
0x8e,0xa0,0x00,0x8e,0xa0,  //+@%...+@%
0x8e,0xa0,0x00,0x5e,0xa0,  //+@%...+@%
0x8e,0xa0,0x00,0x8e,0xa0,  //+@%...+@%
0x3e,0xc0,0x00,0xae,0x80,  //.@%...%@+
0x0a,0xfa,0x05,0xee,0x00,  //.%@%.+@@.
0x00,0xaf,0xff,0xc0,0x00,  //..%@@@%..
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........


/* p */
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x8c,0x8e,0xff,0xc0,0x00,  //+%+@@@%..
0x8e,0xfa,0x0a,0xfa,0x00,  //+@@%.%@%.
0x8e,0xc0,0x00,0xce,0x30,  //+@%...%@.
0x8e,0xa0,0x00,0xae,0x80,  //+@%...%@+
0x8c,0x80,0x00,0xae,0x80,  //+%+...%@+
0x8c,0x80,0x00,0xae,0x80,  //+%+...%@+
0x8e,0xc0,0x00,0xee,0x30,  //+@%...@@.
0x8e,0xe8,0x0a,0xfa,0x00,  //+@@+.%@%.
0x8e,0xae,0xff,0xa0,0x00,  //+@%@@@%..
0x8e,0xa0,0x00,0x00,0x00,  //+@%......
0x8e,0xa0,0x00,0x00,0x00,  //+@%......
0x8e,0xa0,0x00,0x00,0x00,  //+@%......


/* q */
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0x00,0x00,0x00,0x00,  //.........
0x00,0xcf,0xfe,0x8c,0x80,  //..%@@@+%+
0x0e,0xe5,0x0a,0xfe,0x80,  //.@@+.%@@+
0x8e,0xa0,0x00,0xce,0x80,  //+@%...%@+
0xae,0x50,0x00,0x8c,0x80,  //%@+...+%+
0xce,0x50,0x00,0x8c,0x80,  //%@+...+%+
0xae,0x80,0x00,0x8c,0x80,  //%@+...+%+
0x5e,0xa0,0x00,0xce,0x80,  //+@%...%@+
0x0c,0xe8,0x08,0xee,0x80,  //.%@+.+@@+
0x00,0xaf,0xff,0xac,0x80,  //..%@@@%%+
0x00,0x00,0x00,0x8c,0x80,  //......+%+
0x00,0x00,0x00,0x8c,0x80,  //......+%+
0x00,0x00,0x00,0x8c,0x80,  //......+%+


/* r */
0x00,0x00,0x00,  //.....
0x00,0x00,0x00,  //.....
0x00,0x00,0x00,  //.....
0x00,0x00,0x00,  //.....
0x00,0x00,0x00,  //.....
0x00,0x00,0x00,  //.....
0x5c,0xaf,0xe0,  //+%%@@
0x5e,0xe3,0x00,  //+@@..
0x5e,0xc0,0x00,  //+@%..
0x5e,0xa0,0x00,  //+@%..
0x5e,0xa0,0x00,  //+@%..
0x5e,0xa0,0x00,  //+@%..
0x5e,0xa0,0x00,  //+@%..
0x5e,0xa0,0x00,  //+@%..
0x5e,0xa0,0x00,  //+@%..
0x00,0x00,0x00,  //.....
0x00,0x00,0x00,  //.....
0x00,0x00,0x00,  //.....


/* s */
0x00,0x00,0x00,0x00,  //........
0x00,0x00,0x00,0x00,  //........
0x00,0x00,0x00,0x00,  //........
0x00,0x00,0x00,0x00,  //........
0x00,0x00,0x00,0x00,  //........
0x00,0x00,0x00,0x00,  //........
0x08,0xef,0xff,0xa0,  //.+@@@@%.
0x8e,0xa0,0x0a,0xfa,  //+@%..%@%
0xae,0x50,0x00,0x00,  //%@+.....
0x5e,0xe5,0x00,0x00,  //+@@+....
0x03,0xef,0xff,0xa0,  //..@@@@%.
0x00,0x00,0x0e,0xe8,  //.....@@+
0x00,0x00,0x05,0xea,  //.....+@%
0xae,0x80,0x0a,0xe8,  //%@+..%@+
0x0a,0xff,0xfe,0x80,  //.%@@@@+.
0x00,0x00,0x00,0x00,  //........
0x00,0x00,0x00,0x00,  //........
0x00,0x00,0x00,0x00,  //........


/* t */
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....
0x0a,0xa0,  //.%%.
0x5e,0xa0,  //+@%.
0x5e,0xa0,  //+@%.
0xef,0xfe,  //@@@@
0x5e,0xa0,  //+@%.
0x5e,0xa0,  //+@%.
0x5e,0xa0,  //+@%.
0x5e,0xa0,  //+@%.
0x5e,0xa0,  //+@%.
0x5e,0xa0,  //+@%.
0x5e,0xc0,  //+@%.
0x0e,0xfe,  //.@@@
0x00,0x00,  //....
0x00,0x00,  //....
0x00,0x00,  //....


/* u */
0x00,0x00,0x00,0x00,  //........
0x00,0x00,0x00,0x00,  //........
0x00,0x00,0x00,0x00,  //........
0x00,0x00,0x00,0x00,  //........
0x00,0x00,0x00,0x00,  //........
0x00,0x00,0x00,0x00,  //........
0x5e,0xa0,0x05,0xea,  //+@%..+@%
0x5e,0xa0,0x05,0xea,  //+@%..+@%
0x5e,0xa0,0x05,0xea,  //+@%..+@%
0x5e,0xa0,0x05,0xea,  //+@%..+@%
0x5e,0xa0,0x05,0xea,  //+@%..+@%
0x5e,0xa0,0x05,0xea,  //+@%..+@%
0x5e,0xc0,0x08,0xea,  //+@%..+@%
0x3e,0xe3,0x3e,0xfa,  //.@@..@@%
0x05,0xef,0xfa,0xea,  //.+@@@%@%
0x00,0x00,0x00,0x00,  //........
0x00,0x00,0x00,0x00,  //........
0x00,0x00,0x00,0x00,  //........


/* v */
0x00,0x00,0x00,0x00,  //.......
0x00,0x00,0x00,0x00,  //.......
0x00,0x00,0x00,0x00,  //.......
0x00,0x00,0x00,0x00,  //.......
0x00,0x00,0x00,0x00,  //.......
0x00,0x00,0x00,0x00,  //.......
0xea,0x00,0x0a,0xe0,  //@%...%@
0xee,0x00,0x0e,0xe0,  //@@...@@
0xce,0x50,0x5e,0xc0,  //%@+.+@%
0x5e,0xa0,0xae,0x80,  //+@%.%@+
0x0e,0xe0,0xee,0x30,  //.@@.@@.
0x0a,0xe6,0xea,0x00,  //.%@+@%.
0x05,0xea,0xe5,0x00,  //.+@%@+.
0x00,0xef,0xe0,0x00,  //..@@@..
0x00,0xaf,0xa0,0x00,  //..%@%..
0x00,0x00,0x00,0x00,  //.......
0x00,0x00,0x00,0x00,  //.......
0x00,0x00,0x00,0x00,  //.......


/* w */
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0xee,0x00,0xaf,0xa0,0x0e,0xe0,  //@@..%@%..@@
0xce,0x30,0xcf,0xe0,0x3e,0xc0,  //%@..%@@..@%
0xae,0x80,0xea,0xe3,0x8c,0x80,  //%@+.@%@.+%+
0x5e,0xa5,0xc8,0xc5,0xae,0x50,  //+@%+%+%+%@+
0x0e,0xc8,0xc5,0xc8,0xee,0x00,  //.@%+%+%+@@.
0x0c,0xec,0xe0,0xec,0xea,0x00,  //.%@%@.@%@%.
0x08,0xce,0xc0,0xce,0xc8,0x00,  //.+%@%.%@%+.
0x05,0xee,0x80,0xaf,0xe3,0x00,  //.+@@+.%@@..
0x00,0xee,0x50,0x8e,0xc0,0x00,  //..@@+.+@%..
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........
0x00,0x00,0x00,0x00,0x00,0x00,  //...........


/* x */
0x00,0x00,0x00,0x00,  //.......
0x00,0x00,0x00,0x00,  //.......
0x00,0x00,0x00,0x00,  //.......
0x00,0x00,0x00,0x00,  //.......
0x00,0x00,0x00,0x00,  //.......
0x00,0x00,0x00,0x00,  //.......
0xce,0x50,0x5e,0xc0,  //%@+.+@%
0x3e,0xc0,0xce,0x30,  //.@%.%@.
0x0a,0xe6,0xc8,0x00,  //.%@+%+.
0x00,0xef,0xe0,0x00,  //..@@@..
0x00,0xaf,0xa0,0x00,  //..%@%..
0x03,0xea,0xe5,0x00,  //..@%@+.
0x0c,0xe3,0xec,0x00,  //.%@.@%.
0x5e,0xa0,0xae,0x50,  //+@%.%@+
0xee,0x30,0x3e,0xe0,  //@@...@@
0x00,0x00,0x00,0x00,  //.......
0x00,0x00,0x00,0x00,  //.......
0x00,0x00,0x00,0x00,  //.......


/* y */
0x00,0x00,0x00,0x00,  //.......
0x00,0x00,0x00,0x00,  //.......
0x00,0x00,0x00,0x00,  //.......
0x00,0x00,0x00,0x00,  //.......
0x00,0x00,0x00,0x00,  //.......
0x00,0x00,0x00,0x00,  //.......
0xee,0x00,0x0c,0xe0,  //@@...%@
0xae,0x50,0x3e,0xa0,  //%@+..@%
0x5e,0xa0,0x8c,0x80,  //+@%.+%+
0x0e,0xe0,0xce,0x30,  //.@@.%@.
0x0a,0xe5,0xec,0x00,  //.%@+@%.
0x05,0xc8,0xc8,0x00,  //.+%+%+.
0x03,0xec,0xe3,0x00,  //..@%@..
0x00,0xcf,0xc0,0x00,  //..%@%..
0x00,0x8c,0x80,0x00,  //..+%+..
0x00,0x8c,0x30,0x00,  //..+%...
0x00,0xcc,0x00,0x00,  //..%%...
0x8e,0xe3,0x00,0x00,  //+@@....


/* z */
0x00,0x00,0x00,0x00,  //.......
0x00,0x00,0x00,0x00,  //.......
0x00,0x00,0x00,0x00,  //.......
0x00,0x00,0x00,0x00,  //.......
0x00,0x00,0x00,0x00,  //.......
0x00,0x00,0x00,0x00,  //.......
0xcf,0xff,0xff,0xc0,  //%@@@@@%
0x00,0x00,0xce,0x50,  //....%@+
0x00,0x08,0xea,0x00,  //...+@%.
0x00,0x3e,0xe3,0x00,  //...@@..
0x00,0xce,0x80,0x00,  //..%@+..
0x08,0xec,0x00,0x00,  //.+@%...
0x3e,0xe3,0x00,0x00,  //.@@....
0xce,0x80,0x00,0x00,  //%@+....
0xef,0xff,0xff,0xf0,  //@@@@@@@
0x00,0x00,0x00,0x00,  //.......
0x00,0x00,0x00,0x00,  //.......
0x00,0x00,0x00,0x00,  //.......


};


static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0,	.adv_w = 4, .box_h = 18, .box_w = 4, .ofs_x = 0, .ofs_y = 0},/*( )*/
    {.bitmap_index = 36,	.adv_w = 4, .box_h = 18, .box_w = 4, .ofs_x = 0, .ofs_y = 0},/*(.)*/
    {.bitmap_index = 72,	.adv_w = 9, .box_h = 18, .box_w = 10, .ofs_x = 0, .ofs_y = 0},/*(0)*/
    {.bitmap_index = 162,	.adv_w = 9, .box_h = 18, .box_w = 10, .ofs_x = 0, .ofs_y = 0},/*(1)*/
    {.bitmap_index = 252,	.adv_w = 9, .box_h = 18, .box_w = 10, .ofs_x = 0, .ofs_y = 0},/*(2)*/
    {.bitmap_index = 342,	.adv_w = 9, .box_h = 18, .box_w = 10, .ofs_x = 0, .ofs_y = 0},/*(3)*/
    {.bitmap_index = 432,	.adv_w = 9, .box_h = 18, .box_w = 10, .ofs_x = 0, .ofs_y = 0},/*(4)*/
    {.bitmap_index = 522,	.adv_w = 9, .box_h = 18, .box_w = 10, .ofs_x = 0, .ofs_y = 0},/*(5)*/
    {.bitmap_index = 612,	.adv_w = 9, .box_h = 18, .box_w = 10, .ofs_x = 0, .ofs_y = 0},/*(6)*/
    {.bitmap_index = 702,	.adv_w = 9, .box_h = 18, .box_w = 10, .ofs_x = 0, .ofs_y = 0},/*(7)*/
    {.bitmap_index = 792,	.adv_w = 9, .box_h = 18, .box_w = 10, .ofs_x = 0, .ofs_y = 0},/*(8)*/
    {.bitmap_index = 882,	.adv_w = 9, .box_h = 18, .box_w = 10, .ofs_x = 0, .ofs_y = 0},/*(9)*/
    {.bitmap_index = 972,	.adv_w = 4, .box_h = 18, .box_w = 4, .ofs_x = 0, .ofs_y = 0},/*(:)*/
    {.bitmap_index = 1008,	.adv_w = 11, .box_h = 18, .box_w = 12, .ofs_x = 0, .ofs_y = 0},/*(A)*/
    {.bitmap_index = 1116,	.adv_w = 11, .box_h = 18, .box_w = 12, .ofs_x = 0, .ofs_y = 0},/*(B)*/
    {.bitmap_index = 1224,	.adv_w = 12, .box_h = 18, .box_w = 12, .ofs_x = 0, .ofs_y = 0},/*(C)*/
    {.bitmap_index = 1332,	.adv_w = 12, .box_h = 18, .box_w = 12, .ofs_x = 0, .ofs_y = 0},/*(D)*/
    {.bitmap_index = 1440,	.adv_w = 11, .box_h = 18, .box_w = 12, .ofs_x = 0, .ofs_y = 0},/*(E)*/
    {.bitmap_index = 1548,	.adv_w = 10, .box_h = 18, .box_w = 10, .ofs_x = 0, .ofs_y = 0},/*(F)*/
    {.bitmap_index = 1638,	.adv_w = 12, .box_h = 18, .box_w = 12, .ofs_x = 0, .ofs_y = 0},/*(G)*/
    {.bitmap_index = 1746,	.adv_w = 11, .box_h = 18, .box_w = 12, .ofs_x = 0, .ofs_y = 0},/*(H)*/
    {.bitmap_index = 1854,	.adv_w = 3, .box_h = 18, .box_w = 4, .ofs_x = 0, .ofs_y = 0},/*(I)*/
    {.bitmap_index = 1890,	.adv_w = 8, .box_h = 18, .box_w = 8, .ofs_x = 0, .ofs_y = 0},/*(J)*/
    {.bitmap_index = 1962,	.adv_w = 11, .box_h = 18, .box_w = 12, .ofs_x = 0, .ofs_y = 0},/*(K)*/
    {.bitmap_index = 2070,	.adv_w = 9, .box_h = 18, .box_w = 10, .ofs_x = 0, .ofs_y = 0},/*(L)*/
    {.bitmap_index = 2160,	.adv_w = 13, .box_h = 18, .box_w = 14, .ofs_x = 0, .ofs_y = 0},/*(M)*/
    {.bitmap_index = 2286,	.adv_w = 11, .box_h = 18, .box_w = 12, .ofs_x = 0, .ofs_y = 0},/*(N)*/
    {.bitmap_index = 2394,	.adv_w = 12, .box_h = 18, .box_w = 12, .ofs_x = 0, .ofs_y = 0},/*(O)*/
    {.bitmap_index = 2502,	.adv_w = 11, .box_h = 18, .box_w = 12, .ofs_x = 0, .ofs_y = 0},/*(P)*/
    {.bitmap_index = 2610,	.adv_w = 12, .box_h = 18, .box_w = 12, .ofs_x = 0, .ofs_y = 0},/*(Q)*/
    {.bitmap_index = 2718,	.adv_w = 11, .box_h = 18, .box_w = 12, .ofs_x = 0, .ofs_y = 0},/*(R)*/
    {.bitmap_index = 2826,	.adv_w = 11, .box_h = 18, .box_w = 12, .ofs_x = 0, .ofs_y = 0},/*(S)*/
    {.bitmap_index = 2934,	.adv_w = 9, .box_h = 18, .box_w = 10, .ofs_x = 0, .ofs_y = 0},/*(T)*/
    {.bitmap_index = 3024,	.adv_w = 11, .box_h = 18, .box_w = 12, .ofs_x = 0, .ofs_y = 0},/*(U)*/
    {.bitmap_index = 3132,	.adv_w = 11, .box_h = 18, .box_w = 12, .ofs_x = 0, .ofs_y = 0},/*(V)*/
    {.bitmap_index = 3240,	.adv_w = 15, .box_h = 18, .box_w = 16, .ofs_x = 0, .ofs_y = 0},/*(W)*/
    {.bitmap_index = 3384,	.adv_w = 11, .box_h = 18, .box_w = 12, .ofs_x = 0, .ofs_y = 0},/*(X)*/
    {.bitmap_index = 3492,	.adv_w = 9, .box_h = 18, .box_w = 10, .ofs_x = 0, .ofs_y = 0},/*(Y)*/
    {.bitmap_index = 3582,	.adv_w = 9, .box_h = 18, .box_w = 10, .ofs_x = 0, .ofs_y = 0},/*(Z)*/
    {.bitmap_index = 3672,	.adv_w = 9, .box_h = 18, .box_w = 10, .ofs_x = 0, .ofs_y = 0},/*(a)*/
    {.bitmap_index = 3762,	.adv_w = 9, .box_h = 18, .box_w = 10, .ofs_x = 0, .ofs_y = 0},/*(b)*/
    {.bitmap_index = 3852,	.adv_w = 8, .box_h = 18, .box_w = 8, .ofs_x = 0, .ofs_y = 0},/*(c)*/
    {.bitmap_index = 3924,	.adv_w = 9, .box_h = 18, .box_w = 10, .ofs_x = 0, .ofs_y = 0},/*(d)*/
    {.bitmap_index = 4014,	.adv_w = 9, .box_h = 18, .box_w = 10, .ofs_x = 0, .ofs_y = 0},/*(e)*/
    {.bitmap_index = 4104,	.adv_w = 4, .box_h = 18, .box_w = 4, .ofs_x = 0, .ofs_y = 0},/*(f)*/
    {.bitmap_index = 4140,	.adv_w = 9, .box_h = 18, .box_w = 10, .ofs_x = 0, .ofs_y = 0},/*(g)*/
    {.bitmap_index = 4230,	.adv_w = 8, .box_h = 18, .box_w = 8, .ofs_x = 0, .ofs_y = 0},/*(h)*/
    {.bitmap_index = 4302,	.adv_w = 4, .box_h = 18, .box_w = 4, .ofs_x = 0, .ofs_y = 0},/*(i)*/
    {.bitmap_index = 4338,	.adv_w = 3, .box_h = 18, .box_w = 4, .ofs_x = 0, .ofs_y = 0},/*(j)*/
    {.bitmap_index = 4374,	.adv_w = 8, .box_h = 18, .box_w = 8, .ofs_x = 0, .ofs_y = 0},/*(k)*/
    {.bitmap_index = 4446,	.adv_w = 3, .box_h = 18, .box_w = 4, .ofs_x = 0, .ofs_y = 0},/*(l)*/
    {.bitmap_index = 4482,	.adv_w = 13, .box_h = 18, .box_w = 14, .ofs_x = 0, .ofs_y = 0},/*(m)*/
    {.bitmap_index = 4608,	.adv_w = 8, .box_h = 18, .box_w = 8, .ofs_x = 0, .ofs_y = 0},/*(n)*/
    {.bitmap_index = 4680,	.adv_w = 9, .box_h = 18, .box_w = 10, .ofs_x = 0, .ofs_y = 0},/*(o)*/
    {.bitmap_index = 4770,	.adv_w = 9, .box_h = 18, .box_w = 10, .ofs_x = 0, .ofs_y = 0},/*(p)*/
    {.bitmap_index = 4860,	.adv_w = 9, .box_h = 18, .box_w = 10, .ofs_x = 0, .ofs_y = 0},/*(q)*/
    {.bitmap_index = 4950,	.adv_w = 5, .box_h = 18, .box_w = 6, .ofs_x = 0, .ofs_y = 0},/*(r)*/
    {.bitmap_index = 5004,	.adv_w = 8, .box_h = 18, .box_w = 8, .ofs_x = 0, .ofs_y = 0},/*(s)*/
    {.bitmap_index = 5076,	.adv_w = 4, .box_h = 18, .box_w = 4, .ofs_x = 0, .ofs_y = 0},/*(t)*/
    {.bitmap_index = 5112,	.adv_w = 8, .box_h = 18, .box_w = 8, .ofs_x = 0, .ofs_y = 0},/*(u)*/
    {.bitmap_index = 5184,	.adv_w = 7, .box_h = 18, .box_w = 8, .ofs_x = 0, .ofs_y = 0},/*(v)*/
    {.bitmap_index = 5256,	.adv_w = 11, .box_h = 18, .box_w = 12, .ofs_x = 0, .ofs_y = 0},/*(w)*/
    {.bitmap_index = 5364,	.adv_w = 7, .box_h = 18, .box_w = 8, .ofs_x = 0, .ofs_y = 0},/*(x)*/
    {.bitmap_index = 5436,	.adv_w = 7, .box_h = 18, .box_w = 8, .ofs_x = 0, .ofs_y = 0},/*(y)*/
    {.bitmap_index = 5508,	.adv_w = 7, .box_h = 18, .box_w = 8, .ofs_x = 0, .ofs_y = 0},/*(z)*/
};


static const uint16_t unicode_list_1[] = {
    0x0020,	/*( )*/
    0x002e,	/*(.)*/
    0x0030,	/*(0)*/
    0x0031,	/*(1)*/
    0x0032,	/*(2)*/
    0x0033,	/*(3)*/
    0x0034,	/*(4)*/
    0x0035,	/*(5)*/
    0x0036,	/*(6)*/
    0x0037,	/*(7)*/
    0x0038,	/*(8)*/
    0x0039,	/*(9)*/
    0x003a,	/*(:)*/
    0x0041,	/*(A)*/
    0x0042,	/*(B)*/
    0x0043,	/*(C)*/
    0x0044,	/*(D)*/
    0x0045,	/*(E)*/
    0x0046,	/*(F)*/
    0x0047,	/*(G)*/
    0x0048,	/*(H)*/
    0x0049,	/*(I)*/
    0x004a,	/*(J)*/
    0x004b,	/*(K)*/
    0x004c,	/*(L)*/
    0x004d,	/*(M)*/
    0x004e,	/*(N)*/
    0x004f,	/*(O)*/
    0x0050,	/*(P)*/
    0x0051,	/*(Q)*/
    0x0052,	/*(R)*/
    0x0053,	/*(S)*/
    0x0054,	/*(T)*/
    0x0055,	/*(U)*/
    0x0056,	/*(V)*/
    0x0057,	/*(W)*/
    0x0058,	/*(X)*/
    0x0059,	/*(Y)*/
    0x005a,	/*(Z)*/
    0x0061,	/*(a)*/
    0x0062,	/*(b)*/
    0x0063,	/*(c)*/
    0x0064,	/*(d)*/
    0x0065,	/*(e)*/
    0x0066,	/*(f)*/
    0x0067,	/*(g)*/
    0x0068,	/*(h)*/
    0x0069,	/*(i)*/
    0x006a,	/*(j)*/
    0x006b,	/*(k)*/
    0x006c,	/*(l)*/
    0x006d,	/*(m)*/
    0x006e,	/*(n)*/
    0x006f,	/*(o)*/
    0x0070,	/*(p)*/
    0x0071,	/*(q)*/
    0x0072,	/*(r)*/
    0x0073,	/*(s)*/
    0x0074,	/*(t)*/
    0x0075,	/*(u)*/
    0x0076,	/*(v)*/
    0x0077,	/*(w)*/
    0x0078,	/*(x)*/
    0x0079,	/*(y)*/
    0x007a,	/*(z)*/
    0x0000,    /*End indicator*/
};


static const lv_font_fmt_txt_cmap_t cmaps[] = {
    {
        .range_start = 32,
        .range_length = 65,
        .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY,
        .glyph_id_start = 0,
        .unicode_list = unicode_list_1,
        .glyph_id_ofs_list = NULL,
        .list_length = 65,
    }
};


static lv_font_fmt_txt_dsc_t font_dsc = {
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .cmap_num = 1,
    .bpp = 4,

    .kern_scale = 0,
    .kern_dsc = NULL,
    .kern_classes = 0,
};


static int binsearch(const uint16_t *sortedSeq, int seqLength, uint16_t keyData) {
    int low = 0, mid, high = seqLength - 1;
    while (low <= high) {
        mid = (low + high) / 2;//奇数，无论奇偶，有个值就行
        if (keyData < sortedSeq[mid]) {
            high = mid - 1;//是mid-1，因为mid已经比较过了
        }
        else if (keyData > sortedSeq[mid]) {
            low = mid + 1;
        }
        else {
            return mid;
        }
    }
    return -1;
}


static const uint8_t * __user_font_get_bitmap(const lv_font_t * font, uint32_t unicode_letter) {
    lv_font_fmt_txt_dsc_t * fdsc = (lv_font_fmt_txt_dsc_t *) font->dsc;

    if(unicode_letter < fdsc->cmaps[0].range_start) return NULL;

    int i = binsearch(fdsc->cmaps[0].unicode_list, fdsc->cmaps[0].list_length, unicode_letter);
    if( i != -1 ) {
            const lv_font_fmt_txt_glyph_dsc_t * gdsc = &fdsc->glyph_dsc[i];
            return &fdsc->glyph_bitmap[gdsc->bitmap_index];
        }
    return NULL;
}


static bool __user_font_get_glyph_dsc(const lv_font_t * font, lv_font_glyph_dsc_t * dsc_out, uint32_t unicode_letter, uint32_t unicode_letter_next) {
    lv_font_fmt_txt_dsc_t * fdsc = (lv_font_fmt_txt_dsc_t *) font->dsc;

    if(unicode_letter < fdsc->cmaps[0].range_start) return false;

    int i = binsearch(fdsc->cmaps[0].unicode_list, fdsc->cmaps[0].list_length, unicode_letter);
    if( i != -1 ) {
            const lv_font_fmt_txt_glyph_dsc_t * gdsc = &fdsc->glyph_dsc[i];
            dsc_out->adv_w = gdsc->adv_w;
            dsc_out->box_h = gdsc->box_h;
            dsc_out->box_w = gdsc->box_w;
            dsc_out->ofs_x = gdsc->ofs_x;
            dsc_out->ofs_y = gdsc->ofs_y;
            dsc_out->bpp   = fdsc->bpp;
            return true;
        }
    return false;
}


//Arial,Regular,12
//字模高度：18
//内部字体
//未用排序和顺序查表
const lv_font_t lv_font_gb2312_12 = {
    .dsc = &font_dsc,
    .get_glyph_bitmap = __user_font_get_bitmap,
    .get_glyph_dsc = __user_font_get_glyph_dsc,
    .line_height = 18,
    .base_line = 0,
};

//end of file