/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_network_tuya.c
** bef: define the interface for tuya network.
** auth: lines<<EMAIL>>
** create on 2021.12.21 
*/

#include "es_inc.h"

#if ES_TUYA_MODULE_ENABLE

ES_S32 es_network_tuya_init(ES_VOID)
{
    if (ES_RET_SUCCESS != es_tuya_port_init()) {
        return ES_RET_FAILURE;
    }

    return ES_RET_SUCCESS;
}

ES_VOID es_network_tuya_task(ES_VOID)
{
    es_tuya_port_cycle();
}

ES_S32 es_network_tuya_get_status(ES_VOID)
{
    if (1 == es_tuya_port_wifi_connected()) {
        return ES_NETWORK_CONNECTED;
    }
    return ES_NETWORK_CONNECTING;
}

ES_S32 es_network_tuya_upload_pic(const ES_BYTE *pic_data, ES_U32 pic_data_len, ES_U32 timestamp)
{
    es_tuya_pic_data_t tuya_pic_data;
    
    tuya_pic_data.timestamp = timestamp;
    tuya_pic_data.data_len = pic_data_len;
    tuya_pic_data.data = (ES_BYTE *)pic_data;
    return es_tuya_port_upload_pic((const es_tuya_pic_data_t *)&tuya_pic_data);
}

#endif
