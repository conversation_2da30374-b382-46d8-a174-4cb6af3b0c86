/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_uart.h
** bef: define the interface for flash
** auth: lines<<EMAIL>>
** create on 2019.05.08 
*/

#include "es_inc.h"
#if ES_FLASH_MODULE_ENABLE
#include "facelib_inc.h"

// #define ES_FLASH_DEBUG
#ifdef ES_FLASH_DEBUG
#define es_flash_debug es_log_info
#define es_flash_error es_log_error
#else
#define es_flash_debug(...)
#define es_flash_error(...)
#endif

//#define ES_FLASH_TEST_DATA

#define ES_FLASH_CHECK_FLASH_ID(flash_id) do { \
        if (ES_FLASH_ID_COUNT <= flash_id) { \
            es_flash_error("not support flash id:%d", flash_id); \
            return ES_RET_FAILURE; \
        } \
    } while (0)
    

typedef struct {
    ES_S32 magic;
    es_flash_ble_mac_t ble_mac;
#if ES_NFC_MODULE_ENABLE
    es_flash_nfc_t nfc;
#endif
    es_flash_user_data_t user_data;
} es_flash_data_t;

typedef struct {
    ES_U32 addr;
    ES_U32 len;
} es_flash_addr_map_t;

static es_flash_data_t es_flash_data;
static es_flash_addr_map_t es_es_flash_addr_map[ES_FLASH_ID_COUNT];
static ES_U32 sync_flag = 0;

#ifdef ES_FLASH_TEST_DATA
static ES_VOID es_flash_load_test_data(ES_VOID)
{
    ES_U32 i = 0;
    ES_BYTE tmp_data[16] = {
        0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
        0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0F,
    };

    // BLE MAC
    es_flash_data.ble_mac.count = 0;
    for (i = 0; i < ES_FLASH_BLE_MAC_COUNT; i++) {
        es_flash_data.ble_mac.count++;
        es_memcpy(es_flash_data.ble_mac.data[i], tmp_data, 6);
    }

    // NFC
    es_flash_data.nfc.count = 0;
    for (i = 0; i < ES_FLASH_NFC_COUNT; i++) {
        es_flash_data.nfc.count++;
        es_memcpy(es_flash_data.nfc.data[i], tmp_data, ES_NFC_ID_DATA_LEN);
    }

    es_memset(tmp_data, 0x00, sizeof(tmp_data));
    tmp_data[0] = 0x88;
    tmp_data[1] = 0x04;
    tmp_data[2] = 0x70;
    tmp_data[3] = 0x1b;
    es_memcpy(es_flash_data.nfc.data[ES_FLASH_NFC_COUNT-1], tmp_data, ES_NFC_ID_DATA_LEN);
    // 0x88
    // 

}

static ES_VOID es_flash_show_test_data(ES_VOID)
{
    ES_U32 i = 0;

    // BLE MAC
    es_flash_debug("BLE_MAC DATA:");
    for (i = 0; i < es_flash_data.ble_mac.count; i++) {
        es_flash_debug("%d, %02x %02x %02x %02x %02x %02x", i, 
            es_flash_data.ble_mac.data[i][0],
            es_flash_data.ble_mac.data[i][1],
            es_flash_data.ble_mac.data[i][2],
            es_flash_data.ble_mac.data[i][3],
            es_flash_data.ble_mac.data[i][4],
            es_flash_data.ble_mac.data[i][5]);
    }

    es_flash_debug("NFC DATA:");
    for (i = 0; i < 10; i++) {
        es_flash_debug("%d, %02x %02x %02x %02x %02x %02x %02x %02x %02x %02x", i, 
            es_flash_data.nfc.data[i][0],
            es_flash_data.nfc.data[i][1],
            es_flash_data.nfc.data[i][2],
            es_flash_data.nfc.data[i][3],
            es_flash_data.nfc.data[i][4],
            es_flash_data.nfc.data[i][5],
            es_flash_data.nfc.data[i][6],
            es_flash_data.nfc.data[i][7],
            es_flash_data.nfc.data[i][8],
            es_flash_data.nfc.data[i][9]);
    }
    es_flash_debug("...");
    for (i = es_flash_data.nfc.count-10; i < es_flash_data.nfc.count; i++) {
        es_flash_debug("%d, %02x %02x %02x %02x %02x %02x %02x %02x %02x %02x", i, 
            es_flash_data.nfc.data[i][0],
            es_flash_data.nfc.data[i][1],
            es_flash_data.nfc.data[i][2],
            es_flash_data.nfc.data[i][3],
            es_flash_data.nfc.data[i][4],
            es_flash_data.nfc.data[i][5],
            es_flash_data.nfc.data[i][6],
            es_flash_data.nfc.data[i][7],
            es_flash_data.nfc.data[i][8],
            es_flash_data.nfc.data[i][9]);
    }
}
#endif

static ES_S32 es_flash_load_addr_map(ES_VOID)
{
    es_es_flash_addr_map[ES_FLASH_ID_BLE_MAC].addr = (ES_U32)&es_flash_data.ble_mac;
    es_es_flash_addr_map[ES_FLASH_ID_BLE_MAC].len = sizeof(es_flash_ble_mac_t);
#if ES_NFC_MODULE_ENABLE
    es_es_flash_addr_map[ES_FLASH_ID_NFC].addr = (ES_U32)&es_flash_data.nfc;
    es_es_flash_addr_map[ES_FLASH_ID_NFC].len = sizeof(es_flash_nfc_t);
#endif
    es_es_flash_addr_map[ES_FLASH_ID_USER_DATA].addr = (ES_U32)&es_flash_data.user_data;
    es_es_flash_addr_map[ES_FLASH_ID_USER_DATA].len = sizeof(es_flash_user_data_t);
    return ES_RET_SUCCESS;
}

ES_S32 es_flash_init(ES_VOID)
{
    es_memset(es_es_flash_addr_map, 0x00, sizeof(es_es_flash_addr_map));
    es_memset(&es_flash_data, 0x00, sizeof(es_flash_data));
    if (sizeof(es_flash_data) > ES_FLASH_CFG_LEN) {
        while(1) {
            printk("error, the size of es_flash_data_t is overflow.");
            es_os_msleep(1000);
        };
    }


    if (ES_RET_SUCCESS != es_flash_load_addr_map()) {
        return ES_RET_FAILURE;
    }

    mf_flash.read(ES_FLASH_CFG_ADDR, (uint8_t *)&es_flash_data, sizeof(es_flash_data));
    es_flash_debug("flash magic:%x", es_flash_data.magic);
    if (ES_FLASH_MAGIC_HEAD != es_flash_data.magic) {
        es_memset(&es_flash_data, 0x00, sizeof(es_flash_data));
        es_flash_data.magic = ES_FLASH_MAGIC_HEAD;
#ifdef ES_FLASH_TEST_DATA
        es_flash_load_test_data();
#endif
        es_flash_sync();
        return ES_RET_SUCCESS;
    }

#ifdef ES_FLASH_TEST_DATA
    es_flash_show_test_data();
#endif
    return ES_RET_SUCCESS;
}

ES_S32 es_flash_read(es_flash_id_e id, ES_VOID *data)
{
    ES_FLASH_CHECK_FLASH_ID(id);

    es_memcpy(data, (ES_VOID *)es_es_flash_addr_map[id].addr, es_es_flash_addr_map[id].len);

    return ES_RET_SUCCESS;
}

ES_S32 es_flash_write(es_flash_id_e id, ES_VOID *data)
{
    ES_FLASH_CHECK_FLASH_ID(id);

    es_memcpy((ES_VOID *)es_es_flash_addr_map[id].addr, data, es_es_flash_addr_map[id].len);

    return ES_RET_SUCCESS;
}

// just for nfc.
#if ES_NFC_MODULE_ENABLE
ES_S32 es_flash_get_data_handle(es_flash_id_e id, ES_VOID **data)
{
    ES_FLASH_CHECK_FLASH_ID(id);

    *data = (ES_VOID *)es_es_flash_addr_map[id].addr;

    return ES_RET_SUCCESS;
}
#endif

ES_S32 es_flash_sync(ES_VOID)
{
    sync_flag = 1;
    return ES_RET_SUCCESS;
}

ES_U32 es_flash_run(ES_VOID)
{
    if (0 == sync_flag) {
        return ES_RET_SUCCESS;
    }

    es_flash_debug("try write flash.");
    if (MF_ERR_NONE != mf_flash.write(ES_FLASH_CFG_ADDR, (uint8_t *)&es_flash_data, sizeof(es_flash_data))) {
        return ES_RET_FAILURE;
    }
    sync_flag = 0;

    return ES_RET_SUCCESS;
}

#endif
