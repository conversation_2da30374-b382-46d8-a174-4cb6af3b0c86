#ifndef _ES_SPEC_CAR_H_
#define _ES_SPEC_CAR_H_

#ifdef __cplusplus 
extern "C" { 
#endif

#include "es_types.h"

ES_S32 es_spec_car_init(ES_VOID);

ES_VOID es_spec_car_run(ES_VOID);

ES_BOOL es_spec_car_is_lock(ES_VOID);

ES_U32 es_spec_car_get_expire_time(ES_VOID);

ES_BOOL es_spec_car_is_emergency(ES_VOID);

ES_U32 es_spec_car_set_driver_pass(ES_VOID);

ES_BOOL es_spec_car_get_driver_pass(ES_VOID);

ES_BOOL es_spec_car_is_face_flow(ES_VOID);

ES_BOOL es_spec_car_get_safetybelt_detect(ES_VOID);

ES_S32 es_spec_car_get_limit_speed(ES_VOID);

ES_S32 es_spec_car_update_gps_cache_str(const ES_CHAR* gps_str);

ES_S32 es_spec_car_get_gps_from_cache(ES_VOID *gps);

#ifdef __cplusplus 
}
#endif
#endif