/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_utils.c
** bef: implement the interface for utils
** auth: lines<<EMAIL>>
** create on 2019.05.08 
*/

#include "es_inc.h"

ES_BYTE es_utils_char2hex(ES_BYTE c)
{
    if ('0' <= c && c <= '9') {
        return c - '0';
    } else if ('A' <= c && c <= 'F') {
        return c - 'A' + 10;
    } else if ('a' <= c && c <= 'f') {
        return c - 'a' + 10;
    }
		
    return (ES_BYTE)-1;
}

ES_CHAR es_utils_hex2char(ES_BYTE h)
{
    if (h < 10) {
        return '0' + h;
    } else if (h < 16) {
        return 'A' + (h-10);
    } else {
        return 0;
    }
}

ES_S32 es_utils_hex_str_to_bytes(const ES_CHAR *string, ES_BYTE *hex_bytes, ES_U32 hex_len)
{
    ES_BYTE tmp = 0;
    ES_S32 bytes_len = 0;
    ES_BOOL is_byte = ES_TRUE;
    const ES_CHAR *p = string;

    if (ES_NULL == p) {
        return ES_RET_FAILURE;
    }

    while (*p && bytes_len < hex_len) {
        tmp = es_utils_char2hex((ES_BYTE)*p);
        if ((ES_BYTE) -1 == tmp) {
            return ES_RET_FAILURE;
        }

        p++;
        is_byte = !is_byte;
        if (is_byte) {
            hex_bytes[bytes_len] += (tmp & 0x0F);
            bytes_len++;
        } else {
            hex_bytes[bytes_len] = (tmp << 4) & 0xF0;
        }
    }

    return bytes_len;
}

ES_S32 es_utils_byte_to_hex_str(ES_CHAR *string, const ES_BYTE *hex_bytes, ES_U32 hex_len)
{
    ES_U32 bytes_len;
    ES_S32 str_len;

    if (ES_NULL == string || ES_NULL == hex_bytes) {
        return ES_RET_FAILURE;
    }

    bytes_len = 0;
    str_len = 0;
    while (bytes_len < hex_len) {
        string[str_len++] = es_utils_hex2char((hex_bytes[bytes_len]>>4) & 0x0F);
        string[str_len++] = es_utils_hex2char(hex_bytes[bytes_len] & 0x0F);
        bytes_len++;
    }
    string[str_len] = 0;

    return (ES_S32)str_len;
}

ES_BOOL es_utils_check_is_odd(const ES_BYTE byte)
{
    ES_U32 i;
    ES_U32 count = 0;
    ES_BYTE b = byte;

    for (i = 0; i < 8; i++) {
        if (b & 0x01) {
            count++;
        }
        b = (b>>1);
    }

    return ((b%2) == 0);
}

ES_BOOL es_utils_check_timeout(ES_U32 last_time, ES_U32 now_time, ES_U32 timeout)
{
    if (0 == last_time) {
        return ES_TRUE;
    } else if (last_time <= now_time) {
        if (now_time - last_time >= timeout) {
            return ES_TRUE;
        }
    } else {
        // overflow.
        if (now_time >= timeout) {
            return ES_TRUE;
        }
    }

    return ES_FALSE;
}

ES_BOOL es_utils_array_is_all(const ES_BYTE *array, ES_U32 array_len, ES_BYTE val)
{
    ES_U32 i = 0;

    for (i = 0; i < array_len; i++) {
        if (val != array[i]) {
            return ES_FALSE;
        }
    }

    return ES_TRUE;
}