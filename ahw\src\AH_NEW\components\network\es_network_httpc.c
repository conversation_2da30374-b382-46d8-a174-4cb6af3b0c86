/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_network_httpc.c
** bef: define the interface for http client.
** auth: lines<<EMAIL>>
** create on 2022.06.11 
*/

#include "es_inc.h"

#if ES_HTTP_ENABLE

// #define NETWORK_HTTPC_DEBUG
#ifdef NETWORK_HTTPC_DEBUG
#define network_httpc_debug es_log_info
#define network_httpc_error es_log_error
#else
#define network_httpc_debug(...)
#define network_httpc_error(...)
#endif

static ES_VOID http_get_file_data_callback(es_http_event_e event, const ES_BYTE *data, ES_U32 data_len)
{
    static ES_U16 file_offset = 0;
    es_ota_file_info_t ota_file_info;
    network_httpc_debug("event:%d, data_len:%d", event, data_len);
    if (HTTP_EVENT_BODY_DATA == event) {
        es_ota_write(file_offset, data, data_len);
        file_offset += data_len;
    } else if (HTTP_EVENT_CONTENT_LEN == event) {
        file_offset = 0;
        ota_file_info.file_size = data_len;
        es_ota_start((const es_ota_file_info_t *)&ota_file_info);
    } else {
        es_ota_finish(data_len);
    }
}

static ES_VOID http_post_data_callback(es_http_event_e event, const ES_BYTE *data, ES_U32 data_len)
{
#if ES_MODEL_ACTIVE_ENABLE
    network_httpc_debug("event:%d, data_len:%d", event, data_len);
    if (HTTP_EVENT_BODY_DATA == event) {
        es_serv_proto_model_active_decode(data, data_len);
    }
#endif
}

ES_S32 es_network_httpc_init(ES_VOID)
{
    es_hal_lte_register_http_cb(http_get_file_data_callback);
    return ES_RET_SUCCESS;
}

ES_S32 es_network_httpc_upload_pic(const ES_CHAR *url, const ES_BYTE *data, ES_U32 data_len,
   const ES_VOID *headers, ES_U32 header_count)
{
    es_hal_lte_register_http_cb(ES_NULL);
    return es_hal_lte_http_post_jpg(url, (const es_http_header_t*)headers, header_count, data, data_len);
}

ES_S32 es_network_httpc_get_file(const ES_CHAR *url)
{
    es_hal_lte_register_http_cb(http_get_file_data_callback);
    return es_hal_lte_http_get(url);
}

ES_S32 es_network_httpc_get_model_active(const ES_CHAR *url, const ES_BYTE *data, ES_U32 data_len)
{
    es_hal_lte_register_http_cb(http_post_data_callback);
    return es_hal_lte_http_post(url, data, data_len);
}

#endif