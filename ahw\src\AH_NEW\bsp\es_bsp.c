#include "es_inc.h"

#define DBG_UART_NUM                (UART_DEVICE_3)
#define PLL0_OUTPUT_FREQ            (1000000000UL)
#define PLL1_OUTPUT_FREQ            (400000000UL)
#define DBG_BAUDRATE                (1500000)

static int es_bsp_init_sys(void)
{
    /* Set CPU and dvp clk */
    sysctl_pll_set_freq(SYSCTL_PLL0, PLL0_OUTPUT_FREQ);
    sysctl_pll_set_freq(SYSCTL_PLL1, PLL1_OUTPUT_FREQ);
    sysctl_clock_enable(SYSCTL_CLOCK_AI);

    /* Set dvp and spi pin to 1.8V */
    sysctl_set_power_mode(SYSCTL_POWER_BANK6, SYSCTL_POWER_V18);
    sysctl_set_power_mode(SYSCTL_POWER_BANK7, SYSCTL_POWER_V18);

    plic_init();

    // default use ISP uart to print early debug information
    fpioa_set_function(5, FUNC_UART1_TX + DBG_UART_NUM * 2);
    fpioa_set_function(4, FUNC_UART1_RX + DBG_UART_NUM * 2);
	uart_config(DBG_UART_NUM, DBG_BAUDRATE, 8, UART_STOP_1, UART_PARITY_NONE);

    // int flash
    // mf_flash.init(3, 0, ********);

    rtc_init();
    // rtc_timer_set(2020, 2, 2, 20, 02, 00);

    //enable global interrupt 
    sysctl_enable_irq();

    // for lcd blacklight and led flash
    pwm_init(PWM_DEVICE_0);

    return ES_RET_SUCCESS;
}

static int es_bsp_init_wdg(void)
{
    es_wdg_init(1, 5);
    es_wdg_init(0, 5);

    return ES_RET_SUCCESS;
}


ES_S32 es_bsp_init(void)
{
    if (ES_RET_SUCCESS != es_bsp_init_sys()) {
        return ES_RET_FAILURE;
    }

    if (ES_RET_SUCCESS != es_bsp_init_wdg()) {
        return ES_RET_FAILURE;
    }

    return ES_RET_SUCCESS;
}