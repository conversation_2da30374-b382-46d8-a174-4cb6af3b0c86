#ifndef _MF_FLASH_H
#define _MF_FLASH_H

#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include "mf_constants.h"
#include <bsp.h>
#include "atomic.h"
#include "platform.h"
#include "syscalls.h"
#include "utils.h"
#include "dmac.h"
#include "dvp.h"
#include "fpioa.h"
#include "gpiohs.h"
#include "i2c.h"
#include "plic.h"
#include "printf.h"
#include "sha256.h"
#include "sysctl.h"

/*****************************************************************************/
// Enums & Macro
/*****************************************************************************/


/*****************************************************************************/
// Types
/*****************************************************************************/
typedef struct
{
	//Private
	//Public
	uint16_t init_flag;
	
	//Const Public
	mf_err_t (*init)(uint8_t spi_index, uint8_t spi_ss, uint32_t rate);
	mf_err_t (*read)(uint32_t addr, uint8_t *data_buf, uint32_t length);		//fast quad, little-end
	mf_err_t (*read_slow)(uint32_t addr, uint8_t *data_buf, uint32_t length);	//slow one line
	mf_err_t (*write)(uint32_t addr, uint8_t *data_buf, uint32_t length);
	void (*erase)(uint32_t addr);
} mf_flash_t;




/*****************************************************************************/
// Functions
/*****************************************************************************/


/*****************************************************************************/
// Vars
/*****************************************************************************/
extern mf_flash_t mf_flash;

#endif