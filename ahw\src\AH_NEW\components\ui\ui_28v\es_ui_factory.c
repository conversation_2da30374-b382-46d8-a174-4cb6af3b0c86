#include "es_inc.h"

#if (ES_UI_TYPE == ES_UI_TYPE_K28V_240_320)
#if (0 == ES_TUYA_MODULE_ENABLE)

// #define ES_UI_FACTORY_DEBUG
#ifdef ES_UI_FACTORY_DEBUG
#define es_ui_factory_debug es_log_info
#define es_ui_factory_error es_log_error
#else
#define es_ui_factory_debug(...)
#define es_ui_factory_error(...)
#endif

#define ES_UI_QRCODE_SIZE           (120)

static lv_obj_t *lv_factory_bg = ES_NULL;
static lv_obj_t *lv_qrcode = ES_NULL;
static ES_U16 qrcode_x = 0;
static ES_U16 qrcode_y = 0;
static ES_CHAR *factory_note = "Please scan the QR code to configure\nand manage the factory mode";
static ES_CHAR display_mac_info[24] = {0};
static ES_CHAR qrcode_data[64] = {0};

ES_S32 es_ui_factory_qrcode_open(ES_VOID)
{
    if (ES_NULL == lv_qrcode) {
        lv_qrcode = lv_qrcode_create(lv_factory_bg, ES_UI_QRCODE_SIZE, lv_color_hex3(0x33f), lv_color_hex3(0xeef));
    }

    lv_obj_align(lv_qrcode, LV_ALIGN_TOP_LEFT, qrcode_x, qrcode_y);
    lv_qrcode_update(lv_qrcode, qrcode_data, strlen(qrcode_data));

    return ES_RET_SUCCESS;
}

ES_S32 es_ui_factory_qrcode_close(ES_VOID)
{
    if (ES_NULL != lv_qrcode) {
        lv_qrcode_delete(lv_qrcode);
        lv_qrcode = ES_NULL;
    }
    return ES_RET_SUCCESS;
}


ES_S32 es_ui_factory_create_widgets(ES_VOID)
{
    lv_obj_t *obj;
    qrcode_x = (ES_UI_WIDTH - ES_UI_QRCODE_SIZE)>>1;
    qrcode_y = (ES_UI_HEIGHT - ES_UI_QRCODE_SIZE)>>1;

    lv_factory_bg = lv_obj_create(lv_scr_act());
    lv_obj_remove_style_all(lv_factory_bg);
    lv_obj_set_size(lv_factory_bg, ES_UI_WIDTH, ES_UI_HEIGHT);
    lv_obj_align(lv_factory_bg, LV_ALIGN_TOP_LEFT, 0, 0);

    obj = lv_label_create(lv_factory_bg);
    lv_obj_align(obj, LV_ALIGN_TOP_MID, 0, qrcode_y+ES_UI_QRCODE_SIZE);
    lv_label_set_text_static(obj, display_mac_info);

    obj = lv_label_create(lv_factory_bg);
    lv_obj_align(obj, LV_ALIGN_BOTTOM_MID, 0, 0);
    lv_label_set_text_static(obj, factory_note);

    return ES_RET_SUCCESS;
}

static ES_U32 es_ui_factory_init_data(ES_VOID)
{
    ES_BYTE wireless_mac[6] = {0x00, 0x00, 0x00, 0x00, 0x00, 0x00};

#if ES_BLE_MODULE_ENABLE
    ES_BYTE ble_mac[6] = {0};
#endif

#if ES_BLE_MODULE_ENABLE
    if (ES_RET_SUCCESS != es_ble_read_mac(ble_mac)) {
    }
    es_snprintf(display_mac_info, sizeof(display_mac_info), "%02X:%02x:%02x:%02x:%02x:%02x\n%02X%02X",
                wireless_mac[0], wireless_mac[1], wireless_mac[2], wireless_mac[3], wireless_mac[4], wireless_mac[5],
                ble_mac[4], ble_mac[5]);

    es_snprintf(qrcode_data, sizeof(qrcode_data), "{\"mac\":\"%02X:%02x:%02x:%02x:%02x:%02x\",\"ble\":\"AIFACE-%02X%02X\"}",
                wireless_mac[0], wireless_mac[1], wireless_mac[2], wireless_mac[3], wireless_mac[4], wireless_mac[5],
                ble_mac[4], ble_mac[5]);
#else
    es_snprintf(display_mac_info, sizeof(display_mac_info), "%02X:%02x:%02x:%02x:%02x:%02x",
                wireless_mac[0], wireless_mac[1], wireless_mac[2], wireless_mac[3], wireless_mac[4], wireless_mac[5]);
    es_snprintf(qrcode_data, sizeof(qrcode_data), "{\"mac\":\"%02X:%02x:%02x:%02x:%02x:%02x\"}",
                wireless_mac[0], wireless_mac[1], wireless_mac[2], wireless_mac[3], wireless_mac[4], wireless_mac[5]);
#endif

    return ES_RET_SUCCESS;
}

ES_S32 es_ui_factory_init(ES_VOID)
{
    if (ES_RET_SUCCESS != es_ui_factory_init_data()) {
        return ES_RET_FAILURE;
    }
    es_ui_factory_create_widgets();
    es_ui_factory_show(ES_FALSE);
    return ES_RET_SUCCESS;
}

ES_S32 es_ui_factory_show(ES_BOOL show)
{
    if (show) {
        es_ui_factory_qrcode_open();
        lv_obj_clear_flag(lv_factory_bg, LV_OBJ_FLAG_HIDDEN);
    } else {
        lv_obj_add_flag(lv_factory_bg, LV_OBJ_FLAG_HIDDEN);
        es_ui_factory_qrcode_close();
    }

    return ES_RET_SUCCESS;
}

#endif
#endif
