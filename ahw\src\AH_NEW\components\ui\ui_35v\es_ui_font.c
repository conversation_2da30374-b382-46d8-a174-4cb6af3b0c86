#include "es_inc.h"

#if (ES_UI_TYPE == ES_UI_TYPE_K35V_320_480)

// #define ES_UI_FONT_DEBUG
#ifdef ES_UI_FONT_DEBUG
#define es_ui_font_debug es_log_info
#define es_ui_font_error es_log_error
#else
#define es_ui_font_debug(...)
#define es_ui_font_error(...)
#endif

static lv_style_t font_common;
static lv_style_t font_time;
static lv_style_t font_date;

ES_S32 es_ui_font_init(ES_VOID)
{
    #define LV_STYLE_FONT_INIT(style, font) do { \
        lv_style_init(&(style)); \
	    lv_style_set_text_font(&(style), &(font)); \
    } while (0)

    // LV_STYLE_FONT_INIT(font_style_ui, gb2312_bpp1_ui_12);
    // LV_STYLE_FONT_INIT(font_style_time, gb2312_bpp1_time_28);
    // LV_STYLE_FONT_INIT(font_style_names, gb2312_bpp1_names_18);

    LV_STYLE_FONT_INIT(font_common, lv_font_gb2312_18);
    LV_STYLE_FONT_INIT(font_time, lv_font_gb2312_28);
    // LV_STYLE_FONT_INIT(font_style_names, lv_font_montserrat_18);


    return ES_RET_SUCCESS;
}

ES_VOID *es_ui_font_get_common(ES_VOID)
{
    return (ES_VOID*)&font_common;
}

ES_VOID *es_ui_font_get_time(ES_VOID)
{
    return (ES_VOID*)&font_time;
}

ES_VOID *es_ui_font_get_date(ES_VOID)
{
    return (ES_VOID*)&font_date;
}

#endif

