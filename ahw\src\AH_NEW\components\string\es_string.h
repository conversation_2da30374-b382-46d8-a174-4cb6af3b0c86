/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_string.h
** bef: define the interface for string. 
** auth: lines<<EMAIL>>
** create on 2020.11.25
*/

#ifndef _ES_STRING_H_
#define _ES_STRING_H_

#ifdef __cplusplus 
extern "C" { 
#endif

#include "es_types.h"

ES_CHAR *es_string_find_char(const ES_CHAR *p, ES_CHAR c);

ES_BYTE es_string_char2hex(ES_CHAR c);

ES_U32 es_string_str2hex(const ES_CHAR *str, ES_BYTE *hex, ES_U32 hex_len);

ES_CHAR *es_string_get_next_line(const ES_CHAR *p);

ES_BOOL es_string_start_with(const ES_CHAR *string, const ES_CHAR *start);

#ifdef __cplusplus 
}
#endif

#endif