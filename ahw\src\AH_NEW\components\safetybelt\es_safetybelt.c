/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_safetybelt.c
** bef: define the interface for safetybelt.
** auth: lines<<EMAIL>>
** create on 2022.01.08 
*/

#include "es_inc.h"

// #define ES_SAFETBELT_DEBUG
#ifdef ES_SAFETBELT_DEBUG
#define es_safetybelt_debug es_log_info
#define es_safetybelt_error es_log_error
#else
#define es_safetybelt_debug(...)
#define es_safetybelt_error(...)
#endif

static ES_U32 upload_event_count = 0;

ES_S32 es_safetybelt_init(ES_VOID)
{
    fpioa_set_function(ES_SAFETYBELT_PIN, FUNC_GPIOHS0 + ES_SAFETYBELT_HS_NUM);
    gpiohs_set_drive_mode(ES_SAFETYBELT_HS_NUM, GPIO_DM_INPUT);
    return ES_RET_SUCCESS;
}

ES_BOOL es_safetybelt_check(ES_VOID)
{
#define POPEN_READ_STR_LEN          (128)
#define SAFETY_CHECK_TIME_SEC       (15)
    // popen
    ES_BOOL ret = ES_FALSE;
    static ES_U32 last_time = 0;
    ES_U32 now_time = 0;
    ES_U8 io_val = 0;


    if (!es_spec_car_get_driver_pass()) {
        ret = ES_FALSE;
        goto FUNC_END;
    }

    if(!es_spec_car_get_safetybelt_detect()) {
        ret = ES_FALSE;
        goto FUNC_END;
    }

    now_time = es_time_get_timestamp();
    if (0 == last_time) {
        ret = ES_FALSE;
        last_time = now_time;
        goto FUNC_END;
    }

    if ((now_time - last_time) < SAFETY_CHECK_TIME_SEC) {
        ret = ES_FALSE;
        goto FUNC_END;
    }

    io_val = gpiohs_get_pin(ES_SAFETYBELT_HS_NUM);
    // sleep 50ms
    es_os_msleep(50);
    if (io_val != gpiohs_get_pin(ES_SAFETYBELT_HS_NUM)) {
        // sleep 10ms
        es_os_msleep(10);
        io_val = gpiohs_get_pin(ES_SAFETYBELT_HS_NUM);
    }

    if (ES_SAFETYBELT_INSERT_VAL == io_val) {
        upload_event_count = 0;
        last_time = now_time;
    }

    if (ES_SAFETYBELT_INSERT_VAL != io_val) {
        if (upload_event_count < 3) {
            upload_event_count++;
            last_time = now_time;
            ret = ES_FALSE;
            es_voice_play(ES_VOICE_20_SAFETY_BELT);
            es_network_mqtt_upload_ai_event(AI_EVENT_NOT_SEAT_BELT, 1, ES_NULL, ES_NULL);
            goto FUNC_END;
        }
    }
    ret = ES_FALSE;

FUNC_END:
    // if (fstream) {
    //     pclose(fstream);
    // }

    return ret;
}