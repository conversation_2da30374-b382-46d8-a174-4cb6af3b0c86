#include "es_inc.h"

ES_U16 es_crc16(const ES_BYTE *data, ES_U32 len)
{
    return es_crc16_xmodem(data, len);
}

ES_U16 es_crc16_xmodem(const ES_BYTE *data, ES_U32 len)
{
    ES_U8 c, treat, bcrc;
    ES_U16 wcrc = 0;

    for(ES_U32 i = 0; i < len; i++)
    {
        c = data[i];
        for(ES_U8 j = 0; j < 8; j++)
        {
            treat = c & 0x80;
            c <<= 1;
            bcrc = (wcrc >> 8) & 0x80;
            wcrc <<= 1;
            if(treat != bcrc)
                wcrc ^= 0x1021;
        }
    }
    return wcrc;
}

ES_U8 es_crc8(const ES_BYTE *data, ES_U32 len)
{
    ES_U8 crc = 0;

    while(len--)
    {
        crc ^= *data++;
        for(ES_U8 i = 0; i < 8; i++)
        {
            if(crc & 0x01)
                crc = (crc >> 1) ^ 0x8c;
            else
                crc >>= 1;
        }
    }
    return crc;
}


ES_U32 es_crc32(const ES_BYTE *data, ES_U32 len)
{
    ES_U32 wCRCin = 0xFFFFFFFF;
    ES_U32 wCPoly = 0x04C11DAA;
    ES_U32 wChar = 0;
    while (len--)
    {
        wChar = *(data++);
        wCRCin ^= (wChar << 24);
        for (ES_U8 i = 0; i < 8; i++)
        {
            if (wCRCin & 0x80000000)
                wCRCin = (wCRCin << 1) ^ wCPoly;
            else
                wCRCin = wCRCin << 1;
        }
    }
    return (wCRCin ^ 0xFFFFFFFF);
}
