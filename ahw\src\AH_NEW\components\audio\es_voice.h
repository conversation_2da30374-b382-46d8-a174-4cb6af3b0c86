/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_voice.h
** bef: define the interface voice. 
** auth: lines<<EMAIL>>
** create on 2022.12.13
*/

#ifndef _ES_VOICE_H_
#define _ES_VOICE_H_

#ifdef __cplusplus 
extern "C" { 
#endif

#include "es_types.h"

typedef enum {
    ES_VOICE_01_FACE_PASS,
    ES_VOICE_02_CAR_BOOT,
    ES_VOICE_03_CAR_YEAR_EXPIRE_SOON,
    ES_VOICE_04_LOOK_AND_CHECK,
    ES_VOICE_05_NOT_AUTH_AND_LEAVE,
    ES_VOICE_06_AUTH_PASS,
    ES_VOICE_07_UPDATE_DRIVER_INFO_OK,
    ES_VOICE_08_SET_INFO_OK,
    ES_VOICE_09_RESET_INFO_OK,
    ES_VOICE_10_UNLOCK_CAR_OK,
    ES_VOICE_11_LOCK_CAR_OK,
    ES_VOICE_12_DRIVER_LICENSE_EXPIRE,
    ES_VOICE_13_CAR_EXPIRE_AFTER_MONTH,
    ES_VOICE_14_EXPIRE_AND_LOCKED,
    ES_VOICE_15_EXCEPTION_AND_CONTACT,
    ES_VOICE_16_CAR_LOCKED,
    ES_VOICE_17_DO_NOT_USE_PHONE,
    ES_VOICE_18_NO_SMOKING,
    ES_VOICE_19_FATIGUE_DRIVING,
    ES_VOICE_20_SAFETY_BELT,
    ES_VOICE_21_OVER_SPEED,
    ES_VOICE_22_POSTURE_DETECTION,
} es_voice_type_t;

ES_S32 es_voice_init(ES_VOID);

ES_S32 es_voice_play(es_voice_type_t type);

#ifdef __cplusplus 
}
#endif

#endif