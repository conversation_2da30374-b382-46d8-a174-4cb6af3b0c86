#ifndef _MF_CONSTANTS_H
#define _MF_CONSTANTS_H

/******************************************************************************/
//Macro
/******************************************************************************/
#define MF_CAM_W	320
#define MF_CAM_H	240

#define CATCH_ERR(err) {if(err!=MF_ERR_NONE) { \
	printk("Error Hang @ L%d, code: %d\r\n", __LINE__, err); \
	while(1){}; \
	}}

#define ARRAY_LEN(arr) (sizeof(arr)/sizeof(arr[0]))

/******************************************************************************/
//Enums 
/******************************************************************************/
typedef enum 
{
	MF_ERR_NONE=0,
	MF_ERR_TODO,
	MF_ERR_LCD_TYPE,
	MF_ERR_CAM_TYPE,
	MF_ERR_CAM_NOMEM,
	MF_ERR_NOTINIT,
	MF_ERR_BOARD_UNSUPPORT,
	MF_ERR_UI_NOMEM,
	MF_ERR_UI_SUB,
	MF_ERR_MDL_NOMEM,
	MF_ERR_MDL_INIT1,
	MF_ERR_MDL_INIT2,
	MF_ERR_MDL,
	MF_ERR_CFG_INIT1,
	MF_ERR_CFG_INIT2,
	MF_ERR_CFG_SAVE,
	MF_ERR_UARTP_PROTO,
	MF_ERR_UARTP_BUF,
	MF_ERR_LAYOUT_TYPE,
	MF_ERR_FACEDB_TYPE,
	MF_ERR_FACEDB_NOMEM,
	MF_ERR_FACEDB_ID,
	MF_ERR_FACEDB_DEL,
	MF_ERR_FACEDB_HASH,
	MF_ERR_FLOW_TYPE,
	MF_ERR_QR_TYPE,
	MF_ERR_QR_CB,
	MF_ERR_LIST_FULL,
	MF_ERR_CB_NULL,
	MF_ERR_ASR_NOMEM,
	MF_ERR_ASR_MODEL,
	MF_ERR_UARTP_NOMEM,
	MF_ERR_RES_LIST_CHK_FAIL,
	MF_ERR_RES_TOO_MUCH,
	MF_ERR_RES_NOT_FOUND,
	MF_ERR_NO_MEM,
	MF_ERR_FLASH_WRITE_VERIFY_FAIL,
	MF_ERR_NET_TYPE,
	MF_RTC_VOL_LOW,
	MF_RTC_DATA_ERR,
	MF_ERR_LCD_PARA_NULL,
	MF_ERR_MODEL_ACT_ERR_KEY,
	//待更新
	MF_ERR_MAX,
}mf_err_t;

#endif
