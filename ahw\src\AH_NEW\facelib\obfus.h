#ifndef __OBFUS_H
#define __OBFUS_H

#define  enc_kmodel                                     ZY8zNPpbG6nhMovc
#define  lib_compress_feature                           UpMjZIa0svmxfVOg
#define  face_obj_info_roate_right_90                   pL7jPJOWKIr3BvMZ
#define  myotp_init_aaa                                 eGbzDos1u7OWtNJR
#define  myotp_read_data                                Gz5wAktJmiEQofUy
#define  _mf_model_check_pose                           T0uIsgkSqHbXDKCM
#define  blur_detect_run                                MlNqkPpfe3mvDo1Z
#define  myotp_func_reg_disable_set                     oZut2mOG1DB5KRa8
#define  mf_kpu_run                                     bbjkc7SVKamIGn1s

#if 0

#define  mf_spi_send_data_standard_dma                  KbIN5PUoiDar23l4
#define  face_obj_info_print                            TpEsfS48Q6NLhuV7
#define  _mf_facedb_flash_update_uid                    JC0YBjIpn2ED3v7T
#define  myotp_data_block_protect_set                   HSbtNoigDBzA49TI
#define  _flow_v2v_loop                                 tyls4ta93JdPwfR0
#define  kpu_lib_task_init                              b5xmQ2zMcVZTN9iR
#define  enc_kmodel                                     ZY8zNPpbG6nhMovc
#define  _mf_model_run_fr                               JtUMbznkmZJoBL91
#define  lib_compress_feature                           UpMjZIa0svmxfVOg
#define  myotp_data_block_protect_get                   RbkTYMa7nmEZvFPO
#define  kpu_lib_model_load_from_buffer                 HfN8jnsALc9WSxHu
#define  _mf_uartp_json_rec_cb                          xqehxR8tKzwpgbS2
#define  protocol_send_cal_pic_result                   OTs6GfjkNl8Uni3A
#define  otp_data_block_protect_refresh                 oeX76Wu8bYBFJANG
#define  activate_array                                 rmExoXWrPyaTeb9H
#define  key_point_last_handle                          t26JbPK7aDe40yIH
#define  mf_spi_slave_config                            vHZUko4KtxP5SMYq
#define  ai_done                                        rwK3lbzGWrtm04iV
#define  _mf_model_save_cfg                             df8cWpobYAFglixT
#define  _mf_uartp_json_recv_cb                         CYWZgCzAdpFTGcK4
#define  _mf_facedb_flash_num                           CTF9azxCOJ6jt3nc
#define  mf_spi_set_tmod                                OlUdI2hALnXc68Fg
#define  mf_spi_set_clk_rate                            VQC1UOJ2SKDmgaVH
#define  mf_spi_send_data_standard                      gPEIVcDvneNbKJU6
#define  _mf_model_ftr_cal                              eb8vSN2yUFYCpluT
#define  mf_spi_send_data_multiple_dma                  uNH0gltFwy9BIPmq
#define  image_deinit                                   WhnrQVOWJ74UlqHC
#define  myotp_func_reg_disable_set                     oZut2mOG1DB5KRa8
#define  kpu_lock_buf                                   ywkWiA08MUoVgs6F
#define  _mf_facedb_flash_del_uid                       vzquj0w7fdbnvcJG
#define  face_lib_version                               nnarBebSGfYoW7tE
#define  mf_kpu_data_ready                              HAVwTeuSvGK5Dc3y
#define  mf_kpu_release_output_buf                      c2PE6CtKYuMdnAl1
#define  mf_spi_receive_data_normal_dma                 uS4qCJG7hLai5r29
#define  _mf_uartp_json_init_done                       szdo9F8VaI6ulwDN
#define  cam_dualbuf_first_run                          PB461EhqTzkjDGyN
#define  mf_kpu_start                                   Gb01BGUeODo2JXwp
#define  mf_kpu_conv2d_output                           F1o0QEdOvhWa6Lx2
#define  region_layer_deinit                            hRob5IDkrLceSBgC
#define  _mf_model_cfg_live                             Df7NU2KQoyasgkPG
#define  _mf_uartp_json_getchar                         moM3xw9SQGfO2KPT
#define  otp_wrong_address_get                          OQK9oAd03mMXRFL2
#define  mf_kpu_load_kmodel                             OJZSlxnv8pLjrowe
#define  kpu_lib_global_average_pool                    idsDVYapGJ27jKZu
#define  _mf_facedb_flash_del_vid                       XUSBxeYlPCuF2nJK
#define  face_obj_info_roate_left_90                    VBYZ4UpCr8WLn16q
#define  mf_spi_slave_irq                               H2v6ia8qLxZj5GUT
#define  gc0328_dual_set_vflip                          BxMufIVX9Z7PA235
#define  mf_kpu_matmul_end                              JD79fcipsZeOMNrX
#define  _mf_model_load_cfg                             gPeKUO3JZrFLT4o1
#define  mf_kpu_input_with_padding                      Y2weiQqLxTgnf87m
#define  mf_spi_dup_send_receive_data_dma               w3X8xckKOUhEuSLz
#define  mdl_print_ppm                                  Qo4ZmelAU5PTME8v
#define  mf_spi_send_data_multiple                      JPVQHJok64GNu5ts
#define  image_crop_roate_left_90                       RC1tn3OAmewx4W2E
#define  mf_kpu_lib_model_load_from_buffer              V2ahPREVodJcvg9p
#define  _mf_model_cfg_fe                               kHLEOBQ4T85Nq3Rb
#define  blur_detect_run                                MlNqkPpfe3mvDo1Z
#define  protocol_send_result                           vVTfJzo3ltDq8Fj6
// #define  nms_comparator                                 EEwnVQ4qkvICsMW6
#define  protocol_send_clr_lcd_display_ret              iul9WTOnSReM3r7L
#define  _mf_facedb_flash_iterate_init                  ToEW7zaLsHCAh1OQ
#define  otp_key_output_disable                         MPrfmeFT94GhB1w6
#define  _flow_v2v_init                                 ATnh9usqK8oVxH0O
#define  _mf_model_free_fr                              TQRf3lUxE6agqS2A
#define  protocol_send_add_user_spec_uid_ret            yQawBCjrmztx6deH
#define  mf_kpu_input_dma                               HzTjaiA1FIMLYEG9
#define  mf_kpu_config_input                            IXo60McL7IUsN98m
#define  _mf_facedb_flash_get_uid                       PI0eDYA6ilErQWfU
#define  mf_spi_dma_irq                                 PxKGl5MTpXRnOo4V
#define  image_crop                                     jYIKgnEvmSkZzl0N
#define  myotp_read_data                                Gz5wAktJmiEQofUy
#define  image_crop_roate_right90                       qXzmM167eW2cKfFi
#define  gc0328_dual_modify_reg                         AThZJDiEKyYr57Ik
#define  rgb_lock_buf                                   lHpY7lbagzf61CTm
#define  kpu_unlock_buf                                 I2HIscYa6kiSE0QV
#define  mf_spi_send_data_normal_dma                    nACsVnY4vM17HXto
#define  mf_kpu_run_dma_input_done_push_layers          yrRIqVPExhpvjUe5
#define  _mf_facedb_flash_vid2oft                       Vdiv8f6g1tXBAUqV
#define  _mf_uartp_json_deinit                          ng2ZFRXT0i5vqzwL
#define  protocol_send_add_by_fea_ret                   iD9jUt7qPFM5mnZI
#define  mf_kpu_lib_task_init                           c4m93hPEjCdHxgMs
#define  _mf_facedb_flash_get_vid                       KVy6kGCpNDAJU1K5
#define  mf_spi_slave_cs_irq                            QYRsGtWCaz0AnJUK
#define  gc0328_dual_config                             FnLzqF1lNgfuZ5vT
#define  _mf_model_cfg_fd                               aIQzlL2aFKCGUvh9
#define  _mf_uartp_json_loop                            bFKYSryjOeQAc8u4
#define  _mf_facedb_flash_iterate                       ddLJUPZQNewnmM2H
#define  region_layer_run                               USTWYLxHdvfMF3wl
#define  image_similarity                               NHT5NsZPv3bWlkwg
#define  protocol_query_face_ret                        OI2KAx5ZkpXErldR
#define  _cal_living_item                               tR0WVT9AIxtXZNCQ
#define  protocol_send_obj                              I8oxNwGc6L3eVCMB
#define  mf_kpu_single_task_deinit                      iHpVXSA70c5v4UFT
#define  end_myotp_write_data                           soa047YTxO2LvZej
// #define  cJSON_GetObjectItem_Type                       BqD58YzxoZS0WGcd
#define  mf_spi_handle_data_dma                         v5KXFDQ81r4AnjSY
#define  image_umeyama                                  EDjEbHGcTxUgzr8s
#define  end_myotp_data_block_protect_get               Xt3RY8hVOoc9PglQ
#define  myotp_init_aaa                                 eGbzDos1u7OWtNJR
#define  mf_kpu_run                                     bbjkc7SVKamIGn1s
#define  dvp_irq_gc0328_dual                            jfSoYrBu4zWO6wdQ
#define  mf_spi_fill_data_dma                           dBs3Hn1VbYzCxutk
#define  _flow_v2v_update_allir                         VF7hEWCiJHaRBQUu
#define  open_gc0328_850                                smXgd2cYhn9Bl1KJ
#define  myotp_write_data                               sJMOsh6iYCR81Szn
#define  mf_spi_slave_idle_mode                         LVKnrCRecOmqbg2i
#define  _mf_facedb_flash_init                          lo40StyNvbxEIG3s
#define  _mf_facedb_flash_update_vid                    REwksDi1LSRT6UuY
#define  myotp_key_write                                PeOsvcArJMCX4HVx
#define  _mf_model_ftr_compare                          C81IgNXCckFPV2nz
#define  mf_kpu_lib_matmul_end                          pM3lGT2EPktIrAc5
#define  entry_index                                    luBGleHfZL2p8wrt
#define  mf_spi_send_data_normal                        oYxBjZTKJlMNd0oS
#define  _mf_facedb_flash_uid2oft                       qtVqZXlmOJi7B1Pr
#define  mf_kpu_lib_done                                n1Bgzr2yvl0PDw7C
#define  protocol_prase                                 NkT1NqWm3LhZFU7C
#define  mf_kpu_done                                    k49iuCf5BONvVIej
#define  _mf_facedb_flash_add                           aF7uKwU4ndWZvpBt
#define  _mf_uartp_json_send_cb                         Hzfx8tNYa5K6Bdo9
#define  otp_key_output_enable                          Tj5By2rEmG9ahpD7
#define  mf_spi_receive_data_standard                   TcSJxd4H2otKneaw
#define  mf_kpu_single_task_init                        NlgfDVZsd6Si92NR
#define  mf_kpu_conv2d_output_full_add                  RhAkCDeplU1bjEg5
#define  mf_kpu_lib_task_deinit                         R1G8x0YfbNQXZ7Hi
#define  kpu_lib_matmul_end                             n4fux67cz9ALFCpD
#define  mf_kpu_fully_connected                         Nk1pK8wriPlERG6c
#define  crc_check                                      X37EdszFa0klBC5e
#define  _mf_facedb_flash_get_uid_meta                  ErpHjJV7DoEqhzsl
#define  face_obj_info_roate_right_90                   pL7jPJOWKIr3BvMZ
#define  mf_kpu_lib_data_ready                          XXrkxNKWl9CpM3s0
#define  cam_dualbuf_lock_sync                          sVX94rWIgNqe5xL7
#define  protocol_alloc_header                          H8zIUgZeBqhNuro2
#define  mf_spi_receive_data_multiple                   RLR69lDHMjtTw4fF
#define  mf_ai_step                                     sOkW523r6gcXHVRE
#define  end_myotp_key_write                            To05l3nXrK4xg8wU
#define  mf_kpu_run_all_done                            sneRLxyifQ5OBXhd
#define  mf_kpu_lib_global_average_pool                 om9hyJd0Pq3sA71u
#define  gc0328_dual_set_hmirror                        R3vArDZh6uiq8pMU
#define  mf_kpu_kmodel_done                             jj6LA1c8yfG3WlZd
#define  mf_spi_receive_data_multiple_dma               f3QLscq9HBmUo0vI
#define  kpu_lib_task_deinit                            j4CaqA76MeNmgdBz
#define  region_layer_init                              OQFs7OgGXedat0bp
#define  mf_kpu_send_layer                              ZgnKPXi9uCwaAQBH
#define  protocol_send_face_info                        zFQpE27z56dxgLDh
#define  _mf_model_face_living                          J8IvuXf4URh2c10z
#define  mf_kpu_global_average_pool_float               pINfXWcHwpFYkPlh
#define  rgb_unlock_buf                                 qOPYUFcsr3EH2Ni9
#define  end_myotp_data_block_protect_set               ZrJ27cDsUf8ueRxB
#define  myotp_key_compare                              mzW8P15ctF4dXpIy
#define  gc0328_rd_reg                                  Sjwb7Y24QXURgTu8
#define  _mf_model_check_pose                           T0uIsgkSqHbXDKCM
#define  _mf_model_deinit                               cwrvqsil0Sng7T8d
#define  protocol_send_brd_soft_cfg_ret                 YSQFcG7WTsr8KH3j
#define  _mf_uartp_json_init                            Jt7dI6FUanEQcZO1
#define  mf_kpu_conv2d                                  fUER8eVlLTfFaj03
#define  mf_kpu_model_free                              DEHr5hMcVpxaG24S
#define  mf_kpu_lib_start                               nCYLh4uMJOT5y8P9
#define  mf_kpu_dequantize                              HtyOjMGHJkLfhn58
#define  mf_kpu_run_kmodel                              Pn9YDTJsEL0fCpKF
#define  _mf_uartp_json_stop_recv_jpeg                  b52x0m7a6dwViYUj
#define  mf_kpu_lib_config_input                        YP8ryK25bw9RFCVs
#define  matrix_mul                                     X2VPlCsvb8UoHjJk
#define  _mf_facedb_flash_uid2vid                       i8hlBGvSDMJtosI9
#define  _mf_model_face_detect                          r9nzbx5cdqhKe1VW
#define  mf_flow_choose                                 k9qc3FeNI4a7bzwf
#define  end_myotp_key_compare                          OQ8fxXLNqWkoH13C
#define  mf_kpu_continue                                I6gMJPLlYGqeZs5O
#define  mf_spi_get_frame_size                          yQUyFdbaS4g8k7BV
#define  image_similarity_roate_right90                 METJXcayDmIrNK3P
#define  image_init                                     J5n3Ft8sHirXfVKL
#define  process_less_80_bytes                          kyV4TgkWSJH6pjKX
#define  sipeed_mf_spi_send_data_dma                    ZS7el3fZnHpQJtTY
#define  mf_kpu_get_output                              VwHdgn5qMYS4sfEx
#define  sipeed_spi_send_data_dma                       XXgQsHLjmFiW6ZaM
#define  mf_spi_init                                    sC0Q4oOeNsFAZtPV
#define  end_myotp_init_aaa                             eYKWnJ6txplcwN3o
#define  protocol_del_usr_by_uid_ret                    XJnGEdzuWiDf7sOe
#define  gc0328_wr_reg                                  R9s4I2h1Rk3vXLNn
#define  protocol_regesiter_user_cb                     XDOWLzBMt3cv6Xw0
#define  kpu_lib_start                                  MGYwTkx6mV7chjRQ
#define  mdl_save_ppm                                   v8JQ1bESx6GzXZdW
#define  end_myotp_func_reg_disable_set                 UeZ6qTs0PLgI3KwB
#define  mf_kpu_init                                    N0qkLvZSMwoOuAJ4
#define  _mf_facedb_flash_deinit                        lbCJpHlPkTEdDuQX
#define  mf_kpu_global_average_pool                     h3sROQWaunpTJBSV
#define  protocol_send_set_lcd_display_ret              E3fnIq7cuDPbpli8
#define  protocol_cal_pic_fea                           sDiT4hCN8WGmfx7o
#define  _mf_facedb_flash_del_oft                       Zn4UrWG5JRXmZIlV
#define  _mf_facedb_flash_del_all                       tSmxFsy7IqtEX1b2
#define  _flow_v2v_deinit                               WBQinA5YhGaDI8R2
#define  open_gc0328_650                                fQnWKlphoygXsjdL
#define  end_myotp_read_data                            fN5kfFXjcEJiU82r
#define  mf_kpu_add                                     pJKGWpUHMzAjQ4br
#define  mf_uartp_choose                                gfGjeH7l146u9I5w
#define  _mf_facedb_flash_get_oft                       wz2ZJORWs3capx9w
#define  mf_kpu_carry_shift                             azP7Xruk3ldmHDhV
#define  mf_spi_receive_data_standard_dma               LPar71Kwt8FpSnON
#define  ai_done_0                                      hK9yTjO3u1LBXHxf
#define  image_similarity_roate_left90                  sQz5HMrBaXjPKkcI
#define  _mf_facedb_flash_update_oft                    D2v8HUKAfY6Wnjbm
#define  _mf_model_face_align                           c3WbC8gqF9lGsSHa
#define  otp_status_get                                 BK894mkoNMPxL1BA
#define  _mf_uartp_json_send                            nnuao41yr5lVcJRU
#define  mf_kpu_upload_core                             ztpOnFMGAEzDe3Wx
#define  mf_spi_get_frame_format                        HyOSCg8j9wP3vmoe
#define  mf_spi_init_non_standard                       aZmTG06fyLneV8JU
#define  image_resize                                   RT78QIzNCesg9VWa
#define  _mf_model_init                                 R8lk1itGBKPRzvOw
#define  mf_kpu_get_output_buf                          Cvs7yBcAkN9nUOIF
#define  _flow_db_search                                zcGQvN39YJTVw2rS
#define  protocol_send_brd_hard_cfg_ret                 X04uos6NMzDphqxa
#define  _flow_v2v_db_hash                              gtxUILNfAKs4Repi
#define  mf_kpu_model_load_from_buffer                  E5RnVysGMHebYt9B
#define  w25qxx_receive_data                            E5RnVysGMHebYt8C
#define  _mf_flash_read_slow                            D5RnVysGMHebYt9D
#define  w25qxx_is_busy                                 E5RnVysGMHebYt9E
#define  _w25qxx_read_data                              E5RnVysGMHebYt9F
#define  _mf_flash_read                                 E5RnVysGMHebYt90
#define  w25qxx_send_data                               E5RnVysGMHebYt91
#define  w25qxx_write_enable                            E5RnVysGMHebYt92
#define  w25qxx_sector_program                          E5RnVysGMHebYt93
#define  w25qxx_read_id                                 E5RnVysGMHebYt94
#define  _mf_flash_init                                 E5RnVysGMHebYt95
#define  w25qxx_sector_erase                            E5RnVysGMHebYt96
#define  w25qxx_read_data                               E5RnVysGMHebYt97
#define  w25qxx_write_data                              E5RnVysGMHebYt98
#define  _mf_flash_write_verify                         E5RnVysGMHebYt99
#define  internal_w25qxx_read_data                      E5RnVysGMHebYt9A
#define bincmd_baud                                     ooM1Vaaphiegi1ji
#define bincmd_confirm                                  ca2Aihe6SohbeiCh
#define bincmd_fr_res                                   waawu5Weix6geehu
#define bincmd_info                                     quae8yuvoin6aFoa
#define bincmd_invalid                                  eila7Ung9bieF7Tu
#define bincmd_itemdel                                  xeiQuahphiePh1aa
#define bincmd_led                                      reedieyohyahTh2o
#define bincmd_pic_cfg                                  Thai8Upoow1eth4S
#define bincmd_picadd                                   aen9fo1uunip6aKe
#define bincmd_ping                                     Iih1shae5teerigh
#define bincmd_qrres                                    ahpheip2eepiVoon
#define bincmd_qrscan                                   aihopheeG2aija0x
#define bincmd_reboot                                   Uamaomiop9beique
#define bincmd_relay                                    ve6eZieTeiRah7Ai
#define bincmd_rstcfg                                   aebie2tahlai5wuX
#define mf_asr_init                                     Atu0joo8oS9ahr0c
#define model_size                                      Zoo8Aenaez0baeC1
#define proto_pic_stream_cfg_ret                        eequai9xaeSieWau
#define proto_reset_ret                                 koocoot5ahNg8hef
#define proto_restore_ret                               kikaiB4me5Gah2oc
#define uartp_bin_prase                                 CheeNg9nun7shemo
#define uartp_bin_send_large                            taloowieQuien1oj
#define kpu_decode                                      aiyo6eekei1Ohzie
#define kpu_done                                        foo6eixaer8ou6Ie
#define layer_conv_init                                 GienohQuoquohd7f
#define maix_voice                                      ijeoQu7Oxoova2oo
#define mf_asr_deinit                                   chiogu6unoYiy6Oh
#define mf_asr_loop                                     phieLaegheaph3ai
#define model_end                                       wiHeVu4paelieBoh
#define process_edge                                    Runga0uoN0quiixa
#define proto_query_uid_ret                             ahX1ieG1eig4ki6n
#define proto_send_face_position                        yohk5caeze9Zeo5a
#define proto_send_qrcode_ret                           veibeecoh9shaiNo
#define proto_set_face_recognition_stat_ret             phiet9aisha2Naen
#define proto_set_notify_ret                            Atheigiev0xiyai3
#define bincmd_abort                                    haeNith7os8zakoo
#define bincmd_del                                      aiC7raqu3ioru7ni
#define bincmd_fcnt                                     mohx7ieh8torooTa
#define bincmd_flist                                    re0ohFe0Ooshee2j
#define bincmd_fr_gate                                  caecee7Eiyocoosh
#define bincmd_fr_run                                   aevuu1eat8Aiceih
#define bincmd_ftr                                      air1Quah1geixaiC
#define bincmd_ftrall                                   eiLaidiisheing2u
#define bincmd_hard_cfg                                 we1ahFee4sai9Nua
#define bincmd_import                                   nai9booch2Woo0ie
#define bincmd_record                                   oe3foovohC2Oosah
#define bincmd_soft_cfg                                 much4Xo4pie9quuS
#define bincmd_stradd                                   bahcooCoSh5wohca
#define cal_iou                                         IeMoW0eeGhe4sah9
#define calc_thresh                                     gei9johR3ietae8O
#define _flow_ir2ir_db_hash                             Gae9OMohXei0iegi
#define _flow_ir2ir_deinit                              zoora1ePhip4miet
#define _flow_ir2ir_init                                ohp1Eeg9ni1eeNg4
#define _flow_ir2ir_loop                                ieneithaeGh4fah7
#define _flow_ir2ir_update_allir                        paibah7ohcaex9Oo
#define _flow_v2r_db_hash                               Yooheigu8aeJegho
#define _flow_v2r_deinit                                suxumaebupai0Oze
#define _flow_v2r_init                                  loo8eeVieZez4cho
#define _flow_v2r_loop                                  geogooko0Seeteen
#define _flow_v2r_update_allir                          eekeeshae6Cohkoh
#define _flow_vis_db_hash                               Ye5ou7nee6ti6eik
#define _flow_vis_deinit                                oaBei1ie5iekiphe
#define _flow_vis_init                                  Rai5iesh2ieCh8ga
#define _flow_vis_loop                                  Ahqua1aixaujaeV8
#define _mf_facedb_flash_add_fast                       xeim2ahMeenaefai
#define _mf_facedb_flash_tryread                        oowahF7ceeM7ezae
#define _mf_mdl_init_fd                                 EGiegooK9Guok2ae
#define _mf_mdl_init_ld                                 aed0Iu0quahwo1Ai
#define _mf_model_get_cpuid                             EiYootiKeegh5bao
#define _mf_uart_parse                                  ohZeiyaiGheiy0ti
#define _mf_uart_set_recv_flag                          iT2eeM8aephahfee
#define _mf_uartp_bin_deinit                            iedae1PahX1yiexo
#define _mf_uartp_bin_face_postion                      Phees1eiquahrohc
#define _mf_uartp_bin_getchar                           iwaesei4ichoiTae
#define _mf_uartp_bin_init                              ahR5jaayeiluh8Bo
#define _mf_uartp_bin_init_done                         yit0OoCu3ohc5aho
#define _mf_uartp_bin_loop                              AeWeeyi1oyeegh7o
#define _mf_uartp_bin_rec_cb                            oK4koh2luu5oes3e
#define _mf_uartp_bin_recv_cb                           Iyaeghee7ohDohP9
#define _mf_uartp_bin_send                              ahdairae3AeJoiv0
#define _mf_uartp_bin_send_cb                           coo8waiGh8Ooghae
#define _mf_uartp_json_buf_init                         ieGeih1Ahm8Ze4ta
#define _mf_uartp_json_start_recv_jpeg                  aiQu2kepha7fieph
#define _reg_kpubuf_cb                                  gae9Xie7AhthieNi
#define _reg_rgbbuf_cb0                                 aseBoo9eehae8mie
#define _reg_rgbbuf_cb1                                 avoo9Uagai4Eebui
#define fake_ops_cb                                     ohhaciechee3Fah4
#define i2s_rx_dma_irq                                  ung0BoovohSheequ
#define softmax                                         Itoichaeping7vie
#define stft_compute                                    wup2cahheiGhaeni
#define uart_channel_putc                               eepahriaN5peefah
// #define uartp_bin_send                                  rie7Veixeich4eef
#define _process_cblist                                 lo6uR1xoa8Keish3
#define proto_rtc_cfg_ret                               Oquie7zie0de6poh
#define proto_rtc_cfg                                   ijaeY2rohjoofooZ
#define proto_pic_stream_cfg                            aeFilohPachu2Ei7

#if 1
#undef  mf_uartp_choose
#undef  mf_spi_send_data_normal
#undef  mf_spi_receive_data_standard_dma
#undef  mf_spi_set_clk_rate
#undef  mf_spi_receive_data_multiple_dma
#undef  gc0328_dual_read_id
#undef  rgb_lock_buf
#undef  sipeed_mf_spi_send_data_dma
#undef  open_gc0328_650
#undef  dvp_irq_gc0328_dual
#undef  mf_flow_choose
#undef  gc0328_wr_reg
#undef  gc0328_dual_modify_reg
#undef  gc0328_dual_set_hmirror
#undef  mf_spi_receive_data_standard
#undef  rgb_unlock_buf
#undef  gc0328_dual_config
#undef  mf_spi_init
#undef  mf_spi_init_non_standard
#undef  kpu_lock_buf
#undef  gc0328_dual_set_vflip
#undef  mf_spi_send_data_multiple_dma
#undef  face_lib_version
#undef  mf_spi_send_data_standard
#undef  cam_dualbuf_lock_sync
#undef  gc0328_rd_reg
#undef  open_gc0328_850
#undef  cam_dualbuf_first_run
#undef  kpu_unlock_buf
#endif

#endif
#endif
