#ifndef _MF_CAMERA_H
#define _MF_CAMERA_H

#include <stdint.h>
#include "mf_constants.h"

/*****************************************************************************/
// Enums
/*****************************************************************************/
typedef enum 
{
	MF_CAM_GC0328_DUAL=0,
	MF_CAM_GC0328_SINGLE,
	MF_CAM_OV2640_SINGLE
}mf_cam_type_t;

/*****************************************************************************/
// Types
/*****************************************************************************/
typedef struct
{	//Private
    uint8_t* _kpu_image_original;
	uint8_t* _rgb_image_original;
	mf_err_t (*_init_buf)(void);
	void     (*_deinit_buf)(void);
	mf_err_t (*_init_dev)(void);
	void     (*_deinit_dev)(void);
	//Public
	mf_cam_type_t cam_type;
	uint16_t init_flag;
	uint8_t  dir;			//0 hor, 1 ver
	volatile uint8_t  index;			//0 850, 1 650
	uint16_t exp_time;		//0x05ff~0x0fff
	uint8_t  (*kpu_image)[MF_CAM_W * MF_CAM_H * 3];
	uint8_t  (*rgb_image)[MF_CAM_W * MF_CAM_H * 2];
	uint8_t  kpu_buf_index;
	uint8_t  rgb_buf_index;
	int (*config)(void);
	int (*read_id)(uint16_t *manuf_id, uint16_t *device_id);
	int (*set_hmirror)(uint8_t val);
	int (*set_vflip)(uint8_t val);
	int (*set_reg)(uint8_t reg_data[][2]);
	void     (*switch_650)(void);
	void     (*switch_850)(void);
	void     (*update_lum)(void);
	uint8_t  (*is_night)(uint8_t gate);
	void     (*lock_rgb)(void);
	void     (*unlock_rgb)(void);
	void     (*lock_kpu)(void);
	void     (*unlock_kpu)(void);
	void     (*first_run)(void);
	void     (*lock_sync)(void);
	uint8_t  (*lock_buf)(void);
	//uint8_t     (*unlock_buf)(void);
	//const Public
	mf_err_t (*init)(uint8_t dir, uint8_t exp_time);
	void     (*deinit)(void);
	void     (*stop)(void);
	void     (*start)(void);
} mf_cam_t;



/*****************************************************************************/
// Functions
/*****************************************************************************/
mf_err_t mf_camera_choose(mf_cam_type_t type);

void mf_dvp_init(uint8_t reg_len);
uint32_t mf_dvp_set_xclk_rate(uint32_t xclk_rate);

/*****************************************************************************/
// Vars
/*****************************************************************************/
extern mf_cam_t mf_cam;


#endif