#include "facelib_inc.h"

static mf_err_t dummy_db_search(int8_t *ftr, uint8_t ir_or_rgb, uint32_t* vid, float *score)
{
    return MF_ERR_TODO;
}

static mf_err_t dummy_init(uint16_t model_type)
{
    return MF_ERR_NONE;
}

static void dummy_deinit(uint16_t model_type)
{

}

static mf_err_t dummy_load_cfg(void)
{
    return MF_ERR_TODO;
}

static mf_err_t dummy_save_cfg(void)
{
    return MF_ERR_TODO;
}

static void dummy_cfg_fd(uint16_t minw, uint16_t minh, float threshold)
{

}

static void dummy_cfg_fe(uint16_t ftr_len, float threshold, float threshold_ir)
{

}

static void dummy_cfg_live(float threshold)
{

}

static mf_err_t dummy_face_detect(image_t* kpu_img, face_obj_info_t* info)
{
    return MF_ERR_TODO;
}

static mf_err_t dummy_face_align(image_t* kpu_img, face_obj_t* obj, image_t* simg)
{
    return MF_ERR_TODO;
}

static mf_err_t dummy_ftr_cal(image_t* img, int8_t* ftr)
{
    return MF_ERR_TODO;
}

static float dummy_ftr_compare(int8_t* ftr0, int8_t* ftr1)
{
    return (-1.0);
}

static float dummy_face_living(image_t* img)
{
    return (-1.0);
}

static uint8_t dummy_check_pose(key_point_t *kp)
{
    return -1;
}

static uint8_t dummy_check_blur(uint8_t * buf_in, uint16_t w, uint16_t h, float scale)
{
    return -1;
}

static void dummy_resize(image_t *src, image_t *dst)
{

}

static void dummy_cpuid(uint8_t *id)
{
    memset(id, 0x00, 16);
}

static fr_ret_t dummy_run_fr(image_t* kpu_img, uint8_t out_ftr, face_step_t step, uint8_t ir_or_rgb, uint8_t only_biggest)
{
    fr_ret_t face_ret;
    face_ret.ret = 0;
    face_ret.result = NULL;
    return face_ret; 
}

static void dummy_free_fr(fr_ret_t face_ret)
{
    if(face_ret.result == NULL)
        return;
    if(face_ret.result->face_obj_info.obj[0].feature) {
        free(face_ret.result->face_obj_info.obj[0].feature);
    }
    free(face_ret.result);
	face_ret.result = NULL;
}

static mf_model_t mf_model_dummy = {
	.model_flag   = 0,
	.face_minw    = FACE_WIDTH_MIN,
	.face_minh    = FACE_HEIGHT_MIN,
	.fd_gate      = FACE_DETECT_THRESHOLD,
	.ftr_len      = FACE_FTR_LEN,
	.fe_gate      = FE_GATE,
	.fe_gate_ir   = FE_GATE_IR,
	.live_gate    = LIVE_GATE,
	.is_check_pose= 1,
	.db_search    = dummy_db_search,
	.init         = dummy_init,
	.deinit       = dummy_deinit,
	.load_cfg     = dummy_load_cfg,
	.save_cfg     = dummy_save_cfg,
	.cfg_fd       = dummy_cfg_fd,
	.cfg_fe       = dummy_cfg_fe,
	.cfg_live     = dummy_cfg_live,
	.face_detect  = dummy_face_detect,
	.face_align   = dummy_face_align,
	.ftr_cal      = dummy_ftr_cal,
	.ftr_compare  = dummy_ftr_compare,
	.face_living  = dummy_face_living,
	.run_fr       = dummy_run_fr,
	.free_fr      = dummy_free_fr,
	.check_pose   = dummy_check_pose,
	.check_blur   = dummy_check_blur,
	.resize       = dummy_resize,
	.cpuid		  = dummy_cpuid,
};

void mf_model_use_dummy(void)
{
	memcpy((uint8_t*)&mf_model, (uint8_t*)&mf_model_dummy, sizeof(mf_model_t));
}
