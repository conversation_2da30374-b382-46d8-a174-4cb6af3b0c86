#include "es_inc.h"

#if (ES_LCD_DRIVER_NT35510 == ES_LCD_DRIVER_TYPE)
#include "es_lcd_tft_spi.h"

#define LCD_TE_PIN              (1)
#define LCD_TE_IO_PIN           (13)
#define LCD_TE_IO_NUM           (19)
#define LCD_TE_IO_VAL           (0)

static ES_U32 count_flag = 0;
static void LCD_TE_WAIT() {
    do {
        if(LCD_TE_IO_VAL != gpiohs_get_pin(LCD_TE_IO_NUM)) {
            usleep(1);
            if(LCD_TE_IO_VAL == gpiohs_get_pin(LCD_TE_IO_NUM))
                return;
        }
    } while (1);
}

#define SPI_CS_AS_OUTPUT        (1)
#define NT35510_DELAY_MS        (120)
/* clang-format off */
#define NO_OPERATION            0x00
#define SOFTWARE_RESET          0x01
#define READ_ID                 0x04
#define READ_STATUS             0x09
#define READ_POWER_MODE         0x0A
#define READ_MADCTL             0x0B
#define READ_PIXEL_FORMAT       0x0C
#define READ_IMAGE_FORMAT       0x0D
#define READ_SIGNAL_MODE        0x0E
#define READ_SELT_DIAG_RESULT   0x0F
#define SLEEP_ON                0x10
#define SLEEP_OFF               0x11
#define PARTIAL_DISPALY_ON      0x12
#define NORMAL_DISPALY_ON       0x13
#define INVERSION_DISPALY_OFF   0x20
#define INVERSION_DISPALY_ON    0x21
#define GAMMA_SET               0x26
#define DISPALY_OFF             0x28
#define DISPALY_ON              0x29
#define HORIZONTAL_ADDRESS_SET  0x2A
#define VERTICAL_ADDRESS_SET    0x2B
#define MEMORY_WRITE            0x2C
#define COLOR_SET               0x2D
#define MEMORY_READ             0x2E
#define PARTIAL_AREA            0x30
#define VERTICAL_SCROL_DEFINE   0x33
#define TEAR_EFFECT_LINE_OFF    0x34
#define TEAR_EFFECT_LINE_ON     0x35
#define MEMORY_ACCESS_CTL       0x36
#define VERTICAL_SCROL_S_ADD    0x37
#define IDLE_MODE_OFF           0x38
#define IDLE_MODE_ON            0x39
#define PIXEL_FORMAT_SET        0x3A
#define WRITE_MEMORY_CONTINUE   0x3C
#define READ_MEMORY_CONTINUE    0x3E
#define SET_TEAR_SCANLINE       0x44
#define GET_SCANLINE            0x45
#define WRITE_BRIGHTNESS        0x51
#define READ_BRIGHTNESS         0x52
#define WRITE_CTRL_DISPALY      0x53
#define READ_CTRL_DISPALY       0x54
#define WRITE_BRIGHTNESS_CTL    0x55
#define READ_BRIGHTNESS_CTL     0x56
#define WRITE_MIN_BRIGHTNESS    0x5E
#define READ_MIN_BRIGHTNESS     0x5F
#define READ_ID1                0xDA
#define READ_ID2                0xDB
#define READ_ID3                0xDC
#define RGB_IF_SIGNAL_CTL       0xB0
#define NORMAL_FRAME_CTL        0xB1
#define IDLE_FRAME_CTL          0xB2
#define PARTIAL_FRAME_CTL       0xB3
#define INVERSION_CTL           0xB4
#define BLANK_PORCH_CTL         0xB5
#define DISPALY_FUNCTION_CTL    0xB6
#define ENTRY_MODE_SET          0xB7
#define BACKLIGHT_CTL1          0xB8
#define BACKLIGHT_CTL2          0xB9
#define BACKLIGHT_CTL3          0xBA
#define BACKLIGHT_CTL4          0xBB
#define BACKLIGHT_CTL5          0xBC
#define BACKLIGHT_CTL7          0xBE
#define BACKLIGHT_CTL8          0xBF
#define POWER_CTL1              0xC0
#define POWER_CTL2              0xC1
#define VCOM_CTL1               0xC5
#define VCOM_CTL2               0xC7
#define NV_MEMORY_WRITE         0xD0
#define NV_MEMORY_PROTECT_KEY   0xD1
#define NV_MEMORY_STATUS_READ   0xD2
#define READ_ID4                0xD3
#define POSITIVE_GAMMA_CORRECT  0xE0
#define NEGATIVE_GAMMA_CORRECT  0xE1
#define DIGITAL_GAMMA_CTL1      0xE2
#define DIGITAL_GAMMA_CTL2      0xE3
#define INTERFACE_CTL           0xF6
/* clang-format on */

static ES_U16 lcd_width = ES_LCD_WIDTH;
static ES_U16 lcd_height = ES_LCD_HEIGHT;


// static ES_BYTE pos_gamma_list[]={
// 0x0F, 0x1F, 0x1C, 0x0C, 0x0F, 0x08, 0x48, 0x98,0x37, 0x0A, 0x13, 0x04, 0x11, 0x0D, 0x00};
// static ES_BYTE neg_gamma_list[]={
// 0x0F, 0x32, 0x2E, 0x0B, 0x0D, 0x05, 0x47, 0x75,0x37, 0x06, 0x10, 0x03, 0x24, 0x20, 0x00};
// static ES_BYTE dig_gamma_list[]={
// 0x0F, 0x32, 0x2E, 0x0B, 0x0D, 0x05, 0x47, 0x75,0x37, 0x06, 0x10, 0x03, 0x24, 0x20, 0x00};

///////////////////////////////////////////////////////////////////////////////
static void WriteComm(unsigned int i)
{	
    uint8_t cmd_buf[2];
    cmd_buf[0] = (uint8_t)((i>>8)&0xFF);
    cmd_buf[1] = (uint8_t)((i)&0xFF);
    es_tft_write_command_word(cmd_buf);
}

static void WriteData(unsigned int i)
{
    uint8_t data_buf[2];
    data_buf[0] = (uint8_t)((i>>8)&0xFF);
    data_buf[1] = (uint8_t)((i)&0xFF);
    es_tft_write_byte_word(data_buf);
}

void BlockWrite(unsigned int Xstart,unsigned int Ystart,unsigned int Xend,unsigned int Yend)
{
	WriteComm(0x2a00);   
	WriteData(Xstart>>8);
	WriteComm(0x2a01); 
	WriteData(Xstart&0xff);
	WriteComm(0x2a02); 
	WriteData(Xend>>8);
	WriteComm(0x2a03); 
	WriteData(Xend&0xff);

	WriteComm(0x2b00);   
	WriteData(Ystart>>8);
	WriteComm(0x2b01); 
	WriteData(Ystart&0xff);
	WriteComm(0x2b02); 
	WriteData(Yend>>8);
	WriteComm(0x2b03); 
	WriteData(Yend&0xff);

	WriteComm(0x2c00);
}

void init_nt35510(void)
{
    gpiohs_set_pin(ES_LCD_RST_HS_NUM, GPIO_PV_HIGH); msleep(5);
    gpiohs_set_pin(ES_LCD_RST_HS_NUM, GPIO_PV_LOW);  msleep(10);
    gpiohs_set_pin(ES_LCD_RST_HS_NUM, GPIO_PV_HIGH); msleep(NT35510_DELAY_MS);

    //------------------------------------LCD_CODE------------------------------------------------------------------------------------------------------------------------//
    //NT35510SH_BOE3.97IPS_24BIT_20141224
    WriteComm(0xFF00);WriteData(0x00AA);
    WriteComm(0xFF01);WriteData(0x0055); 
    WriteComm(0xFF02);WriteData(0x00A5); 
    WriteComm(0xFF03);WriteData(0x0080); 
    
    WriteComm(0xF200);WriteData(0x0000); 
    WriteComm(0xF201);WriteData(0x0084); 
    WriteComm(0xF202);WriteData(0x0008);

    WriteComm(0x6F00);WriteData(0x000A); 
    WriteComm(0xF400);WriteData(0x0013);

    WriteComm(0xFF00);WriteData(0x00AA); 
    WriteComm(0xFF01);WriteData(0x0055); 
    WriteComm(0xFF02);WriteData(0x00A5); 
    WriteComm(0xFF03);WriteData(0x0000);

    //Enable Page 1
    WriteComm(0xF000);WriteData(0x0055); 
    WriteComm(0xF001);WriteData(0x00AA); 
    WriteComm(0xF002);WriteData(0x0052); 
    WriteComm(0xF003);WriteData(0x0008);
    WriteComm(0xF004);WriteData(0x0001);

    //AVDD Set AVDD 5.2V
    WriteComm(0xB000);WriteData(0x0009);
    WriteComm(0xB001);WriteData(0x0009);
    WriteComm(0xB002);WriteData(0x0009);

    //AVDD ratio
    WriteComm(0xB600);WriteData(0x0034);
    WriteComm(0xB601);WriteData(0x0034);
    WriteComm(0xB602);WriteData(0x0034); 

    //AVEE  -5.2V
    WriteComm(0xB100);WriteData(0x0009);
    WriteComm(0xB101);WriteData(0x0009);
    WriteComm(0xB102);WriteData(0x0009);

    //AVEE ratio
    WriteComm(0xB700);WriteData(0x0024);
    WriteComm(0xB701);WriteData(0x0024);
    WriteComm(0xB702);WriteData(0x0024); 

    //VCL  -2.5V   
    WriteComm(0xB200);WriteData(0x0001);
    WriteComm(0xB201);WriteData(0x0001);
    WriteComm(0xB202);WriteData(0x0001);

    //VCL ratio
    WriteComm(0xB800);WriteData(0x0034); 
    WriteComm(0xB801);WriteData(0x0034);
    WriteComm(0xB802);WriteData(0x0034);

    //VGH 15V  (Free pump) 
    WriteComm(0xBF00);WriteData(0x0001);
    WriteComm(0xB300);WriteData(0x0005);
    WriteComm(0xB301);WriteData(0x0005);
    WriteComm(0xB302);WriteData(0x0005);  

    //VGH ratio
    WriteComm(0xB900);WriteData(0x0024); //0x0032
    WriteComm(0xB901);WriteData(0x0024);
    WriteComm(0xB902);WriteData(0x0024); 
    //VGL_REG -10V 
    WriteComm(0xB500);WriteData(0x000B);
    WriteComm(0xB501);WriteData(0x000B);//50//6B854
    WriteComm(0xB502);WriteData(0x000B);

    //VGLX ratio
    WriteComm(0xBA00);WriteData(0x0034);
    WriteComm(0xBA01);WriteData(0x0034);
    WriteComm(0xBA02);WriteData(0x0034); 

    //VGMP/VGSP 4.5V/0V
    WriteComm(0xBC00);WriteData(0x0000); 
    WriteComm(0xBC01);WriteData(0x00A3); 
    WriteComm(0xBC02);WriteData(0x0000);

    //VGMN/VGSN -4.5V/0V
    WriteComm(0xBD00);WriteData(0x0000); 
    WriteComm(0xBD01);WriteData(0x00A3); 
    WriteComm(0xBD02);WriteData(0x0000);

    //VCOM 
    WriteComm(0xBE00);WriteData(0x0000); 
    WriteComm(0xBE01);WriteData(0x0058); // 6A

    //Gamma Setting
    WriteComm(0xD100);WriteData(0x0000); 
    WriteComm(0xD101);WriteData(0x0001); 
    WriteComm(0xD102);WriteData(0x0000); 
    WriteComm(0xD103);WriteData(0x002F); 
    WriteComm(0xD104);WriteData(0x0000); 
    WriteComm(0xD105);WriteData(0x0067); 
    WriteComm(0xD106);WriteData(0x0000); 
    WriteComm(0xD107);WriteData(0x0089); 
    WriteComm(0xD108);WriteData(0x0000); 
    WriteComm(0xD109);WriteData(0x00A4); 
    WriteComm(0xD10A);WriteData(0x0000); 
    WriteComm(0xD10B);WriteData(0x00B9); 
    WriteComm(0xD10C);WriteData(0x0000); 
    WriteComm(0xD10D);WriteData(0x00E3);
    WriteComm(0xD10E);WriteData(0x0001);
    WriteComm(0xD10F);WriteData(0x001D); 
    WriteComm(0xD110);WriteData(0x0001); 
    WriteComm(0xD111);WriteData(0x0044); 
    WriteComm(0xD112);WriteData(0x0001); 
    WriteComm(0xD113);WriteData(0x0080); 
    WriteComm(0xD114);WriteData(0x0001); 
    WriteComm(0xD115);WriteData(0x00B1); 
    WriteComm(0xD116);WriteData(0x0001); 
    WriteComm(0xD117);WriteData(0x00FD); 
    WriteComm(0xD118);WriteData(0x0002); 
    WriteComm(0xD119);WriteData(0x003C); 
    WriteComm(0xD11A);WriteData(0x0002); 
    WriteComm(0xD11B);WriteData(0x003E); 
    WriteComm(0xD11C);WriteData(0x0002); 
    WriteComm(0xD11D);WriteData(0x0076); 
    WriteComm(0xD11E);WriteData(0x0002);
    WriteComm(0xD11F);WriteData(0x00B6);  
    WriteComm(0xD120);WriteData(0x0002); 
    WriteComm(0xD121);WriteData(0x00D6); 
    WriteComm(0xD122);WriteData(0x0003);
    WriteComm(0xD123);WriteData(0x0003); 
    WriteComm(0xD124);WriteData(0x0003); 
    WriteComm(0xD125);WriteData(0x002A); 
    WriteComm(0xD126);WriteData(0x0003); 
    WriteComm(0xD127);WriteData(0x0050); 
    WriteComm(0xD128);WriteData(0x0003); 
    WriteComm(0xD129);WriteData(0x0070); 
    WriteComm(0xD12A);WriteData(0x0003); 
    WriteComm(0xD12B);WriteData(0x0091); 
    WriteComm(0xD12C);WriteData(0x0003); 
    WriteComm(0xD12D);WriteData(0x00A2); 
    WriteComm(0xD12E);WriteData(0x0003); 
    WriteComm(0xD12F);WriteData(0x00BA); 
    WriteComm(0xD130);WriteData(0x0003); 
    WriteComm(0xD131);WriteData(0x00C5); 
    WriteComm(0xD132);WriteData(0x0003); 
    WriteComm(0xD133);WriteData(0x00FE); 

    WriteComm(0xD200);WriteData(0x0000); 
    WriteComm(0xD201);WriteData(0x0001); 
    WriteComm(0xD202);WriteData(0x0000); 
    WriteComm(0xD203);WriteData(0x002F); 
    WriteComm(0xD204);WriteData(0x0000); 
    WriteComm(0xD205);WriteData(0x0067); 
    WriteComm(0xD206);WriteData(0x0000); 
    WriteComm(0xD207);WriteData(0x0089); 
    WriteComm(0xD208);WriteData(0x0000); 
    WriteComm(0xD209);WriteData(0x00A4); 
    WriteComm(0xD20A);WriteData(0x0000); 
    WriteComm(0xD20B);WriteData(0x00B9); 
    WriteComm(0xD20C);WriteData(0x0000); 
    WriteComm(0xD20D);WriteData(0x00E3); 
    WriteComm(0xD20E);WriteData(0x0001);
    WriteComm(0xD20F);WriteData(0x001D); 
    WriteComm(0xD210);WriteData(0x0001); 
    WriteComm(0xD211);WriteData(0x0044); 
    WriteComm(0xD212);WriteData(0x0001); 
    WriteComm(0xD213);WriteData(0x0080); 
    WriteComm(0xD214);WriteData(0x0001); 
    WriteComm(0xD215);WriteData(0x00B1); 
    WriteComm(0xD216);WriteData(0x0001); 
    WriteComm(0xD217);WriteData(0x00FD); 
    WriteComm(0xD218);WriteData(0x0002); 
    WriteComm(0xD219);WriteData(0x003C); 
    WriteComm(0xD21A);WriteData(0x0002); 
    WriteComm(0xD21B);WriteData(0x003E); 
    WriteComm(0xD21C);WriteData(0x0002); 
    WriteComm(0xD21D);WriteData(0x0076); 
    WriteComm(0xD21E);WriteData(0x0002);
    WriteComm(0xD21F);WriteData(0x00B6);  
    WriteComm(0xD220);WriteData(0x0002); 
    WriteComm(0xD221);WriteData(0x00D6);
    WriteComm(0xD222);WriteData(0x0003);
    WriteComm(0xD223);WriteData(0x0003); 
    WriteComm(0xD224);WriteData(0x0003); 
    WriteComm(0xD225);WriteData(0x002A); 
    WriteComm(0xD226);WriteData(0x0003); 
    WriteComm(0xD227);WriteData(0x0050); 
    WriteComm(0xD228);WriteData(0x0003); 
    WriteComm(0xD229);WriteData(0x0070); 
    WriteComm(0xD22A);WriteData(0x0003); 
    WriteComm(0xD22B);WriteData(0x0091); 
    WriteComm(0xD22C);WriteData(0x0003); 
    WriteComm(0xD22D);WriteData(0x00A2); 
    WriteComm(0xD22E);WriteData(0x0003); 
    WriteComm(0xD22F);WriteData(0x00BA); 
    WriteComm(0xD230);WriteData(0x0003); 
    WriteComm(0xD231);WriteData(0x00C5); 
    WriteComm(0xD232);WriteData(0x0003); 
    WriteComm(0xD233);WriteData(0x00FE); 

    WriteComm(0xD300);WriteData(0x0000); 
    WriteComm(0xD301);WriteData(0x0001); 
    WriteComm(0xD302);WriteData(0x0000); 
    WriteComm(0xD303);WriteData(0x002F); 
    WriteComm(0xD304);WriteData(0x0000); 
    WriteComm(0xD305);WriteData(0x0067); 
    WriteComm(0xD306);WriteData(0x0000); 
    WriteComm(0xD307);WriteData(0x0089); 
    WriteComm(0xD308);WriteData(0x0000); 
    WriteComm(0xD309);WriteData(0x00A4); 
    WriteComm(0xD30A);WriteData(0x0000); 
    WriteComm(0xD30B);WriteData(0x00B9); 
    WriteComm(0xD30C);WriteData(0x0000); 
    WriteComm(0xD30D);WriteData(0x00E3); 
    WriteComm(0xD30E);WriteData(0x0001); 
    WriteComm(0xD30F);WriteData(0x001D); 
    WriteComm(0xD310);WriteData(0x0001); 
    WriteComm(0xD311);WriteData(0x0044); 
    WriteComm(0xD312);WriteData(0x0001); 
    WriteComm(0xD313);WriteData(0x0080); 
    WriteComm(0xD314);WriteData(0x0001); 
    WriteComm(0xD315);WriteData(0x00B1); 
    WriteComm(0xD316);WriteData(0x0001); 
    WriteComm(0xD317);WriteData(0x00FD); 
    WriteComm(0xD318);WriteData(0x0002); 
    WriteComm(0xD319);WriteData(0x003C); 
    WriteComm(0xD31A);WriteData(0x0002); 
    WriteComm(0xD31B);WriteData(0x003E); 
    WriteComm(0xD31C);WriteData(0x0002); 
    WriteComm(0xD31D);WriteData(0x0076); 
    WriteComm(0xD31E);WriteData(0x0002);
    WriteComm(0xD31F);WriteData(0x00B6);  
    WriteComm(0xD320);WriteData(0x0002); 
    WriteComm(0xD321);WriteData(0x00D6); 
    WriteComm(0xD322);WriteData(0x0003); 
    WriteComm(0xD323);WriteData(0x0003); 
    WriteComm(0xD324);WriteData(0x0003); 
    WriteComm(0xD325);WriteData(0x002A); 
    WriteComm(0xD326);WriteData(0x0003); 
    WriteComm(0xD327);WriteData(0x0050); 
    WriteComm(0xD328);WriteData(0x0003); 
    WriteComm(0xD329);WriteData(0x0070); 
    WriteComm(0xD32A);WriteData(0x0003); 
    WriteComm(0xD32B);WriteData(0x0091); 
    WriteComm(0xD32C);WriteData(0x0003); 
    WriteComm(0xD32D);WriteData(0x00A2); 
    WriteComm(0xD32E);WriteData(0x0003); 
    WriteComm(0xD32F);WriteData(0x00BA); 
    WriteComm(0xD330);WriteData(0x0003); 
    WriteComm(0xD331);WriteData(0x00C5); 
    WriteComm(0xD332);WriteData(0x0003); 
    WriteComm(0xD333);WriteData(0x00FE); 

    WriteComm(0xD400);WriteData(0x0000); 
    WriteComm(0xD401);WriteData(0x0001); 
    WriteComm(0xD402);WriteData(0x0000); 
    WriteComm(0xD403);WriteData(0x002F); 
    WriteComm(0xD404);WriteData(0x0000); 
    WriteComm(0xD405);WriteData(0x0067); 
    WriteComm(0xD406);WriteData(0x0000); 
    WriteComm(0xD407);WriteData(0x0089); 
    WriteComm(0xD408);WriteData(0x0000); 
    WriteComm(0xD409);WriteData(0x00A4); 
    WriteComm(0xD40A);WriteData(0x0000); 
    WriteComm(0xD40B);WriteData(0x00B9); 
    WriteComm(0xD40C);WriteData(0x0000); 
    WriteComm(0xD40D);WriteData(0x00E3); 
    WriteComm(0xD40E);WriteData(0x0001); 
    WriteComm(0xD40F);WriteData(0x001D); 
    WriteComm(0xD410);WriteData(0x0001); 
    WriteComm(0xD411);WriteData(0x0044); 
    WriteComm(0xD412);WriteData(0x0001); 
    WriteComm(0xD413);WriteData(0x0080); 
    WriteComm(0xD414);WriteData(0x0001); 
    WriteComm(0xD415);WriteData(0x00B1); 
    WriteComm(0xD416);WriteData(0x0001); 
    WriteComm(0xD417);WriteData(0x00FD); 
    WriteComm(0xD418);WriteData(0x0002); 
    WriteComm(0xD419);WriteData(0x003C); 
    WriteComm(0xD41A);WriteData(0x0002); 
    WriteComm(0xD41B);WriteData(0x003E); 
    WriteComm(0xD41C);WriteData(0x0002); 
    WriteComm(0xD41D);WriteData(0x0076); 
    WriteComm(0xD41E);WriteData(0x0002);
    WriteComm(0xD41F);WriteData(0x00B6);  
    WriteComm(0xD420);WriteData(0x0002); 
    WriteComm(0xD421);WriteData(0x00D6); 
    WriteComm(0xD422);WriteData(0x0003); 
    WriteComm(0xD423);WriteData(0x0003); 
    WriteComm(0xD424);WriteData(0x0003); 
    WriteComm(0xD425);WriteData(0x002A); 
    WriteComm(0xD426);WriteData(0x0003); 
    WriteComm(0xD427);WriteData(0x0050); 
    WriteComm(0xD428);WriteData(0x0003); 
    WriteComm(0xD429);WriteData(0x0070); 
    WriteComm(0xD42A);WriteData(0x0003); 
    WriteComm(0xD42B);WriteData(0x0091); 
    WriteComm(0xD42C);WriteData(0x0003); 
    WriteComm(0xD42D);WriteData(0x00A2); 
    WriteComm(0xD42E);WriteData(0x0003); 
    WriteComm(0xD42F);WriteData(0x00BA); 
    WriteComm(0xD430);WriteData(0x0003); 
    WriteComm(0xD431);WriteData(0x00C5); 
    WriteComm(0xD432);WriteData(0x0003); 
    WriteComm(0xD433);WriteData(0x00FE); 

    WriteComm(0xD500);WriteData(0x0000); 
    WriteComm(0xD501);WriteData(0x0001); 
    WriteComm(0xD502);WriteData(0x0000); 
    WriteComm(0xD503);WriteData(0x002F); 
    WriteComm(0xD504);WriteData(0x0000); 
    WriteComm(0xD505);WriteData(0x0067); 
    WriteComm(0xD506);WriteData(0x0000); 
    WriteComm(0xD507);WriteData(0x0089); 
    WriteComm(0xD508);WriteData(0x0000); 
    WriteComm(0xD509);WriteData(0x00A4); 
    WriteComm(0xD50A);WriteData(0x0000); 
    WriteComm(0xD50B);WriteData(0x00B9); 
    WriteComm(0xD50C);WriteData(0x0000); 
    WriteComm(0xD50D);WriteData(0x00E3); 
    WriteComm(0xD50E);WriteData(0x0001); 
    WriteComm(0xD50F);WriteData(0x001D); 
    WriteComm(0xD510);WriteData(0x0001); 
    WriteComm(0xD511);WriteData(0x0044); 
    WriteComm(0xD512);WriteData(0x0001); 
    WriteComm(0xD513);WriteData(0x0080); 
    WriteComm(0xD514);WriteData(0x0001); 
    WriteComm(0xD515);WriteData(0x00B1); 
    WriteComm(0xD516);WriteData(0x0001); 
    WriteComm(0xD517);WriteData(0x00FD); 
    WriteComm(0xD518);WriteData(0x0002); 
    WriteComm(0xD519);WriteData(0x003C); 
    WriteComm(0xD51A);WriteData(0x0002); 
    WriteComm(0xD51B);WriteData(0x003E); 
    WriteComm(0xD51C);WriteData(0x0002); 
    WriteComm(0xD51D);WriteData(0x0076); 
    WriteComm(0xD51E);WriteData(0x0002);
    WriteComm(0xD51F);WriteData(0x00B6);  
    WriteComm(0xD520);WriteData(0x0002);
    WriteComm(0xD521);WriteData(0x00D6);
    WriteComm(0xD522);WriteData(0x0003);
    WriteComm(0xD523);WriteData(0x0003); 
    WriteComm(0xD524);WriteData(0x0003); 
    WriteComm(0xD525);WriteData(0x002A); 
    WriteComm(0xD526);WriteData(0x0003); 
    WriteComm(0xD527);WriteData(0x0050); 
    WriteComm(0xD528);WriteData(0x0003); 
    WriteComm(0xD529);WriteData(0x0070); 
    WriteComm(0xD52A);WriteData(0x0003); 
    WriteComm(0xD52B);WriteData(0x0091); 
    WriteComm(0xD52C);WriteData(0x0003); 
    WriteComm(0xD52D);WriteData(0x00A2); 
    WriteComm(0xD52E);WriteData(0x0003); 
    WriteComm(0xD52F);WriteData(0x00BA); 
    WriteComm(0xD530);WriteData(0x0003); 
    WriteComm(0xD531);WriteData(0x00C5); 
    WriteComm(0xD532);WriteData(0x0003); 
    WriteComm(0xD533);WriteData(0x00FE); 

    WriteComm(0xD600);WriteData(0x0000);
    WriteComm(0xD601);WriteData(0x0001); 
    WriteComm(0xD602);WriteData(0x0000); 
    WriteComm(0xD603);WriteData(0x002F); 
    WriteComm(0xD604);WriteData(0x0000); 
    WriteComm(0xD605);WriteData(0x0067); 
    WriteComm(0xD606);WriteData(0x0000); 
    WriteComm(0xD607);WriteData(0x0089); 
    WriteComm(0xD608);WriteData(0x0000); 
    WriteComm(0xD609);WriteData(0x00A4); 
    WriteComm(0xD60A);WriteData(0x0000); 
    WriteComm(0xD60B);WriteData(0x00B9); 
    WriteComm(0xD60C);WriteData(0x0000); 
    WriteComm(0xD60D);WriteData(0x00E3); 
    WriteComm(0xD60E);WriteData(0x0001); 
    WriteComm(0xD60F);WriteData(0x001D); 
    WriteComm(0xD610);WriteData(0x0001); 
    WriteComm(0xD611);WriteData(0x0044); 
    WriteComm(0xD612);WriteData(0x0001); 
    WriteComm(0xD613);WriteData(0x0080); 
    WriteComm(0xD614);WriteData(0x0001); 
    WriteComm(0xD615);WriteData(0x00B1); 
    WriteComm(0xD616);WriteData(0x0001); 
    WriteComm(0xD617);WriteData(0x00FD); 
    WriteComm(0xD618);WriteData(0x0002); 
    WriteComm(0xD619);WriteData(0x003C); 
    WriteComm(0xD61A);WriteData(0x0002); 
    WriteComm(0xD61B);WriteData(0x003E); 
    WriteComm(0xD61C);WriteData(0x0002); 
    WriteComm(0xD61D);WriteData(0x0076); 
    WriteComm(0xD61E);WriteData(0x0002);
    WriteComm(0xD61F);WriteData(0x00B6);  
    WriteComm(0xD620);WriteData(0x0002); 
    WriteComm(0xD621);WriteData(0x00D6); 
    WriteComm(0xD622);WriteData(0x0003); 
    WriteComm(0xD623);WriteData(0x0003); 
    WriteComm(0xD624);WriteData(0x0003); 
    WriteComm(0xD625);WriteData(0x002A); 
    WriteComm(0xD626);WriteData(0x0003); 
    WriteComm(0xD627);WriteData(0x0050); 
    WriteComm(0xD628);WriteData(0x0003); 
    WriteComm(0xD629);WriteData(0x0070); 
    WriteComm(0xD62A);WriteData(0x0003); 
    WriteComm(0xD62B);WriteData(0x0091); 
    WriteComm(0xD62C);WriteData(0x0003); 
    WriteComm(0xD62D);WriteData(0x00A2); 
    WriteComm(0xD62E);WriteData(0x0003); 
    WriteComm(0xD62F);WriteData(0x00BA); 
    WriteComm(0xD630);WriteData(0x0003); 
    WriteComm(0xD631);WriteData(0x00C5); 
    WriteComm(0xD632);WriteData(0x0003); 
    WriteComm(0xD633);WriteData(0x00FE); 


    //Enable Page0
    WriteComm(0xF000);WriteData(0x0055); 
    WriteComm(0xF001);WriteData(0x00AA); 
    WriteComm(0xF002);WriteData(0x0052); 
    WriteComm(0xF003);WriteData(0x0008);
    WriteComm(0xF004);WriteData(0x0000);

    //Display control
    WriteComm(0xB100);WriteData(0x00F8);// MCU I/F & mipi cmd mode?CC, RGB I/F  & mipi video mode ?FC
    WriteComm(0xB101);WriteData(0x0000); 

    // WriteComm(0xB400);WriteData(0x0010); 
    WriteComm(0xB500);WriteData(0x006B);

    //Source hold time
    WriteComm(0xB600);WriteData(0x000A);

    //Gate EQ control
    WriteComm(0xB700);WriteData(0x0000);
    WriteComm(0xB701);WriteData(0x0000);

    //Source EQ control (Mode 2)
    WriteComm(0xB800);WriteData(0x0001); 
    WriteComm(0xB801);WriteData(0x0005); 
    WriteComm(0xB802);WriteData(0x0005); 
    WriteComm(0xB803);WriteData(0x0005);

    WriteComm(0xBA00);WriteData(0x0001);
    //Inversion mode  (C)
    WriteComm(0xBC00);WriteData(0x0000); //02:2DOT
    WriteComm(0xBC01);WriteData(0x0000); 
    WriteComm(0xBC02);WriteData(0x0000); 

    // BOE's Setting (default)
    WriteComm(0xCC00);WriteData(0x0003); 
    WriteComm(0xCC01);WriteData(0x0000);
    WriteComm(0xCC02);WriteData(0x0000); 

    //Porch Adjust
    WriteComm(0xBD00);WriteData(0x0001); 
    WriteComm(0xBD01);WriteData(0x0084); 
    WriteComm(0xBD02);WriteData(0x0007); 
    WriteComm(0xBD03);WriteData(0x0031); 
    WriteComm(0xBD04);WriteData(0x0000); 

    WriteComm(0xBE00);WriteData(0x0001); 
    WriteComm(0xBE01);WriteData(0x0084); 
    WriteComm(0xBE02);WriteData(0x0007); 
    WriteComm(0xBE03);WriteData(0x0031); 
    WriteComm(0xBE04);WriteData(0x0000);

    WriteComm(0xBF00);WriteData(0x0001); 
    WriteComm(0xBF01);WriteData(0x0084); 
    WriteComm(0xBF02);WriteData(0x0007); 
    WriteComm(0xBF03);WriteData(0x0031); 
    WriteComm(0xBF04);WriteData(0x0000);

    WriteComm(0x3500);WriteData(0x0000); 
    WriteComm(0x3600);WriteData(0x0000); 
    WriteComm(0x3A00);WriteData(0x0055);	 //0x77:24BIT

    msleep(NT35510_DELAY_MS);
    WriteComm(0x1100);
    msleep(NT35510_DELAY_MS); 
    WriteComm(0x2900);
    msleep(NT35510_DELAY_MS); 
	WriteComm(0x2c00);

	return;
}

static ES_S32 es_hal_lcd_set_direction(ES_U8 dir)
{
    ES_U16 tmp = 0;
    // dir |= 0x08;
    // dir = 0x08;

    if(dir & ES_LCD_DIR_XY_MASK) {
        if(lcd_width < lcd_height) {
            tmp = lcd_width;
            lcd_width = lcd_height;
            lcd_height = tmp;
        }
    } else {
        if(lcd_width > lcd_height) {
            tmp = lcd_width;
            lcd_width = lcd_height;
            lcd_height = tmp;
        }
    }

    // printk("dir:%02x, lcd_width:%d, lcd_height:%d\r\n", dir, lcd_width, lcd_height);
    es_tft_write_command(MEMORY_ACCESS_CTL);
    es_tft_write_byte((ES_BYTE *)&dir, 1);
    return ES_RET_SUCCESS;
}

static void ili9486_gpio_init(void)
{
    // backlight
    fpioa_set_function(ES_LCD_BL_PIN,   FUNC_GPIOHS0 + ES_LCD_BL_HS_NUM);
    gpiohs_set_drive_mode(ES_LCD_BL_HS_NUM, GPIO_DM_OUTPUT);
    gpiohs_set_pin(ES_LCD_BL_HS_NUM, 1);

    /* LCD IO*/
    fpioa_set_function(ES_LCD_RST_PIN, FUNC_GPIOHS0 + ES_LCD_RST_HS_NUM);
    fpioa_set_function(ES_LCD_DCX_PIN, FUNC_GPIOHS0 + ES_LCD_DCX_HS_NUM);
#if SPI_CS_AS_OUTPUT
    fpioa_set_function(ES_LCD_CS_PIN, FUNC_GPIOHS0+ES_LCD_SPI_CS_HS_NUM);
    gpiohs_set_drive_mode(ES_LCD_SPI_CS_HS_NUM, GPIO_DM_OUTPUT);
    gpiohs_set_pin(ES_LCD_SPI_CS_HS_NUM, 1);
#else
    fpioa_set_function(ES_LCD_CS_PIN, FUNC_SPI0_SS3);
#endif
    fpioa_set_function(ES_LCD_SCK_PIN, FUNC_SPI0_SCLK);

    fpioa_set_io_driving(ES_LCD_RST_PIN, FPIOA_DRIVING_15);
    fpioa_set_io_driving(ES_LCD_DCX_PIN, FPIOA_DRIVING_15);
#if (0 == SPI_CS_AS_OUTPUT)
    fpioa_set_io_driving(ES_LCD_CS_PIN, FPIOA_DRIVING_15);
#endif
    fpioa_set_io_driving(ES_LCD_SCK_PIN, FPIOA_DRIVING_15);

#if LCD_TE_PIN
    fpioa_set_function(LCD_TE_IO_PIN,   FUNC_GPIOHS0 + LCD_TE_IO_NUM);
    gpiohs_set_drive_mode(LCD_TE_IO_NUM, GPIO_DM_INPUT);
#endif

}

ES_S32 es_hal_lcd_init(ES_VOID)
{
    ili9486_gpio_init();
    es_tft_hard_init(30);
    init_nt35510();
    printk("init lcd nt35510\r\n");

    es_hal_lcd_on();
    es_hal_lcd_set_direction(ES_LCD_DIR_PARAM);

    return ES_RET_SUCCESS;
}


ES_S32 es_hal_lcd_deinit(ES_VOID)
{
    return ES_RET_SUCCESS;
}


ES_S32 es_hal_lcd_set_area(ES_U16 x, ES_U16 y, ES_U16 w, ES_U16 h)
{
    ES_BYTE data[4] = {0};

    // printk("x:%d, y:%d, w:%d, h:%d\r\n", x, y, w, h);
    LCD_TE_WAIT();

    data[0] = (ES_BYTE)(x >> 8);
    data[1] = (ES_BYTE)(x);
    data[2] = (ES_BYTE)((x + w - 1) >> 8);
    data[3] = (ES_BYTE)((x + w - 1));
#if (1 == ES_LCD_DIR)
    es_tft_write_command(HORIZONTAL_ADDRESS_SET); // 0x2A
#else
    es_tft_write_command(VERTICAL_ADDRESS_SET); // 0x2B
#endif
    es_tft_write_byte(data, 4);

    data[0] = (ES_BYTE)(y >> 8);
    data[1] = (ES_BYTE)(y);
    data[2] = (ES_BYTE)((y + h - 1) >> 8);
    data[3] = (ES_BYTE)((y + h - 1));
#if (1 == ES_LCD_DIR)
    es_tft_write_command(VERTICAL_ADDRESS_SET); // 0x2B
#else
    es_tft_write_command(HORIZONTAL_ADDRESS_SET); // 0x2A
#endif
    es_tft_write_byte(data, 4);

    es_tft_write_command(MEMORY_WRITE);

    return ES_RET_SUCCESS;
}

ES_S32 es_hal_lcd_clear(ES_U16 rgb565_color)
{
    uint32_t data = ((uint32_t)rgb565_color << 16) | (uint32_t)rgb565_color;

    LCD_TE_WAIT();
    BlockWrite(0, 0, lcd_width, lcd_height);
    es_tft_fill_data(&data, lcd_width * lcd_height / 2);
    // printk("lcd_width:%d, lcd_height:%d, rgb565_color:0x%04x\r\n", lcd_width, lcd_height, rgb565_color);
    return ES_RET_SUCCESS;
}

ES_S32 es_hal_lcd_draw_picture(ES_U16 x, ES_U16 y, ES_U16 w, ES_U16 h, const ES_BYTE *imamge)
{
    if (0 == count_flag){
        count_flag = 10;
        LCD_TE_WAIT();
    }
    count_flag--;
    BlockWrite(x, y, w - 1, h - 1);
    es_tft_write_word((uint32_t*)imamge, w * h / 2, 0);
    return ES_RET_SUCCESS;
}


ES_U32 es_hal_lcd_get_width(ES_VOID)
{
    return lcd_width;
}

ES_U32 es_hal_lcd_get_height(ES_VOID)
{
    return lcd_height;
}

ES_S32 es_hal_lcd_on(ES_VOID)
{
    gpiohs_set_pin(ES_LCD_BL_HS_NUM, 0);
    return ES_RET_SUCCESS;
}

ES_S32 es_hal_lcd_standby(ES_VOID)
{
    fpioa_set_function(ES_LCD_BL_PIN, FUNC_TIMER0_TOGGLE1 + ES_LCD_PWDM_CHN_BL);
    pwm_init(ES_LCD_PWM_DEV_BL);
    pwm_set_frequency(ES_LCD_PWM_DEV_BL, ES_LCD_PWDM_CHN_BL, 1000, 0.2);
    pwm_set_enable(ES_LCD_PWM_DEV_BL, ES_LCD_PWDM_CHN_BL, 1);
    return ES_RET_SUCCESS;
}

#endif
