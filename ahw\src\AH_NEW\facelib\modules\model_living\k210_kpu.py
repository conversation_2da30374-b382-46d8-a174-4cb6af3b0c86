import struct



# V3的头部
# typedef struct
# {
    # uint32_t version;			//固定3
    # uint32_t flags;
    # uint32_t arch;				//固定0
    # uint32_t layers_length;		//层数
    # uint32_t max_start_address;
    # uint32_t main_mem_usage;	//主缓存大小
    # uint32_t output_count;		//output数量
# } kpu_model_header_t;
kheader = struct.Struct('IIIIIII')

# typedef struct
# {
    # uint32_t address;
    # uint32_t size;
# } kpu_model_output_t;
koutput = struct.Struct('II')

# typedef struct
# {
    # uint32_t type;
    # uint32_t body_size;
# } kpu_model_layer_header_t;
klayer_header = struct.Struct('II')

# typedef struct
# {
    # uint32_t flags;
    # uint32_t main_mem_out_address;
    # uint32_t layer_offset;
    # uint32_t weights_offset;
    # uint32_t bn_offset;
    # uint32_t act_offset;
# } kpu_model_conv_layer_argument_t;
kconv_layer = struct.Struct('IIIIII')

# typedef struct
# {
    # uint32_t flags;
    # uint32_t main_mem_in_address;
    # uint32_t main_mem_out_address;
    # uint32_t in_channels;
    # uint32_t out_channels;
    # kpu_model_activation_t act;
    # float weights[0];
# } kpu_model_fully_connected_layer_argument_t;

kfc_layer = struct.Struct('IIIIII')

# typedef struct
# {
    # uint32_t flags;
    # uint32_t main_mem_in_address;
    # uint32_t main_mem_out_address;
    # uint32_t count;
    # kpu_model_quant_param_t quant_param;
# } kpu_model_dequantize_layer_argument_t;
# typedef struct _quantize_param
# {
    # float scale;
    # float bias;
# } quantize_param_t;
kdeq_layer = struct.Struct('IIIIff')

klayer_arg = struct.Struct('QQQQQQQQQQQQ')
# typedef struct
# {
    # union
    # {
        # uint64_t reg;
        # struct
        # {
            # uint64_t shift_number:8;
            # uint64_t y_mul:16;
            # uint64_t x_start:36;
        # } data;
    # } activate_para[16];

    # union
    # {
        # uint64_t reg;
        # struct
        # {
            # uint8_t result_bias[8];
        # } data;
    # } activate_para_bias0;

    # union
    # {
        # uint64_t reg;
        # struct
        # {
            # uint8_t result_bias[8];
        # } data;
    # } activate_para_bias1;
# } kpu_activate_table_t;
kact_table = struct.Struct('QQQQQQQQQQQQQQQQQQ')
# typedef struct
# {
    # union
    # {
        # uint64_t reg;
        # struct
        # {
            # uint64_t norm_mul:24;
            # uint64_t norm_add:32;
            # uint64_t norm_shift:4;
        # } data;
    # } batchnorm;
# } kpu_batchnorm_argument_t;
kbn_arg = struct.Struct('Q')
kconv_3x3_8b_arg = struct.Struct('BBBBBBBBB')
kconv_3x3_16b_arg = struct.Struct('HHHHHHHHH')
kconv_1x1_8b_arg = struct.Struct('B')
kconv_1x1_16b_arg = struct.Struct('H')

# typedef struct
# {
    # uint32_t flags;
    # uint32_t main_mem_in_address;
    # uint32_t main_mem_out_address;
    # uint32_t in_channels;
    # uint32_t out_channels;
    # kpu_model_activation_t act;
    # float weights[0];
# } kpu_model_fully_connected_layer_argument_t;
kfc_arg = struct.Struct('IIIIII')

# typedef struct
# {
    # uint32_t flags;
    # uint32_t main_mem_in_address;
    # uint32_t main_mem_out_address;
    # uint32_t kernel_size;
    # uint32_t channels;
# } kpu_model_gap2d_layer_argument_t;
kgap2d_layer = struct.Struct('IIIII')

KL_INVALID 							= 0
KL_ADD								= 1
KL_QUANTIZED_ADD					= 2
KL_GLOBAL_MAX_POOL2D				= 3
KL_QUANTIZED_GLOBAL_MAX_POOL2D		= 4
KL_GLOBAL_AVERAGE_POOL2D			= 5
KL_QUANTIZED_GLOBAL_AVERAGE_POOL2D	= 6
KL_MAX_POOL2D						= 7
KL_QUANTIZED_MAX_POOL2D				= 8
KL_AVERAGE_POOL2D					= 9
KL_QUANTIZED_AVERAGE_POOL2D			= 10
KL_QUANTIZE							= 11
KL_DEQUANTIZE						= 12
KL_REQUANTIZE						= 13
KL_L2_NORMALIZATION					= 14
KL_SOFTMAX							= 15
KL_CONCAT							= 16
KL_QUANTIZED_CONCAT					= 17
KL_FULLY_CONNECTED					= 18
KL_QUANTIZED_FULLY_CONNECTED		= 19
KL_TENSORFLOW_FLATTEN				= 20
KL_QUANTIZED_TENSORFLOW_FLATTEN		= 21
KL_RESIZE_NEAREST_NEIGHBOR          = 22
KL_QUANTIZED_RESIZE_NEAREST_NEIGHBOR= 23
KL_CHANNELWISE_DEQUANTIZE           = 24
KL_LOGISTIC                         = 25
KL_K210_CONV 						= 10240
KL_K210_ADD_PADDING					= 10241
KL_K210_REMOVE_PADDING				= 10242
KL_K210_UPLOAD						= 10243

layer_name_dict = {
    KL_INVALID 							:"invalid layer",
    KL_ADD								:"add layer",
    KL_QUANTIZED_ADD					:"quantized add layer",
    KL_GLOBAL_MAX_POOL2D				:"global max pool2d layer",
    KL_QUANTIZED_GLOBAL_MAX_POOL2D		:"quantized global max pool2d layer",
    KL_GLOBAL_AVERAGE_POOL2D			:"global average pool2d layer",
    KL_QUANTIZED_GLOBAL_AVERAGE_POOL2D	:"quantized global average pool2d layer",
    KL_MAX_POOL2D						:"max pool2d layer",
    KL_QUANTIZED_MAX_POOL2D				:"quantized max pool2d layer",
    KL_AVERAGE_POOL2D					:"average pool2d layer",
    KL_QUANTIZED_AVERAGE_POOL2D			:"quantized average pool2d layer",
    KL_QUANTIZE							:"quantize layer",
    KL_DEQUANTIZE						:"dequantize layer",
    KL_REQUANTIZE						:"requantize layer",
    KL_L2_NORMALIZATION					:"l2 normalization layer",
    KL_SOFTMAX							:"softmax layer",
    KL_CONCAT							:"concat layer",
    KL_QUANTIZED_CONCAT					:"quantized concat layer",
    KL_FULLY_CONNECTED					:"fully connected layer",
    KL_QUANTIZED_FULLY_CONNECTED		:"quantized fully connected layer",
    KL_TENSORFLOW_FLATTEN				:"tensorflow flatten layer",
    KL_QUANTIZED_TENSORFLOW_FLATTEN		:"quantized tensorflow flatten layer",
    KL_RESIZE_NEAREST_NEIGHBOR          :"KL_RESIZE_NEAREST_NEIGHBOR",
    KL_QUANTIZED_RESIZE_NEAREST_NEIGHBOR:"KL_QUANTIZED_RESIZE_NEAREST_NEIGHBOR",
    KL_CHANNELWISE_DEQUANTIZE           :"KL_CHANNELWISE_DEQUANTIZE",
    KL_LOGISTIC                         :"KL_LOGISTIC",
    KL_K210_CONV 						:"k210 conv layer",
    KL_K210_ADD_PADDING					:"k210 add padding layer",
    KL_K210_REMOVE_PADDING				:"k210 remove padding layer",
    KL_K210_UPLOAD						:"k210 upload layer"



}


def helper_reg_data(val, map):
	oft = 0
	data = [0]*len(map)
	for i in range(len(map)):
		data[i] = (val>>oft)&((1<<map[i])-1)
		oft = oft + map[i]
	return tuple(data)
		
		
		
class interrupt_enabe:
	def __init__(self, val):
		map=[1,1,1,1,60]; 
		(self.int_en, self.ram_flag, self.full_add, 
		self.depth_wise_layer, self.reserved) = \
		helper_reg_data(val, map)	
	def dump(self):
		for name,value in vars(self).items():
			print('%s=%d'%(name,value),end=' ')
		print("")
		
class image_addr:
	def __init__(self, val):
		map=[15,17,15,17]; 
		(self.image_src_addr, self.reserved0, 
		self.image_dst_addr, self.reserved1) = \
		helper_reg_data(val, map)
	def dump(self):
		for name,value in vars(self).items():
			print('%s=%d'%(name,value),end=' ')
		print("")		
		
class image_channel_num:
	def __init__(self, val):
		map=[10,22,10,6,10,6]; 
		(self.i_ch_num, self.reserved0, self.o_ch_num, 
		self.reserved1, self.o_ch_num_coef, self.reserved2) = \
		helper_reg_data(val, map)	
	def dump(self):
		for name,value in vars(self).items():
			print('%s=%d'%(name,value),end=' ')
		print("")	
		
class image_size:
	def __init__(self, val):
		map=[10,9,13,10,9,13]; 
		(self.i_row_wid, self.i_col_high, self.reserved0, 
		self.o_row_wid, self.o_col_high, self.reserved1) = \
		helper_reg_data(val, map)			
	def dump(self):
		for name,value in vars(self).items():
			print('%s=%d'%(name,value),end=' ')
		print("")	


		
class kernel_pool_type_cfg:
	def __init__(self, val):
		map=[3,1,4,1,1,1,5,8,8,32]; 
		(self.kernel_type, self.pad_type, self.pool_type, 
		self.first_stride, self.bypass_conv, self.load_para,
		self.reserved0, self.dma_burst_size, self.pad_value,
		bwsx_base_addr) = \
		helper_reg_data(val, map)
	def dump(self):
		for name,value in vars(self).items():
			print('%s=%d'%(name,value),end=' ')
		print("")	
		
class kernel_load_cfg:
	def __init__(self, val):
		map=[1,6,8,17,32]; 
		(self.load_coor, self.load_time, self.reserved0, 
		self.para_size, self.para_start_addr) = \
		helper_reg_data(val, map)			
	def dump(self):
		for name,value in vars(self).items():
			print('%s=%d'%(name,value),end=' ')
		print("")	
		
class kernel_offset:
	def __init__(self, val):
		map=[4,12,48]; 
		(self.coef_column_offset, self.coef_row_offset, self.reserved0) = \
		helper_reg_data(val, map)	
	def dump(self):
		for name,value in vars(self).items():
			print('%s=%d'%(name,value),end=' ')
		print("")

		
class kernel_calc_type_cfg:
	def __init__(self, val):
		map=[15,1,4,8,3,1,32]; 
		(self.channel_switch_addr, self.reserved, self.row_switch_addr, 
		self.coef_size, self.coef_group, self.load_act,
		self.active_addr) = \
		helper_reg_data(val, map)	
	def dump(self):
		for name,value in vars(self).items():
			print('%s=%d'%(name,value),end=' ')
		print("")
	
class write_back_cfg:
	def __init__(self, val):
		map=[15,1,4,3,41]; 
		(self.wb_channel_switch_addr, self.reserved0, self.wb_row_switch_addr, 
		self.wb_group, self.reserved1) = \
		helper_reg_data(val, map)			
	def dump(self):
		for name,value in vars(self).items():
			print('%s=%d'%(name,value),end=' ')
		print("")
		
		
class conv_value:
	def __init__(self, val):
		map=[4,4,24,24,8]; 
		(self.shr_w, self.shr_x, self.arg_w, self.arg_x, self.reserved0) = \
		helper_reg_data(val, map)
	def dump(self):
		for name,value in vars(self).items():
			print('%s=%d'%(name,value),end=' ')
		print("")
		
		
class conv_value2:
	def __init__(self, val):
		map=[40,24]; 
		(self.arg_add, self.reserved) = \
		helper_reg_data(val, map)
	def dump(self):
		for name,value in vars(self).items():
			print('%s=%d'%(name,value),end=' ')
		print("")

class dma_parameter:
	def __init__(self, val):
		map=[1,15,16,32]; 
		(self.send_data_out, self.reserved,
		self.channel_byte_num,self.dma_total_byte) = \
		helper_reg_data(val, map)
	def dump(self):
		for name,value in vars(self).items():
			print('%s=%d'%(name,value),end=' ')
		print("")
		
