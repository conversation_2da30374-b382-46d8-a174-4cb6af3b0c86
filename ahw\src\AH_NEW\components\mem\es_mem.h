/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_mem.h
** bef: define the interface for memory management. 
** auth: lines<<EMAIL>>
** create on 2022.09.02
*/

#ifndef _ES_MEM_H_
#define _ES_MEM_H_

#ifdef __cplusplus 
extern "C" { 
#endif

#include "es_types.h"

#define ES_MEM_WRAPPER_DEBUG        (0)


ES_S32 es_mem_init(ES_VOID);

#if ES_MEM_WRAPPER_DEBUG
#define es_malloc(size) es_malloc_wrapper(size, __FILE__, __LINE__)
ES_VOID *es_malloc_wrapper(ES_U32 size, char *file, int line);
#else
ES_VOID *es_malloc(ES_U32 size);
#endif

ES_VOID *es_realloc(ES_VOID *ptr, ES_U32 size);

ES_VOID es_free(ES_VOID *p);

#ifdef __cplusplus
}
#endif
#endif