/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_circle_buffer.h
** bef: define the interface for flash. 
** auth: lines<<EMAIL>>
** create on 2019.11.16 
*/

#ifndef _ES_FLASH_H_
#define _ES_FLASH_H_

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */


#include "es_types.h"

typedef enum {
    ES_FLASH_ID_BLE_MAC,
    ES_FLASH_ID_NFC,
    ES_FLASH_ID_USER_DATA,
    ES_FLASH_ID_COUNT
} es_flash_id_e;

typedef struct {
    ES_U32 count;
    ES_BYTE mac[ES_FLASH_BLE_MAC_COUNT][ES_BLE_MAC_LEN];
    ES_BYTE data[ES_FLASH_BLE_MAC_COUNT][ES_BLE_PAYLOAD_DATA_LEN];
    ES_BYTE serv_uuid[ES_FLASH_BLE_MAC_COUNT][ES_BLE_UUID_LEN];
    ES_BYTE char_uuid[ES_FLASH_BLE_MAC_COUNT][ES_BLE_UUID_LEN];
    ES_U32 data_len[ES_FLASH_BLE_MAC_COUNT];
} es_flash_ble_mac_t;

typedef struct {
    ES_U32 count;
    ES_BYTE data[ES_FLASH_NFC_COUNT][ES_NFC_ID_DATA_LEN];
} es_flash_nfc_t;

typedef struct {
    ES_BYTE qrcode[ES_FLASH_USER_QRCODE_LEN];
} es_flash_user_data_t;

ES_S32 es_flash_init(ES_VOID);

ES_S32 es_flash_read(es_flash_id_e id, ES_VOID *data);

ES_S32 es_flash_write(es_flash_id_e id, ES_VOID *data);

// just for nfc
ES_S32 es_flash_get_data_handle(es_flash_id_e id, ES_VOID **data);

ES_S32 es_flash_sync(ES_VOID);

ES_U32 es_flash_run(ES_VOID);

#ifdef __cplusplus 
}
#endif
#endif
