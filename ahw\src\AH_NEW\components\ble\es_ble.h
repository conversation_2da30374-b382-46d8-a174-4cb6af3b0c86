/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_uart.h
** bef: define the interface ble. 
** auth: lines<<EMAIL>>
** create on 2020.05.06
*/

#ifndef _ES_BLE_H_
#define _ES_BLE_H_

#ifdef __cplusplus 
extern "C" { 
#endif

#include "es_types.h"

#define ES_BLE_DATA_LEN                     (256)

typedef struct {
    ES_BYTE mac[ES_BLE_MAC_LEN];   /* the length is ES_BLE_MAC_LEN, 6. */
    ES_BYTE data[ES_BLE_DATA_LEN];
    ES_BYTE serv_uuid[ES_BLE_UUID_LEN]; // 128 bit, 16 hex
    ES_BYTE char_uuid[ES_BLE_UUID_LEN]; // 128 bit, 16 hex
    ES_U16 data_len;
    ES_U8 to_slave;     // 1: send to slave, 0: send to master
    ES_U8 is_ack;       // ack message. 
} es_ble_msg_t;

typedef enum {
    ES_BLE_MASTER,
    ES_BLE_SLAVE
} es_ble_type_e;


ES_S32 es_ble_init(void);

ES_S32 es_ble_add_msg(const es_ble_msg_t *msg);

ES_VOID es_ble_run(ES_VOID);

ES_S32 es_ble_read_mac(ES_BYTE *mac);

ES_BOOL es_ble_is_connected(ES_VOID);

ES_S32 es_ble_send_payload_from_serv(const ES_VOID *json_obj);

#ifdef __cplusplus 
}
#endif

#endif