#include "es_inc.h"

// #define APP_MAIN_DEBUG
#ifdef APP_MAIN_DEBUG
#define app_main_debug es_log_info
#define app_main_error es_log_error
#else
#define app_main_debug(...)
#define app_main_error(...)
#endif


int main(void)
{
    if (ES_RET_SUCCESS != es_app_init()) {
        app_main_debug("system fail, exit");
        while(1);
    }

    es_app_run();
    app_main_debug("system exit");

    return 0;
}