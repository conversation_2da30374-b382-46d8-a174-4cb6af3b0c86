/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_relay.h
** bef: define the interface for relay. 
** auth: lines<<EMAIL>>
** create on 2022.01.08
*/

#ifndef _ES_RELAY_H_
#define _ES_RELAY_H_

#ifdef __cplusplus 
extern "C" { 
#endif

#include "es_types.h"


ES_S32 es_relay_init(ES_VOID);

ES_VOID es_relay_run(ES_VOID);

ES_S32 es_relay_open(ES_U16 doing_ms);

ES_S32 es_relay_open_always(ES_VOID);

ES_S32 es_relay_close_always(ES_VOID);

#ifdef __cplusplus 
}
#endif

#endif
