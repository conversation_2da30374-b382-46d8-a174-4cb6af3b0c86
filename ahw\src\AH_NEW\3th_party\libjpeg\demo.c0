#include "libjpeg.h"

#include "sysctl.h"

uint64_t t;

uint64_t jpeg_commpress_size = 30 * 1024;
uint8_t *jpeg_commpress_buf = NULL;

jpeg_commpress_buf = malloc(jpeg_commpress_size);

if(NULL == jpeg_commpress_buf) {
    printk("malloc failed\r\n");
    return 1;
}

/* 压缩图片 */
jpeg_img_t compress = {.w = 240, .h = 240, .bpp = 3, .data = gImage_img};
uint8_t quality = 50;

t = sysctl_get_time_us();
libjpeg_compress(&compress, quality, &jpeg_commpress_buf, &jpeg_commpress_size);
t = sysctl_get_time_us() - t;

printk("compress use %ld us / (%ld ms), w:%d, h:%d, size:%ld @ quality:%d\r\n", t, (t / 1000), compress.w, compress.h, jpeg_commpress_size, quality);
if(jpeg_commpress_buf) {
    free(jpeg_commpress_buf);
    jpeg_commpress_buf = NULL;
}

/* 解压图片 */
jpeg_img_t decompress;

t = sysctl_get_time_us();
libjpeg_decompress(&decompress, jpeg_commpress_buf, jpeg_commpress_size);
t = sysctl_get_time_us() - t;

printk("decompress use %ld us / (%ld ms), w:%d, h:%d, size:%d\r\n", t, (t / 1000), decompress.w, decompress.h, decompress.bpp);

uint8_t *buf = decompress.data;
uint32_t cnt = 0;
for(uint32_t i = 0; i < (decompress.w * decompress.h); i ++) {
    unsigned short color = RGB888ToRGB565(buf[i * 3 + 0] << 16 | buf[i * 3 + 1] << 8 | buf[i * 3 + 2]);
    buf[cnt++] = color >> 8 & 0xff;
    buf[cnt++] = color & 0xff;
}
convert_jpeg_order(decompress.data, decompress.w, decompress.h);
lcd_draw_picture(0, 0, decompress.w, decompress.h, decompress.data);

if(decompress.data) {
    free(decompress.data);
    decompress.data = NULL;
}

