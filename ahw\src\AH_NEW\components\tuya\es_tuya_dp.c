/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_tuya_dp.c
** bef: define the interface for data point.
** auth: lines<<EMAIL>>
** create on 2021.09.28 
*/

#include "es_inc.h"

#if ES_TUYA_MODULE_ENABLE
#include "facelib_inc.h"
#include "tuya_mcu_api.h"
#include "tuya_wifi.h"

// #define ES_TUYA_DP_DEBUG
#ifdef ES_TUYA_DP_DEBUG
#define es_tuya_dp_debug es_log_info
#define es_tuya_dp_error es_log_error
#else
#define es_tuya_dp_debug(...)
#define es_tuya_dp_error(...)
#endif

static int es_dp_save_brd_cfg(void)
{
    es_task_param_t task_param;

    task_param.type = ES_TASK_SAVE_BRD_CFG;
    task_param.param = ES_NULL;
    task_param.timeout = 0;

    return es_task_queue_push_wait(&task_param);
}

static int es_dp_upload_cmd_sync(cJSON *root, int ret)
{
    es_tuya_dp_data_t sync_dp;
    cJSON *sn = ES_NULL;
    char sn_val[32] = {0};

    sn = cJSON_GetObjectItem(root, "sn");
    if (sn == ES_NULL) {
        return ES_RET_FAILURE;
    }

    if (cJSON_String == sn->type) {
        snprintf(sn_val, 32, "\"%s\"", sn->valuestring);
    } else if (cJSON_Number == sn->type) {
        int64_t val = (int64_t)sn->valueint;
        snprintf(sn_val, 32, "%ld", val);
    } else {
        es_tuya_dp_error("sn type error");
        return ES_RET_FAILURE;
    }

    memset(&sync_dp, 0x00, sizeof(sync_dp));
    sync_dp.dp_id = DPID_CMD_SYNC;
    sync_dp.dp_type = ES_TUYA_DP_TYPE_STRING;

    if (0 == ret) {
        sync_dp.data_len = snprintf((char *)sync_dp.data, ES_DP_DATA_BUF_LEN, 
                "{\"sn\":%s,\"success\":1,\"message\":\"success\"}", sn_val);
    } else {
        sync_dp.data_len = snprintf((char *)sync_dp.data, ES_DP_DATA_BUF_LEN, 
                "{\"sn\":%s,\"success\":0,\"message\":\"failure\"}", sn_val);
    }

    es_tuya_dp_debug("sync_dp.data:%s", sync_dp.data);
    return es_tuya_port_add_dp((const es_tuya_dp_data_t*)&sync_dp);
}

int es_dp_recv_sync_time_ret(const unsigned char value[], unsigned short length)
{
    es_time_t sync_time;

    if (8 != length) {
        return -1;
    }

    memset(&sync_time, 0x00, sizeof(sync_time));
    sync_time.year = 2000 + (int)value[1];
    sync_time.mon = (int)value[2];
    sync_time.mday = (int)value[3];
    sync_time.hour = (int)value[4];
    sync_time.min = (int)value[5];
    sync_time.sec = (int)value[6];

    if (sync_time.year < 2022) {
        return 0;
    }

    es_tuya_port_sync_time_ok();
    es_time_sync(&sync_time);

    return 0;
}




static int es_dp_pass_info_add(cJSON *data)
{
    es_task_param_t task_param;

    es_memset(&task_param, 0x00, sizeof(task_param));
    task_param.type = ES_TASK_PARSE_FACE_TUYA_ADD;
    task_param.param = (ES_VOID *)data;
    task_param.timeout = 0;
    return es_task_queue_push_wait((const es_task_param_t *)&task_param);
}

static int es_dp_pass_info_del(cJSON *data)
{
    es_task_param_t task_param;

    es_memset(&task_param, 0x00, sizeof(task_param));
    task_param.type = ES_TASK_PARSE_FACE_TUYA_DEL;
    task_param.param = (ES_VOID *)data;
    task_param.timeout = 0;
    return es_task_queue_push_wait((const es_task_param_t *)&task_param);
}

// {
// 	"data": {
// 		"faceId": "1450280916004896784",
// 		"featureCont": "/fIOA+cIDywt+Oo7+t8aDRAcDf7h1STeF+T5/gQxDk70IOPe6Soh/Q6MJAgmDc0ZCvcI+A3/jAn6Ah0o5bQC+/b9AgEn5xfe5dQW+xO7H9j9LfzK0tcp3y/xHwg59dnQGgwFERQ56DQxyuLXLAHJ+K/jANUQ2jsFBdcJzRcB7fW8rQ4zEhTSFgTXzeX41Cg4kP7rOs5fIO81D/UzMw9H+Swf6BAMAen3+gTs0+4O+xTJ7eLyMBAu5ggG+entAwC4Grrt+A==",
// 		"uid": "ubay1634126568561KVt3",
// 		"url": "https://tuya-biz-data-1254153901.cos.ap-shanghai.myqcloud.com/microapp/1634270460d348e9c7f7b.jpg?sign=q-sign-algorithm%3Dsha1%26q-ak%3DAKIDopcCYgw0qRoyV5qfKjvg2pPkqESnb5zI%26q-sign-time%3D1634608889%3B1634612489%26q-key-time%3D1634608889%3B1634612489%26q-header-list%3Dhost%26q-url-param-list%3D%26q-signature%3De6061d432ebc795aa7f9796ac53a5f0671980b07&imageMogr2/format/jpeg/quality/50/thumbnail/1024x1024/thumbnail/5000000@/auto-orient"
// 	},
// 	"gateway": false,
// 	"mode": "dc_faceInfo",
// 	"sn": 1450280916113948753,
// 	"type": "add"
// }
int es_dp_set_pass_info(unsigned char *value, unsigned short length)
{
    cJSON *root = ES_NULL;
    cJSON *data = ES_NULL;
    cJSON *json_tmp = ES_NULL;
    int ret = 0;

    value[length] = 0;
    es_tuya_dp_debug("pass set(length:%d):%s", length, value);

    root = cJSON_Parse((const char *)value);
    if (root == ES_NULL) {
        es_tuya_dp_debug("json parser fail");
        return ES_RET_FAILURE;
    }

    data = cJSON_GetObjectItem(root, "data");
    if (data == ES_NULL) {
        ret = -1;
        es_tuya_dp_debug("get data field fail");
        goto END;
    }


    json_tmp = cJSON_GetObjectItem(root, "type");
    if (json_tmp == ES_NULL) {
        ret = -1;
        es_tuya_dp_debug("get type field fail");
        goto END;
    }

    if (cJSON_String != json_tmp->type) {
        ret = -1;
        es_tuya_dp_debug("type field must been string");
        goto END;
    }

    // es_tuya_dp_debug("type:%s", json_tmp->valuestring);
    if (0 == strncmp("add", json_tmp->valuestring, 3)) {
        ret = es_dp_pass_info_add(data);
    } else if (0 == strncmp("del",json_tmp->valuestring, 3)) {
        ret = es_dp_pass_info_del(data);
    } else if (0 == strncmp("update",json_tmp->valuestring, 3)) {
        // ret = es_dp_pass_info_del(data);
        ret = es_dp_pass_info_add(data);
    } else {
        ret = -1;
        es_tuya_dp_debug("unknown type(%s) fail", json_tmp->valuestring);
        goto END;
    }
    
    
END:
    ret = es_dp_upload_cmd_sync(root, ret);

    if (root) {
        cJSON_Delete(root);
        root = ES_NULL;
    }
    return ret;
}

int es_dp_open_door(const unsigned char *value, unsigned short length)
{
    int ret = 0;
    cJSON *root = ES_NULL;

    root = cJSON_Parse((const char *)value);
    if (root == ES_NULL) {
        es_tuya_dp_debug("json parser fail");
        return ES_RET_FAILURE;
    }

    ret = es_relay_open((ES_U16)(mf_brd.cfg.relay.opent*1000));;
    es_tuya_dp_debug("open door");

    ret = es_dp_upload_cmd_sync(root, ret);
    if (root) {
        cJSON_Delete(root);
        root = ES_NULL;
    }
    return ret;
}

int es_dp_send_dev_info(void)
{
    es_tuya_dp_data_t dp_data;

    memset(&dp_data, 0x00, sizeof(dp_data));
    sprintf((char *)dp_data.data, "{\"sver\":\"%s\",\"hver\":\"%s\",\"mode\":\"%s\"}", 
                ES_SYS_SVER_STR, ES_SYS_HVER_STR, ES_BRD_MODE);
    es_tuya_dp_debug("device info :%s", dp_data.data);

    dp_data.dp_id = DPID_MASTER_INFORMATION;
    dp_data.dp_type = ES_TUYA_DP_TYPE_RAW;
    dp_data.data_len = strlen((char *)dp_data.data);
    return es_tuya_port_add_dp((const es_tuya_dp_data_t *)&dp_data);
}


int es_dp_send_pass_data(const es_dp_pass_data_t *data)
{
    es_tuya_dp_data_t dp_data;

    memset(&dp_data, 0x00, sizeof(dp_data));


    // sprintf((char *)dp_data.data, "{\"uid\":\"%s\",\"t\":%d000,\"way\":%d,\"imageId\":\"%s\",\"success\":1}", 
    //             data->uid, data->timestamp, data->type, data->data);
    sprintf((char *)dp_data.data, "{\"uid\":\"%s\",\"t\":%d000,\"way\":%d,\"success\":1}", 
                data->uid, data->timestamp, data->type);
    dp_data.dp_id = DPID_PASS_EVENT;
    dp_data.dp_type = ES_TUYA_DP_TYPE_STRING;
    dp_data.data_len = strlen((char *)dp_data.data);
    es_tuya_dp_debug("pass:%s", (char *)dp_data.data);
    return es_tuya_port_add_dp((const es_tuya_dp_data_t *)&dp_data);
}


int es_dp_upload_face_data(unsigned char *uid, unsigned int timestamp, es_dp_face_data_type_e type)
{
    es_dp_pass_data_t pass_data;
    char uid_str[64] = {0};

    if (1 != es_tuya_port_wifi_connected()) {
        return -1;
    }

    // es_utils_byte_to_hex_str(uid_str, uid, 16);
    memcpy(uid_str, uid, FACEDB_UID_LEN);
    memset(&pass_data, 0x00, sizeof(pass_data));
    if (type == ES_DP_FACE_DATA_PASS) {
        pass_data.type = ES_DP_PASS_BY_FACE;
        pass_data.uid = (unsigned char *)uid_str;
        pass_data.data = (unsigned char *)"0";
        pass_data.timestamp = timestamp;
        if (0 != es_dp_send_pass_data((const es_dp_pass_data_t *)&pass_data)) {
            return -1;
        }
    }

    es_tuya_dp_debug("type:%d, uid:%s", type, uid_str);

    return 0;
}

int es_dp_set_recognition_threshold(unsigned int value)
{
    mf_brd.cfg.model.fe_gate = (float)value;
    mf_model.fe_gate = (float)value;

    es_dp_save_brd_cfg();
    es_tuya_dp_debug("set fe_gate:%d", (unsigned int)mf_brd.cfg.model.fe_gate);
    return 0;
}

unsigned int es_dp_get_recognition_threshold(void)
{
    es_tuya_dp_debug("get fe_gate:%d", (unsigned int)mf_brd.cfg.model.fe_gate);
    return (unsigned int)mf_brd.cfg.model.fe_gate;
}

int es_dp_set_face_threshold(unsigned int value)
{
    mf_brd.cfg.model.fd_gate = (float)value;
    mf_model.fd_gate = (float)value;

    es_dp_save_brd_cfg();
    es_tuya_dp_debug("set fd_gate:%d", (unsigned int)mf_brd.cfg.model.fd_gate);
    return 0;
}

unsigned int es_dp_get_face_threshold(void)
{
    es_tuya_dp_debug("get fd_gate:%d", (unsigned int)mf_brd.cfg.model.fd_gate);
    return (unsigned int)mf_brd.cfg.model.fd_gate;
}

int es_dp_set_living_threshold(unsigned int value)
{
    mf_brd.cfg.model.live_gate = (float)value;
    mf_model.live_gate = (float)value;

    es_dp_save_brd_cfg();
    es_tuya_dp_debug("set live_gate:%d", (unsigned int)mf_brd.cfg.model.live_gate);
    return 0;
}

unsigned int es_dp_get_living_threshold(void)
{
    es_tuya_dp_debug("get live_gate:%d", (unsigned int)mf_brd.cfg.model.live_gate);
    return (unsigned int)mf_brd.cfg.model.live_gate;
}

int es_dp_set_led_brightness(unsigned int value)
{
    if (value >= 10) {
        value = 9;
    } else if (0 == value) {
        value = 1;
    }

    mf_brd.cfg.led.low = (float)(((float)value) / 10.0);
    es_dp_save_brd_cfg();
    es_tuya_dp_debug("set led_brightness:%d", value);
    return 0;
}

unsigned int es_dp_get_led_brightness(void)
{
    unsigned int value = 1;

    value = (unsigned int)(mf_brd.cfg.led.low * 10.0);
    if (value >= 10) {
        value = 9;
    } else if (0 == value) {
        value = 1;
    }
    es_tuya_dp_debug("get led_brightness:%d", value);
    return value;
}

int es_dp_set_led_brightness_offset(unsigned int value)
{
     if (value >= 10) {
        value = 9;
    } else if (0 == value) {
        value = 1;
    }

    mf_brd.cfg.led.high = (float)(((float)value) / 10.0);
    es_dp_save_brd_cfg();
    es_tuya_dp_debug("set led_brightness_offset:%d", value);
    return 0;
}

unsigned int es_dp_get_led_brightness_offset(void)
{
    unsigned int value = 1;

    value = (unsigned int)(mf_brd.cfg.led.high * 10.0);
    if (value >= 10) {
        value = 9;
    } else if (0 == value) {
        value = 1;
    }
    es_tuya_dp_debug("get led_brightness_offset:%d", value);
    return value;
}

int es_dp_set_door_opening_duration(unsigned int value)
{
    if (value < 1) {
        value = 1;
    } if (value > 60) {
        value = 60;
    }

    mf_brd.cfg.relay.opent = value;
    es_dp_save_brd_cfg();
    es_tuya_dp_debug("set door_opening_duration: %ds", mf_brd.cfg.relay.opent);
    return 0;
}

unsigned int es_dp_get_door_opening_duration(void)
{
    unsigned int value = 1;

    value = (unsigned int)(mf_brd.cfg.relay.opent);
   if (value < 1) {
        value = 1;
    } if (value > 60) {
        value = 60;
    }
    es_tuya_dp_debug("get door_opening_duration:%d", value);
    return value;
}


#endif