#include "es_inc.h"

#if (ES_UI_TYPE == ES_BRD_TYPE_35H)

#include "facelib_inc.h"

// #define ES_UI_INFOBAR_DEBUG
#ifdef ES_UI_INFOBAR_DEBUG
#define es_ui_infobar_debug es_log_info
#define es_ui_infobar_error es_log_error
#else
#define es_ui_infobar_debug(...)
#define es_ui_infobar_error(...)
#endif


static es_time_t ui_date_time;
static ES_U32 face_num = 0;

static lv_obj_t *lv_infobar_bg = ES_NULL;
static lv_obj_t *label_time = ES_NULL;
static lv_obj_t *label_date = ES_NULL;
static lv_obj_t *label_wday = ES_NULL;
static lv_obj_t *label_face_num = ES_NULL;
static lv_timer_t *infobar_timer = NULL;

static ES_CHAR sver_str_buf[20] = {0};
static ES_CHAR time_str_buf[32] = {0};
static ES_CHAR wday_str_buf[16] = {0};
static ES_CHAR date_str_buf[32] = {0};
static ES_CHAR face_num_str_buf[16] = {0};

static ES_CHAR *wday_str[7] = {"Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"};

static ES_VOID es_ui_infobar_create_widgets(ES_VOID)
{
    lv_obj_t *obj;

    // info bar 
    lv_infobar_bg = lv_obj_create(lv_scr_act());
    lv_obj_remove_style_all(lv_infobar_bg);
    lv_obj_set_size(lv_infobar_bg, 108, 320);
    lv_obj_align(lv_infobar_bg, LV_ALIGN_TOP_LEFT, ES_UI_CAM_WIDTH, 0);

    obj = lv_img_create(lv_infobar_bg);
    lv_obj_align(obj, LV_ALIGN_TOP_LEFT, 0, 0);
    lv_img_set_src(obj, ES_UI_IMG_SRC_BAR);

    obj = lv_label_create(lv_infobar_bg);
    lv_obj_align(obj, LV_ALIGN_BOTTOM_RIGHT, 0, 0);
    lv_label_set_recolor(obj, ES_TRUE);
    es_snprintf(sver_str_buf, sizeof(sver_str_buf), "#ffffff %s#", ES_SYS_SVER_STR);
    lv_label_set_text_static(obj, sver_str_buf);

    label_time = lv_label_create(lv_infobar_bg);
    lv_obj_add_style(label_time, (lv_style_t *)es_ui_font_get_time(), 0);
    lv_obj_align(label_time, LV_ALIGN_TOP_MID, 0, 30);
    lv_label_set_recolor(label_time, ES_TRUE);
    es_snprintf(time_str_buf, sizeof(time_str_buf), "#ffffff %02d:%02d#", ui_date_time.hour, ui_date_time.min);
    lv_label_set_text_static(label_time, time_str_buf);

    label_date = lv_label_create(lv_infobar_bg);
    lv_obj_align(label_date, LV_ALIGN_TOP_MID, 0, 65);
    lv_label_set_recolor(label_date, ES_TRUE);
    es_snprintf(date_str_buf, sizeof(date_str_buf), "#ffffff %04d.%02d.%02d", ui_date_time.year, ui_date_time.mon, ui_date_time.mday);
    lv_label_set_text_static(label_date, date_str_buf);

    label_wday = lv_label_create(lv_infobar_bg);
    lv_obj_align(label_wday, LV_ALIGN_TOP_MID, 0, 90);
    lv_label_set_recolor(label_wday, ES_TRUE);
    es_snprintf(wday_str_buf, sizeof(wday_str_buf), "#ffffff %s#", wday_str[ui_date_time.wday-1]);
    lv_label_set_text_static(label_wday, wday_str_buf);

    label_face_num = lv_label_create(lv_infobar_bg);
    lv_obj_align(label_face_num, LV_ALIGN_TOP_MID, 0, 220);
    lv_label_set_recolor(label_face_num, ES_TRUE);
    es_snprintf(face_num_str_buf, sizeof(face_num_str_buf), "#ffffff %04d#", face_num);
    lv_label_set_text_static(label_face_num, face_num_str_buf);
}


static ES_VOID es_ui_infobar_update_datetime(ES_VOID)
{
    es_time_t now;
    
    if (ES_RET_SUCCESS != es_time_get_now(&now)) {
        es_ui_infobar_debug("get time fail");
        return;
    }

    if (0 == es_memcmp(&now, &ui_date_time, sizeof(now))) {
        return;
    }

    if (now.min != ui_date_time.min) {
        // update time
        es_snprintf(time_str_buf, sizeof(time_str_buf), "#ffffff %02d:%02d#", now.hour&0xFF, now.min&0xFF);
        lv_label_set_text_static(label_time, time_str_buf);

    }

    if (now.mday != ui_date_time.mday) {
        es_snprintf(date_str_buf, sizeof(date_str_buf), "#ffffff %04d.%02d.%02d", ui_date_time.year, ui_date_time.mon, ui_date_time.mday);
        lv_label_set_text_static(label_date, date_str_buf);
    }

    if (now.wday != ui_date_time.wday) {
        // update date and week
        es_snprintf(wday_str_buf, sizeof(wday_str_buf), "#ffffff %s#", wday_str[now.wday-1]);
        lv_label_set_text_static(label_wday, wday_str_buf);
    }

    memcpy(&ui_date_time, &now, sizeof(now));
}

static ES_VOID es_ui_infobar_update_face_num(ES_VOID)
{
    ES_U32 num = 0;

    num = mf_facedb.num();
    if (num == face_num) {
        return;
    }

    face_num = num;
    es_snprintf(face_num_str_buf, sizeof(face_num_str_buf), "#ffffff %04d#", face_num);
    lv_label_set_text_static(label_face_num, face_num_str_buf);
}

static void infobar_timer_cb(lv_timer_t *timer)
{
    es_ui_infobar_update_datetime();
    es_ui_infobar_update_face_num();
}


ES_S32 es_ui_infobar_init(ES_VOID)
{
    memset(&ui_date_time, 0x00, sizeof(ui_date_time));
    if (ES_RET_SUCCESS != es_time_get_now(&ui_date_time)) {
        return ES_RET_FAILURE;
    }

    face_num = mf_facedb.num();

    es_ui_infobar_create_widgets();
    infobar_timer = lv_timer_create(infobar_timer_cb, 1000, NULL);
    if (ES_NULL == infobar_timer) {
        return ES_RET_FAILURE;
    }
    return ES_RET_SUCCESS;
}

ES_S32 es_ui_infobar_show(ES_BOOL show)
{
    if (show) {
        lv_obj_clear_flag(lv_infobar_bg, LV_OBJ_FLAG_HIDDEN);
    } else {
        lv_obj_add_flag(lv_infobar_bg, LV_OBJ_FLAG_HIDDEN);
    }
    return ES_RET_SUCCESS;
}

#endif
