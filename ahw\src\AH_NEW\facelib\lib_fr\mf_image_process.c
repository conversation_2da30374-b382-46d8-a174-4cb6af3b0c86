#include "facelib_inc.h"
#include "es_mem.h"
/* image_process.c */
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/* local */


///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

int image_init(image_t *image)
{ 
    //printk("heap:%ld KB, pic %d*%d*%d, alloc %d KB\r\n", get_free_heap_size() / 1024, 
	// image->width , image->height , image->pixel, 
	// image->width * image->height * image->pixel/1024);
    
    image->addr = es_malloc(image->width * image->height * image->pixel);
    if(image->addr == NULL)
        return -1;
    //FIXME:K210 BUG
    image->addr -= 0x40000000;
    return 0;
}

void image_deinit(image_t *image)
{
    //FIXME:K210 BUG
    image->addr += 0x40000000;
    es_free(image->addr);
}

void image_crop(image_t *image_src, image_t *image_dst, uint16_t x_offset, uint16_t y_offset)
{
    uint8_t *src, *r_src, *g_src, *b_src, *dst, *r_dst, *g_dst, *b_dst;
    uint16_t w_src, h_src, w_dst, h_dst;

    src = image_src->addr + 0x40000000;
    w_src = image_src->width;
    h_src = image_src->height;
    dst = image_dst->addr + 0x40000000;
    w_dst = image_dst->width;
    h_dst = image_dst->height;

    r_src = src + y_offset * w_src + x_offset;
    g_src = r_src + w_src * h_src;
    b_src = g_src + w_src * h_src;
    r_dst = dst;
    g_dst = r_dst + w_dst * h_dst;
    b_dst = g_dst + w_dst * h_dst;

    for(uint16_t y = 0; y < h_dst; y++)
    {
        for(uint16_t x = 0; x < w_dst / 4 * 4;)
        {
            *r_dst++ = r_src[x];
            *g_dst++ = g_src[x];
            *b_dst++ = b_src[x];
            x++;
            *r_dst++ = r_src[x];
            *g_dst++ = g_src[x];
            *b_dst++ = b_src[x];
            x++;
            *r_dst++ = r_src[x];
            *g_dst++ = g_src[x];
            *b_dst++ = b_src[x];
            x++;
            *r_dst++ = r_src[x];
            *g_dst++ = g_src[x];
            *b_dst++ = b_src[x];
            x++;
        }
        for(uint16_t x = w_dst / 4 * 4; x < w_dst; x++)
        {
            *r_dst++ = r_src[x];
            *g_dst++ = g_src[x];
            *b_dst++ = b_src[x];
        }
        r_src += w_src;
        g_src += w_src;
        b_src += w_src;
    }
    memcpy(image_dst->addr, dst, w_dst * h_dst * 3);
}

void image_crop_roate_right90(image_t *image_src, image_t *image_dst, uint16_t x_offset, uint16_t y_offset)
{
    uint8_t *src, *r_src, *g_src, *b_src, *dst, *r_dst, *g_dst, *b_dst;
    uint16_t w_src, h_src, w_dst, h_dst;

    src = image_src->addr + 0x40000000;
    w_src = image_src->width;
    h_src = image_src->height;
    dst = image_dst->addr + 0x40000000;
    w_dst = image_dst->width;
    h_dst = image_dst->height;

    r_src = src + (h_src - x_offset) * w_src + y_offset;
    g_src = r_src + w_src * h_src;
    b_src = g_src + w_src * h_src;

    r_dst = dst;
    g_dst = r_dst + w_dst * h_dst;
    b_dst = g_dst + w_dst * h_dst;

    for(uint16_t y = 0; y < h_dst; y++)
    {
        for(uint16_t x = 0; x < w_dst / 4 * 4;)
        {
            *r_dst++ = *(r_src - x * w_src);
            *g_dst++ = *(g_src - x * w_src);
            *b_dst++ = *(b_src - x * w_src);
            x += 1;
            *r_dst++ = *(r_src - x * w_src);
            *g_dst++ = *(g_src - x * w_src);
            *b_dst++ = *(b_src - x * w_src);
            x += 1;
            *r_dst++ = *(r_src - x * w_src);
            *g_dst++ = *(g_src - x * w_src);
            *b_dst++ = *(b_src - x * w_src);
            x += 1;
            *r_dst++ = *(r_src - x * w_src);
            *g_dst++ = *(g_src - x * w_src);
            *b_dst++ = *(b_src - x * w_src);
            x += 1;
        }
        for(uint16_t x = w_dst / 4 * 4; x < w_dst; x++)
        {
            *r_dst++ = *(r_src - x * w_src);
            *g_dst++ = *(g_src - x * w_src);
            *b_dst++ = *(b_src - x * w_src);
        }
        r_src += 1;
        g_src += 1;
        b_src += 1;
    }
    memcpy(image_dst->addr, dst, w_dst * h_dst * 3);
}

void image_crop_roate_left_90(image_t *image_src, image_t *image_dst, uint16_t x_offset, uint16_t y_offset)
{
    uint8_t *src, *r_src, *g_src, *b_src, *dst, *r_dst, *g_dst, *b_dst;
    uint16_t w_src, h_src, w_dst, h_dst;

    src = image_src->addr + 0x40000000;
    w_src = image_src->width;
    h_src = image_src->height;
    dst = image_dst->addr + 0x40000000;
    w_dst = image_dst->width;
    h_dst = image_dst->height;

    r_src = src + x_offset * w_src + (w_src - y_offset);
    g_src = r_src + w_src * h_src;
    b_src = g_src + w_src * h_src;

    r_dst = dst;
    g_dst = r_dst + w_dst * h_dst;
    b_dst = g_dst + w_dst * h_dst;

    for(uint16_t y = 0; y < h_dst; y++)
    {
        for(uint16_t x = 0; x < w_dst / 4 * 4;)
        {
            *r_dst++ = *(r_src + x * w_src);
            *g_dst++ = *(g_src + x * w_src);
            *b_dst++ = *(b_src + x * w_src);
            x++;
            *r_dst++ = *(r_src + x * w_src);
            *g_dst++ = *(g_src + x * w_src);
            *b_dst++ = *(b_src + x * w_src);
            x++;
            *r_dst++ = *(r_src + x * w_src);
            *g_dst++ = *(g_src + x * w_src);
            *b_dst++ = *(b_src + x * w_src);
            x++;
            *r_dst++ = *(r_src + x * w_src);
            *g_dst++ = *(g_src + x * w_src);
            *b_dst++ = *(b_src + x * w_src);
            x++;
        }
        for(uint16_t x = w_dst / 4 * 4; x < w_dst; x++)
        {
            *r_dst++ = *(r_src + x * w_src);
            *g_dst++ = *(g_src + x * w_src);
            *b_dst++ = *(b_src + x * w_src);
        }
        r_src -= 1;
        g_src -= 1;
        b_src -= 1;
    }
}

#define IMG_RESIZE_UNIT()                                                                                  \
    {                                                                                                      \
        x_src = (x + 0.5f) * w_scale - 0.5f;                                                               \
        x1 = (uint16_t)x_src;                                                                              \
        x2 = x1 + 1;                                                                                       \
        y_src = (y + 0.5f) * h_scale - 0.5f;                                                               \
        y1 = (uint16_t)y_src;                                                                              \
        y2 = y1 + 1;                                                                                       \
        if(x2 >= w_src || y2 >= h_src)                                                                     \
        {                                                                                                  \
            *(r_dst + x + y * w_dst) = *(r_src + x1 + y1 * w_src);                                         \
            *(g_dst + x + y * w_dst) = *(g_src + x1 + y1 * w_src);                                         \
            *(b_dst + x + y * w_dst) = *(b_src + x1 + y1 * w_src);                                         \
            /*continue;*/                                                                                  \
        } else                                                                                             \
        {                                                                                                  \
            temp1 = (x2 - x_src) * *(r_src + x1 + y1 * w_src) + (x_src - x1) * *(r_src + x2 + y1 * w_src); \
            temp2 = (x2 - x_src) * *(r_src + x1 + y2 * w_src) + (x_src - x1) * *(r_src + x2 + y2 * w_src); \
            *(r_dst + x + y * w_dst) = (uint8_t)((y2 - y_src) * temp1 + (y_src - y1) * temp2);             \
            temp1 = (x2 - x_src) * *(g_src + x1 + y1 * w_src) + (x_src - x1) * *(g_src + x2 + y1 * w_src); \
            temp2 = (x2 - x_src) * *(g_src + x1 + y2 * w_src) + (x_src - x1) * *(g_src + x2 + y2 * w_src); \
            *(g_dst + x + y * w_dst) = (uint8_t)((y2 - y_src) * temp1 + (y_src - y1) * temp2);             \
            temp1 = (x2 - x_src) * *(b_src + x1 + y1 * w_src) + (x_src - x1) * *(b_src + x2 + y1 * w_src); \
            temp2 = (x2 - x_src) * *(b_src + x1 + y2 * w_src) + (x_src - x1) * *(b_src + x2 + y2 * w_src); \
            *(b_dst + x + y * w_dst) = (uint8_t)((y2 - y_src) * temp1 + (y_src - y1) * temp2);             \
        }                                                                                                  \
        x++;                                                                                               \
    }

void image_resize(image_t *image_src, image_t *image_dst)
{
    uint16_t x1, x2, y1, y2;
    float w_scale, h_scale;
    float temp1, temp2;
    float x_src, y_src;

    uint8_t *r_src, *g_src, *b_src, *r_dst, *g_dst, *b_dst;
    uint16_t w_src, h_src, w_dst, h_dst;

    w_src = image_src->width;
    h_src = image_src->height;
    r_src = image_src->addr + 0x40000000;
    g_src = r_src + w_src * h_src;
    b_src = g_src + w_src * h_src;
    w_dst = image_dst->width;
    h_dst = image_dst->height;
    r_dst = image_dst->addr + 0x40000000;
    g_dst = r_dst + w_dst * h_dst;
    b_dst = g_dst + w_dst * h_dst;

    w_scale = (float)w_src / w_dst;
    h_scale = (float)h_src / h_dst;

    for(uint16_t y = 0; y < h_dst; y++)
    {
        for(uint16_t x = 0; x < w_dst;)
        {
            IMG_RESIZE_UNIT();
        }
    }
    memcpy(image_dst->addr, r_dst, w_dst * h_dst * 3); //sync cache
}

static void svd22(const float a[4], float u[4], float s[2], float v[4])
{
    s[0] = (sqrt(pow(a[0] - a[3], 2) + pow(a[1] + a[2], 2)) + sqrt(pow(a[0] + a[3], 2) + pow(a[1] - a[2], 2))) / 2;
    s[1] = fabs(s[0] - sqrt(pow(a[0] - a[3], 2) + pow(a[1] + a[2], 2)));
    v[2] = (s[0] > s[1]) ? sin((atan2(2 * (a[0] * a[1] + a[2] * a[3]), a[0] * a[0] - a[1] * a[1] + a[2] * a[2] - a[3] * a[3])) / 2) : 0;
    v[0] = sqrt(1 - v[2] * v[2]);
    v[1] = -v[2];
    v[3] = v[0];
    u[0] = (s[0] != 0) ? -(a[0] * v[0] + a[1] * v[2]) / s[0] : 1;
    u[2] = (s[0] != 0) ? -(a[2] * v[0] + a[3] * v[2]) / s[0] : 0;
    u[1] = (s[1] != 0) ? (a[0] * v[1] + a[1] * v[3]) / s[1] : -u[2];
    u[3] = (s[1] != 0) ? (a[2] * v[1] + a[3] * v[3]) / s[1] : u[0];
    v[0] = -v[0];
    v[2] = -v[2];
}

static float umeyama_args[] =
    {
#define PIC_SIZE 128
        38.2946f * PIC_SIZE / 112, 51.6963f * PIC_SIZE / 112,
        73.5318f * PIC_SIZE / 112, 51.5014f * PIC_SIZE / 112,
        56.0252f * PIC_SIZE / 112, 71.7366f * PIC_SIZE / 112,
        41.5493f * PIC_SIZE / 112, 92.3655f * PIC_SIZE / 112,
        70.7299f * PIC_SIZE / 112, 92.2041f * PIC_SIZE / 112};

void image_umeyama(float *src, float *dst)
{
#define SRC_NUM 5
#define SRC_DIM 2
    int i, j, k;
    float src_mean[SRC_DIM] = {0.0f};
    float dst_mean[SRC_DIM] = {0.0f};
    for(i = 0; i < SRC_NUM * 2; i += 2)
    {
        src_mean[0] += umeyama_args[i];
        src_mean[1] += umeyama_args[i + 1];
        dst_mean[0] += src[i];
        dst_mean[1] += src[i + 1];
    }
    src_mean[0] /= SRC_NUM;
    src_mean[1] /= SRC_NUM;
    dst_mean[0] /= SRC_NUM;
    dst_mean[1] /= SRC_NUM;

    float src_demean[SRC_NUM][2] = {0.0f};
    float dst_demean[SRC_NUM][2] = {0.0f};

    for(i = 0; i < SRC_NUM; i++)
    {
        src_demean[i][0] = umeyama_args[2 * i] - src_mean[0];
        src_demean[i][1] = umeyama_args[2 * i + 1] - src_mean[1];
        dst_demean[i][0] = src[2 * i] - dst_mean[0];
        dst_demean[i][1] = src[2 * i + 1] - dst_mean[1];
    }

    float A[SRC_DIM][SRC_DIM] = {0.0f};
    for(i = 0; i < SRC_DIM; i++)
    {
        for(k = 0; k < SRC_DIM; k++)
        {
            for(j = 0; j < SRC_NUM; j++)
            {
                A[i][k] += dst_demean[j][i] * src_demean[j][k];
            }
            A[i][k] /= SRC_NUM;
        }
    }

    float(*T)[SRC_DIM + 1] = (float(*)[SRC_DIM + 1]) dst;
    T[0][0] = 1;
    T[0][1] = 0;
    T[0][2] = 0;
    T[1][0] = 0;
    T[1][1] = 1;
    T[1][2] = 0;
    T[2][0] = 0;
    T[2][1] = 0;
    T[2][2] = 1;

    float U[SRC_DIM][SRC_DIM] = {0};
    float S[SRC_DIM] = {0};
    float V[SRC_DIM][SRC_DIM] = {0};
    svd22(&A[0][0], &U[0][0], S, &V[0][0]);

    T[0][0] = U[0][0] * V[0][0] + U[0][1] * V[1][0];
    T[0][1] = U[0][0] * V[0][1] + U[0][1] * V[1][1];
    T[1][0] = U[1][0] * V[0][0] + U[1][1] * V[1][0];
    T[1][1] = U[1][0] * V[0][1] + U[1][1] * V[1][1];

    float scale = 1.0f;
    float src_demean_mean[SRC_DIM] = {0.0f};
    float src_demean_var[SRC_DIM] = {0.0f};
    for(i = 0; i < SRC_NUM; i++)
    {
        src_demean_mean[0] += src_demean[i][0];
        src_demean_mean[1] += src_demean[i][1];
    }
    src_demean_mean[0] /= SRC_NUM;
    src_demean_mean[1] /= SRC_NUM;

    for(i = 0; i < SRC_NUM; i++)
    {
        src_demean_var[0] += (src_demean_mean[0] - src_demean[i][0]) * (src_demean_mean[0] - src_demean[i][0]);
        src_demean_var[1] += (src_demean_mean[1] - src_demean[i][1]) * (src_demean_mean[1] - src_demean[i][1]);
    }
    src_demean_var[0] /= (SRC_NUM);
    src_demean_var[1] /= (SRC_NUM);
    scale = 1.0f / (src_demean_var[0] + src_demean_var[1]) * (S[0] + S[1]);
    T[0][2] = dst_mean[0] - scale * (T[0][0] * src_mean[0] + T[0][1] * src_mean[1]);
    T[1][2] = dst_mean[1] - scale * (T[1][0] * src_mean[0] + T[1][1] * src_mean[1]);
    T[0][0] *= scale;
    T[0][1] *= scale;
    T[1][0] *= scale;
    T[1][1] *= scale;
}

void image_similarity(image_t *image_src, image_t *image_dst, float *T)
{
    //相似变换
    int width = image_src->width;
    int height = image_src->height;
    int channels = image_src->pixel;
    int step = width;
    int color_step = width * height;
    int sim_step;
    int i, j, k;

    //初始化处理后图片的信息
    image_dst->pixel = channels;
    image_dst->width = 128;
    image_dst->height = 128;
    int sim_color_step = image_dst->width * image_dst->height;
    sim_step = image_dst->width;
    image_dst->addr = es_malloc(image_dst->width * image_dst->height * image_dst->pixel);
    //FIXME:K210 BUG
    //image_dst->addr -= 0x40000000;
    image_src->addr += 0x40000000;
    //初始化图像
    memset(image_dst->addr, 0, image_dst->width * image_dst->height * image_dst->pixel);

    int pre_x, pre_y /*, after_x, after_y*/; //缩放前对应的像素点坐标
    int x, y;
    unsigned short color[2][2];
    float(*TT)[3] = (float(*)[3])T;
    for(i = 0; i < image_dst->height; i++)
    {
        for(j = 0; j < image_dst->width; j++)
        {
            pre_x = (int)(TT[0][0] * (j << 8) + TT[0][1] * (i << 8) + TT[0][2] * (1 << 8));
            pre_y = (int)(TT[1][0] * (j << 8) + TT[1][1] * (i << 8) + TT[1][2] * (1 << 8));

            y = pre_y & 0xFF;
            x = pre_x & 0xFF;
            pre_x >>= 8;
            pre_y >>= 8;
            if(pre_x < 0 || pre_x > (width - 1) || pre_y < 0 || pre_y > (height - 1))
                continue;
            for(k = 0; k < channels; k++)
            {
                color[0][0] = image_src->addr[pre_y * step + pre_x + k * color_step];
                color[1][0] = image_src->addr[pre_y * step + (pre_x + 1) + k * color_step];
                color[0][1] = image_src->addr[(pre_y + 1) * step + pre_x + k * color_step];
                color[1][1] = image_src->addr[(pre_y + 1) * step + (pre_x + 1) + k * color_step];
                int final = (0x100 - x) * (0x100 - y) * color[0][0] + x * (0x100 - y) * color[1][0] + (0x100 - x) * y * color[0][1] + x * y * color[1][1];
                final = final >> 16;
                image_dst->addr[i * sim_step + j + k * sim_color_step] = final;
            }
        }
    }
    image_dst->addr -= 0x40000000;
    image_src->addr -= 0x40000000;
}

void image_similarity_roate_right90(image_t *image_src, image_t *image_dst, float *T)
{
    //相似变换
    int width = image_src->width;
    int height = image_src->height;
    int channels = image_src->pixel;
    int step = width;
    int color_step = width * height;
    int sim_step;
    int i, j, k;

    //初始化处理后图片的信息
    image_dst->pixel = channels;
    image_dst->width = 128;
    image_dst->height = 128;
    int sim_color_step = image_dst->width * image_dst->height;
    sim_step = image_dst->width;
    image_dst->addr = es_malloc(image_dst->width * image_dst->height * image_dst->pixel);
    //FIXME:K210 BUG
    //image_dst->addr -= 0x40000000;

    image_src->addr += 0x40000000;
    //初始化图像
    memset(image_dst->addr, 0, image_dst->width * image_dst->height * image_dst->pixel);

    int pre_x, pre_y /*, after_x, after_y*/; //缩放前对应的像素点坐标
    int pre_x_90, pre_y_90;
    int x, y;
    int x_90, y_90;
    unsigned short color[2][2];
    float(*TT)[3] = (float(*)[3])T;
    for(i = 0; i < image_dst->height; i++)
    {
        for(j = 0; j < image_dst->width; j++) //逐点计算目标图像点
        {
            pre_x = (int)(TT[0][0] * (j << 8) + TT[0][1] * (i << 8) + TT[0][2] * (1 << 8)); //竖的
            pre_y = (int)(TT[1][0] * (j << 8) + TT[1][1] * (i << 8) + TT[1][2] * (1 << 8)); //竖的

            y = pre_y & 0xFF; //竖图像的坐标小数部分
            x = pre_x & 0xFF;
            pre_x >>= 8;      //竖图像的坐标整数部分
            pre_y >>= 8;

            x_90     = y;     //横图像的坐标
            y_90     = 0x100 - x;
            pre_x_90 = pre_y; 
            pre_y_90 = (height - pre_x - 1) + (y_90>>8);
            y_90     = y_90&0xff;
            if(pre_x_90 < 0 || pre_x_90 > (width - 1) || pre_y_90 < 0 || pre_y_90 > (height - 1))
                continue;
            for(k = 0; k < channels; k++)
            {
                color[0][0] = image_src->addr[pre_y_90 * step + pre_x_90 + k * color_step];
                color[1][0] = image_src->addr[pre_y_90 * step + (pre_x_90 + 1) + k * color_step];
                color[0][1] = image_src->addr[(pre_y_90 + 1) * step + pre_x_90 + k * color_step];
                color[1][1] = image_src->addr[(pre_y_90 + 1) * step + (pre_x_90 + 1) + k * color_step];
                int final = (0x100 - x_90) * (0x100 - y_90) * color[0][0] + x_90 * (0x100 - y_90) * color[1][0] + (0x100 - x_90) * y_90 * color[0][1] + x_90 * y_90 * color[1][1];
                final = final >> 16;
                image_dst->addr[i * sim_step + j + k * sim_color_step] = final;
            }
        }
    }
    image_dst->addr -= 0x40000000;
    image_src->addr -= 0x40000000;
}

void image_similarity_roate_left90(image_t *image_src, image_t *image_dst, float *T)
{
    //相似变换
    int width = image_src->width;
    int height = image_src->height;
    int channels = image_src->pixel;
    int step = width;
    int color_step = width * height;
    int sim_step;
    int i, j, k;

    //初始化处理后图片的信息
    image_dst->pixel = channels;
    image_dst->width = 128;
    image_dst->height = 128;
    int sim_color_step = image_dst->width * image_dst->height;
    sim_step = image_dst->width;
    image_dst->addr = es_malloc(image_dst->width * image_dst->height * image_dst->pixel);
    //FIXME:K210 BUG
    //image_dst->addr -= 0x40000000;
    image_src->addr += 0x40000000;
    //初始化图像
    memset(image_dst->addr, 0, image_dst->width * image_dst->height * image_dst->pixel);

    int pre_x, pre_y /*, after_x, after_y*/; //缩放前对应的像素点坐标
    int pre_x_90, pre_y_90;
    int x, y;
    unsigned short color[2][2];
    float(*TT)[3] = (float(*)[3])T;
    for(i = 0; i < image_dst->height; i++)
    {
        for(j = 0; j < image_dst->width; j++)
        {
            pre_x = (int)(TT[0][0] * (j << 8) + TT[0][1] * (i << 8) + TT[0][2] * (1 << 8)); //竖的
            pre_y = (int)(TT[1][0] * (j << 8) + TT[1][1] * (i << 8) + TT[1][2] * (1 << 8)); //竖的

            y = pre_y & 0xFF;
            x = pre_x & 0xFF;
            pre_x >>= 8;
            pre_y >>= 8;

            pre_x_90 = width - pre_y;
            pre_y_90 = pre_x;
            if(pre_x_90 < 0 || pre_x_90 > (width - 1) || pre_y_90 < 0 || pre_y_90 > (height - 1))
                continue;
            for(k = 0; k < channels; k++)
            {
                color[0][0] = image_src->addr[pre_y_90 * step + pre_x_90 + k * color_step];
                color[1][0] = image_src->addr[pre_y_90 * step + (pre_x_90 + 1) + k * color_step];
                color[0][1] = image_src->addr[(pre_y_90 + 1) * step + pre_x_90 + k * color_step];
                color[1][1] = image_src->addr[(pre_y_90 + 1) * step + (pre_x_90 + 1) + k * color_step];
                int final = (0x100 - x) * (0x100 - y) * color[0][0] + x * (0x100 - y) * color[1][0] + (0x100 - x) * y * color[0][1] + x * y * color[1][1];
                final = final >> 16;
                image_dst->addr[i * sim_step + j + k * sim_color_step] = final;
            }
        }
    }
    image_dst->addr -= 0x40000000;
    image_src->addr -= 0x40000000;
}
