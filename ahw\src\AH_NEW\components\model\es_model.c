/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_model.c
** bef: define the interface for model.
** auth: lines<<EMAIL>>
** create on 2022.10.15 
*/

#include "es_inc.h"
#if ES_MODEL_ACTIVE_ENABLE
#include "facelib_inc.h"

// #define ES_MODEL_DEBUG
#ifdef ES_MODEL_DEBUG
#define es_model_debug es_log_info
#define es_model_error es_log_error
#else
#define es_model_debug(...)
#define es_model_error(...)
#endif

#define MODEL_ACTIVE_DATA       (0)
#if MODEL_ACTIVE_DATA
const char *test_data = "kmstLvoGZlzdPj0UShN2aJUPz887GGz496L29nswcQoDAZgTkiBqRf66AevE4SRlObGAsHUir2JDevEw6SHUZDYcqCO/h8CQA0LDYpy/kgezajBTDkcZo1pWoEmGTIjpEcIg+UGQn6kyHFPawaiTtT0yZFurC7x09HPWuIVyDsr1Y0veg7kLTOeAKqyx0ZxK6grkT17lo7xncyaP4C3HQqNL374jpITHTKJKFtW9xGQ=";
#endif

ES_S32 es_model_init(ES_BOOL active)
{
    return ES_RET_SUCCESS;
}

ES_VOID es_model_run(ES_VOID)
{
    static ES_U32 time = 0;
	static ES_U32 timeout_ms = 15;
    ES_BYTE cpu_id[16];
    ES_BYTE mac[6];
    ES_CHAR *data = ES_NULL;

    if (ES_NETWORK_CONNECTED != es_network_get_status()) {
        return;
    }

	if ((es_time_get_sytem_ms() - time) < (timeout_ms*1000)) {
		return;
	}

    mf_model.cpuid(cpu_id);
    if (ES_RET_SUCCESS != es_network_get_mac(mac)) {
        es_model_error("get mac fail");
        goto FUNC_END;
    }

    if (ES_RET_SUCCESS != es_serv_proto_get_model_active(cpu_id, mac, &data)) {
        es_model_error("get proto data fail");
        goto FUNC_END;
    }
    es_model_debug(data);
    es_network_httpc_get_model_active("http://iface.aihardware.cn/api/activation/device", (const ES_BYTE *)data, (ES_U32)es_strlen(data));

#if MODEL_ACTIVE_DATA
    es_task_param_t task_param;
    task_param.type = ES_TASK_MODEL_ACTIVE;
    task_param.param = (ES_VOID *)test_data;
    task_param.timeout = 0;
    es_task_queue_push_wait(&task_param);
#endif

FUNC_END:
    if (ES_NULL != data) {
        es_free(data);
    }

    time = es_time_get_sytem_ms();
	timeout_ms += 10;
	if (timeout_ms > 60) {
		timeout_ms = 15;
	}
}


ES_S32 es_model_active(const ES_BYTE *active_key)
{
    extern mf_err_t mf_model_act_models(uint16_t model_type, uint8_t key[32]);
    if (MF_ERR_NONE != mf_model_act_models(MF_MDL_ALL, (unsigned char *)active_key)) {
        printk("act_models fail\r\n");
        return ES_RET_FAILURE;
    }
    printk("active model success, reboot after 3s\r\n");
    sleep(3);
    sysctl_reset(SYSCTL_RESET_SOC);
    return ES_RET_SUCCESS;
}

#endif

