#ifndef _MF_DUALCAM_IRQ_H
#define _MF_DUALCAM_IRQ_H

#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include "mf_lib.h"
#include "mf_cam.h"

/*****************************************************************************/
// Enums & Macro
/*****************************************************************************/
//
/*****************************************************************************/
// Types
/*****************************************************************************/






/*****************************************************************************/
// Functions
/*****************************************************************************/
void rgb_lock_buf(void);
void rgb_unlock_buf(void);
void kpu_lock_buf(void);
void kpu_unlock_buf(void);
void cam_dualbuf_first_run(void);
void cam_dualbuf_lock_sync(void);
int dvp_irq_gc0328_dual(void *ctx);
uint8_t cam_dualbuf_lock_buf(void);
//void cam_dualbuf_unlock_buf(void);

void camera_lock_650(void);
void camera_unlock_650(void);

/*****************************************************************************/
// Vars
/*****************************************************************************/
// extern mf_flow_t mf_flow_vis2vis;

#endif