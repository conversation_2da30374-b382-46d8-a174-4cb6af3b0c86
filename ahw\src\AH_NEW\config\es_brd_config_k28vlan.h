/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_brd_config_k28vlan.h
** bef: define the interface for configure 
** auth: lines<<EMAIL>>
** create on 2022.02.26 
*/

#ifndef _ES_BRD_CONFIG_K28VLAN_H_
#define _ES_BRD_CONFIG_K28VLAN_H_

#ifdef __cplusplus 
extern "C" { 
#endif

// Col is 96
/////////////////////////////////////// WiFi //////////////////////////////////////////////////
#define ES_WIFI_MODULE_ENABLE               (1)
#define ES_WIFI_UART_ID                     (ES_UART_ID_1)



/////////////////////////////////////// LCD ///////////////////////////////////////////////////
#define ES_LCD_DRIVER_TYPE                  (ES_LCD_DRIVER_GC9306)
#define ES_LCD_DCX_HS_NUM                   (5)
#define ES_LCD_RST_HS_NUM                   (6)
#define ES_LCD_BL_HS_NUM                    (16)
#define ES_LCD_SPI_CS_HS_NUM                (17)
#define ES_LCD_CS_PIN                       (36)
#define ES_LCD_RST_PIN                      (37)
#define ES_LCD_DCX_PIN                      (38)
#define ES_LCD_SCK_PIN                      (39)
#define ES_LCD_BL_PIN                       (17)
#define ES_LCD_PWM_DEV_BL                   (PWM_DEVICE_0)
#define ES_LCD_PWDM_CHN_BL                  (PWM_CHANNEL_1)
#define ES_LCD_DIR                          (1) // 0:horizontal, 1:vertical
#define ES_LCD_HMIRROR                      (0)
#define ES_LCD_VFLIP                        (1)
#define ES_LCD_XY_SWAP                      (0)
#define ES_LCD_DIR_PARAM                    (0)
#define ES_LCD_WIDTH                        (240)
#define ES_LCD_HEIGHT                       (320)


/////////////////////////////////////// UI ////////////////////////////////////////////////////
#define ES_UI_WIDTH                         (ES_LCD_WIDTH)
#define ES_UI_HEIGHT                        (ES_LCD_HEIGHT)
#define ES_UI_CAM_WIDTH                     (240)
#define ES_UI_CAM_HEIGHT                    (320)
#define ES_UI_FACE_EDGE_LINE_WIDTH          (2) // pixel
#define ES_UI_FACE_EDGE_LINE_LENGTH         (10) // pixel
#define ES_UI_TYPE                          (ES_UI_TYPE_K28V_240_320)


/////////////////////////////////////// Camera ////////////////////////////////////////////////
#define ES_CAM_WIDTH                        (320)
#define ES_CAM_HEIGHT                       (240)
#define ES_CAM_DIR                          (1) // direction, 0:horizontal, 1:vertical
#define ES_CAM_HMIRROR                      (1) // hmirror
#define ES_CAM_VFLIP                        (0)// vflip
#define ES_CAM_EXP_TIME                     (128)


/////////////////////////////////////// UART //////////////////////////////////////////////////
#define ES_UART_MODULE_ENABLE               (1)
#define ES_UART0_RX_PIN                     (6)
#define ES_UART0_TX_PIN                     (7)
#define ES_UART1_RX_PIN                     (27)
#define ES_UART1_TX_PIN                     (26)
#define ES_UART2_RX_PIN                     (4)
#define ES_UART2_TX_PIN                     (5)

/////////////////////////////////////// KEY ///////////////////////////////////////////////////
#define ES_KEY_MODULE_ENABLE                (1)
#define ES_KEY1_GPIO_PIN                    (8)
#define ES_KEY1_GPIO_HS_NUM                 (0)
#define ES_KEY1_PRESS_VAL                   (0)


/////////////////////////////////////// file system ///////////////////////////////////////////
#define ES_FS_ENABLE                        (0)


#ifdef __cplusplus 
}
#endif
#endif