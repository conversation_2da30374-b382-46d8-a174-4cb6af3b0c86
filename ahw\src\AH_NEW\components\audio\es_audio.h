/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_audio.h
** bef: define the interface audio. 
** auth: lines<<EMAIL>>
** create on 2022.01.08
*/

#ifndef _ES_AUDIO_H_
#define _ES_AUDIO_H_

#ifdef __cplusplus 
extern "C" { 
#endif

#include "es_types.h"

typedef enum {
    ES_AUDIO_PLAY_PASS,
    ES_AUDIO_PLAY_TEMP_OK,
    ES_AUDIO_PLAY_TEMP_FAIL,
} es_audio_play_e;

ES_S32 es_audio_init(ES_VOID);

ES_VOID es_audio_run(ES_VOID);

ES_S32 es_audio_play(es_audio_play_e play);

ES_S32 es_audio_play_flash(ES_U32 addr, ES_U32 size);

#ifdef __cplusplus 
}
#endif

#endif
