#ifndef _ES_HAL_LCD_H_
#define _ES_HAL_LCD_H_

#ifdef __cplusplus 
extern "C" { 
#endif

#include "es_types.h"


typedef enum 
{
	ES_LCD_DIR_H            = 0x00, 
	ES_LCD_DIR_V            = 0x01,
    ES_LCD_DIR_XY_RLUD      = 0x00,
    ES_LCD_DIR_YX_RLUD      = 0x20,
    ES_LCD_DIR_XY_LRUD      = 0x40,
    ES_LCD_DIR_YX_LRUD      = 0x60,
    ES_LCD_DIR_XY_RLDU      = 0x80,
    ES_LCD_DIR_YX_RLDU      = 0xA0,
    ES_LCD_DIR_XY_LRDU      = 0xC0,
    ES_LCD_DIR_YX_LRDU      = 0xE0,
    ES_LCD_DIR_XY_MASK      = 0x20,
    ES_LCD_DIR_MASK         = 0xE0,
} es_hal_lcd_dir_t;


ES_S32 es_hal_lcd_init(ES_VOID);

ES_S32 es_hal_lcd_deinit(ES_VOID);

ES_S32 es_hal_lcd_set_area(ES_U16 x, ES_U16 y, ES_U16 w, ES_U16 h);

ES_S32 es_hal_lcd_clear(ES_U16 rgb565_color);

ES_S32 es_hal_lcd_draw_picture(ES_U16 x, ES_U16 y, ES_U16 w, ES_U16 h, const ES_BYTE *img);

ES_U32 es_hal_lcd_get_width(ES_VOID);

ES_U32 es_hal_lcd_get_height(ES_VOID);

ES_S32 es_hal_lcd_on(ES_VOID);

ES_S32 es_hal_lcd_standby(ES_VOID);

#ifdef __cplusplus 
}
#endif
#endif