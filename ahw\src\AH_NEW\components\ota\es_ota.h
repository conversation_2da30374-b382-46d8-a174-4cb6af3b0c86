/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_ota.h
** bef: define the interface for ota. 
** auth: lines<<EMAIL>>
** create on 2022.01.12
*/

#ifndef _ES_OTA_H_
#define _ES_OTA_H_

#ifdef __cplusplus 
extern "C" { 
#endif

#include "es_types.h"

typedef enum {
    ES_OTA_STATUS_IDLE,
    ES_OTA_STATUS_START,
    ES_OTA_STATUS_DOWNLOAD,
    ES_OTA_STATUS_FAIL,
    ES_OTA_STATUS_SUCCESS,
    ES_OTA_STATUS_MAX
} es_ota_status_e;

typedef struct {
    ES_U32 file_size;
} es_ota_file_info_t;

ES_S32 es_ota_init(ES_VOID);

ES_S32 es_ota_start(const es_ota_file_info_t *info);

ES_S32 es_ota_write(ES_U32 offset, const ES_BYTE *data, ES_U32 len);

ES_S32 es_ota_write_all(const ES_BYTE *data, ES_U32 len);

ES_S32 es_ota_finish(ES_BOOL success);

// ref es_ota_status_e
ES_S32 es_ota_get_status(ES_VOID);

ES_VOID es_ota_run(ES_VOID);

ES_S32 es_ota_get_percent(ES_VOID);

#ifdef __cplusplus 
}
#endif

#endif
