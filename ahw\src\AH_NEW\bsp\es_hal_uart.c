/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_utils.c
** bef: implement the interface for uart (k210) 
** auth: lines<<EMAIL>>
** create on 2019.05.08 
*/

#include "es_inc.h"

//#define ES_UART_K210_DEBUG
#ifdef ES_UART_K210_DEBUG
#define es_uart_debug es_log_info
#define es_uart_error es_log_error
#else
#define es_uart_debug(...)
#define es_uart_error(...)
#endif

#define ES_UART_TASK_SLEEP_MS   (100)
#define ES_UART_K210_MAX       (3)
#define ES_UART_DMA_RX_EN       (0)

// #ifndef es_malloc
// #define es_malloc malloc
// #define es_free free
// #endif

#ifndef ES_UART_RX_BUFF_SIZE
#define ES_UART_RX_BUFF_SIZE    (64)
#endif

typedef struct {
    ES_U8 is_open;
    ES_U8 rx_ready;
    es_uart_rx_cb_t rx_cb;
} k210_uart_param_t;

typedef struct {
    ES_U8 uart_channel;
    ES_U8 rx_pin;
    ES_U8 tx_pin;
} k210_uart_io_map_t;

static k210_uart_param_t k210_uarts[ES_UART_K210_MAX] = {
    {ES_FALSE, ES_FALSE, ES_NULL},
    {ES_FALSE, ES_FALSE, ES_NULL},
    {ES_FALSE, ES_FALSE, ES_NULL},
};

static k210_uart_io_map_t k210_uart_io_map[ES_UART_K210_MAX] = {
    {.uart_channel=UART_DEVICE_1, .rx_pin=ES_UART0_RX_PIN, .tx_pin=ES_UART0_TX_PIN},
    {.uart_channel=UART_DEVICE_2, .rx_pin=ES_UART1_RX_PIN, .tx_pin=ES_UART1_TX_PIN},
    {.uart_channel=UART_DEVICE_3, .rx_pin=ES_UART2_RX_PIN, .tx_pin=ES_UART2_TX_PIN},
};


static ES_S32 k210_uart_recv_irq(ES_VOID *ctx)
{
    ES_U8 uart_id = (ES_U8)ctx;
    ES_S32 ret;
    ES_BYTE r_buf[ES_UART_RX_BUFF_SIZE] = {0};

    //es_uart_debug("uart irq comming ...");
    if (ES_UART_K210_MAX <= uart_id) {
        return 0;
    }

    if (!k210_uarts[uart_id].is_open) {
        return 0;
    }

    ret = uart_receive_data(k210_uart_io_map[uart_id].uart_channel, (char *)r_buf, ES_UART_RX_BUFF_SIZE-1);
    if (ret <= 0) {
        return 0;
    }

    if (ES_NULL != k210_uarts[uart_id].rx_cb) {
        k210_uarts[uart_id].rx_cb(uart_id, (const ES_BYTE *)r_buf, ret);
    }

    //es_uart_debug("uart rx (len:%d):%s", ret, r_buf);

    return 0;
}

static ES_S32 es_uart_init_io_mux(ES_U8 uart_id)
{
    fpioa_set_function(k210_uart_io_map[uart_id].rx_pin, 
        FUNC_UART1_RX + k210_uart_io_map[uart_id].uart_channel * 2);
    fpioa_set_function(k210_uart_io_map[uart_id].tx_pin, 
        FUNC_UART1_TX + k210_uart_io_map[uart_id].uart_channel  * 2);

    return ES_RET_SUCCESS;
}

ES_S32 es_uart_init(ES_VOID)
{
#if ES_UART_K210_SUPPORT_OS
    rx_task_handle = es_os_create_task("uart_rx_task", ES_TASK_HEAP_SIZE_UART,
        ES_TASK_PRIORITY_UART, uart_rx_task, ES_NULL);
    if (ES_NULL == rx_task_handle) {
        return ES_RET_FAILURE;
    }
#endif
    return ES_RET_SUCCESS;
}

ES_S32 es_uart_open(ES_U8 id, es_uart_param_t *p)
{
    ES_U8 uart_parity;

    if (id >= ES_UART_K210_MAX) {
        es_uart_error("uart open uart_%d fail.");
        return ES_RET_FAILURE;
    }

    if (k210_uarts[id].is_open) {
        es_uart_error("uart open fail, open again.");
        return ES_RET_FAILURE;
    }

    k210_uarts[id].is_open = ES_TRUE;
    k210_uarts[id].rx_cb = p->rx_cb;

    es_uart_init_io_mux(id);

    uart_init(k210_uart_io_map[id].uart_channel);
    
    uart_parity = UART_PARITY_NONE;
    if (ES_UART_PARITY_ODD == p->parity) {
        uart_parity = UART_PARITY_ODD;
    } else if (ES_UART_PARITY_EVEN ==  p->parity) {
        uart_parity = UART_PARITY_EVEN;
    }
    uart_configure(k210_uart_io_map[id].uart_channel, p->baud,
                    p->data, p->stop, uart_parity);

    //uart_set_receive_trigger(k210_uart_io_map[id].uart_channel, UART_RECEIVE_FIFO_1);
    uart_irq_register(k210_uart_io_map[id].uart_channel, UART_RECEIVE,
        k210_uart_recv_irq, (ES_VOID *)id, 4);

    return ES_RET_SUCCESS;
}


ES_S32 es_uart_write(ES_U8 id, const ES_BYTE *buf, ES_U16 len)
{
    ES_U16 cnt = 0;

    if (!k210_uarts[id].is_open) {
        es_uart_error("es_uart_write(), %d is not open", id);
        return ES_RET_FAILURE;
    }

    es_uart_debug("es_uart_write(), id:%d\r\n", id);
    cnt = uart_send_data(k210_uart_io_map[id].uart_channel, (const char *)buf, len);
    if (cnt != len) {
        return 0;
    }

    //es_uart_debug("es_uart_write(), send(%d):%s", len, buf);
    es_uart_debug("es_uart_write(), send finish:%d", len);
    return cnt;
}

ES_S32 es_uart_close(ES_U8 id)
{
    if (!k210_uarts[id].is_open) {
        return ES_RET_FAILURE;
    }
    return ES_RET_SUCCESS;
}



