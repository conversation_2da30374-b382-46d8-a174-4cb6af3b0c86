#include "es_inc.h"

#if (ES_UI_TYPE == ES_UI_TYPE_K28V_240_320)
#include "es_ui_img_res.h"

// #define ES_UI_FACE_DEBUG
#ifdef ES_UI_FACE_DEBUG
#define es_ui_face_debug es_log_info
#define es_ui_face_error es_log_error
#else
#define es_ui_face_debug(...)
#define es_ui_face_error(...)
#endif

static lv_obj_t *lv_face_bg = ES_NULL;
static lv_style_t face_edge_style;
// point1(5,10), ponit2(100,120), point3(5, 100)
static lv_point_t edge_points[4][4];

static ES_VOID es_ui_edge_update_points(ES_U16 x1, ES_U16 y1, ES_U16 x2, ES_U16 y2)
{
    x1 = x1 * ES_UI_CAM_WIDTH / ES_CAM_HEIGHT;
    x2 = x2 * ES_UI_CAM_WIDTH / ES_CAM_HEIGHT;
    y1 = y1 * ES_UI_CAM_HEIGHT / ES_CAM_WIDTH;
    y2 = y2 * ES_UI_CAM_HEIGHT / ES_CAM_WIDTH;
    // point1
    edge_points[0][0].x = x1; edge_points[0][0].y = y1;
    edge_points[0][1].x = x1; edge_points[0][1].y = y1+ES_UI_FACE_EDGE_LINE_LENGTH;
    edge_points[0][2].x = x1; edge_points[0][2].y = y1;
    edge_points[0][3].x = x1+ES_UI_FACE_EDGE_LINE_LENGTH; edge_points[0][3].y = y1;

     // point2
    edge_points[1][0].x = x1; edge_points[1][0].y = y2;
    edge_points[1][1].x = x1+ES_UI_FACE_EDGE_LINE_LENGTH; edge_points[1][1].y = y2;
    edge_points[1][2].x = x1; edge_points[1][2].y = y2;
    edge_points[1][3].x = x1; edge_points[1][3].y = y2-ES_UI_FACE_EDGE_LINE_LENGTH;


     // point3
    edge_points[2][0].x = x2; edge_points[2][0].y = y1;
    edge_points[2][1].x = x2; edge_points[2][1].y = y1+ES_UI_FACE_EDGE_LINE_LENGTH;
    edge_points[2][2].x = x2; edge_points[2][2].y = y1;
    edge_points[2][3].x = x2-ES_UI_FACE_EDGE_LINE_LENGTH; edge_points[2][3].y = y1;

    // point4
    edge_points[3][0].x = x2; edge_points[3][0].y = y2;
    edge_points[3][1].x = x2; edge_points[3][1].y = y2-ES_UI_FACE_EDGE_LINE_LENGTH;
    edge_points[3][2].x = x2; edge_points[3][2].y = y2;
    edge_points[3][3].x = x2-ES_UI_FACE_EDGE_LINE_LENGTH; edge_points[3][3].y = y2;
}


ES_S32 es_ui_face_init(ES_VOID)
{
    lv_face_bg = lv_obj_create(lv_scr_act());
    lv_obj_remove_style_all(lv_face_bg);
    lv_obj_set_size(lv_face_bg, ES_UI_CAM_WIDTH, ES_UI_CAM_HEIGHT);
    lv_obj_align(lv_face_bg, LV_ALIGN_TOP_LEFT, 0, 0);

    lv_style_init(&face_edge_style);
    lv_style_set_line_width(&face_edge_style, ES_UI_FACE_EDGE_LINE_WIDTH);
    lv_style_set_line_color(&face_edge_style, lv_palette_main(LV_PALETTE_BLUE));

    return ES_RET_SUCCESS;
}

ES_S32 es_ui_face_edge(ES_U16 x1, ES_U16 y1, ES_U16 x2, ES_U16 y2, ES_U8 type)
{
    ES_U8 i = 0;
    lv_obj_t *obj;

    lv_obj_clean(lv_face_bg);
    if (ES_FACECB_NONE == type) {
        return ES_RET_SUCCESS;
    }

    es_ui_edge_update_points(x1, y1, x2, y2);
    if (ES_FACECB_DETECT == type) { // red
        lv_style_set_line_color(&face_edge_style, lv_palette_main(LV_PALETTE_RED));
    } else if (ES_FACECB_PASS == type) {
        lv_style_set_line_color(&face_edge_style, lv_palette_main(LV_PALETTE_GREEN));
    } else {
        lv_style_set_line_color(&face_edge_style, lv_palette_main(LV_PALETTE_BLUE));
    }
    

    /*Create a line and apply the new style*/
    for (i = 0; i < 4; i++) {
        obj = lv_line_create(lv_face_bg);
        lv_obj_remove_style_all(obj);
        lv_obj_add_style(obj, &face_edge_style, 0);
        lv_line_set_points(obj, edge_points[i], 4);     /*Set the points*/
    }

    if (ES_FACECB_DETECT == type || ES_FACECB_END <= type) {
        return ES_RET_SUCCESS;
    }

    obj = lv_img_create(lv_face_bg);
    lv_obj_align(obj, LV_ALIGN_CENTER, 0, 0);
    if (ES_FACECB_PASS == type) {
        lv_img_set_src(obj, ES_UI_IMG_SRC_PASS);
    } else if (ES_FACECB_AUTH_FAIL == type) {
        lv_img_set_src(obj, ES_UI_IMG_SRC_AUTH_FAIL);
    } else {
        lv_img_set_src(obj, ES_UI_IMG_SRC_FAIL);
    }

    return ES_RET_SUCCESS;
}

#endif