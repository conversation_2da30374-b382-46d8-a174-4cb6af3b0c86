/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_network_tuya.h
** bef: define the interface for tuya network. 
** auth: lines<<EMAIL>>
** create on 2020.12.21
*/

#ifndef _ES_NETWORK_TUYA_H_
#define _ES_NETWORK_TUYA_H_

#ifdef __cplusplus 
extern "C" { 
#endif

#include "es_types.h"


ES_S32 es_network_tuya_init(ES_VOID);

ES_VOID es_network_tuya_task(ES_VOID);

ES_S32 es_network_tuya_get_status(ES_VOID);

ES_S32 es_network_tuya_upload_pic(const ES_BYTE *pic_data, ES_U32 pic_data_len, ES_U32 timestamp);

#ifdef __cplusplus 
}
#endif

#endif