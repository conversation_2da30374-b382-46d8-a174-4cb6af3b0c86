#include "es_inc.h"

#if (ES_UI_TYPE == ES_UI_TYPE_K40V_480_800_GPS)
#include "es_ui_img_res.h"

// #define ES_UI_SAVER_DEBUG
#ifdef ES_UI_SAVER_DEBUG
#define es_ui_saver_debug es_log_info
#define es_ui_saver_error es_log_error
#else
#define es_ui_saver_debug(...)
#define es_ui_saver_error(...)
#endif


static lv_obj_t *lv_saver_bg = ES_NULL;
extern lv_disp_t *cam_disp;


ES_S32 es_ui_saver_create_widgets(ES_VOID)
{
    lv_obj_t *obj;

    lv_saver_bg = lv_obj_create(lv_disp_get_scr_act(cam_disp));
    lv_obj_remove_style_all(lv_saver_bg);
    lv_obj_set_size(lv_saver_bg, ES_UI_WIDTH, ES_UI_HEIGHT);
    lv_obj_align(lv_saver_bg, LV_ALIGN_TOP_LEFT, 0, 0);

    obj = lv_img_create(lv_saver_bg);
    lv_obj_align(obj, LV_ALIGN_TOP_LEFT, 0, 0);
    lv_img_set_src(obj, ES_UI_IMG_SRC_SAVER);

    return ES_RET_SUCCESS;
}

ES_S32 es_ui_saver_init(ES_VOID)
{
    es_ui_saver_create_widgets();
    es_ui_saver_show(ES_FALSE);

    return ES_RET_SUCCESS;
}

ES_S32 es_ui_saver_show(ES_BOOL show)
{
    if (show) {
        lv_obj_clear_flag(lv_saver_bg, LV_OBJ_FLAG_HIDDEN);
    } else {
        lv_obj_add_flag(lv_saver_bg, LV_OBJ_FLAG_HIDDEN);
    }

    return ES_RET_SUCCESS;
}

#endif
