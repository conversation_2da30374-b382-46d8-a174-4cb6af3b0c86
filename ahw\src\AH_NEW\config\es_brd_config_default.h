/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_utils.h
** bef: define the interface for configure 
** auth: lines<<EMAIL>>
** create on 2020.06.27 
*/

#ifndef _ES_BRD_CONFIG_DEFAULT_H_
#define _ES_BRD_CONFIG_DEFAULT_H_

#ifdef __cplusplus 
extern "C" { 
#endif

// Col is 96
/////////////////////////////////////// 4G LTE ////////////////////////////////////////////////
#ifndef ES_4GLTE_ENABLE
#define ES_4GLTE_ENABLE                     (0)
#define ES_4GLTE_UART_ID                    (ES_UART_ID_0)
#define ES_4GLTE_UART_BAUD                  (ES_UART_BAUD_115200)
#define ES_4GLTE_MODULE_TYPE                (ES_4GLTE_MODULE_L505)
#endif


/////////////////////////////////////// WiFi //////////////////////////////////////////////////
#ifndef ES_WIFI_MODULE_ENABLE
#define ES_WIFI_MODULE_ENABLE               (0)
#endif


/////////////////////////////////////// Temperature Module ////////////////////////////////////
#ifndef ES_TEMPERATURE_MODULE_ENABLE
#define ES_TEMPERATURE_MODULE_ENABLE        (0)
#define ES_TEMPERATURE_READ_TIMEOUT_MS      (3*1000)
#endif


/////////////////////////////////////// Wireless 433 Module ///////////////////////////////////
#ifndef ES_433_MODULE_ENABLE
#define ES_433_MODULE_ENABLE                (0)
#define ES_433_ENABLE_DINGDONG_DOORBELL     (1)
#if ES_433_MODULE_ENABLE
#define ES_433_MODULE_SEND_PIN              (13)
#define ES_433_MODULE_READ_PIN              (27)
#define ES_433_MODULE_SEND_HS_NUM           (29)
#define ES_433_MODULE_READ_HS_NUM           (13)
#endif
#endif


/////////////////////////////////////// door app //////////////////////////////////////////////
#ifndef ES_APP_MODULE_SBD_ENABLE
#define ES_APP_MODULE_SBD_ENABLE            (0)
#endif

#ifndef ES_APP_DOOR_JWZH_ENABLE
#define ES_APP_DOOR_JWZH_ENABLE             (0)
#endif

/////////////////////////////////////// Cloud Protocol ////////////////////////////////////////
#ifndef ES_CLOUD_PROTOCOL_MODULE_ENABLE
#define ES_CLOUD_PROTOCOL_MODULE_ENABLE     (1)
#endif

/////////////////////////////////////// Motor Module //////////////////////////////////////////
#ifndef ES_MOTOR_MODULE_ENABLE
#define ES_MOTOR_MODULE_ENABLE              (0)
#if ES_MOTOR_MODULE_ENABLE
#define ES_MOTOR_AN1_GPIO_PIN               (27)
#define ES_MOTOR_AN2_GPIO_PIN               (28)
#define ES_MOTOR_AN1_HS_NUM                 (29)
#define ES_MOTOR_AN2_HS_NUM                 (30)
#endif
#endif

/////////////////////////////////////// NFC Module ////////////////////////////////////////////
#ifndef ES_NFC_MODULE_ENABLE
#define ES_NFC_MODULE_ENABLE                (0)
#define ES_NFC_CHIP_NONE                    (0)
#define ES_NFC_CHIP_WS1850S                 (1)
#define ES_NFC_ID_DATA_LEN                  (10)
#define ES_NFC_GPIO_IRQ_PIN                 (20)
#define ES_NFC_GPIO_RST_PIN                 (21)
#define ES_NFC_GPIO_LED_PIN                 (0)
#define ES_NFC_GPIO_IRQ_HS_NUM              (29)
#define ES_NFC_GPIO_RST_HS_NUM              (30)
#define ES_NFC_GPIO_LED_HS_NUM              (11)
#if ES_NFC_MODULE_ENABLE
#define ES_NFC_CHIP_TYPE                    (ES_NFC_CHIP_WS1850S)
#define ES_NFC_I2C_READ_ADDR                (0x28)
#define ES_NFC_I2C_WRITE_ADDR               (0x28)
#define ES_NFC_I2C_SPEED                    (100 * 1000)
#define WS1850S_ENABLE_GPIO_RST             (1)
#else
#define ES_NFC_CHIP_TYPE                    (ES_NFC_CHIP_NONE)
#endif/*  */
#endif

/////////////////////////////////////// BLE Module ////////////////////////////////////////////
#ifndef ES_BLE_MODULE_ENABLE
#define ES_BLE_MODULE_ENABLE                (0)
#define ES_BLE_HAL_TYPE                     (ES_BLE_HAL_571)
#define ES_BLE_UART_ID                      (ES_UART_ID_1)
#define ES_BLE_PAYLOAD_GPIO_PIN             (26)
#define ES_BLE_PAYLOAD_GPIO_HS_NUM          (29)
#define ES_BLE_PAYLOAD_DATA_LEN             (32)
#endif


/////////////////////////////////////// Flash Config ///////////////////////////////////////////
#ifndef ES_FLASH_FACE_DB_ADDR
#define ES_FLASH_FACE_DB_ADDR               (0x800000)
#define ES_FLASH_FACE_DB_SIZE               (4*1024*1024)
#endif

#ifndef ES_FLASH_PASS_LOG_ADDR
#define ES_FLASH_PASS_LOG_ADDR              (0xFE7000)
#define ES_FLASH_PASS_LOG_SIZE              (16*1024)
#endif

#ifndef ES_FLASH_CFG_ADDR
#define ES_FLASH_CFG_ADDR                   (0xFEC000) // 0x1000000(16M) - 0x4000(16K)
#define ES_FLASH_CFG_LEN                    (16*1024)  // 100K
#endif


/////////////////////////////////////// Flash data ////////////////////////////////////////////
#ifndef ES_FLASH_MODULE_ENABLE
#define ES_FLASH_MODULE_ENABLE              (1)
#define ES_FLASH_MAGIC_HEAD                 (0x12345678)
#define ES_FLASH_BLE_MAC_COUNT              (4)
#define ES_FLASH_NFC_COUNT                  (1000)
#define ES_FLASH_USER_QRCODE_LEN            (128)
#endif



/////////////////////////////////////// CAMERA ////////////////////////////////////////////////
#ifndef ES_CAM_HMIRROR
#define ES_CAM_HMIRROR                      (0)
#endif

#ifndef ES_CAM_VFLIP
#define ES_CAM_VFLIP                        (0)
#endif

/////////////////////////////////////// test module ///////////////////////////////////////////
#ifndef ES_TEST_MODULE_ENABLE
#define ES_TEST_MODULE_ENABLE               (0)
#endif

/////////////////////////////////////// Time Module ///////////////////////////////////////////
#define es_os_usleep(us)                    usleep(us)
#define es_os_msleep(ms)                    msleep(ms)

#define ES_MIN_TIMESTAMP                    (1633677770)    // 2021-10-08 15:22:49
#define ES_MAX_TIMESTAMP                    (2051197261)    // 2035-01-01 01:01:01


/////////////////////////////////////// Utils Module //////////////////////////////////////////
#define ES_SET_BIT(x,y)                     (x) |= (1<<(y))
#define ES_CLR_BIT(x,y)                     (x) &= ~(1<<(y))
#define ES_REVERSE_BIT(x,y)                 (x) ^=(1<<(y))
#define ES_GET_BIT(x,y)                     (((x)>>(y))&0x01)

/////////////////////////////////////// GPIO Module ///////////////////////////////////////////
#define ES_GPIO_VAL_HIGH                    (1)
#define ES_GPIO_VAL_LOW                     (0)


/////////////////////////////////////// Tuya Module ///////////////////////////////////////////
#ifndef ES_TUYA_MODULE_ENABLE
#define ES_TUYA_MODULE_ENABLE               (0)
#endif


/////////////////////////////////////// protothread ///////////////////////////////////////////
#ifndef ES_PROTO_THREAD_ENABLE
#define ES_PROTO_THREAD_ENABLE              (0)
#endif


/////////////////////////////////////// I2C ///////////////////////////////////////////////////
#ifndef ES_I2C_SDA_PIN
#define ES_I2C_SDA_PIN                      (31)
#define ES_I2C_SCL_PIN                      (30)
#endif


/////////////////////////////////////// UART //////////////////////////////////////////////////
#ifndef ES_UART_MODULE_ENABLE
#define ES_UART_MODULE_ENABLE               (1)
#define ES_UART0_RX_PIN                     (15)
#define ES_UART0_TX_PIN                     (14)
#define ES_UART1_RX_PIN                     (27)
#define ES_UART1_TX_PIN                     (26)
#define ES_UART2_RX_PIN                     (4)
#define ES_UART2_TX_PIN                     (5)
#endif


/////////////////////////////////////// system info ///////////////////////////////////////////
#ifndef ES_SYSTEM_INFO
#define ES_SYSTEM_INFO
#define ES_SYS_SVER_VAL                     (10000)
#define ES_SYS_SVER_STR                     ("1.0.0")
#define ES_SYS_HVER_VAL                     (10000)
#define ES_SYS_HVER_STR                     ("1.0.0")
#endif


/////////////////////////////////////// Model Param ///////////////////////////////////////////
#ifndef ES_FACE_MODEL_FD_GATE
#define ES_FACE_MODEL_FD_GATE               (60.0) // face detect gate
#endif

#ifndef ES_FACE_MODEL_FE_GATE
#define ES_FACE_MODEL_FE_GATE               (80.0) // face detect pass
#endif

#ifndef ES_FACE_MODEL_LIVE_GATE
#define ES_FACE_MODEL_LIVE_GATE             (35.0)
#endif


/////////////////////////////////////// IR LED ////////////////////////////////////////////////
#ifndef ES_IR_ENABLE
#define ES_IR_ENABLE                        (1)
#define ES_IR_PIN                           (9)
#define ES_IR_HS_NUM                        (1)
#define ES_IR_IO_OPEN_VAL                   (1)
#endif


/////////////////////////////////////// FLASH LED ////////////////////////////////////////////////
#ifndef ES_LED_FLASH_ENABLE
#define ES_LED_FLASH_ENABLE                 (1)
#define ES_LED_FLASH_PIN                    (10)
#define ES_LED_FLASH_IO_OPEN_VAL            (1)
#define ES_LED_FLASH_PWM_CHANNEL            (PWM_CHANNEL_0)
#define ES_LED_FLASH_PWM_DEV                (PWM_DEVICE_0)
#define ES_LED_FLASH_PWM_STRONG_DUTY        (0.5)
#define ES_LED_FLASH_PWM_WEAK_DUTY          (0.05)
#endif


/////////////////////////////////////// LCD ///////////////////////////////////////////////////
#ifndef ES_LCD_PIX_NEED_SWAP
#define ES_LCD_PIX_NEED_SWAP                (1)
#endif


/////////////////////////////////////// PASS LOG //////////////////////////////////////////////
#ifndef ES_PASSLOG_ENABLE
#define ES_PASSLOG_ENABLE                   (1)
#define ES_PASSLOG_HDR_MAGIC                (0x12345678)
#define ES_PASSLOG_CACHE_COUNT              (8)
#define ES_PASSLOG_JSON_STR_LEN             (96)
#define ES_PASSLOG_UID_LEN                  (32)
#endif


/////////////////////////////////////// RELAY /////////////////////////////////////////////////
#ifndef ES_RELAY_ENABLE
#define ES_RELAY_ENABLE                     (1)
#define ES_RELAY_GPIO_PIN                   (29)
#define ES_RELAY_GPIO_HS_NUM                (7)
#define ES_RELAY_OPEN_IO_VAL                (1)
#define ES_RELAY_CONTINUE_MS                (3000)
#endif


/////////////////////////////////////// DOOR //////////////////////////////////////////////////
#ifndef ES_DOOR_ENABLE
#define ES_DOOR_ENABLE                      (0)
#endif


/////////////////////////////////////// AUDIO /////////////////////////////////////////////////
#ifndef ES_AUDIO_ENABLE
#define ES_AUDIO_ENABLE                     (1)
#define ES_AUDIO_GPIO_PIN                   (11)
#define ES_AUDIO_GPIO_HS_NUM                (3)
#define ES_AUDIO_GPIO_IO_VAL                (1)
#define ES_AUDIO_I2S_SCLK_PIN               (35)
#define ES_AUDIO_I2S_WS_PIN                 (33)
#define ES_AUDIO_I2S_DOUT_PIN               (18)
#define ES_AUDIO_PASS_FLASH_ADDR            (3000000)
#define ES_AUDIO_PASS_FLASH_SIZE            (23132)
#define ES_AUDIO_TEMP_OK_FLASH_ADDR         (ES_AUDIO_PASS_FLASH_ADDR+((ES_AUDIO_PASS_FLASH_SIZE/1024)+1)*1024)
#define ES_AUDIO_TEMP_OK_FLASH_SIZE         (0)
#define ES_AUDIO_TEMP_FAIL_FLASH_ADDR       (ES_AUDIO_TEMP_OK_FLASH_ADDR+((ES_AUDIO_PASS_FLASH_SIZE/1024)+1)*1024)
#define ES_AUDIO_TEMP_FAIL_FLASH_SIZE       (0)
#endif


/////////////////////////////////////// OTA ///////////////////////////////////////////////////
#ifndef ES_OTA_ENABLE
#define ES_OTA_ENABLE                       (1)
#define ES_OTA_FILE_MAX_SIZE                (960 * 1024) /* 固件最大为960KB */
#define ES_OTA_BOOT_ENV_ADDR                (28 * 1024)  /* ota配置参数地址 */
#define ES_OTA_APP0_DEFAULT_ADDR            (32 * 1024)  /* APP0起始地址 */
#define ES_OTA_APP1_DEFAULT_ADDR            (1024 * 1024)  /* APP1起始地址 */
#endif

#ifndef ES_OTA_DL_ENABLE
#define ES_OTA_DL_ENABLE                    (0)
#endif


/////////////////////////////////////// file system ///////////////////////////////////////////
#ifndef ES_FS_ENABLE
#define ES_FS_ENABLE                        (1)
#endif


/////////////////////////////////////// UI ////////////////////////////////////////////////////
#ifndef ES_UI_BOOT_BG_COLOR
#define ES_UI_BOOT_BG_COLOR                 (ES_U16)(((1 << 8) & 0xF800) | ((107 << 3) & 0x7E0) | (168 >> 3))
#endif
#ifndef ES_UI_WORK_MODE_TIME_AFTER_BOOT
#define ES_UI_WORK_MODE_TIME_AFTER_BOOT     (20*1000)
#endif
#ifndef ES_UI_ENTER_SAVER_TIME
#define ES_UI_ENTER_SAVER_TIME              (2*1000)
#endif
#ifndef ES_UI_DRAW_BUF_LINE
#define ES_UI_DRAW_BUF_LINE                 (40)
#endif

#ifndef ES_UI_BAR_WIDTH
#define ES_UI_BAR_WIDTH                     (0)
#endif
#ifndef ES_UI_BAR_HEIGHT
#define ES_UI_BAR_HEIGHT                    (0)
#endif


/////////////////////////////////////// UART //////////////////////////////////////////////////
#ifndef ES_UART_MODULE_ENABLE
#define ES_UART_MODULE_ENABLE               (1)
#define ES_UART0_RX_PIN                     (6)
#define ES_UART0_TX_PIN                     (7)
#define ES_UART1_RX_PIN                     (27)
#define ES_UART1_TX_PIN                     (26)
#define ES_UART2_RX_PIN                     (4)
#define ES_UART2_TX_PIN                     (5)
#endif


/////////////////////////////////////// KEY ///////////////////////////////////////////////////
#ifndef ES_KEY_MODULE_ENABLE
#define ES_KEY_MODULE_ENABLE            (0)
#endif


/////////////////////////////////////// file system ///////////////////////////////////////////
#if ES_FS_ENABLE
#define ES_UI_IMG_SRC_SAVER                 ("S:0:saver.sjpg")
#define ES_UI_IMG_SRC_PASS                  ("S:0:pass.sjpg")
#define ES_UI_IMG_SRC_FAIL                  ("S:0:fail.sjpg")
#define ES_UI_IMG_SRC_AUTH_FAIL             ("S:0:auth.sjpg")
#define ES_UI_IMG_SRC_BAR                   ("S:0:bar.sjpg")
#else
#define ES_UI_IMG_SRC_SAVER                 ((lv_img_dsc_t *)es_ui_res_saver())
#define ES_UI_IMG_SRC_PASS                  ((lv_img_dsc_t *)es_ui_res_pass())
#define ES_UI_IMG_SRC_FAIL                  ((lv_img_dsc_t *)es_ui_res_fail())
#define ES_UI_IMG_SRC_AUTH_FAIL             ((lv_img_dsc_t *)es_ui_res_auth_fail())
#define ES_UI_IMG_SRC_BAR                   ((lv_img_dsc_t *)es_ui_res_bar())
#endif


/////////////////////////////////////// FACE //////////////////////////////////////////////////
#ifndef ES_FACE_UPLOAD_ENABLE
#define ES_FACE_UPLOAD_ENABLE               (0)
#endif


/////////////////////////////////////// MQTT //////////////////////////////////////////////////
#ifndef ES_MQTT_ENABLE
#define ES_MQTT_ENABLE                      (1)
#define ES_MQTT_CFG_IP                      ("************")
#define ES_MQTT_CFG_PORT                    (1883)
#define ES_MQTT_CFG_USERNAME                ("admin")
#define ES_MQTT_CFG_PASSWD                  ("Faceos2016")
// #define ES_MQTT_CFG_DEV_MAC_FMT             ("%02X:%02X:%02X:%02X:%02X:%02X")
#define ES_MQTT_CFG_DEV_MAC_FMT             ("%02X%02X%02X%02X%02X%02X")
#define ES_MQTT_CFG_DEV_SUB_TOPIC_FMT       ("/one2one/device/%s")
// #define ES_MQTT_CFG_DEV_PUB_TOPIC           ("/one2one/server/DC3104D4C4E8692EDB2D575637AC93392022")
#define ES_MQTT_CFG_DEV_PUB_TOPIC           ("/one2one/server/DC3104D4C4E8692EDB2D575637AC9339A")
#define ES_MQTT_HEART_TIME_SEC              (60)
#endif


/////////////////////////////////////// HTTP //////////////////////////////////////////////////
#ifndef ES_HTTP_ENABLE
#define ES_HTTP_ENABLE                      (1)
// #define ES_HTTP_UPLOAD_FACE_URL             ("http://face.aihardware.cn:8899/device/v3/upload/face?t=%d&f=%s")
// #define ES_HTTP_UPLOAD_UID_URL              ("http://face.aihardware.cn/device/v3/upload/uid")
// #define ES_HTTP_UPLOAD_OFL_REC_URL          ("http://face.aihardware.cn/device/v3/upload/offrecord")
#define ES_HTTP_UPLOAD_FACE_URL             ("http://119.91.63.55:9901/v1/device/upload/onlineRecord?t=%d&f=%s")
#endif
#define ES_HTTP_HEADER_NAME_LEN             (16)
#define ES_HTTP_HEADER_VALUE_LEN            (32)
#define ES_HTTP_HEADER_MAX_COUNT            (4)


/////////////////////////////////////// MODEL /////////////////////////////////////////////////
#ifndef ES_MODEL_ACTIVE_ENABLE
#define ES_MODEL_ACTIVE_ENABLE              (0)
#define ES_MODEL_ACTIVE_PROTO_DATA_LEN      (128)
#define ES_MODEL_ACTIVE_KEY_LEN             (32)
#endif


/////////////////////////////////////// GPS ///////////////////////////////////////////////////
#ifndef ES_GPS_ENABLE
#define ES_GPS_ENABLE                       (0)
#endif


/////////////////////////////////////// RESOURCE //////////////////////////////////////////////
#ifndef ES_RESOURCE_ENABLE
#define ES_RESOURCE_ENABLE                  (0)
#define ES_RESOURCE_MAGIC                   (0x12344321)
#define ES_RESOURCE_FLASH_ADDR              (0xC00000)
#define ES_RESOURCE_FLASH_SIZE              (0x20000) //(128*1024)
#define ES_RESOURCE_HEAD_SIZE               (0x1000)// (4*1024)
#define ES_RESOURCE_DATA_SIZE               (0x8000) //(32*1024)
#define ES_RESOURCE_MAX_COUNT               (2)
#endif


/////////////////////////////////////// DEBUG LOG /////////////////////////////////////////////
#ifndef ES_DEBUG_LOG_ENABLE
#define ES_DEBUG_LOG_ENABLE                 (1)
#endif


/////////////////////////////////////// RTC ///////////////////////////////////////////////////
#ifndef ES_RTC_ENABLE
#define ES_RTC_ENABLE                       (0)
#endif


/////////////////////////////////////// CAPTURE ///////////////////////////////////////////////
#ifndef ES_CAPTURE_ENABLE
#define ES_CAPTURE_ENABLE                   (0)
#define ES_CAPTURE_MIN_INTERVAL_SEC         (10)
#define ES_CAPTURE_MAX_INTERVAL_SEC         (60*60)
#endif

#ifdef __cplusplus 
}
#endif
#endif