#include "facelib_inc.h"
#include "es_mem.h"

// #define ES_FACEDB_DEBUG
#ifdef ES_FACEDB_DEBUG
#define es_facedb_debug printk
#define es_facedb_error printk
#else
#define es_facedb_debug(...)
#define es_facedb_error(...)
#endif

/*****************************************************************************/
// Macro definitions
/*****************************************************************************/
#define item_addr(x) (mf_facedb.data_addr + (x) * mf_facedb.item_size)

/*****************************************************************************/
// Function definitions
/*****************************************************************************/




/*****************************************************************************/
// Private Var 局部变量
/*****************************************************************************/
typedef struct
{
	dbf_hdr_t hdr;
	uint8_t uid_table[FACE_DATA_MAX_COUNT][FACEDB_UID_LEN];
	int flash_all_face_have_ir_fea;
} mf_facedb_flash_t;

static mf_facedb_flash_t* db = NULL;
static uint8_t facedb_featrue_sha256[32] = {0};
static uint8_t facedb_is_update_sha256 = 0;

///////////////////////////////////////////////////////////////////////////////

///////////////////////////////////////////////////////////////////////////////
static int _db_get_free_oft(void)
{
    int ret = -1;
    int i;
    for (i = 0; i < FACE_DATA_MAX_COUNT; i++)
    {
        if (((db->hdr.index[i / 32] >> (i % 32)) & 0x1) == 0)
        {
            ret = i;
            break;
        }
    }

    if (i >= FACE_DATA_MAX_COUNT)
    {
        es_facedb_debug("get_face_id:> Err too many data\n");
        return -1;
    }
    return ret;
}

static void _db_init_uid_table(uint8_t init_flag)
{
    int i;
    uint32_t uid_addr;
    if (!init_flag)
    {
        for (i = 0; i < FACE_DATA_MAX_COUNT; i++)
        {
            uid_addr = item_addr(i) + 4;
            mf_flash.read(uid_addr, db->uid_table[i], FACEDB_UID_LEN);
        }
    }
    else
    {
        memset(db->uid_table, 0, FACEDB_UID_LEN * FACE_DATA_MAX_COUNT);
    }
    return;
}

static mf_err_t _mf_facedb_flash_tryread(uint32_t oft, dbf_item_t* face_item)
{
	
	if ((db->hdr.index[oft / 32] >> (oft % 32) & 0x1) == 0) {
		es_facedb_debug("item %d not exist!\r\n", oft);
		return MF_ERR_FACEDB_ID;
	}
	
	uint32_t image_address = item_addr(oft);
    mf_flash.read(image_address, (uint8_t *)face_item, mf_facedb.item_size);
    if (face_item->index != oft)
    {
        es_facedb_debug("flash dirty! oft=%x, info.index %d != id %d\r\n", image_address, face_item->index, oft);
		db->hdr.number--;
		db->hdr.index[oft / 32] &= ~(1 << (oft % 32));
		mf_flash.write(mf_facedb.hdr_addr, (uint8_t *)&db->hdr, sizeof(dbf_hdr_t));
        return MF_ERR_FACEDB_ID;
    }
	return MF_ERR_NONE;
}



/*****************************************************************************/
// Private Func 局部函数
/*****************************************************************************/

static uint16_t _mf_facedb_flash_num(void)
{
	return db->hdr.number;
}

static mf_err_t _mf_facedb_flash_del_all(void)
{
    db->hdr.number = 0;
    memset((void *)db->hdr.index, 0, sizeof(db->hdr.index));
    mf_flash.write(mf_facedb.hdr_addr, (uint8_t *)&db->hdr, sizeof(dbf_hdr_t));
    memset(db->uid_table, 0, FACEDB_UID_LEN * FACE_DATA_MAX_COUNT);
	return MF_ERR_NONE;
}

static int32_t _mf_facedb_flash_uid2oft(uint8_t* uid)
{
    uint32_t i;
    for (i = 0; i < FACE_DATA_MAX_COUNT; i++)
    {
        if ((db->hdr.index[i / 32] >> (i % 32)) & 0x1)
        {
            if (memcmp(db->uid_table[i], uid, FACEDB_UID_LEN) == 0)
            {
                return i;
            }
        }
    }
    return -1;
}

static int32_t _mf_facedb_flash_vid2oft(uint32_t vid)
{
    uint32_t i;
	int cnt = 0;
    for (i = 0; i < FACE_DATA_MAX_COUNT; i++)
    {
        if ((db->hdr.index[i / 32] >> (i % 32)) & 0x1)
        {
			if(cnt == vid)
			{
				return i;
			}
			cnt++;
        }
    }
    return -1;
}

static int32_t _mf_facedb_flash_uid2vid(uint8_t* uid)
{
    uint32_t i;
	int cnt = 0;
    for (i = 0; i < FACE_DATA_MAX_COUNT; i++)
    {
        if ((db->hdr.index[i / 32] >> (i % 32)) & 0x1)
        {
            if (memcmp(db->uid_table[i], uid, FACEDB_UID_LEN) == 0)
            {
                return cnt;
            }
			cnt++;
        }
    }
    return -1;
}


static mf_err_t _mf_facedb_flash_add(uint8_t *uid, void* item)
{
	dbf_item_t* db_item = (dbf_item_t*)item;

	if(mf_facedb.uid2oft(uid) >= 0) {
		es_facedb_debug("uid exist!\r\n");
		return MF_ERR_FACEDB_ID;
	}

    int oft = _db_get_free_oft();
    if (oft >= FACE_DATA_MAX_COUNT || oft < 0)
    {
        es_facedb_debug("get_face_id err\n");
        return MF_ERR_FACEDB_ID;
    }
    // es_facedb_debug("Save oft is %d\n", oft);
	
	db_item->index = oft;
	memcpy(db_item->uid, uid, FACEDB_UID_LEN);
    mf_flash.write(item_addr(oft), (uint8_t *)db_item, mf_facedb.item_size);
    db->hdr.number++;
    db->hdr.index[oft / 32] |= (1 << (oft % 32));
    mf_flash.write(mf_facedb.hdr_addr, (uint8_t *)&db->hdr, sizeof(dbf_hdr_t));
	memcpy(db->uid_table[oft], uid, FACEDB_UID_LEN);
	
	if(mf_facedb.ops_cb) {
		mf_facedb.ops_cb(MF_DBOPS_ADD, (uint32_t)oft, item);
	}

	if(mf_facedb.user_ops_cb) {
		mf_facedb.user_ops_cb(MF_DBOPS_ADD, (uint32_t)oft, item);
	}

    return MF_ERR_NONE;
}

static mf_err_t _mf_facedb_flash_add_fast(uint8_t *uid, uint8_t stat, uint8_t* ftr, uint8_t* name, uint8_t* note)
{
	dbf_item_t* item     = (dbf_item_t*)mf_facedb.item_buf;
	dbmeta_t* meta  = (dbmeta_t*)item->meta;

#if FRETRUE_IR_ENABLE
	meta->stat = stat;
	if(stat == 1) {
		memcpy(meta->ftr_ir, ftr, mf_model.ftr_len);
		memset(meta->ftr_rgb, 0, mf_model.ftr_len);
	} else {
		memcpy(meta->ftr_rgb, ftr, mf_model.ftr_len);
		memset(meta->ftr_ir, 0, mf_model.ftr_len);
	}
#else
	meta->stat = 0;
	memcpy(meta->ftr_rgb, ftr, mf_model.ftr_len);
#endif
	meta->valid = 1;
	if(name == NULL) {
		char str[10];
		sprintf(str, "User%d", mf_facedb.num());
		strcpy(meta->name, str);
	} else {
		strncpy(meta->name, (char*)name, FACEDB_NAME_LEN);
	}
	if(note == NULL) {
		strncpy(meta->note, "none", FACEDB_NOTE_LEN);
	} else {
		strncpy(meta->note, (char*)note, FACEDB_NOTE_LEN);
	}
	return mf_facedb.add(uid, item);
}

////////////////////////////////////////////////////////////////////////////
static mf_err_t _mf_facedb_flash_del_oft(uint32_t oft)
{
    if (db->hdr.number == 0)
    {
        es_facedb_debug("del face, no face\n");
        return MF_ERR_FACEDB_DEL;
    }
	
	if ((db->hdr.index[oft / 32] >> (oft % 32) & 0x1) == 0) {
		es_facedb_debug("item %d not exist!\r\n", oft);
		return MF_ERR_FACEDB_ID;
	}

	db->hdr.index[oft / 32] &= ~(1 << (oft % 32));
	db->hdr.number--;
	mf_flash.write(mf_facedb.hdr_addr, (uint8_t *)&db->hdr, sizeof(dbf_hdr_t));
	memset(db->uid_table[oft], 0, FACEDB_UID_LEN);
	
	if(mf_facedb.ops_cb) {
		mf_facedb.ops_cb(MF_DBOPS_DEL, oft, NULL);
	}

	if(mf_facedb.user_ops_cb) {
		mf_facedb.user_ops_cb(MF_DBOPS_DEL, oft, NULL);
	}

    return MF_ERR_NONE;
}

static mf_err_t _mf_facedb_flash_get_oft(uint32_t oft, void* item)
{
	dbf_item_t* face_item = (dbf_item_t*)item;
	mf_err_t err = _mf_facedb_flash_tryread(oft, face_item);
	if(err != MF_ERR_NONE) return err;
	
	if(mf_facedb.ops_cb) {
		mf_facedb.ops_cb(MF_DBOPS_GET, oft, item);
	}

	if(mf_facedb.user_ops_cb) {
		mf_facedb.user_ops_cb(MF_DBOPS_GET, oft, item);
	}

    return MF_ERR_NONE;
}

//info里包含了index信息，所以直接使用info中的信息
//此函数只是预留给其它facedb的兼容实现
static mf_err_t _mf_facedb_flash_update_oft(uint32_t oft, void* item)
{
	dbf_item_t *face_item = (dbf_item_t *)item;
	mf_err_t err = _mf_facedb_flash_tryread(oft, face_item);
	if(err != MF_ERR_NONE) return err;
	
	mf_flash.write(item_addr(face_item->index), (uint8_t *)face_item, mf_facedb.item_size);
	memcpy(db->uid_table[oft], face_item->uid, FACEDB_UID_LEN);
	if(mf_facedb.ops_cb) {
		mf_facedb.ops_cb(MF_DBOPS_UPDATE, oft, item);
	}

	if(mf_facedb.user_ops_cb) {
		mf_facedb.user_ops_cb(MF_DBOPS_UPDATE, oft, item);
	}
    return MF_ERR_NONE;
}

////////////////////////////////////////////////////////////////////////////
static mf_err_t _mf_facedb_flash_del_vid(uint32_t vid)
{
    if (db->hdr.number == 0)
    {
        es_facedb_debug("del face, no face\n");
        return MF_ERR_FACEDB_DEL;
    }

	int32_t oft = _mf_facedb_flash_vid2oft(vid);
	if(oft >= 0){
		return _mf_facedb_flash_del_oft(oft);
	} else {
		return MF_ERR_FACEDB_ID;
	}
    return MF_ERR_FACEDB_DEL;
}

static mf_err_t _mf_facedb_flash_get_vid(uint32_t vid, void* item)
{
	int32_t oft = _mf_facedb_flash_vid2oft(vid);
	if(oft >= 0) {
		return _mf_facedb_flash_get_oft(oft, item);
	}else {
		return MF_ERR_FACEDB_ID;
	}
    return MF_ERR_FACEDB_ID;
}

//info里包含了index信息，所以直接使用info中的信息
//此函数只是预留给其它facedb的兼容实现
static mf_err_t _mf_facedb_flash_update_vid(uint32_t vid, void* item)
{
	dbf_item_t *face_item = (dbf_item_t *)item;
	mf_flash.write(item_addr(face_item->index), (uint8_t *)face_item, mf_facedb.item_size);
    return MF_ERR_NONE;
}


////////////////////////////////////////////////////////////////////////////

static mf_err_t _mf_facedb_flash_del_uid(uint8_t* uid)
{
    if (db->hdr.number == 0)
    {
        es_facedb_debug("del face, no face\n");
        return MF_ERR_FACEDB_DEL;
    }
	
	int32_t oft = _mf_facedb_flash_uid2oft(uid);
	if(oft >= 0){
		return _mf_facedb_flash_del_oft(oft);
	} else {
		return MF_ERR_FACEDB_ID;
	}
    return MF_ERR_FACEDB_DEL;
}

static mf_err_t _mf_facedb_flash_get_uid(uint8_t* uid, void* item)
{
	int32_t oft = _mf_facedb_flash_uid2oft(uid);
	if(oft >= 0) {
		return _mf_facedb_flash_get_oft(oft, item);
	}else {
		return MF_ERR_FACEDB_ID;
	}
    return MF_ERR_FACEDB_ID;
}

//info里包含了index信息，所以直接使用info中的信息
//此函数只是预留给其它facedb的兼容实现
static mf_err_t _mf_facedb_flash_update_uid(uint8_t* uid, void* item)
{
	dbf_item_t *face_item = (dbf_item_t *)item;
	mf_flash.write(item_addr(face_item->index), (uint8_t *)face_item, mf_facedb.item_size);
    return MF_ERR_NONE;
}

////////////////////////////////////////////////////////////////////////////
static uint32_t iter_oft;
static uint32_t iter_vid;
static void _mf_facedb_flash_iterate_init(void)
{
	iter_oft = 0;
	iter_vid = 0;
	return;
}

static uint32_t _mf_facedb_flash_iterate(void* item, uint32_t* vid, uint32_t* oft)
{
	dbf_item_t* face_item = (dbf_item_t*)item;
	while((iter_oft<FACE_DATA_MAX_COUNT) && (iter_vid<db->hdr.number)){
		if((db->hdr.index[iter_oft / 32] >> (iter_oft % 32)) & 0x1)
		{
			*oft = iter_oft;
			*vid = iter_vid;
			_mf_facedb_flash_get_oft(iter_oft, face_item);
			iter_oft++;
			iter_vid++;
			return 1;
		} else {
			iter_oft++;
		}
	}
	//no item any more
	return 0;
}

static mf_err_t _mf_facedb_flash_get_uid_meta(void* item, uint8_t** uid, void** meta)
{
	dbf_item_t* face_item = (dbf_item_t*)item;
	*uid = face_item->uid;
	*meta = (void*)face_item->meta;
	return MF_ERR_NONE;
}

static void _mf_facedb_flash_selftest(void)
{
	uint32_t v_num = db->hdr.number;
	uint32_t dirty_num = 0;
	es_facedb_debug("[mf_facedb_flash]there is %d face\r\n", v_num);
	v_num = 0;
	dbf_item_t* face_item = (dbf_item_t*)mf_facedb.item_buf;
	//calculate all valid face
	for (uint32_t i = 0; i < FACE_DATA_MAX_COUNT; i++)
	{
		if ((db->hdr.index[i / 32] >> (i % 32)) & 0x1)
		{
			uint32_t image_address = item_addr(i);
			mf_flash.read(image_address, (uint8_t *)face_item, mf_facedb.item_size);
			if (face_item->index != i)
			{
				es_facedb_debug("flash dirty %04d! info.index %d != oft %d\r\n", dirty_num, face_item->index, i);
				dirty_num++;
				db->hdr.index[i / 32] &= ~(1 << (i % 32));
			} else {
				v_num++;
			}
		}
	}
	if (v_num != db->hdr.number)
	{
		es_facedb_debug("err:> index is %d, but saved is %d\r\n", v_num, db->hdr.number);
		db->hdr.number = v_num;
		dirty_num++;
	}
	if(dirty_num > 0) { //将前面修复的表项写入
		mf_flash.write(mf_facedb.hdr_addr, (uint8_t *)&db->hdr, sizeof(dbf_hdr_t));
	}

	if (v_num >= FACE_DATA_MAX_COUNT)
	{	
		es_facedb_debug("ERR, too many face\r\n");
		while(1){};
	}
	return;
}


static mf_err_t _mf_facedb_flash_init(uint32_t db_addr)
{
	mf_facedb.item_size = sizeof(dbmeta_t) + sizeof(dbf_item_t);
	if(mf_facedb.item_size<=256) mf_facedb.item_size = 256;
	else if(mf_facedb.item_size<=512) mf_facedb.item_size = 512;
	else if(mf_facedb.item_size<=1024) mf_facedb.item_size = 1024;
	else if(mf_facedb.item_size<=2048) mf_facedb.item_size = 2048;
	else if(mf_facedb.item_size<=4096) mf_facedb.item_size = 4096;
	else return MF_ERR_FACEDB_TYPE;  //因为flash一个扇区为4KB
	
	es_facedb_debug("mf_facedb.meta_size:%d\r\n", mf_facedb.item_size);
	db = es_malloc(sizeof(mf_facedb_flash_t));
	if(db == NULL)
	{
		return MF_ERR_FACEDB_NOMEM;
	}
	mf_facedb.item_buf = es_malloc(mf_facedb.item_size);
	if(mf_facedb.item_buf == NULL)
	{
		es_free(db);
		return MF_ERR_FACEDB_NOMEM;
	}
	
	mf_facedb.hdr_addr = db_addr;
	mf_facedb.data_addr = db_addr + 0x10000;
	
    mf_flash.read(mf_facedb.hdr_addr, (uint8_t *)&db->hdr, sizeof(dbf_hdr_t));

    if (db->hdr.header == FACE_HEADER)
    {
        es_facedb_debug("[mf_facedb_flash]The header ok\r\n");
		_mf_facedb_flash_selftest();
		//read uid table from flash
        _db_init_uid_table(0);
    }
    else
    {
        es_facedb_debug("No header\r\n");
        db->hdr.header = FACE_HEADER;
        db->hdr.number = 0;
        memset((void *)db->hdr.index, 0, sizeof(db->hdr.index));
        mf_flash.write(mf_facedb.hdr_addr, (uint8_t *)&db->hdr, sizeof(dbf_hdr_t));

        _db_init_uid_table(1);
    }

    return MF_ERR_NONE;
}

static void _mf_facedb_flash_deinit(void)
{
	es_free(mf_facedb.item_buf);
	es_free(db);
	return;
}


static mf_err_t _mf_facedb_update_feature_sha256(void)
{
	uint16_t face_num = mf_facedb.num();
	if(face_num == 0) {
        memset(facedb_featrue_sha256, 0xFF, 32);
        facedb_is_update_sha256 = 1;
		return MF_ERR_NONE;
    }

	uint8_t flag = 0, fea[196];
    dbf_item_t *item = (dbf_item_t*)mf_facedb.item_buf;
    dbmeta_t* meta  = (dbmeta_t*)item->meta;

    memset(fea, 0, sizeof(fea));

    for(uint16_t i = 0; i < face_num; i ++) {
        if(mf_facedb.get_vid(i, (void*)item) == MF_ERR_NONE) {
            if(meta->stat == 2) {
                if(flag) {
                    for (uint16_t j = 0; j < 196; j++)
                    {
                        fea[j] ^= meta->ftr_rgb[j];
                    }
                } else {
                    flag = 1;
                    memcpy(fea, meta->ftr_rgb, 196);
                }
            } else {
                es_facedb_debug("get_vid %d, error stat\r\n", i);
            }
        } else {
            es_facedb_debug("get_vid %d failed\r\n", i);
        }
    }

    sha256_hard_calculate(fea, 196, facedb_featrue_sha256);

	return MF_ERR_NONE;
}

static mf_err_t _mf_facedb_get_feature_sha256(uint8_t *sha256)
{
	if (0 == facedb_is_update_sha256) {
		_mf_facedb_update_feature_sha256();
	}

	
	memcpy(sha256, facedb_featrue_sha256, 32);
	return MF_ERR_NONE;
}

/*****************************************************************************/
// Public Var 全局变量
/*****************************************************************************/

mf_facedb_t mf_facedb_flash = {
	.init_flag   = 0,
	.item_size   = 512,
				 
	.init        = _mf_facedb_flash_init,
	.deinit      = _mf_facedb_flash_deinit,
				 
	.num         = _mf_facedb_flash_num,
	.del_all     = _mf_facedb_flash_del_all,
	.uid2oft     = _mf_facedb_flash_uid2oft,
	.vid2oft     = _mf_facedb_flash_vid2oft,
	.uid2vid     = _mf_facedb_flash_uid2vid,
				 
	.add         = _mf_facedb_flash_add,
	.add_fast    = _mf_facedb_flash_add_fast,
	
	.del_oft     = _mf_facedb_flash_del_oft,
	.get_oft     = _mf_facedb_flash_get_oft,
	.update_oft  = _mf_facedb_flash_update_oft,
				 
	.del_vid     = _mf_facedb_flash_del_vid,
	.get_vid     = _mf_facedb_flash_get_vid,
	.update_vid  = _mf_facedb_flash_update_vid,
				 
	.del_uid     = _mf_facedb_flash_del_uid,
	.get_uid     = _mf_facedb_flash_get_uid,
	.update_uid  = _mf_facedb_flash_update_uid,
	
	.ops_cb      = NULL,
	.user_ops_cb = NULL,
	.iterate_init=_mf_facedb_flash_iterate_init,
	.iterate     = _mf_facedb_flash_iterate,
	.get_uid_meta= _mf_facedb_flash_get_uid_meta,
	.get_feature_sha256 = _mf_facedb_get_feature_sha256,
	.update_feature_sha256 = _mf_facedb_update_feature_sha256,
};