import struct
import sys
import os

if len(sys.argv) < 2:
    print("%s $src_file $dst_file"%(sys.argv[0]))
    exit(0)

src_file = sys.argv[1]
dst_file = sys.argv[2]

if not os.path.exists(src_file):
    print('File Path Invalid! Exiting...')
    exit(1)

if os.path.exists(dst_file):
    print("Delete dst_file")
    os.remove(sys.argv[2])

print("Source file: {0}\nTarget File: {1}\n".format(src_file, dst_file))

try:
    sf = open(src_file, "rb")
    df = open(dst_file, "wb")

    buf_tmp = [b'0' for x in range(0, 4)]
    contents = sf.read()
    buf_size = (int(contents.__len__() / 4) + 1) * 4
    #print("buf_size:%d\n", buf_size)
    file_data_bytes = bytearray(buf_size)

    for i in range(0, contents.__len__()):
        file_data_bytes[i] = contents[i]


    for i in range(0, buf_size, 4):
        buf_tmp[3] = file_data_bytes[i]
        buf_tmp[2] = file_data_bytes[i+1]
        buf_tmp[1] = file_data_bytes[i+2]
        buf_tmp[0] = file_data_bytes[i+3]


        # pack into bytes flow
        tmp_bytes = struct.pack("4B", buf_tmp[0], buf_tmp[1], buf_tmp[2], buf_tmp[3])
        df.write(tmp_bytes)
finally:
    if sf:
        sf.close()
    if df:
        df.close()

print("Convert Completed!")
