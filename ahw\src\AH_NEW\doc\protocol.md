# 设备和服务器交互协议



| 版本   | 修改内容                            | 时间       | 修改人 |
| ------ | ----------------------------------- | ---------- | ------ |
| v1.0.0 | 初始化版本<br/>添加获取设备配置协议 | 2020.08.08 | 林尔升 |





### 概述

---

* 设备和服务器之间的交互主要采用MQTT协议，数据内容为json格式

* 支持设备实时上报数据到服务器，支持服务器实时下发控制数据到设备
* 协议是在原有的基础上定义实现的



### 时序图

-----

![](./pic/protocol.png)



### 协议

---

##### 基本格式

```json
{"c":1,"t":"84:0D:8E:6C:3F:24","f":"server","mi":"1234567890","m":{"time":"2019-10-17 15:29:00"}}
```

* c: 指令代码，数字
* t: 目标设备，字符串
* f: 发送方，字符串
* mi: 消息ID，字符串，使用时间戳
* m: 消息内容，Json对象

`'t'和'f'只能取值为"server"或者设备的mac地址，其他的为非法内容`

`所有消息都是基于此协议格式，区别在于消息内容`



##### 设备上报采集数据(16)

* 采集数据主要包括温度采集、人头计数
* 采用主动上报方式，设备采集到数据后，马上上报到服务器
* 字段"temp"，表示温度，字段可选
* 字段"person"，表示人头计数，字段可选
* 字段"nfc"，表示接收到nfc的刷卡
* 字段"ret"，表示返回值，0表示执行成功，其他表示失败
* 字段"des"，表示错误描述

```json
# 设备 -> 服务器，设备主动上报外设数据
{"c":16,"f":"84:0D:8E:6C:3F:24","t":"server","mi":"1234567890","m":{"temp":36.8,"person":1}}

// 上报NFC，n表示nfc卡号，16进制字符串，t表示10位的时间戳
{"c":16,"f":"84:0D:8E:6C:3F:24","t":"server","mi":"1234567890","m":{"nfc":[{"n""1122334455","t":"1234567890"},{"n":"aabbccdd","t":"1234567890"}]}

# 服务器 -> 设备，服务器响应
{"c":16,"t":"84:0D:8E:6C:3F:24","f":"server","mi":"1234567890","m":{"ret":0,"des":"success"}}
```



##### 服务器控制命令下发(17)

* 由用户操作APP（小程序），通过服务器下发控制指令到设备
* 控制命令实时下发，每次只能下发一个命令，命令只能携带本命令相关的参数
* 字段"cmd"，表示命令类型，后续将会有多个命令，如取电命令、删除用户、解冻用户等
* 字段"ret"，表示返回值，0表示执行成功，其他表示失败
* 字段"des"，表示错误描述

```json
# 服务器 -> 设备,服务器主动下发
{"c":17,"t":"84:0D:8E:6C:3F:24","f":"server","mi":"1234567890","m"{"cmd":"get_power","card":"12345678","value":1}}

# 设备 -> 服务器，设备响应服务器
{"c":17,"f":"84:0D:8E:6C:3F:24","t":"server","mi":"1234567890","m":{"ret":0,"des":"success"}}

```

   **命令列表：**

* 取电命令（get_power)，card为卡号，value为取电值（0：关闭取电，1：打开取电）

  **`时间格式说明：`**

  `年月日时分，示例12090D0C05
  12代表18年、09代表09月、0D代表13日
  0C代表12时、05代表05分（全部为16进制）`

  ```json
  // start_time,开始时间
  {"cmd":"get_power","card":"12345678","value":1,"start_time":"14070B0C2232", "end_time":"14090B0C2232"}
  ```

  

* 删除用户(del_user)

  `无参数`
  
  ```
{"cmd":"del_user"}
  ```
  



* 冻结/解冻用户

  `value为1，表示解冻用户`

  `value为0，表示冻结用户`

  ```json
  {"cmd":"user_freeze", "value":1}
  ```



* 蓝牙透传

  `mac`，蓝牙的MAC地址

  `value`,透传的内容，16进制字符串，设备发送到蓝牙模块时，需要转成对应的16进制

  ```json
  {"cmd":"ble_payload", "mac":"112233445566", "value":"1122aabbcc"}
  ```

  

* 抓拍(snapshot)

  `无参数`
  
```
{"cmd":"snapshot"}
```



##### 设备配置信息(18)

* 在设备上电时，主动到服务器获取配置参数
* 在用户修改了配置参数后，服务器自动下发配置参数
* 在没有联网之前，以设备本地的配置为准，当设备联网后，主动同步服务器的配置
* 下发参数时，每次只能下发一个配置参数，只能携带本配置相关的参数
* 字段"cfg"，表示配置参数类型
* 字段"ret"，表示返回值，0表示执行成功，其他表示失败
* 字段"des"，表示错误描述

```json
### 用户修改参数后，服务器自动下发
# 服务器 -> 设备,服务器主动下发
{"c":18,"t":"84:0D:8E:6C:3F:24","f":"server","mi":"1234567890","m"{"cmd":"wgd","t":26,"val":[4662575,13363568]}}

# 设备 -> 服务器，设备响应服务器
{"c":18,"f":"84:0D:8E:6C:3F:24","t":"server","mi":"1234567890","m":{"ret":0,"des":"success"}}

### 设备上电，联网成功，主动请求配置参数,之后就触发服务器主动下发配置
### 服务器根据收到设备的内容做不同的处理，如果设备上报的携带了"ret"字段，说明设备收到了服务器下发的配置
### 如果设备上报的携带了"cfg"字段，说明设备在请求服务器下发配置
{"c":18,"f":"84:0D:8E:6C:3F:24","t":"server","mi":"1234567890","m":{"cfg":"wgd"}}

```



* 韦根协议

  `t:协议类型，如韦根26、34、36、44`

  `val:韦根卡号值数组，如卡号为：0004662575 071,09519，只需要把0004662575传入即可val为数值类型，不需要把前面的0传入`

  ```json
  {"cmd":"wgd","t":26,"val":[4662575,13363568]}
  ```

  

* NFC卡号

  `val:nfc卡的值，数值为16进制的字符串，长度为4字节（A卡）或者10字节（B卡）`

  `pkgid:包ID，每包NFC卡数据控制在100条以内`

  `pkgnum:包总数，在最后一包数据才写入flash`

  ```json
  {"cmd":"nfc","pkgid":0,"pkgnum":10,"val":["2a818516","11223344556677889900"]}
  ```

  

* 433协议

  `val:433数据一般是4字节，要是自定义数据是无法确定长度，数值是16进制的字符串`

  ```json
  {"cmd":"433","val":["11223344","aabbccdd"]}
  ```




* 蓝牙协议

  `mac:蓝牙MAC地址，6字节，即12字符，数值是16进制的字符串`

  `data:蓝牙模块的透传数据(或控制数据)，可选字段，数值是16进制的字符串`
  
  `val:蓝牙模块列表`

  `serv_uuid:服务uuid`
  
  `char_uuid:特征uuid`
  
  ```json
  {"cmd":"ble","val"[{"mac":"112233445566","data":"aabbcc","serv_uuid":"0000ff0000001000800000805f9b34fb","char_uuid":"0000ff0100001000800000805f9b34fb"}, {"mac":"aabbccddeeff","data":"aabbcc","serv_uuid":"0000ff0000001000800000805f9b34fb","char_uuid":"0000ff0100001000800000805f9b34fb"}]}
  ```

##### 设备上报离线记录(26)

* 采集数据主要包括温度采集、人头计数
* 采用主动上报方式
* 字段"u", 表示uid
* 字段"tm", 通过时间戳，10位数字
* 字段"tp"，表示温度，字段可选,暂时没有值
* 字段"records"，离线记录数据
* 字段"ret"，表示返回值，0表示执行成功，其他表示失败
* 字段"des"，表示错误描述

```json
# 设备 -> 服务器
{"c":16,"f":"84:0D:8E:6C:3F:24","t":"server","mi":"1234567890","m":{"records":[{"u":"fdsfs","tm":1234,"tp":"36.8"},{"u":"aavv","tm":1234,"tp":"36.7"}]}}


# 服务器 -> 设备，服务器响应
{"c":26,"t":"84:0D:8E:6C:3F:24","f":"server","mi":"1234567890","m":{"ret":0,"des":"success"}}
```
  
  

