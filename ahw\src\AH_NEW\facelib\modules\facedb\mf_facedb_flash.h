#ifndef _MF_FACEDB_FLASH_H
#define _MF_FACEDB_FLASH_H


/*****************************************************************************/
// Enums & Macro
/*****************************************************************************/

//Face log	
//size=0x1000, 4KB,32768人
// #define FACEDB_HDR_ADDR                             (0x800000)  
// #define FACEDB_DATA_ADDR                         (0x810000)
//size=0x1000, 4KB,32768人
#define FACEDB_UID_LEN                              (32)
#define FACEDB_NAME_LEN                             (16)
#define FACEDB_NOTE_LEN                             (16) //8M+64K
#define FACEDB_WIEGAND_LEN                          (6)
#define MAX_FTR_LEN                                 (200)
#define FACEDB_FTR_DIMENSION                        (196)
#define MAX_FTR_BASE64_STR_LEN                      ((MAX_FTR_LEN/3)*4)
#define FACE_HEADER                                 (0x55AA5503)
#define FACE_DATA_MAX_COUNT                         (2048)

#define FRETRUE_IR_ENABLE                           (0)


//
/*****************************************************************************/
// Types
/*****************************************************************************/
//人脸数据库存储格式
//8MB开始，facedb_info_t, 记录头部，版本，数量，校验和等基础信息
//并使用位段方式存储后面flash里的单位存储有效性
typedef struct _face_save_info_t
{
    uint32_t header;
    uint32_t version;
    uint32_t number;
    uint32_t checksum;
    uint32_t index[FACE_DATA_MAX_COUNT / 32 + 1];
} dbf_hdr_t __attribute__((aligned(8)));


typedef struct
{
    uint32_t valid;
    uint32_t stat; //0,rgb; 1,ir; 2,ir+rgb
    int8_t ftr_rgb[MAX_FTR_LEN];  //fea_rgb
#if FRETRUE_IR_ENABLE
    int8_t ftr_ir[MAX_FTR_LEN];   //fea_ir
#endif
    char name[FACEDB_NAME_LEN];
    char note[FACEDB_NOTE_LEN];
    uint32_t start_time;
    uint32_t end_time;
} dbmeta_t;

typedef struct 
{
    uint32_t index;
    // uint32_t uid_hash;
    uint8_t uid[FACEDB_UID_LEN];
	uint8_t meta[0]; // pointer of dbmeta_t
} dbf_item_t;



/* 一个人是512字节，这里我们保证是4096的倍数 */
#define FLASH_PER_FACE_INFO_NUM (8)
/*
typedef struct
{
    uint32_t start_id;
    face_info_t face_info[FLASH_PER_FACE_INFO_NUM];
} mut_face_info_t;*/








/*****************************************************************************/
// Functions
/*****************************************************************************/


/*****************************************************************************/
// Vars
/*****************************************************************************/

#endif