/*******************************************************************************
 * Size: 16 px
 * Bpp: 4
 * Opts: --no-compress --no-prefilter --bpp 4 --size 16 --font <PERSON><PERSON><PERSON><PERSON>.ttf -r 0x20-0x7f,0x5d0-0x5ea,0x600-0x6FF,0xFB50-0xFDFF,0xFE70-0xFEFF --font <PERSON><PERSON><PERSON>wesome5-Solid+Brands+Regular.woff -r 61441,61448,61451,61452,61452,61453,61457,61459,61461,61465,61468,61473,61478,61479,61480,61502,61507,61512,61515,61516,61517,61521,61522,61523,61524,61543,61544,61550,61552,61553,61556,61559,61560,61561,61563,61587,61589,61636,61637,61639,61641,61664,61671,61674,61683,61724,61732,61787,61931,62016,62017,62018,62019,62020,62087,62099,62212,62189,62810,63426,63650 --format lvgl -o lv_font_dejavu_16_persian_hebrew.c --force-fast-kern-format
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "../../lvgl.h"
#endif

#ifndef LV_FONT_DEJAVU_16_PERSIAN_HEBREW
#define LV_FONT_DEJAVU_16_PERSIAN_HEBREW 1
#endif

#if LV_FONT_DEJAVU_16_PERSIAN_HEBREW

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0x9f, 0x9f, 0x9f, 0x9f, 0x9f, 0x8f, 0x8e, 0x6d,
    0x0, 0x0, 0x9f, 0x9f,

    /* U+0022 "\"" */
    0x7e, 0x8, 0xd7, 0xe0, 0x8d, 0x7e, 0x8, 0xd7,
    0xe0, 0x8d, 0x24, 0x2, 0x40,

    /* U+0023 "#" */
    0x0, 0x0, 0x5e, 0x0, 0xc7, 0x0, 0x0, 0x0,
    0x8b, 0x0, 0xf3, 0x0, 0x0, 0x0, 0xb8, 0x3,
    0xf0, 0x0, 0x0, 0x0, 0xf4, 0x6, 0xd0, 0x0,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x2, 0x38,
    0xd3, 0x3e, 0x73, 0x30, 0x0, 0xa, 0x90, 0x1f,
    0x20, 0x0, 0x0, 0xe, 0x50, 0x5e, 0x0, 0x0,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0x40, 0x23, 0x8d,
    0x33, 0xe7, 0x33, 0x0, 0x0, 0xa9, 0x2, 0xf1,
    0x0, 0x0, 0x0, 0xf4, 0x6, 0xd0, 0x0, 0x0,

    /* U+0024 "$" */
    0x0, 0x6, 0x60, 0x0, 0x0, 0x6, 0x60, 0x0,
    0x5, 0xce, 0xfd, 0xa1, 0x5f, 0x77, 0x84, 0x91,
    0x9d, 0x6, 0x60, 0x0, 0x8f, 0x36, 0x60, 0x0,
    0x1c, 0xfe, 0xc6, 0x10, 0x0, 0x3a, 0xde, 0xe3,
    0x0, 0x6, 0x60, 0xdb, 0x0, 0x6, 0x60, 0xac,
    0x98, 0x47, 0x87, 0xf7, 0x4a, 0xef, 0xfd, 0x70,
    0x0, 0x6, 0x60, 0x0, 0x0, 0x6, 0x60, 0x0,
    0x0, 0x3, 0x30, 0x0,

    /* U+0025 "%" */
    0x2, 0xbf, 0xc3, 0x0, 0x0, 0xa9, 0x0, 0x0,
    0xca, 0x7, 0xe0, 0x0, 0x4e, 0x0, 0x0, 0xf,
    0x30, 0xf, 0x30, 0xd, 0x50, 0x0, 0x0, 0xf2,
    0x0, 0xf3, 0x8, 0xb0, 0x0, 0x0, 0xc, 0x90,
    0x7e, 0x2, 0xf2, 0x0, 0x0, 0x0, 0x2b, 0xfc,
    0x30, 0xb8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5e, 0x1, 0xbe, 0xc3, 0x0, 0x0, 0x0, 0xe,
    0x50, 0xba, 0x6, 0xe0, 0x0, 0x0, 0x8, 0xb0,
    0xf, 0x30, 0xf, 0x40, 0x0, 0x2, 0xf2, 0x0,
    0xf3, 0x0, 0xf4, 0x0, 0x0, 0xc7, 0x0, 0xb,
    0xa0, 0x7e, 0x0, 0x0, 0x5d, 0x0, 0x0, 0x1b,
    0xfd, 0x40,

    /* U+0026 "&" */
    0x0, 0x5d, 0xfe, 0xa0, 0x0, 0x0, 0x4f, 0xa4,
    0x6c, 0x10, 0x0, 0x9, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0x10, 0x0, 0x0, 0x0, 0x2, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0xae, 0xfa, 0x0, 0x0,
    0x41, 0x7f, 0x35, 0xfa, 0x0, 0x2f, 0x4d, 0xa0,
    0x5, 0xf9, 0x6, 0xf0, 0xf9, 0x0, 0x6, 0xf9,
    0xd9, 0xc, 0xd0, 0x0, 0x6, 0xff, 0x10, 0x3f,
    0xb3, 0x13, 0xaf, 0xf8, 0x0, 0x3b, 0xef, 0xd9,
    0x27, 0xf7,

    /* U+0027 "'" */
    0x7e, 0x7e, 0x7e, 0x7e, 0x24,

    /* U+0028 "(" */
    0x0, 0x8b, 0x2, 0xf3, 0x9, 0xc0, 0xe, 0x70,
    0x3f, 0x30, 0x7f, 0x0, 0x9f, 0x0, 0x9e, 0x0,
    0x8f, 0x0, 0x5f, 0x10, 0x2f, 0x50, 0xc, 0xa0,
    0x5, 0xf0, 0x0, 0xd7, 0x0, 0x36,

    /* U+0029 ")" */
    0x7c, 0x0, 0xe, 0x50, 0x9, 0xc0, 0x3, 0xf3,
    0x0, 0xf7, 0x0, 0xcb, 0x0, 0xbc, 0x0, 0xad,
    0x0, 0xbc, 0x0, 0xe9, 0x1, 0xf5, 0x6, 0xf0,
    0xb, 0x90, 0x3f, 0x10, 0x45, 0x0,

    /* U+002A "*" */
    0x0, 0x7, 0x70, 0x0, 0x24, 0x7, 0x70, 0x42,
    0x2b, 0xb8, 0x9a, 0xb2, 0x0, 0x4f, 0xf4, 0x0,
    0x5, 0xcb, 0xcc, 0x50, 0x4a, 0x17, 0x71, 0x94,
    0x0, 0x7, 0x70, 0x0, 0x0, 0x2, 0x20, 0x0,

    /* U+002B "+" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0xf, 0x50, 0x0,
    0x0, 0x0, 0x0, 0xf5, 0x0, 0x0, 0x1, 0x11,
    0x1f, 0x61, 0x11, 0x4, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x14, 0x44, 0x4f, 0x84, 0x44, 0x30, 0x0,
    0x0, 0xf5, 0x0, 0x0, 0x0, 0x0, 0xf, 0x50,
    0x0, 0x0, 0x0, 0x0, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0xf, 0x50, 0x0, 0x0,

    /* U+002C "," */
    0x1d, 0x72, 0xf7, 0x6f, 0x1a, 0x80,

    /* U+002D "-" */
    0x1, 0x11, 0x13, 0xff, 0xff, 0x3, 0x33, 0x30,

    /* U+002E "." */
    0x4f, 0x54, 0xf5,

    /* U+002F "/" */
    0x0, 0x1, 0xf3, 0x0, 0x6, 0xe0, 0x0, 0xb,
    0x90, 0x0, 0xf, 0x40, 0x0, 0x5f, 0x0, 0x0,
    0xaa, 0x0, 0x0, 0xf5, 0x0, 0x4, 0xf1, 0x0,
    0x9, 0xb0, 0x0, 0xe, 0x60, 0x0, 0x3f, 0x10,
    0x0, 0x8c, 0x0, 0x0, 0xd7, 0x0, 0x0,

    /* U+0030 "0" */
    0x1, 0xae, 0xfb, 0x20, 0x0, 0xde, 0x65, 0xde,
    0x10, 0x5f, 0x40, 0x1, 0xf8, 0xa, 0xe0, 0x0,
    0xb, 0xd0, 0xdb, 0x0, 0x0, 0x8f, 0xe, 0xa0,
    0x0, 0x7, 0xf1, 0xea, 0x0, 0x0, 0x7f, 0x1d,
    0xb0, 0x0, 0x8, 0xf0, 0xae, 0x0, 0x0, 0xbd,
    0x6, 0xf3, 0x0, 0x1f, 0x80, 0xd, 0xe6, 0x5d,
    0xe1, 0x0, 0x1a, 0xef, 0xb2, 0x0,

    /* U+0031 "1" */
    0x19, 0xcf, 0xf2, 0x0, 0x3e, 0xbb, 0xf2, 0x0,
    0x0, 0x7, 0xf2, 0x0, 0x0, 0x7, 0xf2, 0x0,
    0x0, 0x7, 0xf2, 0x0, 0x0, 0x7, 0xf2, 0x0,
    0x0, 0x7, 0xf2, 0x0, 0x0, 0x7, 0xf2, 0x0,
    0x0, 0x7, 0xf2, 0x0, 0x0, 0x7, 0xf2, 0x0,
    0x5, 0x59, 0xf6, 0x53, 0xf, 0xff, 0xff, 0xfb,

    /* U+0032 "2" */
    0x49, 0xdf, 0xd9, 0x10, 0xcc, 0x75, 0x8f, 0xd0,
    0x20, 0x0, 0x5, 0xf6, 0x0, 0x0, 0x1, 0xf7,
    0x0, 0x0, 0x5, 0xf5, 0x0, 0x0, 0x1e, 0xc0,
    0x0, 0x0, 0xce, 0x20, 0x0, 0xb, 0xf3, 0x0,
    0x0, 0xbf, 0x30, 0x0, 0xa, 0xf3, 0x0, 0x0,
    0x9f, 0x95, 0x55, 0x53, 0xdf, 0xff, 0xff, 0xf9,

    /* U+0033 "3" */
    0x3b, 0xef, 0xeb, 0x30, 0x5a, 0x75, 0x7d, 0xf3,
    0x0, 0x0, 0x1, 0xf8, 0x0, 0x0, 0x0, 0xf8,
    0x0, 0x0, 0x2a, 0xf2, 0x0, 0xcf, 0xfe, 0x30,
    0x0, 0x23, 0x5c, 0xe3, 0x0, 0x0, 0x0, 0xeb,
    0x0, 0x0, 0x0, 0xbe, 0x0, 0x0, 0x1, 0xeb,
    0xb9, 0x65, 0x8e, 0xf3, 0x6c, 0xef, 0xda, 0x20,

    /* U+0034 "4" */
    0x0, 0x0, 0xa, 0xfa, 0x0, 0x0, 0x0, 0x4e,
    0xfa, 0x0, 0x0, 0x0, 0xe6, 0xfa, 0x0, 0x0,
    0x9, 0xc0, 0xfa, 0x0, 0x0, 0x3f, 0x20, 0xfa,
    0x0, 0x0, 0xc8, 0x0, 0xfa, 0x0, 0x7, 0xe0,
    0x0, 0xfa, 0x0, 0x1f, 0x61, 0x11, 0xfa, 0x10,
    0x3f, 0xff, 0xff, 0xff, 0xf4, 0x4, 0x44, 0x44,
    0xfb, 0x41, 0x0, 0x0, 0x0, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0xfa, 0x0,

    /* U+0035 "5" */
    0x4f, 0xff, 0xff, 0xe0, 0x4f, 0x75, 0x55, 0x40,
    0x4f, 0x20, 0x0, 0x0, 0x4f, 0x20, 0x0, 0x0,
    0x4f, 0xff, 0xfa, 0x20, 0x39, 0x54, 0x7f, 0xe1,
    0x0, 0x0, 0x3, 0xf8, 0x0, 0x0, 0x0, 0xeb,
    0x0, 0x0, 0x0, 0xeb, 0x0, 0x0, 0x4, 0xf8,
    0xb9, 0x66, 0x9f, 0xe1, 0x7c, 0xef, 0xe9, 0x10,

    /* U+0036 "6" */
    0x0, 0x4c, 0xff, 0xc3, 0x0, 0x6f, 0xb7, 0x69,
    0x50, 0x2f, 0x90, 0x0, 0x0, 0x8, 0xf1, 0x0,
    0x0, 0x0, 0xbc, 0x6e, 0xfe, 0x70, 0xd, 0xfe,
    0x64, 0x9f, 0x70, 0xdf, 0x50, 0x0, 0xbe, 0xc,
    0xf0, 0x0, 0x7, 0xf2, 0x9f, 0x0, 0x0, 0x7f,
    0x24, 0xf5, 0x0, 0xc, 0xe0, 0xb, 0xf7, 0x5a,
    0xf6, 0x0, 0x9, 0xef, 0xd5, 0x0,

    /* U+0037 "7" */
    0xbf, 0xff, 0xff, 0xfc, 0x35, 0x55, 0x57, 0xf8,
    0x0, 0x0, 0x8, 0xf2, 0x0, 0x0, 0xe, 0xc0,
    0x0, 0x0, 0x4f, 0x60, 0x0, 0x0, 0xaf, 0x0,
    0x0, 0x0, 0xfa, 0x0, 0x0, 0x6, 0xf4, 0x0,
    0x0, 0xc, 0xe0, 0x0, 0x0, 0x2f, 0x80, 0x0,
    0x0, 0x8f, 0x20, 0x0, 0x0, 0xdc, 0x0, 0x0,

    /* U+0038 "8" */
    0x3, 0xbe, 0xfc, 0x50, 0x3, 0xfc, 0x55, 0xbf,
    0x50, 0x8f, 0x10, 0x0, 0xeb, 0x9, 0xf0, 0x0,
    0xd, 0xb0, 0x2f, 0x91, 0x17, 0xf4, 0x0, 0x3e,
    0xff, 0xf5, 0x0, 0x2e, 0xb4, 0x49, 0xf5, 0xb,
    0xe0, 0x0, 0xb, 0xe0, 0xeb, 0x0, 0x0, 0x8f,
    0x1c, 0xe0, 0x0, 0xb, 0xf0, 0x5f, 0xc5, 0x5a,
    0xf8, 0x0, 0x4c, 0xef, 0xc6, 0x0,

    /* U+0039 "9" */
    0x3, 0xcf, 0xea, 0x10, 0x3, 0xfc, 0x56, 0xed,
    0x0, 0xbe, 0x0, 0x2, 0xf7, 0xe, 0xa0, 0x0,
    0xe, 0xc0, 0xfa, 0x0, 0x0, 0xdf, 0xc, 0xd0,
    0x0, 0x1f, 0xf0, 0x6f, 0x91, 0x2b, 0xff, 0x0,
    0x7f, 0xff, 0xbb, 0xe0, 0x0, 0x2, 0x10, 0xdb,
    0x0, 0x0, 0x0, 0x6f, 0x40, 0x39, 0x66, 0xaf,
    0x90, 0x2, 0xbe, 0xfd, 0x60, 0x0,

    /* U+003A ":" */
    0x2f, 0x81, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0x82, 0xf8,

    /* U+003B ";" */
    0x2f, 0x81, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1d, 0x72, 0xf7, 0x6f, 0x1a, 0x80,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0,
    0x0, 0x28, 0xeb, 0x0, 0x0, 0x16, 0xcf, 0xe8,
    0x20, 0x5, 0xbf, 0xe9, 0x30, 0x0, 0x3f, 0xfa,
    0x50, 0x0, 0x0, 0x3, 0xef, 0xb5, 0x0, 0x0,
    0x0, 0x0, 0x4a, 0xff, 0xa4, 0x0, 0x0, 0x0,
    0x0, 0x5b, 0xfe, 0x93, 0x0, 0x0, 0x0, 0x1,
    0x7d, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x1,

    /* U+003D "=" */
    0x1, 0x11, 0x11, 0x11, 0x11, 0x4, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x14, 0x44, 0x44, 0x44, 0x44,
    0x20, 0x11, 0x11, 0x11, 0x11, 0x10, 0x4f, 0xff,
    0xff, 0xff, 0xff, 0xb1, 0x44, 0x44, 0x44, 0x44,
    0x42,

    /* U+003E ">" */
    0x11, 0x0, 0x0, 0x0, 0x0, 0x4, 0xfa, 0x40,
    0x0, 0x0, 0x0, 0x6, 0xbf, 0xe9, 0x30, 0x0,
    0x0, 0x0, 0x17, 0xcf, 0xd7, 0x20, 0x0, 0x0,
    0x0, 0x28, 0xef, 0x90, 0x0, 0x0, 0x3, 0x9e,
    0xf8, 0x0, 0x2, 0x8d, 0xfc, 0x61, 0x0, 0x7c,
    0xfe, 0x82, 0x0, 0x0, 0x4f, 0x94, 0x0, 0x0,
    0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+003F "?" */
    0x4b, 0xef, 0xb3, 0xd, 0xa5, 0x6e, 0xe1, 0x20,
    0x0, 0x4f, 0x40, 0x0, 0x6, 0xf3, 0x0, 0x3,
    0xfa, 0x0, 0x2, 0xeb, 0x0, 0x0, 0xbd, 0x0,
    0x0, 0xe, 0x90, 0x0, 0x0, 0xc8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xea, 0x0, 0x0, 0xf,
    0xa0, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x7b, 0xee, 0xd8, 0x20, 0x0, 0x0,
    0x3e, 0xd6, 0x43, 0x5a, 0xf6, 0x0, 0x3, 0xf6,
    0x0, 0x0, 0x0, 0x3e, 0x60, 0xe, 0x60, 0x0,
    0x0, 0x0, 0x3, 0xf2, 0x6c, 0x0, 0x1a, 0xed,
    0x6c, 0x50, 0xa8, 0xb6, 0x0, 0xbc, 0x44, 0xcf,
    0x50, 0x6c, 0xe3, 0x2, 0xf2, 0x0, 0x2f, 0x50,
    0x4d, 0xe3, 0x4, 0xf0, 0x0, 0xf, 0x50, 0x5c,
    0xc5, 0x2, 0xf1, 0x0, 0x1f, 0x50, 0xb8, 0x8a,
    0x0, 0xda, 0x11, 0xaf, 0x68, 0xe1, 0x1f, 0x30,
    0x2d, 0xff, 0x9c, 0xfa, 0x10, 0x6, 0xe3, 0x0,
    0x11, 0x1, 0x0, 0x0, 0x0, 0x7f, 0x83, 0x0,
    0x16, 0xd4, 0x0, 0x0, 0x2, 0xaf, 0xff, 0xfb,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x11, 0x0, 0x0,
    0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x9f, 0x80, 0x0, 0x0, 0x0, 0xf,
    0xfe, 0x0, 0x0, 0x0, 0x5, 0xfb, 0xf4, 0x0,
    0x0, 0x0, 0xbe, 0x1f, 0xa0, 0x0, 0x0, 0x1f,
    0x90, 0xaf, 0x10, 0x0, 0x7, 0xf3, 0x4, 0xf6,
    0x0, 0x0, 0xdd, 0x0, 0xe, 0xc0, 0x0, 0x3f,
    0x81, 0x11, 0x9f, 0x20, 0x9, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0xec, 0x44, 0x44, 0x4c, 0xe0, 0x5f,
    0x50, 0x0, 0x0, 0x6f, 0x4b, 0xf0, 0x0, 0x0,
    0x0, 0xfa,

    /* U+0042 "B" */
    0x6f, 0xff, 0xfe, 0xa2, 0x6, 0xf6, 0x44, 0x7e,
    0xe0, 0x6f, 0x20, 0x0, 0x5f, 0x46, 0xf2, 0x0,
    0x4, 0xf4, 0x6f, 0x30, 0x3, 0xce, 0x6, 0xff,
    0xff, 0xfd, 0x20, 0x6f, 0x53, 0x35, 0xce, 0x26,
    0xf2, 0x0, 0x1, 0xfa, 0x6f, 0x20, 0x0, 0xd,
    0xc6, 0xf2, 0x0, 0x1, 0xfb, 0x6f, 0x64, 0x46,
    0xdf, 0x46, 0xff, 0xff, 0xeb, 0x40,

    /* U+0043 "C" */
    0x0, 0x3, 0xae, 0xfe, 0xb5, 0x0, 0x6, 0xfd,
    0x75, 0x6a, 0xf4, 0x3, 0xfa, 0x0, 0x0, 0x2,
    0x30, 0xaf, 0x10, 0x0, 0x0, 0x0, 0xf, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0xf, 0x90, 0x0, 0x0, 0x0, 0x0, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xa0, 0x0, 0x0, 0x23, 0x0,
    0x6f, 0xd7, 0x56, 0xaf, 0x40, 0x0, 0x3a, 0xef,
    0xeb, 0x40,

    /* U+0044 "D" */
    0x6f, 0xff, 0xfd, 0xa5, 0x0, 0x6, 0xf6, 0x45,
    0x7b, 0xfb, 0x0, 0x6f, 0x20, 0x0, 0x6, 0xf9,
    0x6, 0xf2, 0x0, 0x0, 0xb, 0xf0, 0x6f, 0x20,
    0x0, 0x0, 0x6f, 0x36, 0xf2, 0x0, 0x0, 0x4,
    0xf5, 0x6f, 0x20, 0x0, 0x0, 0x5f, 0x56, 0xf2,
    0x0, 0x0, 0x6, 0xf3, 0x6f, 0x20, 0x0, 0x0,
    0xbf, 0x6, 0xf2, 0x0, 0x0, 0x6f, 0x80, 0x6f,
    0x64, 0x57, 0xbf, 0xb0, 0x6, 0xff, 0xff, 0xea,
    0x50, 0x0,

    /* U+0045 "E" */
    0x6f, 0xff, 0xff, 0xff, 0x6, 0xf6, 0x55, 0x55,
    0x50, 0x6f, 0x20, 0x0, 0x0, 0x6, 0xf2, 0x0,
    0x0, 0x0, 0x6f, 0x31, 0x11, 0x10, 0x6, 0xff,
    0xff, 0xff, 0xb0, 0x6f, 0x54, 0x44, 0x42, 0x6,
    0xf2, 0x0, 0x0, 0x0, 0x6f, 0x20, 0x0, 0x0,
    0x6, 0xf2, 0x0, 0x0, 0x0, 0x6f, 0x65, 0x55,
    0x55, 0x6, 0xff, 0xff, 0xff, 0xf1,

    /* U+0046 "F" */
    0x6f, 0xff, 0xff, 0xf4, 0x6f, 0x65, 0x55, 0x51,
    0x6f, 0x20, 0x0, 0x0, 0x6f, 0x20, 0x0, 0x0,
    0x6f, 0x31, 0x11, 0x0, 0x6f, 0xff, 0xff, 0xc0,
    0x6f, 0x54, 0x44, 0x30, 0x6f, 0x20, 0x0, 0x0,
    0x6f, 0x20, 0x0, 0x0, 0x6f, 0x20, 0x0, 0x0,
    0x6f, 0x20, 0x0, 0x0, 0x6f, 0x20, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x3, 0xae, 0xff, 0xc8, 0x10, 0x0, 0x6f,
    0xd7, 0x55, 0x9e, 0xc0, 0x3, 0xfa, 0x0, 0x0,
    0x1, 0x70, 0xb, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xf, 0x90,
    0x0, 0x0, 0x0, 0x0, 0xf, 0x90, 0x0, 0xf,
    0xff, 0xf1, 0xf, 0xb0, 0x0, 0x3, 0x39, 0xf1,
    0xb, 0xf1, 0x0, 0x0, 0x7, 0xf1, 0x3, 0xfa,
    0x0, 0x0, 0x7, 0xf1, 0x0, 0x6f, 0xd7, 0x55,
    0x8d, 0xf1, 0x0, 0x3, 0xae, 0xff, 0xc7, 0x10,

    /* U+0048 "H" */
    0x6f, 0x20, 0x0, 0x1, 0xf7, 0x6f, 0x20, 0x0,
    0x1, 0xf7, 0x6f, 0x20, 0x0, 0x1, 0xf7, 0x6f,
    0x20, 0x0, 0x1, 0xf7, 0x6f, 0x31, 0x11, 0x12,
    0xf7, 0x6f, 0xff, 0xff, 0xff, 0xf7, 0x6f, 0x54,
    0x44, 0x45, 0xf7, 0x6f, 0x20, 0x0, 0x1, 0xf7,
    0x6f, 0x20, 0x0, 0x1, 0xf7, 0x6f, 0x20, 0x0,
    0x1, 0xf7, 0x6f, 0x20, 0x0, 0x1, 0xf7, 0x6f,
    0x20, 0x0, 0x1, 0xf7,

    /* U+0049 "I" */
    0x6f, 0x26, 0xf2, 0x6f, 0x26, 0xf2, 0x6f, 0x26,
    0xf2, 0x6f, 0x26, 0xf2, 0x6f, 0x26, 0xf2, 0x6f,
    0x26, 0xf2,

    /* U+004A "J" */
    0x0, 0x6f, 0x20, 0x6, 0xf2, 0x0, 0x6f, 0x20,
    0x6, 0xf2, 0x0, 0x6f, 0x20, 0x6, 0xf2, 0x0,
    0x6f, 0x20, 0x6, 0xf2, 0x0, 0x6f, 0x20, 0x6,
    0xf2, 0x0, 0x6f, 0x20, 0x7, 0xf2, 0x0, 0xaf,
    0x4, 0x8f, 0xa0, 0xde, 0x90, 0x0,

    /* U+004B "K" */
    0x6f, 0x20, 0x0, 0x3e, 0xd1, 0x6f, 0x20, 0x3,
    0xec, 0x10, 0x6f, 0x20, 0x3f, 0xc0, 0x0, 0x6f,
    0x24, 0xfb, 0x0, 0x0, 0x6f, 0x7f, 0xb0, 0x0,
    0x0, 0x6f, 0xfd, 0x0, 0x0, 0x0, 0x6f, 0xbf,
    0x80, 0x0, 0x0, 0x6f, 0x29, 0xf7, 0x0, 0x0,
    0x6f, 0x20, 0xaf, 0x70, 0x0, 0x6f, 0x20, 0xa,
    0xf6, 0x0, 0x6f, 0x20, 0x0, 0xaf, 0x60, 0x6f,
    0x20, 0x0, 0xb, 0xf5,

    /* U+004C "L" */
    0x6f, 0x20, 0x0, 0x0, 0x6f, 0x20, 0x0, 0x0,
    0x6f, 0x20, 0x0, 0x0, 0x6f, 0x20, 0x0, 0x0,
    0x6f, 0x20, 0x0, 0x0, 0x6f, 0x20, 0x0, 0x0,
    0x6f, 0x20, 0x0, 0x0, 0x6f, 0x20, 0x0, 0x0,
    0x6f, 0x20, 0x0, 0x0, 0x6f, 0x20, 0x0, 0x0,
    0x6f, 0x65, 0x55, 0x54, 0x6f, 0xff, 0xff, 0xfd,

    /* U+004D "M" */
    0x6f, 0xf1, 0x0, 0x0, 0x4f, 0xf4, 0x6f, 0xf7,
    0x0, 0x0, 0xaf, 0xf4, 0x6f, 0xad, 0x0, 0x0,
    0xf9, 0xf4, 0x6f, 0x4f, 0x30, 0x6, 0xe5, 0xf4,
    0x6f, 0x1c, 0x90, 0xc, 0x94, 0xf4, 0x6f, 0x16,
    0xe0, 0x2f, 0x34, 0xf4, 0x6f, 0x11, 0xf4, 0x7d,
    0x4, 0xf4, 0x6f, 0x10, 0xba, 0xd8, 0x4, 0xf4,
    0x6f, 0x10, 0x5f, 0xf2, 0x4, 0xf4, 0x6f, 0x10,
    0xa, 0x90, 0x4, 0xf4, 0x6f, 0x10, 0x0, 0x0,
    0x4, 0xf4, 0x6f, 0x10, 0x0, 0x0, 0x4, 0xf4,

    /* U+004E "N" */
    0x6f, 0xe0, 0x0, 0x2, 0xf6, 0x6f, 0xf7, 0x0,
    0x2, 0xf6, 0x6f, 0xbe, 0x0, 0x2, 0xf6, 0x6f,
    0x3f, 0x80, 0x2, 0xf6, 0x6f, 0x19, 0xf1, 0x2,
    0xf6, 0x6f, 0x11, 0xf8, 0x2, 0xf6, 0x6f, 0x10,
    0x8f, 0x12, 0xf6, 0x6f, 0x10, 0x1f, 0x92, 0xf6,
    0x6f, 0x10, 0x8, 0xf3, 0xf6, 0x6f, 0x10, 0x1,
    0xfb, 0xf6, 0x6f, 0x10, 0x0, 0x7f, 0xf6, 0x6f,
    0x10, 0x0, 0xe, 0xf6,

    /* U+004F "O" */
    0x0, 0x4, 0xbe, 0xfd, 0x91, 0x0, 0x0, 0x7f,
    0xc6, 0x58, 0xee, 0x20, 0x3, 0xfa, 0x0, 0x0,
    0x2f, 0xd0, 0xa, 0xf1, 0x0, 0x0, 0x7, 0xf4,
    0xf, 0xb0, 0x0, 0x0, 0x2, 0xf8, 0xf, 0x90,
    0x0, 0x0, 0x0, 0xfa, 0xf, 0x90, 0x0, 0x0,
    0x0, 0xfa, 0xf, 0xb0, 0x0, 0x0, 0x2, 0xf8,
    0xb, 0xf1, 0x0, 0x0, 0x7, 0xf4, 0x3, 0xfa,
    0x0, 0x0, 0x2e, 0xd0, 0x0, 0x7f, 0xc6, 0x58,
    0xee, 0x20, 0x0, 0x4, 0xbe, 0xfd, 0x91, 0x0,

    /* U+0050 "P" */
    0x6f, 0xff, 0xfc, 0x60, 0x6, 0xf6, 0x45, 0xbf,
    0x80, 0x6f, 0x20, 0x0, 0xdf, 0x6, 0xf2, 0x0,
    0x9, 0xf1, 0x6f, 0x20, 0x0, 0xbf, 0x6, 0xf3,
    0x1, 0x7f, 0xa0, 0x6f, 0xff, 0xff, 0xa1, 0x6,
    0xf5, 0x32, 0x0, 0x0, 0x6f, 0x20, 0x0, 0x0,
    0x6, 0xf2, 0x0, 0x0, 0x0, 0x6f, 0x20, 0x0,
    0x0, 0x6, 0xf2, 0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x4, 0xbe, 0xfd, 0x81, 0x0, 0x0, 0x7f,
    0xc6, 0x58, 0xee, 0x20, 0x3, 0xfa, 0x0, 0x0,
    0x2f, 0xc0, 0xa, 0xf1, 0x0, 0x0, 0x8, 0xf4,
    0xf, 0xb0, 0x0, 0x0, 0x2, 0xf8, 0xf, 0x90,
    0x0, 0x0, 0x0, 0xfa, 0xf, 0x90, 0x0, 0x0,
    0x0, 0xfa, 0xf, 0xb0, 0x0, 0x0, 0x2, 0xf8,
    0xb, 0xf1, 0x0, 0x0, 0x7, 0xf4, 0x3, 0xfa,
    0x0, 0x0, 0x2e, 0xd0, 0x0, 0x7f, 0xc6, 0x47,
    0xef, 0x30, 0x0, 0x4, 0xbe, 0xff, 0xd2, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0x30,

    /* U+0052 "R" */
    0x6f, 0xff, 0xfd, 0x80, 0x0, 0x6f, 0x64, 0x5a,
    0xf8, 0x0, 0x6f, 0x20, 0x0, 0xcf, 0x0, 0x6f,
    0x20, 0x0, 0x9f, 0x10, 0x6f, 0x20, 0x0, 0xbe,
    0x0, 0x6f, 0x30, 0x16, 0xf6, 0x0, 0x6f, 0xff,
    0xff, 0x90, 0x0, 0x6f, 0x53, 0x5c, 0xf5, 0x0,
    0x6f, 0x20, 0x0, 0xde, 0x0, 0x6f, 0x20, 0x0,
    0x5f, 0x70, 0x6f, 0x20, 0x0, 0xc, 0xe0, 0x6f,
    0x20, 0x0, 0x4, 0xf6,

    /* U+0053 "S" */
    0x4, 0xbe, 0xfd, 0xa4, 0x6, 0xfc, 0x65, 0x7b,
    0x80, 0xdd, 0x0, 0x0, 0x0, 0xe, 0xb0, 0x0,
    0x0, 0x0, 0xaf, 0x71, 0x0, 0x0, 0x1, 0xcf,
    0xfe, 0xa4, 0x0, 0x0, 0x27, 0xbf, 0xf7, 0x0,
    0x0, 0x0, 0x1c, 0xf1, 0x0, 0x0, 0x0, 0x6f,
    0x32, 0x0, 0x0, 0x9, 0xf2, 0xec, 0x75, 0x5a,
    0xfb, 0x5, 0xad, 0xff, 0xd7, 0x0,

    /* U+0054 "T" */
    0xf, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x55, 0x55,
    0xec, 0x55, 0x54, 0x0, 0x0, 0xe, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0xeb, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xeb, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0xeb, 0x0, 0x0, 0x0, 0x0, 0xe, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0xeb, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xeb,
    0x0, 0x0,

    /* U+0055 "U" */
    0x9f, 0x0, 0x0, 0x4, 0xf5, 0x9f, 0x0, 0x0,
    0x4, 0xf5, 0x9f, 0x0, 0x0, 0x4, 0xf5, 0x9f,
    0x0, 0x0, 0x4, 0xf5, 0x9f, 0x0, 0x0, 0x4,
    0xf5, 0x9f, 0x0, 0x0, 0x4, 0xf5, 0x9f, 0x0,
    0x0, 0x4, 0xf5, 0x9f, 0x0, 0x0, 0x4, 0xf5,
    0x8f, 0x10, 0x0, 0x6, 0xf3, 0x4f, 0x70, 0x0,
    0xb, 0xe0, 0xb, 0xf9, 0x56, 0xbf, 0x60, 0x0,
    0x8d, 0xff, 0xc5, 0x0,

    /* U+0056 "V" */
    0xbe, 0x0, 0x0, 0x0, 0xf, 0xa5, 0xf5, 0x0,
    0x0, 0x6, 0xf4, 0xe, 0xb0, 0x0, 0x0, 0xbe,
    0x0, 0x9f, 0x10, 0x0, 0x1f, 0x80, 0x3, 0xf6,
    0x0, 0x7, 0xf2, 0x0, 0xd, 0xc0, 0x0, 0xdc,
    0x0, 0x0, 0x7f, 0x20, 0x3f, 0x60, 0x0, 0x1,
    0xf8, 0x9, 0xf1, 0x0, 0x0, 0xb, 0xe0, 0xea,
    0x0, 0x0, 0x0, 0x5f, 0x8f, 0x40, 0x0, 0x0,
    0x0, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x9, 0xf8,
    0x0, 0x0,

    /* U+0057 "W" */
    0x5f, 0x30, 0x0, 0x1f, 0xe0, 0x0, 0x6, 0xf2,
    0x1f, 0x70, 0x0, 0x5f, 0xf2, 0x0, 0xa, 0xe0,
    0xd, 0xb0, 0x0, 0x9b, 0xd6, 0x0, 0xe, 0xb0,
    0x9, 0xf0, 0x0, 0xd7, 0xaa, 0x0, 0x2f, 0x70,
    0x6, 0xf3, 0x1, 0xf3, 0x6e, 0x0, 0x6f, 0x30,
    0x2, 0xf7, 0x4, 0xf0, 0x2f, 0x20, 0xaf, 0x0,
    0x0, 0xeb, 0x8, 0xb0, 0xe, 0x50, 0xdb, 0x0,
    0x0, 0xae, 0xc, 0x80, 0xa, 0x91, 0xf7, 0x0,
    0x0, 0x6f, 0x3f, 0x40, 0x7, 0xd5, 0xf3, 0x0,
    0x0, 0x2f, 0xbf, 0x0, 0x3, 0xfb, 0xf0, 0x0,
    0x0, 0xe, 0xfc, 0x0, 0x0, 0xff, 0xb0, 0x0,
    0x0, 0xa, 0xf8, 0x0, 0x0, 0xbf, 0x70, 0x0,

    /* U+0058 "X" */
    0xa, 0xe1, 0x0, 0x0, 0xcd, 0x0, 0x1e, 0xa0,
    0x0, 0x7f, 0x30, 0x0, 0x5f, 0x50, 0x2f, 0x90,
    0x0, 0x0, 0xbe, 0x1c, 0xe0, 0x0, 0x0, 0x1,
    0xfd, 0xf4, 0x0, 0x0, 0x0, 0x8, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xe0, 0x0, 0x0, 0x0,
    0x9f, 0x4f, 0x90, 0x0, 0x0, 0x4f, 0x70, 0x7f,
    0x30, 0x0, 0xd, 0xc0, 0x0, 0xdd, 0x0, 0x8,
    0xf2, 0x0, 0x3, 0xf8, 0x3, 0xf8, 0x0, 0x0,
    0x8, 0xf2,

    /* U+0059 "Y" */
    0xb, 0xe1, 0x0, 0x0, 0x3f, 0x70, 0x1f, 0xa0,
    0x0, 0xd, 0xc0, 0x0, 0x6f, 0x40, 0x8, 0xf3,
    0x0, 0x0, 0xbe, 0x12, 0xf8, 0x0, 0x0, 0x2,
    0xfa, 0xcd, 0x0, 0x0, 0x0, 0x6, 0xff, 0x30,
    0x0, 0x0, 0x0, 0xe, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0xeb, 0x0, 0x0, 0x0, 0x0, 0xe, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0xeb, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xeb,
    0x0, 0x0,

    /* U+005A "Z" */
    0x1f, 0xff, 0xff, 0xff, 0xff, 0x10, 0x55, 0x55,
    0x55, 0x7f, 0xc0, 0x0, 0x0, 0x0, 0xc, 0xe2,
    0x0, 0x0, 0x0, 0x9, 0xf4, 0x0, 0x0, 0x0,
    0x6, 0xf7, 0x0, 0x0, 0x0, 0x3, 0xfb, 0x0,
    0x0, 0x0, 0x1, 0xdd, 0x10, 0x0, 0x0, 0x0,
    0xbf, 0x30, 0x0, 0x0, 0x0, 0x7f, 0x60, 0x0,
    0x0, 0x0, 0x4f, 0x90, 0x0, 0x0, 0x0, 0x1e,
    0xe5, 0x55, 0x55, 0x55, 0x14, 0xff, 0xff, 0xff,
    0xff, 0xf4,

    /* U+005B "[" */
    0xaf, 0xfb, 0xad, 0x21, 0xad, 0x0, 0xad, 0x0,
    0xad, 0x0, 0xad, 0x0, 0xad, 0x0, 0xad, 0x0,
    0xad, 0x0, 0xad, 0x0, 0xad, 0x0, 0xad, 0x0,
    0xad, 0x0, 0xaf, 0xfa, 0x12, 0x21,

    /* U+005C "\\" */
    0xd7, 0x0, 0x0, 0x8c, 0x0, 0x0, 0x3f, 0x10,
    0x0, 0xe, 0x60, 0x0, 0x9, 0xb0, 0x0, 0x4,
    0xf1, 0x0, 0x0, 0xf5, 0x0, 0x0, 0xaa, 0x0,
    0x0, 0x5f, 0x0, 0x0, 0xf, 0x40, 0x0, 0xb,
    0x90, 0x0, 0x6, 0xe0, 0x0, 0x1, 0xf3,

    /* U+005D "]" */
    0x7f, 0xfe, 0x2, 0xae, 0x0, 0x9e, 0x0, 0x9e,
    0x0, 0x9e, 0x0, 0x9e, 0x0, 0x9e, 0x0, 0x9e,
    0x0, 0x9e, 0x0, 0x9e, 0x0, 0x9e, 0x0, 0x9e,
    0x0, 0x9e, 0x6f, 0xfe, 0x2, 0x21,

    /* U+005E "^" */
    0x0, 0x0, 0x28, 0x50, 0x0, 0x0, 0x0, 0x1e,
    0xff, 0x60, 0x0, 0x0, 0x1d, 0xd1, 0x8f, 0x50,
    0x0, 0x1d, 0xc1, 0x0, 0x7f, 0x50, 0xc, 0xc0,
    0x0, 0x0, 0x6f, 0x40,

    /* U+005F "_" */
    0x2f, 0xff, 0xff, 0xff, 0xf2, 0x2, 0x22, 0x22,
    0x22, 0x20,

    /* U+0060 "`" */
    0x4f, 0x40, 0x0, 0x6e, 0x10, 0x0, 0x8b, 0x0,

    /* U+0061 "a" */
    0x2, 0xae, 0xfd, 0x90, 0x0, 0x48, 0x53, 0x6d,
    0xc0, 0x0, 0x0, 0x0, 0x3f, 0x20, 0x7, 0xce,
    0xff, 0xf5, 0x8, 0xf6, 0x32, 0x3f, 0x50, 0xe8,
    0x0, 0x3, 0xf5, 0xf, 0x70, 0x0, 0x8f, 0x50,
    0xbd, 0x20, 0x6e, 0xf5, 0x1, 0xae, 0xfb, 0x3f,
    0x50,

    /* U+0062 "b" */
    0x8e, 0x0, 0x0, 0x0, 0x8, 0xe0, 0x0, 0x0,
    0x0, 0x8e, 0x0, 0x0, 0x0, 0x8, 0xe3, 0xcf,
    0xe7, 0x0, 0x8f, 0xe6, 0x49, 0xf6, 0x8, 0xf5,
    0x0, 0xa, 0xe0, 0x8f, 0x0, 0x0, 0x5f, 0x28,
    0xe0, 0x0, 0x3, 0xf3, 0x8f, 0x0, 0x0, 0x5f,
    0x28, 0xf5, 0x0, 0xa, 0xe0, 0x8f, 0xe6, 0x49,
    0xf6, 0x8, 0xe3, 0xcf, 0xe7, 0x0,

    /* U+0063 "c" */
    0x0, 0x2a, 0xef, 0xd7, 0x3, 0xfd, 0x54, 0x69,
    0xb, 0xe0, 0x0, 0x0, 0xf, 0x80, 0x0, 0x0,
    0x1f, 0x70, 0x0, 0x0, 0xf, 0x80, 0x0, 0x0,
    0xb, 0xe0, 0x0, 0x0, 0x3, 0xfd, 0x64, 0x69,
    0x0, 0x3b, 0xff, 0xd7,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0xb, 0xb0, 0x0, 0x0, 0x0,
    0xbb, 0x0, 0x0, 0x0, 0xb, 0xb0, 0x5, 0xdf,
    0xd5, 0xbb, 0x4, 0xfb, 0x45, 0xee, 0xb0, 0xcd,
    0x0, 0x3, 0xfb, 0xf, 0x80, 0x0, 0xd, 0xb1,
    0xf6, 0x0, 0x0, 0xcb, 0xf, 0x70, 0x0, 0xd,
    0xb0, 0xcb, 0x0, 0x2, 0xfb, 0x4, 0xf7, 0x2,
    0xcf, 0xb0, 0x5, 0xdf, 0xd6, 0xbb,

    /* U+0065 "e" */
    0x0, 0x2b, 0xff, 0xc3, 0x0, 0x2f, 0xc5, 0x4a,
    0xf3, 0xb, 0xe0, 0x0, 0xd, 0xa0, 0xf8, 0x0,
    0x0, 0x8e, 0x1f, 0xff, 0xff, 0xff, 0xf0, 0xf9,
    0x22, 0x22, 0x22, 0xb, 0xd0, 0x0, 0x0, 0x0,
    0x2f, 0xc6, 0x45, 0x88, 0x0, 0x2a, 0xef, 0xeb,
    0x40,

    /* U+0066 "f" */
    0x0, 0x5d, 0xff, 0x1, 0xf9, 0x33, 0x3, 0xf3,
    0x0, 0xaf, 0xff, 0xf9, 0x15, 0xf4, 0x21, 0x4,
    0xf3, 0x0, 0x4, 0xf3, 0x0, 0x4, 0xf3, 0x0,
    0x4, 0xf3, 0x0, 0x4, 0xf3, 0x0, 0x4, 0xf3,
    0x0, 0x4, 0xf3, 0x0,

    /* U+0067 "g" */
    0x0, 0x5d, 0xfd, 0x5b, 0xb0, 0x4f, 0xa4, 0x5d,
    0xeb, 0xc, 0xc0, 0x0, 0x2f, 0xb0, 0xf7, 0x0,
    0x0, 0xdb, 0x1f, 0x60, 0x0, 0xc, 0xb0, 0xf7,
    0x0, 0x0, 0xdb, 0xc, 0xc0, 0x0, 0x2f, 0xb0,
    0x4f, 0xa4, 0x5d, 0xeb, 0x0, 0x5d, 0xfd, 0x5c,
    0xa0, 0x0, 0x0, 0x1, 0xf7, 0x0, 0x94, 0x35,
    0xde, 0x10, 0xb, 0xef, 0xea, 0x20,

    /* U+0068 "h" */
    0x8e, 0x0, 0x0, 0x0, 0x8e, 0x0, 0x0, 0x0,
    0x8e, 0x0, 0x0, 0x0, 0x8e, 0x2b, 0xfd, 0x60,
    0x8f, 0xd7, 0x49, 0xf4, 0x8f, 0x40, 0x0, 0xda,
    0x8f, 0x0, 0x0, 0xac, 0x8e, 0x0, 0x0, 0xac,
    0x8e, 0x0, 0x0, 0xac, 0x8e, 0x0, 0x0, 0xac,
    0x8e, 0x0, 0x0, 0xac, 0x8e, 0x0, 0x0, 0xac,

    /* U+0069 "i" */
    0x7f, 0x6c, 0x0, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f,
    0x7f, 0x7f, 0x7f, 0x7f,

    /* U+006A "j" */
    0x0, 0x7f, 0x0, 0x6c, 0x0, 0x0, 0x0, 0x7f,
    0x0, 0x7f, 0x0, 0x7f, 0x0, 0x7f, 0x0, 0x7f,
    0x0, 0x7f, 0x0, 0x7f, 0x0, 0x7f, 0x0, 0x7f,
    0x0, 0x8e, 0x14, 0xda, 0x4f, 0xb2,

    /* U+006B "k" */
    0x8e, 0x0, 0x0, 0x0, 0x8, 0xe0, 0x0, 0x0,
    0x0, 0x8e, 0x0, 0x0, 0x0, 0x8, 0xe0, 0x0,
    0x6f, 0x70, 0x8e, 0x0, 0x7f, 0x60, 0x8, 0xe0,
    0x9f, 0x40, 0x0, 0x8e, 0xae, 0x30, 0x0, 0x8,
    0xff, 0xb0, 0x0, 0x0, 0x8e, 0x3f, 0xa0, 0x0,
    0x8, 0xe0, 0x3f, 0xa0, 0x0, 0x8e, 0x0, 0x3f,
    0xa0, 0x8, 0xe0, 0x0, 0x3f, 0xb0,

    /* U+006C "l" */
    0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f,
    0x7f, 0x7f, 0x7f, 0x7f,

    /* U+006D "m" */
    0x8e, 0x3c, 0xfd, 0x50, 0x6d, 0xfb, 0x10, 0x8f,
    0xd6, 0x4b, 0xf8, 0xc5, 0x5e, 0xb0, 0x8f, 0x40,
    0x1, 0xfe, 0x0, 0x6, 0xf1, 0x8f, 0x0, 0x0,
    0xea, 0x0, 0x3, 0xf3, 0x8e, 0x0, 0x0, 0xe9,
    0x0, 0x3, 0xf3, 0x8e, 0x0, 0x0, 0xe9, 0x0,
    0x3, 0xf3, 0x8e, 0x0, 0x0, 0xe9, 0x0, 0x3,
    0xf3, 0x8e, 0x0, 0x0, 0xe9, 0x0, 0x3, 0xf3,
    0x8e, 0x0, 0x0, 0xe9, 0x0, 0x3, 0xf3,

    /* U+006E "n" */
    0x8e, 0x3c, 0xfd, 0x60, 0x8f, 0xc4, 0x16, 0xf4,
    0x8f, 0x30, 0x0, 0xda, 0x8f, 0x0, 0x0, 0xac,
    0x8e, 0x0, 0x0, 0xac, 0x8e, 0x0, 0x0, 0xac,
    0x8e, 0x0, 0x0, 0xac, 0x8e, 0x0, 0x0, 0xac,
    0x8e, 0x0, 0x0, 0xac,

    /* U+006F "o" */
    0x0, 0x4c, 0xff, 0xb2, 0x0, 0x4f, 0xb4, 0x5d,
    0xe1, 0xc, 0xd0, 0x0, 0x1f, 0x90, 0xf8, 0x0,
    0x0, 0xbd, 0x1f, 0x60, 0x0, 0xa, 0xe0, 0xf8,
    0x0, 0x0, 0xbd, 0xc, 0xd0, 0x0, 0x1f, 0x90,
    0x4f, 0xb5, 0x5d, 0xe1, 0x0, 0x4c, 0xff, 0xb2,
    0x0,

    /* U+0070 "p" */
    0x8e, 0x4d, 0xfe, 0x70, 0x8, 0xfd, 0x30, 0x6f,
    0x60, 0x8f, 0x40, 0x0, 0x9e, 0x8, 0xf0, 0x0,
    0x4, 0xf2, 0x8e, 0x0, 0x0, 0x3f, 0x38, 0xf0,
    0x0, 0x5, 0xf2, 0x8f, 0x60, 0x0, 0xbe, 0x8,
    0xfe, 0x74, 0x9f, 0x60, 0x8e, 0x3c, 0xfe, 0x70,
    0x8, 0xe0, 0x0, 0x0, 0x0, 0x8e, 0x0, 0x0,
    0x0, 0x8, 0xe0, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x5d, 0xfd, 0x5b, 0xb0, 0x4f, 0xa4, 0x5e,
    0xeb, 0xc, 0xd0, 0x0, 0x3f, 0xb0, 0xf7, 0x0,
    0x0, 0xdb, 0x1f, 0x60, 0x0, 0xc, 0xb0, 0xf7,
    0x0, 0x0, 0xdb, 0xc, 0xd0, 0x0, 0x3f, 0xb0,
    0x4f, 0xb4, 0x5e, 0xeb, 0x0, 0x5d, 0xfd, 0x5b,
    0xb0, 0x0, 0x0, 0x0, 0xbb, 0x0, 0x0, 0x0,
    0xb, 0xb0, 0x0, 0x0, 0x0, 0xbb,

    /* U+0072 "r" */
    0x0, 0x0, 0x0, 0x8e, 0x4c, 0xf9, 0x8f, 0xd4,
    0x12, 0x8f, 0x40, 0x0, 0x8f, 0x0, 0x0, 0x8e,
    0x0, 0x0, 0x8e, 0x0, 0x0, 0x8e, 0x0, 0x0,
    0x8e, 0x0, 0x0, 0x8e, 0x0, 0x0,

    /* U+0073 "s" */
    0x1, 0xae, 0xfe, 0xb0, 0xc, 0xd5, 0x35, 0x91,
    0xf, 0x60, 0x0, 0x0, 0xc, 0xe6, 0x20, 0x0,
    0x1, 0x9e, 0xfe, 0x70, 0x0, 0x0, 0x28, 0xf5,
    0x0, 0x0, 0x0, 0xf8, 0x1c, 0x64, 0x49, 0xf3,
    0x9, 0xdf, 0xfc, 0x40,

    /* U+0074 "t" */
    0x4, 0x70, 0x0, 0x8, 0xf0, 0x0, 0x8, 0xf0,
    0x0, 0x9f, 0xff, 0xfe, 0x19, 0xf2, 0x21, 0x8,
    0xf0, 0x0, 0x8, 0xf0, 0x0, 0x8, 0xf0, 0x0,
    0x8, 0xf0, 0x0, 0x7, 0xf0, 0x0, 0x5, 0xf6,
    0x32, 0x0, 0x9e, 0xfe,

    /* U+0075 "u" */
    0xac, 0x0, 0x0, 0xcb, 0xac, 0x0, 0x0, 0xcb,
    0xac, 0x0, 0x0, 0xcb, 0xac, 0x0, 0x0, 0xcb,
    0xac, 0x0, 0x0, 0xcb, 0x9d, 0x0, 0x0, 0xcb,
    0x7e, 0x0, 0x1, 0xfb, 0x2f, 0x81, 0x2b, 0xfb,
    0x5, 0xdf, 0xd5, 0xcb,

    /* U+0076 "v" */
    0x5f, 0x20, 0x0, 0xb, 0xd0, 0xf8, 0x0, 0x1,
    0xf7, 0x9, 0xe0, 0x0, 0x7f, 0x10, 0x3f, 0x40,
    0xc, 0xb0, 0x0, 0xda, 0x2, 0xf5, 0x0, 0x8,
    0xf0, 0x8f, 0x0, 0x0, 0x2f, 0x5e, 0xa0, 0x0,
    0x0, 0xce, 0xf4, 0x0, 0x0, 0x6, 0xfe, 0x0,
    0x0,

    /* U+0077 "w" */
    0x3f, 0x30, 0x6, 0xf8, 0x0, 0x2f, 0x40, 0xe7,
    0x0, 0xaf, 0xc0, 0x6, 0xf0, 0xb, 0xb0, 0xe,
    0x8f, 0x0, 0xac, 0x0, 0x6f, 0x3, 0xf1, 0xf4,
    0xe, 0x80, 0x2, 0xf4, 0x7c, 0xb, 0x82, 0xf4,
    0x0, 0xe, 0x8b, 0x80, 0x7c, 0x6f, 0x0, 0x0,
    0xac, 0xf4, 0x3, 0xfb, 0xc0, 0x0, 0x6, 0xff,
    0x0, 0xf, 0xf8, 0x0, 0x0, 0x2f, 0xc0, 0x0,
    0xbf, 0x40, 0x0,

    /* U+0078 "x" */
    0xd, 0xc0, 0x0, 0x4f, 0x60, 0x3f, 0x80, 0x1e,
    0xb0, 0x0, 0x7f, 0x4b, 0xe1, 0x0, 0x0, 0xbf,
    0xf4, 0x0, 0x0, 0x5, 0xfc, 0x0, 0x0, 0x1,
    0xec, 0xf6, 0x0, 0x0, 0xbe, 0x18, 0xf2, 0x0,
    0x7f, 0x30, 0xc, 0xd0, 0x3f, 0x80, 0x0, 0x2f,
    0x90,

    /* U+0079 "y" */
    0x4f, 0x30, 0x0, 0xb, 0xc0, 0xe9, 0x0, 0x2,
    0xf6, 0x7, 0xf0, 0x0, 0x8f, 0x0, 0x1f, 0x60,
    0xe, 0x90, 0x0, 0xac, 0x5, 0xf2, 0x0, 0x3,
    0xf3, 0xcc, 0x0, 0x0, 0xd, 0xcf, 0x50, 0x0,
    0x0, 0x6f, 0xe0, 0x0, 0x0, 0x1, 0xf8, 0x0,
    0x0, 0x0, 0x6f, 0x20, 0x0, 0x2, 0x4e, 0xb0,
    0x0, 0x0, 0xbf, 0xc1, 0x0, 0x0,

    /* U+007A "z" */
    0x1f, 0xff, 0xff, 0xfb, 0x2, 0x22, 0x26, 0xf8,
    0x0, 0x0, 0x1e, 0xb0, 0x0, 0x0, 0xcd, 0x10,
    0x0, 0xa, 0xe2, 0x0, 0x0, 0x8f, 0x40, 0x0,
    0x5, 0xf6, 0x0, 0x0, 0x2f, 0xb2, 0x22, 0x21,
    0x5f, 0xff, 0xff, 0xfb,

    /* U+007B "{" */
    0x0, 0x9, 0xef, 0x30, 0x6, 0xf6, 0x20, 0x0,
    0x8e, 0x0, 0x0, 0x9, 0xe0, 0x0, 0x0, 0x9d,
    0x0, 0x0, 0xa, 0xd0, 0x0, 0x2, 0xe9, 0x0,
    0xf, 0xfc, 0x10, 0x0, 0x25, 0xf8, 0x0, 0x0,
    0xa, 0xd0, 0x0, 0x0, 0x9d, 0x0, 0x0, 0x9,
    0xe0, 0x0, 0x0, 0x8e, 0x0, 0x0, 0x6, 0xf3,
    0x0, 0x0, 0x1c, 0xff, 0x20, 0x0, 0x1, 0x20,

    /* U+007C "|" */
    0xf5, 0xf5, 0xf5, 0xf5, 0xf5, 0xf5, 0xf5, 0xf5,
    0xf5, 0xf5, 0xf5, 0xf5, 0xf5, 0xf5, 0xf5, 0xf5,

    /* U+007D "}" */
    0xfe, 0xb1, 0x0, 0x2, 0x4f, 0x80, 0x0, 0x0,
    0xbb, 0x0, 0x0, 0xb, 0xc0, 0x0, 0x0, 0xbc,
    0x0, 0x0, 0xa, 0xd0, 0x0, 0x0, 0x7f, 0x40,
    0x0, 0x0, 0xaf, 0xf2, 0x0, 0x5f, 0x73, 0x0,
    0xa, 0xd0, 0x0, 0x0, 0xac, 0x0, 0x0, 0xb,
    0xc0, 0x0, 0x0, 0xbb, 0x0, 0x0, 0x1e, 0x90,
    0x0, 0xff, 0xd2, 0x0, 0x2, 0x10, 0x0, 0x0,

    /* U+007E "~" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6d, 0xfe,
    0xa5, 0x12, 0x7a, 0x4d, 0x64, 0x6b, 0xff, 0xfd,
    0x31, 0x0, 0x0, 0x0, 0x31, 0x0,

    /* U+05D0 "א" */
    0x3f, 0x80, 0x0, 0x3f, 0x40, 0x8f, 0x30, 0x3,
    0xf3, 0x0, 0xdd, 0x0, 0x3f, 0x20, 0x1b, 0xf8,
    0x6, 0xf0, 0xc, 0xb8, 0xf6, 0xe8, 0x4, 0xf1,
    0xc, 0xf7, 0x0, 0x7e, 0x0, 0x2f, 0x80, 0x8,
    0xe0, 0x0, 0x7f, 0x30, 0x8e, 0x0, 0x0, 0xcd,
    0x0,

    /* U+05D1 "ב" */
    0x5f, 0xff, 0xe8, 0x0, 0x0, 0x11, 0x25, 0xea,
    0x0, 0x0, 0x0, 0x6, 0xf1, 0x0, 0x0, 0x0,
    0x3f, 0x30, 0x0, 0x0, 0x3, 0xf4, 0x0, 0x0,
    0x0, 0x3f, 0x40, 0x0, 0x0, 0x3, 0xf4, 0x0,
    0x22, 0x22, 0x4f, 0x51, 0x5f, 0xff, 0xff, 0xff,
    0x90,

    /* U+05D2 "ג" */
    0x1f, 0xd8, 0x0, 0x0, 0x14, 0xf7, 0x0, 0x0,
    0x9, 0xd0, 0x0, 0x0, 0x7f, 0x0, 0x0, 0x7,
    0xf0, 0x0, 0x0, 0x9f, 0x10, 0x0, 0xd, 0xf4,
    0x1, 0x4a, 0xbe, 0x80, 0x4f, 0xc1, 0x9e, 0x0,

    /* U+05D3 "ד" */
    0x5f, 0xff, 0xff, 0xff, 0x30, 0x11, 0x11, 0xda,
    0x10, 0x0, 0x0, 0xd, 0xa0, 0x0, 0x0, 0x0,
    0xda, 0x0, 0x0, 0x0, 0xd, 0xa0, 0x0, 0x0,
    0x0, 0xda, 0x0, 0x0, 0x0, 0xd, 0xa0, 0x0,
    0x0, 0x0, 0xda, 0x0, 0x0, 0x0, 0xd, 0xa0,
    0x0,

    /* U+05D4 "ה" */
    0x8f, 0xff, 0xfd, 0x70, 0x1, 0x11, 0x26, 0xf6,
    0x0, 0x0, 0x0, 0x9c, 0x4, 0x0, 0x0, 0x7f,
    0x5f, 0x10, 0x0, 0x7f, 0x6f, 0x10, 0x0, 0x7f,
    0x6f, 0x10, 0x0, 0x7f, 0x6f, 0x10, 0x0, 0x7f,
    0x6f, 0x10, 0x0, 0x7f,

    /* U+05D5 "ו" */
    0x8e, 0x8e, 0x8e, 0x8e, 0x8e, 0x8e, 0x8e, 0x8e,
    0x8e,

    /* U+05D6 "ז" */
    0x5f, 0xff, 0xd0, 0x1c, 0x81, 0x3, 0xf2, 0x0,
    0x6f, 0x10, 0x6, 0xf0, 0x0, 0x6f, 0x0, 0x6,
    0xf0, 0x0, 0x6f, 0x0, 0x6, 0xf0, 0x0,

    /* U+05D7 "ח" */
    0x8f, 0xff, 0xfd, 0x70, 0x8e, 0x11, 0x26, 0xf7,
    0x8e, 0x0, 0x0, 0x9d, 0x8e, 0x0, 0x0, 0x7f,
    0x8e, 0x0, 0x0, 0x7f, 0x8e, 0x0, 0x0, 0x7f,
    0x8e, 0x0, 0x0, 0x7f, 0x8e, 0x0, 0x0, 0x7f,
    0x8e, 0x0, 0x0, 0x7f,

    /* U+05D8 "ט" */
    0x8e, 0x0, 0xcf, 0xd4, 0x8, 0xe0, 0x4, 0x3b,
    0xe0, 0x8e, 0x0, 0x0, 0x3f, 0x48, 0xe0, 0x0,
    0x0, 0xf7, 0x8e, 0x0, 0x0, 0xf, 0x77, 0xf0,
    0x0, 0x0, 0xf6, 0x4f, 0x30, 0x0, 0x4f, 0x40,
    0xde, 0x63, 0x6e, 0xc0, 0x1, 0xae, 0xfe, 0x91,
    0x0,

    /* U+05D9 "י" */
    0xf8, 0xf8, 0xf8, 0xf8, 0xf8, 0x40,

    /* U+05DA "ך" */
    0x5f, 0xff, 0xd7, 0x0, 0x1, 0x13, 0x8f, 0x70,
    0x0, 0x0, 0xa, 0xe0, 0x0, 0x0, 0x6, 0xf1,
    0x0, 0x0, 0x5, 0xf2, 0x0, 0x0, 0x5, 0xf2,
    0x0, 0x0, 0x5, 0xf2, 0x0, 0x0, 0x5, 0xf2,
    0x0, 0x0, 0x5, 0xf2, 0x0, 0x0, 0x5, 0xf2,
    0x0, 0x0, 0x5, 0xf2, 0x0, 0x0, 0x5, 0xf2,

    /* U+05DB "כ" */
    0x5f, 0xff, 0xd8, 0x0, 0x1, 0x12, 0x5d, 0xa0,
    0x0, 0x0, 0x4, 0xf3, 0x0, 0x0, 0x0, 0xf7,
    0x0, 0x0, 0x0, 0xe8, 0x0, 0x0, 0x0, 0xf7,
    0x0, 0x0, 0x4, 0xf3, 0x1, 0x12, 0x5d, 0xa0,
    0x5f, 0xff, 0xd8, 0x0,

    /* U+05DC "ל" */
    0x28, 0x10, 0x0, 0x0, 0x5f, 0x20, 0x0, 0x0,
    0x5f, 0x20, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xfd,
    0x1, 0x11, 0x12, 0xf9, 0x0, 0x0, 0x6, 0xf2,
    0x0, 0x0, 0xc, 0xc0, 0x0, 0x0, 0x2f, 0x50,
    0x0, 0x0, 0x9e, 0x0, 0x0, 0x0, 0xf8, 0x0,
    0x0, 0x6, 0xf2, 0x0, 0x0, 0xc, 0xb0, 0x0,

    /* U+05DD "ם" */
    0x8f, 0xff, 0xfe, 0x90, 0x8, 0xe1, 0x12, 0x5e,
    0x90, 0x8e, 0x0, 0x0, 0x7f, 0x8, 0xe0, 0x0,
    0x4, 0xf2, 0x8e, 0x0, 0x0, 0x4f, 0x28, 0xe0,
    0x0, 0x4, 0xf2, 0x8e, 0x0, 0x0, 0x4f, 0x28,
    0xe2, 0x22, 0x25, 0xf2, 0x8f, 0xff, 0xff, 0xff,
    0x20,

    /* U+05DE "מ" */
    0x1f, 0x90, 0xae, 0xfc, 0x20, 0x9, 0xea, 0xc2,
    0x1c, 0xd0, 0x3, 0xff, 0x20, 0x4, 0xf3, 0x0,
    0xfc, 0x0, 0x1, 0xf6, 0x0, 0xf8, 0x0, 0x0,
    0xf6, 0x2, 0xf5, 0x0, 0x0, 0xf6, 0x5, 0xf2,
    0x0, 0x0, 0xf6, 0x8, 0xf0, 0x1, 0x12, 0xf6,
    0xb, 0xc0, 0xf, 0xff, 0xf6,

    /* U+05DF "ן" */
    0x8e, 0x8e, 0x8e, 0x8e, 0x8e, 0x8e, 0x8e, 0x8e,
    0x8e, 0x8e, 0x8e, 0x8e,

    /* U+05E0 "נ" */
    0x1f, 0xea, 0x10, 0x14, 0xea, 0x0, 0x9, 0xe0,
    0x0, 0x8e, 0x0, 0x8, 0xf0, 0x0, 0x8f, 0x0,
    0x8, 0xf0, 0x22, 0x9f, 0x5f, 0xff, 0xf0,

    /* U+05E1 "ס" */
    0x8f, 0xff, 0xfe, 0x91, 0x8, 0xe3, 0x33, 0x6e,
    0xd0, 0x8e, 0x0, 0x0, 0x3f, 0x48, 0xe0, 0x0,
    0x0, 0xf7, 0x8e, 0x0, 0x0, 0xf, 0x77, 0xf0,
    0x0, 0x1, 0xf6, 0x3f, 0x50, 0x0, 0x6f, 0x30,
    0xcf, 0x64, 0x7f, 0xb0, 0x1, 0x9e, 0xfe, 0x90,
    0x0,

    /* U+05E2 "ע" */
    0x2f, 0x50, 0x0, 0xf, 0x90, 0xea, 0x0, 0x0,
    0xf9, 0x9, 0xe0, 0x0, 0xf, 0x80, 0x4f, 0x30,
    0x0, 0xf7, 0x0, 0xf7, 0x0, 0x2f, 0x50, 0xb,
    0xc0, 0x6, 0xf1, 0x0, 0x6f, 0x2, 0xe9, 0x0,
    0x2, 0xfa, 0xea, 0x0, 0x5, 0xbf, 0xd5, 0x0,
    0x4, 0xe9, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+05E3 "ף" */
    0x8f, 0xff, 0xfc, 0x50, 0x8e, 0x11, 0x39, 0xf3,
    0x8e, 0x0, 0x0, 0xe9, 0x7f, 0x20, 0x0, 0xbc,
    0x1d, 0xfe, 0x0, 0xac, 0x0, 0x21, 0x0, 0xac,
    0x0, 0x0, 0x0, 0xac, 0x0, 0x0, 0x0, 0xac,
    0x0, 0x0, 0x0, 0xac, 0x0, 0x0, 0x0, 0xac,
    0x0, 0x0, 0x0, 0xac, 0x0, 0x0, 0x0, 0xac,

    /* U+05E4 "פ" */
    0x8f, 0xff, 0xeb, 0x30, 0x8, 0xe1, 0x24, 0xbf,
    0x30, 0x8e, 0x0, 0x0, 0xbc, 0x6, 0xf2, 0x0,
    0x6, 0xf0, 0x1d, 0xfe, 0x0, 0x5f, 0x10, 0x2,
    0x10, 0x6, 0xf0, 0x0, 0x0, 0x0, 0xcc, 0x0,
    0x11, 0x24, 0xaf, 0x30, 0x8f, 0xff, 0xfb, 0x30,
    0x0,

    /* U+05E5 "ץ" */
    0x1e, 0xa0, 0x0, 0x7f, 0x4, 0xf5, 0x0, 0x8e,
    0x0, 0x9e, 0x10, 0xbb, 0x0, 0x1e, 0x86, 0xf3,
    0x0, 0x9, 0xfe, 0x50, 0x0, 0x6, 0xf1, 0x0,
    0x0, 0x5, 0xf1, 0x0, 0x0, 0x5, 0xf1, 0x0,
    0x0, 0x5, 0xf1, 0x0, 0x0, 0x5, 0xf1, 0x0,
    0x0, 0x5, 0xf1, 0x0, 0x0, 0x5, 0xf1, 0x0,

    /* U+05E6 "צ" */
    0x1e, 0xb0, 0x0, 0x6f, 0x0, 0x5f, 0x60, 0x6,
    0xf0, 0x0, 0xaf, 0x10, 0x7e, 0x0, 0x1, 0xeb,
    0xa, 0xb0, 0x0, 0x5, 0xfb, 0xf3, 0x0, 0x0,
    0xa, 0xf4, 0x0, 0x0, 0x0, 0x1e, 0xb0, 0x0,
    0x11, 0x11, 0x7f, 0x60, 0x5f, 0xff, 0xff, 0xff,
    0x0,

    /* U+05E7 "ק" */
    0x8f, 0xff, 0xff, 0xff, 0xf2, 0x1, 0x11, 0x11,
    0x1c, 0xd0, 0x0, 0x0, 0x0, 0x1f, 0x70, 0x4,
    0x0, 0x0, 0x8f, 0x10, 0x5f, 0x10, 0x0, 0xea,
    0x0, 0x5f, 0x10, 0x4, 0xf3, 0x0, 0x5f, 0x10,
    0xb, 0xd0, 0x0, 0x5f, 0x10, 0x1f, 0x60, 0x0,
    0x5f, 0x10, 0x8f, 0x0, 0x0, 0x5f, 0x10, 0x0,
    0x0, 0x0, 0x5f, 0x10, 0x0, 0x0, 0x0, 0x5f,
    0x10, 0x0, 0x0, 0x0, 0x27, 0x0, 0x0, 0x0,
    0x0,

    /* U+05E8 "ר" */
    0x5f, 0xff, 0xd9, 0x10, 0x1, 0x12, 0x5e, 0xc0,
    0x0, 0x0, 0x4, 0xf4, 0x0, 0x0, 0x0, 0xf7,
    0x0, 0x0, 0x0, 0xe9, 0x0, 0x0, 0x0, 0xe9,
    0x0, 0x0, 0x0, 0xe9, 0x0, 0x0, 0x0, 0xe9,
    0x0, 0x0, 0x0, 0xe9,

    /* U+05E9 "ש" */
    0x3f, 0x30, 0xf, 0x50, 0xd, 0x91, 0xf5, 0x1,
    0xf4, 0x0, 0xf7, 0xf, 0x70, 0x4f, 0x10, 0x1f,
    0x40, 0xda, 0x1b, 0xc0, 0x4, 0xf1, 0xb, 0xff,
    0xd2, 0x0, 0x8d, 0x0, 0x9e, 0x10, 0x0, 0xd,
    0x80, 0x7, 0xf0, 0x0, 0x9, 0xf1, 0x0, 0x5f,
    0x42, 0x5c, 0xf4, 0x0, 0x2, 0xff, 0xfd, 0x82,
    0x0, 0x0,

    /* U+05EA "ת" */
    0x7f, 0xff, 0xff, 0xd8, 0x0, 0x3, 0xf6, 0x12,
    0x6f, 0x70, 0x2, 0xf5, 0x0, 0x9, 0xe0, 0x2,
    0xf5, 0x0, 0x6, 0xf0, 0x2, 0xf5, 0x0, 0x6,
    0xf0, 0x2, 0xf5, 0x0, 0x6, 0xf1, 0x2, 0xf4,
    0x0, 0x6, 0xf1, 0x17, 0xf1, 0x0, 0x6, 0xf1,
    0xde, 0x70, 0x0, 0x6, 0xf1,

    /* U+0606 "؆" */
    0x0, 0x1, 0x51, 0x53, 0x30, 0x0, 0x0, 0xe7,
    0xb8, 0x50, 0xd9, 0x0, 0x9f, 0xcc, 0x10, 0xd,
    0x0, 0x58, 0x0, 0x0, 0x7, 0x60, 0x3a, 0x0,
    0x0, 0x1, 0xc0, 0x2b, 0x0, 0x0, 0x0, 0xb2,
    0x14, 0x0, 0x0, 0x0, 0x58, 0x0, 0x5, 0x50,
    0x0, 0xd, 0x0, 0xe, 0xa5, 0x0, 0x9, 0x40,
    0x4f, 0x10, 0x0, 0x2, 0xa0, 0xab, 0x0, 0x0,
    0x0, 0xc2, 0xf5, 0x0, 0x0, 0x0, 0x6d, 0xf0,
    0x0, 0x0, 0x0, 0x1f, 0x90, 0x0, 0x0, 0x0,
    0xa, 0x30, 0x0,

    /* U+0607 "؇" */
    0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0xa,
    0xb2, 0x0, 0xc9, 0x0, 0x3c, 0x10, 0x0, 0xc,
    0x0, 0x3d, 0x90, 0x0, 0x7, 0x60, 0xb4, 0x1,
    0x0, 0x1, 0xc0, 0x3c, 0xca, 0x0, 0x0, 0xb2,
    0x0, 0x0, 0x0, 0x0, 0x59, 0x0, 0xb, 0xd5,
    0x0, 0xd, 0x0, 0x1f, 0x51, 0x0, 0x8, 0x50,
    0x6f, 0x0, 0x0, 0x2, 0xb0, 0xca, 0x0, 0x0,
    0x0, 0xc4, 0xf4, 0x0, 0x0, 0x0, 0x6e, 0xe0,
    0x0, 0x0, 0x0, 0x1f, 0x90, 0x0, 0x0, 0x0,
    0xa, 0x30, 0x0,

    /* U+0609 "؉" */
    0xd9, 0x0, 0xe, 0x40, 0x0, 0xc8, 0x0, 0x7c,
    0x0, 0x0, 0x0, 0x1, 0xe3, 0x0, 0x0, 0x0,
    0x8, 0xb0, 0x0, 0x0, 0x0, 0x1f, 0x30, 0x0,
    0x0, 0x0, 0x9a, 0x0, 0x0, 0x0, 0x2, 0xf2,
    0x0, 0x0, 0x0, 0xa, 0x90, 0x0, 0x0, 0x0,
    0x2f, 0x10, 0xe, 0x60, 0x6e, 0xb8, 0x0, 0xf,
    0x70, 0x7f,

    /* U+060A "؊" */
    0xd9, 0x0, 0xe, 0x40, 0x0, 0x0, 0x0, 0xc8,
    0x0, 0x7c, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xe3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9a, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0x10, 0xe, 0x60, 0x6e, 0x0, 0xe7, 0xb8,
    0x0, 0xf, 0x70, 0x7f, 0x0, 0xf7,

    /* U+060C "،" */
    0x2, 0xc0, 0xba, 0x2f, 0x64, 0xf5,

    /* U+0615 "ؕ" */
    0x0, 0x0, 0x0, 0x6, 0x60, 0x0, 0x6, 0x65,
    0x50, 0x6, 0xd6, 0xe0, 0xd, 0xdc, 0x70,

    /* U+061B "؛" */
    0x4, 0xd0, 0xc9, 0x3f, 0x63, 0xd4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0x54, 0xf5,

    /* U+061F "؟" */
    0x8, 0xef, 0xd8, 0x17, 0xf9, 0x57, 0xd6, 0xcd,
    0x0, 0x0, 0x1b, 0xe0, 0x0, 0x0, 0x3f, 0xa0,
    0x0, 0x0, 0x4f, 0x90, 0x0, 0x0, 0x5f, 0x30,
    0x0, 0x1, 0xf6, 0x0, 0x0, 0xd, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0x70, 0x0, 0x1,
    0xf7, 0x0,

    /* U+0621 "ء" */
    0x0, 0x0, 0x0, 0x9, 0xff, 0x90, 0x6f, 0x95,
    0x40, 0xad, 0x0, 0x0, 0x8f, 0x40, 0x11, 0x1a,
    0xff, 0xf3, 0x5c, 0xfe, 0x70, 0x9a, 0x40, 0x0,

    /* U+0622 "آ" */
    0x19, 0x40, 0x19, 0x8, 0x5a, 0xdb, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xf0, 0x0, 0x0, 0x7f,
    0x0, 0x0, 0x7, 0xf0, 0x0, 0x0, 0x7f, 0x0,
    0x0, 0x7, 0xf0, 0x0, 0x0, 0x7f, 0x0, 0x0,
    0x7, 0xf0, 0x0, 0x0, 0x7f, 0x0, 0x0, 0x7,
    0xf0, 0x0, 0x0, 0x7f, 0x0, 0x0, 0x7, 0xf0,
    0x0, 0x0, 0x7f, 0x0, 0x0,

    /* U+0623 "أ" */
    0x9, 0xc3, 0x1b, 0x0, 0xd, 0xb6, 0x17, 0x30,
    0x7, 0xf0, 0x7, 0xf0, 0x7, 0xf0, 0x7, 0xf0,
    0x7, 0xf0, 0x7, 0xf0, 0x7, 0xf0, 0x7, 0xf0,
    0x7, 0xf0, 0x7, 0xf0, 0x7, 0xf0, 0x7, 0xf0,

    /* U+0624 "ؤ" */
    0x0, 0x5, 0xc7, 0x0, 0x0, 0xc, 0x0, 0x0,
    0x0, 0x9, 0xa7, 0x0, 0x0, 0xb, 0x94, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xed, 0x40,
    0x0, 0x6f, 0xac, 0xf1, 0x0, 0x9e, 0x1, 0xf6,
    0x0, 0x6f, 0xa6, 0xf7, 0x0, 0x8, 0xdf, 0xf7,
    0x0, 0x0, 0x4, 0xf4, 0x0, 0x0, 0x2e, 0xe0,
    0x12, 0x48, 0xef, 0x30, 0xaf, 0xff, 0xa2, 0x0,
    0x34, 0x20, 0x0, 0x0,

    /* U+0625 "إ" */
    0x7, 0xf0, 0x7, 0xf0, 0x7, 0xf0, 0x7, 0xf0,
    0x7, 0xf0, 0x7, 0xf0, 0x7, 0xf0, 0x7, 0xf0,
    0x7, 0xf0, 0x7, 0xf0, 0x7, 0xf0, 0x7, 0xf0,
    0x9, 0xc3, 0x1b, 0x0, 0xd, 0xb6, 0x16, 0x20,

    /* U+0626 "ئ" */
    0x1, 0xbb, 0x0, 0x0, 0x0, 0x0, 0x66, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xeb, 0x20, 0x0, 0x0,
    0x0, 0x35, 0x20, 0x6d, 0xfe, 0x70, 0x0, 0x0,
    0x4f, 0x94, 0x7f, 0x40, 0x0, 0x5, 0xf7, 0x0,
    0x10, 0x12, 0x0, 0x9, 0xfe, 0x80, 0xc, 0xa0,
    0x0, 0x2, 0x7e, 0xb0, 0xf7, 0x0, 0x0, 0x0,
    0xaf, 0xd, 0xc0, 0x0, 0x2, 0x9f, 0x90, 0x4f,
    0xfc, 0xcf, 0xff, 0x90, 0x0, 0x28, 0xba, 0x85,
    0x10, 0x0,

    /* U+0627 "ا" */
    0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f,
    0x7f, 0x7f, 0x7f, 0x7f,

    /* U+0628 "ب" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0x5b, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x9c, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xbd, 0xb0, 0x0, 0x0, 0x0,
    0x2a, 0xf4, 0x5f, 0xd8, 0x66, 0x8a, 0xdf, 0xe5,
    0x0, 0x3a, 0xef, 0xfe, 0xc9, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x89, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x10,
    0x0, 0x0, 0x0,

    /* U+0629 "ة" */
    0xf, 0x3f, 0x10, 0x0, 0x20, 0x20, 0x0, 0x0,
    0x10, 0x0, 0x2, 0xef, 0xd5, 0x0, 0xae, 0x5a,
    0xf7, 0xd, 0x90, 0x7, 0xf1, 0xe8, 0x0, 0x3f,
    0x3b, 0xe6, 0x6d, 0xe0, 0x2b, 0xfe, 0x91, 0x0,

    /* U+062A "ت" */
    0x0, 0x0, 0x8a, 0x99, 0x0, 0x0, 0x5, 0x70,
    0x1, 0x11, 0x10, 0x0, 0x9c, 0xe8, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xce, 0xa0, 0x0, 0x0, 0x0,
    0x7, 0xf7, 0x7f, 0xd8, 0x66, 0x79, 0xcf, 0xf8,
    0x0, 0x4a, 0xef, 0xfe, 0xc9, 0x61, 0x0,

    /* U+062B "ث" */
    0x0, 0x0, 0x8, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x11, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8a,
    0x99, 0x0, 0x0, 0x5, 0x70, 0x1, 0x11, 0x10,
    0x0, 0x9c, 0xe8, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xce, 0xa0, 0x0, 0x0, 0x0, 0x7, 0xf7, 0x7f,
    0xd8, 0x66, 0x79, 0xcf, 0xf8, 0x0, 0x4a, 0xef,
    0xfe, 0xc9, 0x61, 0x0,

    /* U+062C "ج" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7c, 0xff, 0xff,
    0xd8, 0x0, 0x98, 0x5b, 0xfe, 0xb6, 0x0, 0x0,
    0xbf, 0x70, 0x0, 0x0, 0x7, 0xf4, 0x0, 0x0,
    0x0, 0xf, 0x80, 0x0, 0x0, 0x0, 0x4f, 0x20,
    0x2, 0x0, 0x0, 0x6f, 0x0, 0xf, 0x30, 0x0,
    0x5f, 0x20, 0x0, 0x0, 0x0, 0x1f, 0x90, 0x0,
    0x0, 0x0, 0x7, 0xfa, 0x41, 0x14, 0xa4, 0x0,
    0x5d, 0xff, 0xff, 0xc2, 0x0, 0x0, 0x13, 0x31,
    0x0,

    /* U+062D "ح" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9e, 0xed, 0xff,
    0xe9, 0x0, 0x41, 0x3d, 0xe8, 0x41, 0x0, 0x3,
    0xfa, 0x0, 0x0, 0x0, 0xc, 0xc0, 0x0, 0x0,
    0x0, 0x3f, 0x30, 0x0, 0x0, 0x0, 0x6f, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0x10, 0x0, 0x0, 0x0,
    0x1f, 0x80, 0x0, 0x0, 0x0, 0x8, 0xf9, 0x31,
    0x14, 0x94, 0x0, 0x6d, 0xff, 0xff, 0xc2, 0x0,
    0x0, 0x13, 0x31, 0x0,

    /* U+062E "خ" */
    0x0, 0x7, 0xa0, 0x0, 0x0, 0x0, 0x1, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9e,
    0xed, 0xff, 0xe9, 0x0, 0x41, 0x3d, 0xe8, 0x41,
    0x0, 0x3, 0xfa, 0x0, 0x0, 0x0, 0xc, 0xc0,
    0x0, 0x0, 0x0, 0x3f, 0x30, 0x0, 0x0, 0x0,
    0x6f, 0x0, 0x0, 0x0, 0x0, 0x5f, 0x10, 0x0,
    0x0, 0x0, 0x1f, 0x80, 0x0, 0x0, 0x0, 0x8,
    0xf9, 0x31, 0x14, 0x94, 0x0, 0x6d, 0xff, 0xff,
    0xc2, 0x0, 0x0, 0x13, 0x31, 0x0,

    /* U+062F "د" */
    0x0, 0x2e, 0xa0, 0x0, 0x0, 0x3f, 0x70, 0x0,
    0x0, 0x9e, 0x0, 0x0, 0x5, 0xf2, 0x0, 0x0,
    0x7f, 0x10, 0x96, 0xaf, 0xb0, 0xd, 0xfe, 0x90,
    0x0,

    /* U+0630 "ذ" */
    0x0, 0x7b, 0x0, 0x0, 0x1, 0x10, 0x0, 0x0,
    0x13, 0x0, 0x0, 0x1, 0xdc, 0x0, 0x0, 0x2,
    0xf8, 0x0, 0x0, 0x8, 0xe0, 0x0, 0x0, 0x4f,
    0x20, 0x0, 0x8, 0xf1, 0x9, 0x7a, 0xfb, 0x0,
    0xdf, 0xe8, 0x0,

    /* U+0631 "ر" */
    0x0, 0x0, 0x0, 0xa5, 0x0, 0x0, 0x0, 0xba,
    0x0, 0x0, 0x0, 0xab, 0x0, 0x0, 0x0, 0xca,
    0x0, 0x0, 0x3, 0xf6, 0x0, 0x0, 0x4e, 0xd0,
    0x14, 0x6c, 0xfd, 0x20, 0xaf, 0xfc, 0x60, 0x0,
    0x33, 0x10, 0x0, 0x0,

    /* U+0632 "ز" */
    0x0, 0x0, 0x1, 0xf0, 0x0, 0x0, 0x0, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc7,
    0x0, 0x0, 0x0, 0xbb, 0x0, 0x0, 0x0, 0xab,
    0x0, 0x0, 0x0, 0xda, 0x0, 0x0, 0x4, 0xf5,
    0x0, 0x0, 0x4f, 0xd0, 0x14, 0x6c, 0xfd, 0x20,
    0xaf, 0xfc, 0x60, 0x0, 0x33, 0x10, 0x0, 0x0,

    /* U+0633 "س" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xf3, 0x0, 0x0, 0x0, 0xd, 0x90, 0x4, 0xf2,
    0x3, 0xf3, 0x0, 0x0, 0x0, 0x9, 0xd0, 0x6,
    0xf3, 0x4, 0xf3, 0x5f, 0x10, 0x0, 0x6, 0xf3,
    0xa, 0xf8, 0x6, 0xf1, 0xcb, 0x0, 0x0, 0x6,
    0xfe, 0xbf, 0xcf, 0xbf, 0xc0, 0xe8, 0x0, 0x0,
    0x9, 0xfc, 0xfa, 0x1a, 0xfb, 0x10, 0xf8, 0x0,
    0x0, 0x3f, 0xa0, 0x0, 0x0, 0x0, 0x0, 0xbe,
    0x52, 0x27, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x2d, 0xff, 0xff, 0xb2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x24, 0x41, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+0634 "ش" */
    0x0, 0x0, 0x0, 0x0, 0x3, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3e, 0x4e,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x2, 0x0, 0x1, 0x61, 0x0, 0x0, 0x0, 0x2,
    0x10, 0x0, 0x20, 0x3, 0xf3, 0x0, 0x0, 0x0,
    0xc, 0x90, 0x5, 0xf2, 0x3, 0xf3, 0x1, 0x0,
    0x0, 0x8, 0xd0, 0x6, 0xf3, 0x4, 0xf2, 0x6f,
    0x10, 0x0, 0x6, 0xf3, 0xa, 0xf9, 0x7, 0xf1,
    0xca, 0x0, 0x0, 0x6, 0xff, 0xbf, 0xcf, 0xbf,
    0xb0, 0xe8, 0x0, 0x0, 0xa, 0xfc, 0xfa, 0x1a,
    0xfb, 0x10, 0xe8, 0x0, 0x0, 0x4f, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0xbe, 0x52, 0x27, 0xfe, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x2d, 0xff, 0xff, 0xb2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x24, 0x41,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0635 "ص" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xaf,
    0xfc, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3e,
    0xd6, 0x5c, 0xe0, 0x0, 0x0, 0x0, 0xa, 0x43,
    0xfb, 0x0, 0x5, 0xf1, 0x4e, 0x10, 0x0, 0xc,
    0xce, 0xc0, 0x0, 0x2d, 0xf0, 0xac, 0x0, 0x0,
    0xa, 0xff, 0x87, 0x8b, 0xff, 0x50, 0xd9, 0x0,
    0x0, 0xb, 0xde, 0xff, 0xed, 0x81, 0x0, 0xf7,
    0x0, 0x0, 0xd, 0x90, 0x0, 0x0, 0x0, 0x0,
    0xe8, 0x0, 0x0, 0x5f, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0x63, 0x28, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1c, 0xff, 0xff, 0xa1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x13, 0x41, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+0636 "ض" */
    0x0, 0x0, 0x0, 0x0, 0xe, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xaf,
    0xfc, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3e,
    0xd6, 0x5c, 0xe0, 0x0, 0x0, 0x0, 0xa, 0x43,
    0xfb, 0x0, 0x5, 0xf1, 0x4e, 0x10, 0x0, 0xc,
    0xce, 0xc0, 0x0, 0x2d, 0xf0, 0xac, 0x0, 0x0,
    0xa, 0xff, 0x87, 0x8b, 0xff, 0x50, 0xd9, 0x0,
    0x0, 0xb, 0xde, 0xff, 0xed, 0x81, 0x0, 0xf7,
    0x0, 0x0, 0xd, 0x90, 0x0, 0x0, 0x0, 0x0,
    0xe8, 0x0, 0x0, 0x5f, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0x63, 0x28, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1c, 0xff, 0xff, 0xa1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x13, 0x41, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+0637 "ط" */
    0x0, 0xf, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0x70, 0x6, 0xdf, 0xf9, 0x0, 0x0, 0xf7,
    0xb, 0xfa, 0x46, 0xf7, 0x0, 0xf, 0x7b, 0xf6,
    0x0, 0xc, 0xa0, 0x0, 0xfd, 0xf5, 0x0, 0x6,
    0xf8, 0x67, 0x7f, 0xfd, 0x77, 0x9d, 0xfc, 0x1e,
    0xff, 0xff, 0xff, 0xfe, 0xb5, 0x0,

    /* U+0638 "ظ" */
    0x0, 0xf, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0x70, 0x5c, 0x0, 0x0,
    0x0, 0x0, 0xf7, 0x1, 0x20, 0x0, 0x0, 0x0,
    0xf, 0x70, 0x6, 0xdf, 0xf9, 0x0, 0x0, 0xf7,
    0xb, 0xfa, 0x46, 0xf7, 0x0, 0xf, 0x7b, 0xf6,
    0x0, 0xc, 0xa0, 0x0, 0xfd, 0xf5, 0x0, 0x6,
    0xf8, 0x67, 0x7f, 0xfd, 0x77, 0x9d, 0xfc, 0x1e,
    0xff, 0xff, 0xff, 0xfe, 0xb5, 0x0,

    /* U+0639 "ع" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xef,
    0x20, 0x0, 0x0, 0xce, 0x75, 0x0, 0x0, 0x4,
    0xf2, 0x0, 0x0, 0x0, 0x4, 0xf4, 0x26, 0xbd,
    0x0, 0x0, 0xaf, 0xff, 0xd9, 0x0, 0x0, 0xbf,
    0x92, 0x0, 0x0, 0x9, 0xf4, 0x0, 0x0, 0x0,
    0xf, 0x80, 0x0, 0x0, 0x0, 0xf, 0x50, 0x0,
    0x0, 0x0, 0xe, 0x90, 0x0, 0x0, 0x0, 0x6,
    0xf9, 0x31, 0x13, 0x95, 0x0, 0x6d, 0xff, 0xff,
    0xd3, 0x0, 0x0, 0x13, 0x31, 0x0,

    /* U+063A "غ" */
    0x0, 0xf, 0x10, 0x0, 0x0, 0x0, 0x2, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xef, 0x20, 0x0, 0x0,
    0xce, 0x75, 0x0, 0x0, 0x4, 0xf2, 0x0, 0x0,
    0x0, 0x4, 0xf4, 0x26, 0xbd, 0x0, 0x0, 0xaf,
    0xff, 0xd9, 0x0, 0x0, 0xbf, 0x92, 0x0, 0x0,
    0x9, 0xf4, 0x0, 0x0, 0x0, 0xf, 0x80, 0x0,
    0x0, 0x0, 0xf, 0x50, 0x0, 0x0, 0x0, 0xe,
    0x90, 0x0, 0x0, 0x0, 0x6, 0xf9, 0x31, 0x13,
    0x95, 0x0, 0x6d, 0xff, 0xff, 0xd3, 0x0, 0x0,
    0x13, 0x31, 0x0,

    /* U+0640 "ـ" */
    0x17, 0x77, 0x75, 0x2f, 0xff, 0xfd,

    /* U+0641 "ف" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xfd, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xf9, 0xee, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xda, 0x5, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xd1, 0x8f, 0x3b, 0xa0, 0x0, 0x0,
    0x0, 0x4f, 0xfe, 0xf1, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xbc, 0xb, 0xf8, 0x32, 0x12, 0x24,
    0x6a, 0xfe, 0x20, 0x9, 0xff, 0xff, 0xff, 0xff,
    0xc7, 0x0, 0x0, 0x0, 0x24, 0x44, 0x31, 0x0,
    0x0, 0x0,

    /* U+0642 "ق" */
    0x0, 0x0, 0x0, 0x5c, 0x6c, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x12, 0x0, 0x0, 0x0, 0x0, 0x1b,
    0xfc, 0x10, 0x0, 0x0, 0x0, 0xaf, 0x9e, 0xa0,
    0x0, 0x0, 0x0, 0xf9, 0x7, 0xf0, 0x0, 0x0,
    0x0, 0xdc, 0x19, 0xf2, 0x3, 0x50, 0x0, 0x5f,
    0xfe, 0xf3, 0xc, 0x90, 0x0, 0x1, 0x24, 0xf1,
    0xf, 0x50, 0x0, 0x0, 0x9, 0xd0, 0x2f, 0x40,
    0x0, 0x0, 0x3f, 0x60, 0xf, 0x70, 0x0, 0x4,
    0xeb, 0x0, 0xb, 0xe5, 0x35, 0xaf, 0xb0, 0x0,
    0x1, 0xdf, 0xff, 0xe7, 0x0, 0x0, 0x0, 0x3,
    0x43, 0x0, 0x0, 0x0,

    /* U+0643 "ك" */
    0x0, 0x0, 0x0, 0x0, 0xe, 0x90, 0x0, 0x0,
    0x0, 0x0, 0xe9, 0x0, 0x0, 0x0, 0x0, 0xe,
    0x90, 0x0, 0x2, 0x96, 0x0, 0xe9, 0x0, 0x0,
    0x76, 0x0, 0xe, 0x90, 0x0, 0x0, 0x58, 0x0,
    0xe9, 0x0, 0x2, 0xab, 0x20, 0xe, 0x90, 0x0,
    0x0, 0x0, 0x0, 0xe8, 0x54, 0x0, 0x0, 0x0,
    0xf, 0x7d, 0xa0, 0x0, 0x0, 0x9, 0xf3, 0x8f,
    0xb7, 0x55, 0x8e, 0xf8, 0x0, 0x5c, 0xef, 0xfe,
    0xb4, 0x0,

    /* U+0644 "ل" */
    0x0, 0x0, 0x0, 0x4, 0xf3, 0x0, 0x0, 0x0,
    0x4, 0xf3, 0x0, 0x0, 0x0, 0x4, 0xf3, 0x0,
    0x0, 0x0, 0x4, 0xf3, 0x0, 0x0, 0x0, 0x4,
    0xf3, 0x0, 0x0, 0x0, 0x4, 0xf3, 0x0, 0x0,
    0x0, 0x4, 0xf3, 0x0, 0x0, 0x0, 0x4, 0xf3,
    0x0, 0x0, 0x0, 0x4, 0xf2, 0x36, 0x0, 0x0,
    0x5, 0xf2, 0xcb, 0x0, 0x0, 0x8, 0xf0, 0xd9,
    0x0, 0x0, 0x2e, 0xb0, 0x9f, 0x61, 0x37, 0xef,
    0x20, 0xa, 0xff, 0xff, 0xa2, 0x0, 0x0, 0x13,
    0x30, 0x0, 0x0,

    /* U+0645 "م" */
    0x0, 0x2, 0x87, 0x10, 0x0, 0x2f, 0xff, 0xf3,
    0x0, 0xae, 0x11, 0xea, 0x3, 0xdd, 0x32, 0xea,
    0x5f, 0xbe, 0xff, 0xe3, 0xcb, 0x0, 0x23, 0x0,
    0xea, 0x0, 0x0, 0x0, 0xea, 0x0, 0x0, 0x0,
    0xea, 0x0, 0x0, 0x0, 0xea, 0x0, 0x0, 0x0,

    /* U+0646 "ن" */
    0x0, 0x7, 0xa0, 0x0, 0x0, 0x0, 0x1, 0x10,
    0x4, 0x50, 0x0, 0x0, 0x0, 0x5, 0xf2, 0x23,
    0x0, 0x0, 0x1, 0xf6, 0xbc, 0x0, 0x0, 0x0,
    0xf8, 0xda, 0x0, 0x0, 0x0, 0xf8, 0xc9, 0x0,
    0x0, 0x3, 0xf5, 0xad, 0x0, 0x0, 0xa, 0xe0,
    0x3f, 0xa4, 0x24, 0xaf, 0x60, 0x5, 0xef, 0xff,
    0xe5, 0x0, 0x0, 0x3, 0x42, 0x0, 0x0,

    /* U+0647 "ه" */
    0x0, 0x10, 0x0, 0x2, 0xef, 0xd5, 0x0, 0xae,
    0x5a, 0xf7, 0xd, 0x90, 0x7, 0xf1, 0xe8, 0x0,
    0x3f, 0x3b, 0xe6, 0x6d, 0xe0, 0x2b, 0xfe, 0x91,
    0x0,

    /* U+0648 "و" */
    0x0, 0x8, 0xed, 0x40, 0x0, 0x6f, 0xac, 0xf1,
    0x0, 0x9e, 0x1, 0xf6, 0x0, 0x6f, 0xa6, 0xf7,
    0x0, 0x8, 0xdf, 0xf7, 0x0, 0x0, 0x4, 0xf4,
    0x0, 0x0, 0x2e, 0xe0, 0x12, 0x48, 0xef, 0x30,
    0xaf, 0xff, 0xa2, 0x0, 0x34, 0x20, 0x0, 0x0,

    /* U+0649 "ى" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6d, 0xfe, 0x70, 0x0, 0x0, 0x4f, 0x94, 0x7f,
    0x40, 0x0, 0x5, 0xf7, 0x0, 0x10, 0x12, 0x0,
    0x9, 0xfe, 0x80, 0xc, 0xa0, 0x0, 0x2, 0x7e,
    0xb0, 0xf7, 0x0, 0x0, 0x0, 0xaf, 0xd, 0xc0,
    0x0, 0x2, 0x9f, 0x90, 0x4f, 0xfc, 0xcf, 0xff,
    0x90, 0x0, 0x28, 0xba, 0x85, 0x10, 0x0,

    /* U+064A "ي" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6d, 0xfe, 0x70, 0x0, 0x0, 0x4f, 0x94, 0x7f,
    0x40, 0x0, 0x4, 0xf8, 0x10, 0x10, 0x24, 0x0,
    0x8, 0xff, 0xa1, 0xc, 0xa0, 0x0, 0x0, 0x5e,
    0xc0, 0xf7, 0x0, 0x0, 0x0, 0xbf, 0xb, 0xd1,
    0x0, 0x4, 0xaf, 0x70, 0x1c, 0xfd, 0xef, 0xfb,
    0x40, 0x0, 0x3, 0x55, 0x30, 0x0, 0x0, 0x0,
    0x1f, 0x3f, 0x0, 0x0, 0x0, 0x0, 0x20, 0x20,
    0x0, 0x0,

    /* U+064B "ً" */
    0x0, 0x0, 0x0, 0x5, 0x9c, 0xd3, 0x27, 0x41,
    0x21, 0x17, 0xbd, 0xb2, 0x25, 0x20, 0x0,

    /* U+064C "ٌ" */
    0x0, 0x5d, 0x60, 0x0, 0xa7, 0xc0, 0x36, 0x1c,
    0xe3, 0x2a, 0x1c, 0x10, 0x9, 0xc3, 0x0,

    /* U+064D "ٍ" */
    0x1, 0x48, 0xb3, 0x3c, 0x85, 0x10, 0x3, 0x6a,
    0xd3, 0x3a, 0x63, 0x0,

    /* U+064E "َ" */
    0x0, 0x0, 0x21, 0x17, 0xbd, 0xb2, 0x25, 0x20,
    0x0,

    /* U+064F "ُ" */
    0x0, 0x5d, 0x60, 0x0, 0xa7, 0xc0, 0x0, 0x3e,
    0xe3, 0x1, 0x8b, 0x0, 0x3c, 0x60, 0x0,

    /* U+0650 "ِ" */
    0x0, 0x14, 0x82, 0x3d, 0xc9, 0x50, 0x0, 0x0,
    0x0,

    /* U+0651 "ّ" */
    0x0, 0x0, 0x32, 0x12, 0x66, 0x56, 0x66, 0x67,
    0x66, 0x66, 0x9c, 0xd3, 0x2e, 0x92, 0x30,

    /* U+0652 "ْ" */
    0x5, 0xdd, 0x50, 0xe, 0x12, 0xe0, 0xe, 0x22,
    0xe0, 0x5, 0xed, 0x50,

    /* U+0653 "ٓ" */
    0x2a, 0x20, 0x28, 0x94, 0xbd, 0xa3, 0x0, 0x0,
    0x0,

    /* U+0654 "ٔ" */
    0x1b, 0xb1, 0x57, 0x0, 0x2e, 0xb3, 0x35, 0x20,

    /* U+0655 "ٕ" */
    0x1b, 0xb1, 0x57, 0x0, 0x2e, 0xb3, 0x35, 0x20,

    /* U+0657 "ٗ" */
    0x0, 0x5, 0xc3, 0x0, 0xa8, 0x10, 0x3e, 0xe3,
    0x0, 0xc, 0x7a, 0x0, 0x6, 0xe5, 0x0,

    /* U+065A "ٚ" */
    0x7, 0x11, 0x70, 0x7, 0xaa, 0x70, 0x0, 0xdd,
    0x0,

    /* U+0660 "٠" */
    0x8f, 0x29, 0xf2,

    /* U+0661 "١" */
    0xae, 0x0, 0x4f, 0x30, 0xe, 0x90, 0x9, 0xd0,
    0x5, 0xf1, 0x2, 0xf4, 0x0, 0xf6, 0x0, 0xf6,
    0x0, 0xf7, 0x0, 0xf7,

    /* U+0662 "٢" */
    0x2f, 0x70, 0x0, 0xac, 0xc, 0xf7, 0x26, 0xf7,
    0x6, 0xff, 0xff, 0xc0, 0x2, 0xf7, 0x33, 0x0,
    0x0, 0xea, 0x0, 0x0, 0x0, 0xbc, 0x0, 0x0,
    0x0, 0x9d, 0x0, 0x0, 0x0, 0x8e, 0x0, 0x0,
    0x0, 0x7e, 0x0, 0x0, 0x0, 0x7e, 0x0, 0x0,

    /* U+0663 "٣" */
    0x3f, 0x40, 0xf6, 0x4f, 0x20, 0xda, 0xf, 0x97,
    0xf1, 0x7, 0xfd, 0xff, 0xfc, 0x0, 0x2f, 0x91,
    0x33, 0x0, 0x0, 0xe9, 0x0, 0x0, 0x0, 0xb,
    0xb0, 0x0, 0x0, 0x0, 0xac, 0x0, 0x0, 0x0,
    0x9, 0xd0, 0x0, 0x0, 0x0, 0x8d, 0x0, 0x0,
    0x0, 0x8, 0xe0, 0x0, 0x0,

    /* U+0664 "٤" */
    0x0, 0x0, 0x0, 0x0, 0x2, 0x9e, 0x40, 0x4,
    0xfd, 0x71, 0x0, 0xda, 0x0, 0x0, 0xa, 0xe4,
    0x0, 0x0, 0x2e, 0xf6, 0x0, 0x1e, 0xd6, 0x10,
    0x8, 0xe0, 0x0, 0x0, 0x8e, 0x0, 0x0, 0x3,
    0xfb, 0x79, 0xd5, 0x5, 0xdf, 0xd9, 0x10,

    /* U+0665 "٥" */
    0x0, 0x1, 0x0, 0x0, 0x1d, 0xf9, 0x0, 0xb,
    0xd8, 0xf5, 0x3, 0xf4, 0xb, 0xc0, 0x8e, 0x0,
    0x4f, 0x2c, 0xa0, 0x0, 0xf6, 0xe8, 0x0, 0xe,
    0x8e, 0x80, 0x0, 0xe8, 0xcc, 0x0, 0x2f, 0x67,
    0xfa, 0x8d, 0xf1, 0x8, 0xef, 0xd4, 0x0,

    /* U+0666 "٦" */
    0x25, 0x21, 0x24, 0x40, 0x4f, 0xff, 0xff, 0xa0,
    0x1, 0x34, 0x3c, 0xa0, 0x0, 0x0, 0xb, 0xb0,
    0x0, 0x0, 0xa, 0xc0, 0x0, 0x0, 0x8, 0xe0,
    0x0, 0x0, 0x7, 0xf0, 0x0, 0x0, 0x4, 0xf2,
    0x0, 0x0, 0x2, 0xf5, 0x0, 0x0, 0x0, 0xf8,
    0x0, 0x0, 0x0, 0xbc,

    /* U+0667 "٧" */
    0x4f, 0x30, 0x0, 0xad, 0x0, 0xda, 0x0, 0x1f,
    0x60, 0x6, 0xf1, 0x7, 0xe0, 0x0, 0x1f, 0x70,
    0xd9, 0x0, 0x0, 0xbc, 0x2f, 0x40, 0x0, 0x5,
    0xf9, 0xf0, 0x0, 0x0, 0x1f, 0xfb, 0x0, 0x0,
    0x0, 0xdf, 0x70, 0x0, 0x0, 0x9, 0xf4, 0x0,
    0x0, 0x0, 0x7f, 0x10, 0x0,

    /* U+0668 "٨" */
    0x0, 0x7, 0xf1, 0x0, 0x0, 0x0, 0x9f, 0x40,
    0x0, 0x0, 0xd, 0xf7, 0x0, 0x0, 0x1, 0xff,
    0xb0, 0x0, 0x0, 0x5f, 0x9f, 0x0, 0x0, 0xb,
    0xc2, 0xf4, 0x0, 0x1, 0xf6, 0xd, 0x90, 0x0,
    0x6f, 0x10, 0x7e, 0x0, 0xd, 0xa0, 0x1, 0xf6,
    0x4, 0xf3, 0x0, 0xa, 0xd0,

    /* U+0669 "٩" */
    0x0, 0x0, 0x0, 0x0, 0x2, 0xcf, 0xf7, 0x0,
    0xd, 0xc5, 0xaf, 0x40, 0x2f, 0x40, 0xd, 0x90,
    0xe, 0xb4, 0x2b, 0xc0, 0x3, 0xdf, 0xff, 0xd0,
    0x0, 0x2, 0x49, 0xf0, 0x0, 0x0, 0x5, 0xf2,
    0x0, 0x0, 0x2, 0xf5, 0x0, 0x0, 0x0, 0xf8,
    0x0, 0x0, 0x0, 0xbc,

    /* U+066A "٪" */
    0xd9, 0x0, 0xe, 0x4c, 0x80, 0x7, 0xc0, 0x0,
    0x1, 0xe3, 0x0, 0x0, 0x8b, 0x0, 0x0, 0x1f,
    0x30, 0x0, 0x9, 0xa0, 0x0, 0x2, 0xf2, 0x0,
    0x0, 0xa9, 0x0, 0x0, 0x2f, 0x10, 0xe, 0x6b,
    0x80, 0x0, 0xf7,

    /* U+066B "٫" */
    0x0, 0x6, 0x80, 0x0, 0x6c, 0x0, 0x8, 0xb0,
    0x0, 0xc7, 0x0, 0x6f, 0x21, 0x7f, 0x60, 0xfe,
    0x60, 0x2, 0x0, 0x0,

    /* U+066C "٬" */
    0xa, 0x70, 0xf9, 0x3f, 0x27, 0xa0,

    /* U+066D "٭" */
    0x0, 0x0, 0x20, 0x0, 0x0, 0x0, 0xb, 0x0,
    0x0, 0x0, 0x5, 0xf1, 0x0, 0x0, 0x9f, 0xff,
    0xff, 0x60, 0x0, 0x5f, 0xfe, 0x20, 0x0, 0x5,
    0xfd, 0xf1, 0x0, 0x0, 0x94, 0x8, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+066E "ٮ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0x5b, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x9c, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xbd, 0xb0, 0x0, 0x0, 0x0,
    0x2a, 0xf4, 0x5f, 0xd8, 0x66, 0x8a, 0xdf, 0xe5,
    0x0, 0x3a, 0xef, 0xfe, 0xc9, 0x50, 0x0,

    /* U+066F "ٯ" */
    0x0, 0x0, 0x0, 0x1b, 0xfc, 0x10, 0x0, 0x0,
    0x0, 0xaf, 0x9e, 0xa0, 0x0, 0x0, 0x0, 0xf9,
    0x7, 0xf0, 0x0, 0x0, 0x0, 0xdc, 0x19, 0xf2,
    0x3, 0x50, 0x0, 0x5f, 0xfe, 0xf3, 0xc, 0x90,
    0x0, 0x1, 0x24, 0xf1, 0xf, 0x50, 0x0, 0x0,
    0x9, 0xd0, 0x2f, 0x40, 0x0, 0x0, 0x3f, 0x60,
    0xf, 0x70, 0x0, 0x4, 0xeb, 0x0, 0xb, 0xe5,
    0x35, 0xaf, 0xb0, 0x0, 0x1, 0xdf, 0xff, 0xe7,
    0x0, 0x0, 0x0, 0x3, 0x43, 0x0, 0x0, 0x0,

    /* U+0670 "ٰ" */
    0x33, 0x67, 0x67, 0x67, 0x67,

    /* U+0674 "ٴ" */
    0x8, 0xc5, 0xc, 0x0, 0xc, 0xc7, 0x6, 0x30,

    /* U+0679 "ٹ" */
    0x0, 0x0, 0x23, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x57,
    0x66, 0x0, 0x0, 0x0, 0x0, 0x5, 0xe7, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0xbb, 0xa6, 0x0, 0x1,
    0x18, 0x90, 0x0, 0x0, 0x0, 0x0, 0x9c, 0xe7,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xce, 0xa0, 0x0,
    0x0, 0x0, 0x18, 0xf6, 0x6f, 0xd8, 0x66, 0x7a,
    0xcf, 0xf7, 0x0, 0x4a, 0xef, 0xfe, 0xc9, 0x61,
    0x0,

    /* U+067A "ٺ" */
    0x0, 0x0, 0x8, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x11, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0x90, 0x0, 0x0, 0x5, 0x70, 0x0, 0x11, 0x0,
    0x0, 0x9c, 0xe8, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xce, 0xa0, 0x0, 0x0, 0x0, 0x7, 0xf7, 0x7f,
    0xd8, 0x66, 0x79, 0xcf, 0xf8, 0x0, 0x4a, 0xef,
    0xfe, 0xc9, 0x61, 0x0,

    /* U+067B "ٻ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0x5b, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x9c, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xbd, 0xb0, 0x0, 0x0, 0x0,
    0x2a, 0xf4, 0x5f, 0xd8, 0x66, 0x8a, 0xdf, 0xe5,
    0x0, 0x3a, 0xef, 0xfe, 0xc9, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x89, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x89, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x10, 0x0, 0x0, 0x0,

    /* U+067C "ټ" */
    0x0, 0x0, 0x8a, 0x99, 0x0, 0x0, 0x5, 0x70,
    0x1, 0x11, 0x10, 0x0, 0x9c, 0xe8, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xce, 0xa0, 0x0, 0x0, 0x0,
    0x7, 0xf7, 0x7f, 0xd8, 0x66, 0x79, 0xcf, 0xf8,
    0x0, 0x4a, 0xef, 0xff, 0xd9, 0x61, 0x0, 0x0,
    0x0, 0x87, 0x69, 0x0, 0x0, 0x0, 0x0, 0x9,
    0x76, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xd3,
    0x0, 0x0, 0x0,

    /* U+067D "ٽ" */
    0x0, 0x0, 0x8a, 0x99, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x11, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0x60, 0x0, 0x89, 0x0,
    0x0, 0x9b, 0xe8, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xce, 0xa0, 0x0, 0x0, 0x0, 0x7, 0xf7, 0x7f,
    0xd8, 0x66, 0x79, 0xcf, 0xf8, 0x0, 0x4a, 0xef,
    0xfe, 0xc9, 0x61, 0x0,

    /* U+067E "پ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0x5b, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x9c, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xbd, 0xb0, 0x0, 0x0, 0x0,
    0x2a, 0xf4, 0x5f, 0xd8, 0x66, 0x8a, 0xdf, 0xe5,
    0x0, 0x3a, 0xef, 0xfe, 0xc9, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xa9, 0x90, 0x0, 0x0, 0x0, 0x0, 0x11, 0x11,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x89, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x10, 0x0, 0x0, 0x0,

    /* U+067F "ٿ" */
    0x0, 0x0, 0x8a, 0x99, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x11, 0x10, 0x0, 0x0, 0x0, 0x0, 0x8a,
    0x99, 0x0, 0x0, 0x5, 0x70, 0x1, 0x11, 0x10,
    0x0, 0x9c, 0xe8, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xce, 0xa0, 0x0, 0x0, 0x0, 0x7, 0xf7, 0x7f,
    0xd8, 0x66, 0x79, 0xcf, 0xf8, 0x0, 0x4a, 0xef,
    0xfe, 0xc9, 0x61, 0x0,

    /* U+0680 "ڀ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0x5b, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x9c, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xbd, 0xb0, 0x0, 0x0, 0x0,
    0x2a, 0xf4, 0x5f, 0xd8, 0x66, 0x8a, 0xdf, 0xe5,
    0x0, 0x3a, 0xef, 0xfe, 0xc9, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xa9, 0x90, 0x0, 0x0, 0x0, 0x0, 0x11, 0x11,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xa9, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x11, 0x11, 0x0, 0x0, 0x0,

    /* U+0681 "ځ" */
    0x0, 0xa, 0xc2, 0x0, 0x0, 0x0, 0x38, 0x0,
    0x0, 0x0, 0x0, 0x1e, 0xa4, 0x0, 0x0, 0x0,
    0x38, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9e, 0xed, 0xff, 0xe9, 0x0, 0x41, 0x3d,
    0xe8, 0x41, 0x0, 0x3, 0xfa, 0x0, 0x0, 0x0,
    0xc, 0xc0, 0x0, 0x0, 0x0, 0x3f, 0x30, 0x0,
    0x0, 0x0, 0x6f, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0x10, 0x0, 0x0, 0x0, 0x1f, 0x80, 0x0, 0x0,
    0x0, 0x8, 0xf9, 0x31, 0x14, 0x94, 0x0, 0x6d,
    0xff, 0xff, 0xc2, 0x0, 0x0, 0x13, 0x31, 0x0,

    /* U+0682 "ڂ" */
    0x0, 0x7, 0xa0, 0x0, 0x0, 0x0, 0x1, 0x10,
    0x0, 0x0, 0x0, 0x7, 0xa0, 0x0, 0x0, 0x0,
    0x1, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9e, 0xed, 0xff, 0xe9, 0x0, 0x41, 0x3d,
    0xe8, 0x41, 0x0, 0x3, 0xfa, 0x0, 0x0, 0x0,
    0xc, 0xc0, 0x0, 0x0, 0x0, 0x3f, 0x30, 0x0,
    0x0, 0x0, 0x6f, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0x10, 0x0, 0x0, 0x0, 0x1f, 0x80, 0x0, 0x0,
    0x0, 0x8, 0xf9, 0x31, 0x14, 0x94, 0x0, 0x6d,
    0xff, 0xff, 0xc2, 0x0, 0x0, 0x13, 0x31, 0x0,

    /* U+0683 "ڃ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7c, 0xff, 0xff,
    0xd8, 0x0, 0x98, 0x5b, 0xfe, 0xb6, 0x0, 0x0,
    0xbf, 0x70, 0x0, 0x0, 0x7, 0xf4, 0x0, 0x0,
    0x0, 0xf, 0x80, 0x0, 0x0, 0x0, 0x4f, 0x20,
    0x20, 0x20, 0x0, 0x6f, 0x0, 0xe4, 0xf3, 0x0,
    0x5f, 0x20, 0x0, 0x0, 0x0, 0x1f, 0x90, 0x0,
    0x0, 0x0, 0x7, 0xfa, 0x41, 0x14, 0xa4, 0x0,
    0x5d, 0xff, 0xff, 0xc2, 0x0, 0x0, 0x13, 0x31,
    0x0,

    /* U+0684 "ڄ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9e, 0xed, 0xff,
    0xe9, 0x0, 0x41, 0x3d, 0xe8, 0x41, 0x0, 0x3,
    0xfa, 0x0, 0x0, 0x0, 0xc, 0xc0, 0x0, 0x0,
    0x0, 0x3f, 0x30, 0xe, 0x30, 0x0, 0x6f, 0x0,
    0x2, 0x0, 0x0, 0x5f, 0x10, 0xf, 0x30, 0x0,
    0x1f, 0x80, 0x2, 0x0, 0x0, 0x8, 0xf9, 0x31,
    0x13, 0x83, 0x0, 0x6d, 0xff, 0xff, 0xc2, 0x0,
    0x0, 0x13, 0x31, 0x0,

    /* U+0685 "څ" */
    0x0, 0x7, 0xa0, 0x0, 0x0, 0x0, 0x1, 0x10,
    0x0, 0x0, 0x0, 0x7a, 0x8a, 0x0, 0x0, 0x0,
    0x11, 0x11, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9e, 0xed, 0xff, 0xe9, 0x0, 0x41, 0x3d,
    0xe8, 0x41, 0x0, 0x3, 0xfa, 0x0, 0x0, 0x0,
    0xc, 0xc0, 0x0, 0x0, 0x0, 0x3f, 0x30, 0x0,
    0x0, 0x0, 0x6f, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0x10, 0x0, 0x0, 0x0, 0x1f, 0x80, 0x0, 0x0,
    0x0, 0x8, 0xf9, 0x31, 0x14, 0x94, 0x0, 0x6d,
    0xff, 0xff, 0xc2, 0x0, 0x0, 0x13, 0x31, 0x0,

    /* U+0686 "چ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9e, 0xed, 0xff,
    0xe9, 0x0, 0x41, 0x3d, 0xe8, 0x41, 0x0, 0x3,
    0xfa, 0x0, 0x0, 0x0, 0xc, 0xc0, 0x0, 0x0,
    0x0, 0x3f, 0x30, 0xd5, 0xd4, 0x0, 0x6f, 0x0,
    0x20, 0x20, 0x0, 0x5f, 0x10, 0xd, 0x50, 0x0,
    0x1f, 0x80, 0x2, 0x0, 0x0, 0x8, 0xf9, 0x31,
    0x14, 0x94, 0x0, 0x6d, 0xff, 0xff, 0xc2, 0x0,
    0x0, 0x13, 0x31, 0x0,

    /* U+0687 "ڇ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9e, 0xed, 0xff,
    0xe9, 0x0, 0x41, 0x3d, 0xe8, 0x41, 0x0, 0x3,
    0xfa, 0x0, 0x0, 0x0, 0xc, 0xc0, 0x0, 0x0,
    0x0, 0x3f, 0x30, 0xd5, 0xd4, 0x0, 0x6f, 0x0,
    0x20, 0x20, 0x0, 0x5f, 0x10, 0xd5, 0xe4, 0x0,
    0x1f, 0x80, 0x21, 0x20, 0x0, 0x8, 0xf9, 0x31,
    0x13, 0x83, 0x0, 0x6d, 0xff, 0xff, 0xc2, 0x0,
    0x0, 0x13, 0x31, 0x0,

    /* U+0688 "ڈ" */
    0x0, 0xc0, 0x0, 0x0, 0xc, 0x26, 0x10, 0x0,
    0xda, 0x98, 0x0, 0x6e, 0xcc, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xb7, 0x0, 0x0, 0x5, 0xf5,
    0x0, 0x0, 0xa, 0xd0, 0x0, 0x0, 0x5f, 0x20,
    0x0, 0x7, 0xf1, 0x9, 0x6a, 0xfc, 0x0, 0xdf,
    0xe9, 0x10,

    /* U+0689 "ډ" */
    0x0, 0x2e, 0xa0, 0x0, 0x0, 0x3f, 0x70, 0x0,
    0x0, 0x9e, 0x0, 0x0, 0x5, 0xf2, 0x0, 0x0,
    0x7f, 0x10, 0x96, 0xaf, 0xb0, 0xd, 0xff, 0xf1,
    0x0, 0xa, 0x58, 0x80, 0x0, 0xb5, 0x88, 0x0,
    0x3, 0xdc, 0x10,

    /* U+068A "ڊ" */
    0x0, 0x2e, 0xa0, 0x0, 0x0, 0x3f, 0x70, 0x0,
    0x0, 0x9e, 0x0, 0x0, 0x5, 0xf2, 0x0, 0x0,
    0x7f, 0x10, 0x96, 0xaf, 0xb0, 0xd, 0xfe, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0x70, 0x0,
    0x0, 0x11, 0x0,

    /* U+068B "ڋ" */
    0x0, 0xc0, 0x0, 0x0, 0xc, 0x26, 0x10, 0x0,
    0xda, 0x98, 0x0, 0x6e, 0xcc, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xb7, 0x0, 0x0, 0x5, 0xf5,
    0x0, 0x0, 0xa, 0xd0, 0x0, 0x0, 0x5f, 0x20,
    0x0, 0x7, 0xf1, 0x9, 0x6a, 0xfc, 0x0, 0xdf,
    0xe9, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa7,
    0x0, 0x0, 0x1, 0x10, 0x0,

    /* U+068C "ڌ" */
    0x0, 0xf3, 0xf1, 0x0, 0x2, 0x2, 0x0, 0x0,
    0x13, 0x0, 0x0, 0x1, 0xdc, 0x0, 0x0, 0x2,
    0xf8, 0x0, 0x0, 0x8, 0xe0, 0x0, 0x0, 0x4f,
    0x20, 0x0, 0x8, 0xf1, 0x9, 0x7a, 0xfb, 0x0,
    0xdf, 0xe8, 0x0,

    /* U+068D "ڍ" */
    0x0, 0x2e, 0xa0, 0x0, 0x0, 0x3f, 0x70, 0x0,
    0x0, 0x9e, 0x0, 0x0, 0x5, 0xf2, 0x0, 0x0,
    0x7f, 0x10, 0x96, 0xaf, 0xb0, 0xd, 0xfe, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa8, 0xb7, 0x0,
    0x1, 0x11, 0x10,

    /* U+068E "ڎ" */
    0x0, 0xf, 0x10, 0x0, 0x0, 0x20, 0x0, 0x0,
    0xf3, 0xf1, 0x0, 0x2, 0x2, 0x0, 0x0, 0x13,
    0x0, 0x0, 0x1, 0xdc, 0x0, 0x0, 0x2, 0xf8,
    0x0, 0x0, 0x8, 0xe0, 0x0, 0x0, 0x4f, 0x20,
    0x0, 0x8, 0xf1, 0x9, 0x7a, 0xfb, 0x0, 0xdf,
    0xe8, 0x0,

    /* U+068F "ڏ" */
    0x0, 0xf3, 0xf1, 0x0, 0x2, 0x2, 0x0, 0x0,
    0xf, 0x10, 0x0, 0x0, 0x20, 0x0, 0x0, 0x2b,
    0x60, 0x0, 0x0, 0x5f, 0x40, 0x0, 0x0, 0xad,
    0x0, 0x0, 0x5, 0xf2, 0x0, 0x0, 0x7f, 0x10,
    0x96, 0xaf, 0xc0, 0xd, 0xfe, 0x91, 0x0,

    /* U+0690 "ڐ" */
    0x0, 0xf3, 0xf1, 0x0, 0x2, 0x2, 0x0, 0x0,
    0xf3, 0xf1, 0x0, 0x2, 0x2, 0x0, 0x0, 0x13,
    0x0, 0x0, 0x1, 0xdc, 0x0, 0x0, 0x2, 0xf8,
    0x0, 0x0, 0x8, 0xe0, 0x0, 0x0, 0x4f, 0x20,
    0x0, 0x8, 0xf1, 0x9, 0x7a, 0xfb, 0x0, 0xdf,
    0xe8, 0x0,

    /* U+0691 "ڑ" */
    0x0, 0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0xc0,
    0x0, 0x0, 0x0, 0xc, 0x27, 0x10, 0x0, 0x0,
    0xda, 0x97, 0x0, 0x0, 0x7e, 0xcb, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0x70, 0x0, 0x0, 0x0, 0xbb,
    0x0, 0x0, 0x0, 0xa, 0xb0, 0x0, 0x0, 0x0,
    0xd9, 0x0, 0x0, 0x0, 0x4f, 0x50, 0x0, 0x0,
    0x4f, 0xd0, 0x1, 0x46, 0xcf, 0xd1, 0x0, 0xaf,
    0xfc, 0x60, 0x0, 0x3, 0x31, 0x0, 0x0, 0x0,

    /* U+0692 "ڒ" */
    0x0, 0x0, 0x47, 0x8, 0x40, 0x0, 0x0, 0xd8,
    0xd0, 0x0, 0x0, 0x4, 0xd3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0x50, 0x0, 0x0, 0x0, 0xba, 0x0,
    0x0, 0x0, 0xa, 0xb0, 0x0, 0x0, 0x0, 0xca,
    0x0, 0x0, 0x0, 0x3f, 0x60, 0x0, 0x0, 0x4e,
    0xd0, 0x1, 0x46, 0xcf, 0xd2, 0x0, 0xaf, 0xfc,
    0x60, 0x0, 0x3, 0x31, 0x0, 0x0, 0x0,

    /* U+0693 "ړ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe8, 0x0, 0x0, 0x0, 0x0, 0xbb, 0x0, 0x0,
    0x0, 0x0, 0xab, 0x0, 0x0, 0x0, 0x0, 0xe9,
    0x0, 0x0, 0x0, 0x7, 0xf4, 0x0, 0x0, 0x1,
    0x9f, 0xfe, 0x90, 0x59, 0xbf, 0xfc, 0xd1, 0xf0,
    0xae, 0xb7, 0x10, 0xbe, 0x90,

    /* U+0694 "ڔ" */
    0x0, 0x0, 0x0, 0xa5, 0x0, 0x0, 0x0, 0xb,
    0xa0, 0x0, 0x0, 0x0, 0xab, 0x0, 0x0, 0x0,
    0xc, 0xa0, 0x0, 0x0, 0x3, 0xf6, 0x0, 0x0,
    0x2, 0xed, 0x0, 0x1, 0x4a, 0xfd, 0x20, 0xa,
    0xff, 0xc6, 0x0, 0x98, 0x33, 0x10, 0x0, 0x0,
    0x0,

    /* U+0695 "ڕ" */
    0x0, 0x0, 0x0, 0xa5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xba, 0x0, 0x0, 0x0, 0x0, 0x0, 0xab,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xca, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x4e, 0xd3, 0x0, 0x20, 0x14, 0x6c, 0xfd, 0x2a,
    0x86, 0xb0, 0xaf, 0xfc, 0x60, 0x1, 0xee, 0x20,
    0x33, 0x10, 0x0, 0x0, 0x33, 0x0,

    /* U+0696 "ږ" */
    0x0, 0x0, 0x0, 0xe8, 0x0, 0x0, 0x0, 0xb,
    0xb0, 0x0, 0x11, 0x0, 0xbb, 0x0, 0x9, 0x90,
    0xe, 0x90, 0x0, 0x0, 0x9, 0xf4, 0x0, 0x0,
    0x4b, 0xfa, 0x0, 0x7c, 0xef, 0xf9, 0x6, 0x5a,
    0xec, 0x72, 0x0, 0x54,

    /* U+0697 "ڗ" */
    0x0, 0x0, 0x1f, 0x3f, 0x0, 0x0, 0x0, 0x20,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0x70, 0x0, 0x0, 0x0, 0xbb, 0x0, 0x0,
    0x0, 0xa, 0xb0, 0x0, 0x0, 0x0, 0xda, 0x0,
    0x0, 0x0, 0x4f, 0x50, 0x0, 0x0, 0x4f, 0xd0,
    0x1, 0x46, 0xcf, 0xd2, 0x0, 0xaf, 0xfc, 0x60,
    0x0, 0x3, 0x31, 0x0, 0x0, 0x0,

    /* U+0698 "ژ" */
    0x0, 0x0, 0x1, 0xf0, 0x0, 0x0, 0x0, 0x2,
    0x0, 0x0, 0x0, 0x1f, 0x3f, 0x0, 0x0, 0x0,
    0x20, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0x70, 0x0, 0x0, 0x0, 0xbb, 0x0,
    0x0, 0x0, 0xa, 0xb0, 0x0, 0x0, 0x0, 0xda,
    0x0, 0x0, 0x0, 0x4f, 0x50, 0x0, 0x0, 0x4f,
    0xd0, 0x1, 0x46, 0xcf, 0xd2, 0x0, 0xaf, 0xfc,
    0x60, 0x0, 0x3, 0x31, 0x0, 0x0, 0x0,

    /* U+0699 "ڙ" */
    0x0, 0x0, 0x1f, 0x3f, 0x0, 0x0, 0x0, 0x20,
    0x20, 0x0, 0x0, 0x1f, 0x3f, 0x0, 0x0, 0x0,
    0x20, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0x70, 0x0, 0x0, 0x0, 0xbb, 0x0,
    0x0, 0x0, 0xa, 0xb0, 0x0, 0x0, 0x0, 0xda,
    0x0, 0x0, 0x0, 0x4f, 0x50, 0x0, 0x0, 0x4f,
    0xd0, 0x1, 0x46, 0xcf, 0xd2, 0x0, 0xaf, 0xfc,
    0x60, 0x0, 0x3, 0x31, 0x0, 0x0, 0x0,

    /* U+069A "ښ" */
    0x0, 0x0, 0x0, 0x0, 0x3, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0x0,
    0x1, 0x61, 0x0, 0x0, 0x0, 0x2, 0x10, 0x0,
    0x20, 0x3, 0xf3, 0x0, 0x0, 0x0, 0xc, 0x90,
    0x5, 0xf2, 0x3, 0xf3, 0x1, 0x0, 0x0, 0x8,
    0xd0, 0x6, 0xf3, 0x4, 0xf2, 0x6f, 0x10, 0x0,
    0x6, 0xf3, 0xa, 0xf9, 0x7, 0xf1, 0xca, 0x0,
    0x0, 0x6, 0xff, 0xbf, 0xcf, 0xbf, 0xb0, 0xe8,
    0x0, 0x0, 0xa, 0xfc, 0xfa, 0x1a, 0xfb, 0x10,
    0xe8, 0x0, 0x0, 0x4f, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0xbe, 0x52, 0x27, 0xfe, 0x20, 0x1f, 0x10,
    0x0, 0x0, 0x2d, 0xff, 0xff, 0xb2, 0x0, 0x2,
    0x0, 0x0, 0x0, 0x0, 0x24, 0x41, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+069B "ڛ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xf3, 0x0, 0x0, 0x0, 0xd, 0x90, 0x4, 0xf2,
    0x3, 0xf3, 0x0, 0x0, 0x0, 0x8, 0xd0, 0x6,
    0xf3, 0x4, 0xf3, 0x6f, 0x10, 0x0, 0x6, 0xf3,
    0xa, 0xf8, 0x6, 0xf1, 0xca, 0x0, 0x0, 0x6,
    0xfe, 0xbf, 0xcf, 0xbf, 0xc0, 0xe8, 0x0, 0x0,
    0xa, 0xfc, 0xfa, 0x1a, 0xfb, 0x10, 0xe8, 0x0,
    0x0, 0x5f, 0xa0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0x85, 0x59, 0xfe, 0x20, 0xb2, 0xb0, 0x0, 0x0,
    0x1b, 0xff, 0xff, 0x91, 0x0, 0x1f, 0x10, 0x0,
    0x0, 0x0, 0x1, 0x20, 0x0, 0x0, 0x2, 0x0,
    0x0, 0x0,

    /* U+069C "ڜ" */
    0x0, 0x0, 0x0, 0x0, 0x3, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3e, 0x4e,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x2, 0x0, 0x1, 0x61, 0x0, 0x0, 0x0, 0x2,
    0x10, 0x0, 0x20, 0x3, 0xf3, 0x0, 0x0, 0x0,
    0xc, 0x90, 0x5, 0xf2, 0x3, 0xf3, 0x1, 0x0,
    0x0, 0x8, 0xd0, 0x6, 0xf3, 0x4, 0xf2, 0x6f,
    0x10, 0x0, 0x5, 0xf3, 0xa, 0xf9, 0x7, 0xf1,
    0xca, 0x0, 0x0, 0x6, 0xff, 0xbf, 0xcf, 0xbf,
    0xb0, 0xe8, 0x0, 0x0, 0xa, 0xfc, 0xfa, 0x1a,
    0xfb, 0x10, 0xe8, 0x0, 0x0, 0x5f, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0x85, 0x59, 0xfe, 0x20,
    0xb2, 0xb0, 0x0, 0x0, 0x1a, 0xff, 0xff, 0x91,
    0x0, 0x1f, 0x10, 0x0, 0x0, 0x0, 0x1, 0x20,
    0x0, 0x0, 0x2, 0x0, 0x0, 0x0,

    /* U+069D "ڝ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xaf,
    0xfc, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3e,
    0xd6, 0x5c, 0xe0, 0x0, 0x0, 0x0, 0xa, 0x43,
    0xfb, 0x0, 0x5, 0xf1, 0x4e, 0x10, 0x0, 0xc,
    0xce, 0xc0, 0x0, 0x2d, 0xf0, 0xac, 0x0, 0x0,
    0xa, 0xff, 0x87, 0x8b, 0xff, 0x50, 0xd9, 0x0,
    0x0, 0xb, 0xde, 0xff, 0xed, 0x81, 0x0, 0xf7,
    0x0, 0x0, 0xd, 0x90, 0x0, 0x0, 0x0, 0x0,
    0xe8, 0x0, 0x0, 0x5f, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0x63, 0x28, 0xfd, 0x0, 0xf3, 0xf0,
    0x0, 0x0, 0x1c, 0xff, 0xff, 0xa1, 0x0, 0x20,
    0x20, 0x0, 0x0, 0x0, 0x13, 0x41, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+069E "ڞ" */
    0x0, 0x0, 0x0, 0x0, 0xf, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe3, 0xe1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xaf, 0xfc, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3e, 0xd6, 0x5c, 0xe0, 0x0, 0x0, 0x0,
    0xa, 0x43, 0xfb, 0x0, 0x5, 0xf1, 0x4e, 0x10,
    0x0, 0xc, 0xce, 0xc0, 0x0, 0x2d, 0xf0, 0xac,
    0x0, 0x0, 0xa, 0xff, 0x87, 0x8b, 0xff, 0x50,
    0xd9, 0x0, 0x0, 0xb, 0xde, 0xff, 0xed, 0x81,
    0x0, 0xf7, 0x0, 0x0, 0xd, 0x90, 0x0, 0x0,
    0x0, 0x0, 0xe8, 0x0, 0x0, 0x5f, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0x63, 0x28, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1c, 0xff, 0xff, 0xa1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x13, 0x41,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+069F "ڟ" */
    0x0, 0xf, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0x70,
    0xf, 0x20, 0x0, 0x0, 0x0, 0xf7, 0x0, 0x20,
    0x0, 0x0, 0x0, 0xf, 0x70, 0xf3, 0xf2, 0x0,
    0x0, 0x0, 0xf7, 0x2, 0x2, 0x10, 0x0, 0x0,
    0xf, 0x70, 0x6, 0xdf, 0xf9, 0x0, 0x0, 0xf7,
    0xb, 0xfa, 0x46, 0xf7, 0x0, 0xf, 0x7b, 0xf6,
    0x0, 0xc, 0xa0, 0x0, 0xfd, 0xf5, 0x0, 0x6,
    0xf8, 0x67, 0x7f, 0xfd, 0x77, 0x9d, 0xfc, 0x1e,
    0xff, 0xff, 0xff, 0xfe, 0xb5, 0x0,

    /* U+06A0 "ڠ" */
    0x0, 0xa, 0x80, 0x0, 0x0, 0x0, 0x1, 0x10,
    0x0, 0x0, 0x0, 0xa8, 0xb7, 0x0, 0x0, 0x0,
    0x11, 0x11, 0x0, 0x0, 0x0, 0x8, 0xef, 0x20,
    0x0, 0x0, 0xce, 0x75, 0x0, 0x0, 0x4, 0xf2,
    0x0, 0x0, 0x0, 0x4, 0xf4, 0x26, 0xbd, 0x0,
    0x0, 0xaf, 0xff, 0xd9, 0x0, 0x0, 0xbf, 0x92,
    0x0, 0x0, 0x9, 0xf4, 0x0, 0x0, 0x0, 0xf,
    0x80, 0x0, 0x0, 0x0, 0xf, 0x50, 0x0, 0x0,
    0x0, 0xe, 0x90, 0x0, 0x0, 0x0, 0x6, 0xf9,
    0x31, 0x13, 0x95, 0x0, 0x6d, 0xff, 0xff, 0xd3,
    0x0, 0x0, 0x13, 0x31, 0x0,

    /* U+06A1 "ڡ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xd2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0x9e, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xa0, 0x5f, 0x30, 0x0,
    0x0, 0x0, 0x0, 0xcd, 0x18, 0xf3, 0xba, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xef, 0x1f, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x3b, 0xc0, 0xbf, 0x83, 0x21,
    0x22, 0x46, 0xaf, 0xe2, 0x0, 0x9f, 0xff, 0xff,
    0xff, 0xfc, 0x70, 0x0, 0x0, 0x2, 0x44, 0x43,
    0x10, 0x0, 0x0, 0x0,

    /* U+06A2 "ڢ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x37, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xc1, 0x8f, 0x10, 0x0,
    0x0, 0x0, 0x0, 0xcc, 0x17, 0xf3, 0xbb, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xef, 0x2f, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x26, 0xf0, 0xea, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xfb, 0x7, 0xfa, 0x42, 0x12,
    0x35, 0x8d, 0xfd, 0x10, 0x6, 0xef, 0xff, 0xff,
    0xff, 0xb6, 0x0, 0x0, 0x0, 0x24, 0x44, 0x31,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x0, 0x0,
    0x0, 0x0,

    /* U+06A3 "ڣ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x37, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xc1, 0x8f, 0x10, 0x0, 0x0,
    0x0, 0x0, 0xcc, 0x17, 0xf3, 0xbb, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xef, 0x2f, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x26, 0xf0, 0xea, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xfb, 0x7, 0xfa, 0x42, 0x12, 0x35,
    0x8d, 0xfd, 0x10, 0x6, 0xef, 0xff, 0xff, 0xff,
    0xb6, 0x0, 0x0, 0x0, 0x24, 0x44, 0x31, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x0, 0x0, 0x0,
    0x0,

    /* U+06A4 "ڤ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf3, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x37, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xc1, 0x8f, 0x10, 0x0, 0x0, 0x0,
    0x0, 0xcc, 0x17, 0xf3, 0xbb, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xef, 0x2f, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x26, 0xf0, 0xea, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xfb, 0x7, 0xfa, 0x42, 0x12, 0x35, 0x8d,
    0xfd, 0x10, 0x6, 0xef, 0xff, 0xff, 0xff, 0xb6,
    0x0, 0x0, 0x0, 0x24, 0x44, 0x31, 0x0, 0x0,
    0x0,

    /* U+06A5 "ڥ" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0xbf, 0xd2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0x9d, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xc1, 0x6f, 0x3b, 0xb0,
    0x0, 0x0, 0x0, 0x5f, 0xfe, 0xf3, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x12, 0x6f, 0x1e, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xc0, 0x7f, 0xa4, 0x21,
    0x23, 0x58, 0xdf, 0xe2, 0x0, 0x6e, 0xff, 0xff,
    0xff, 0xfc, 0x70, 0x0, 0x0, 0x2, 0x44, 0x43,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x2, 0xf3, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0,
    0x0, 0x0,

    /* U+06A6 "ڦ" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0xf3, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf3, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x37, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xc1, 0x8f, 0x10, 0x0, 0x0, 0x0,
    0x0, 0xcc, 0x17, 0xf3, 0xbb, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xef, 0x2f, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x26, 0xf0, 0xea, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xfb, 0x7, 0xfa, 0x42, 0x12, 0x35, 0x8d,
    0xfd, 0x10, 0x6, 0xef, 0xff, 0xff, 0xff, 0xb6,
    0x0, 0x0, 0x0, 0x24, 0x44, 0x31, 0x0, 0x0,
    0x0,

    /* U+06A7 "ڧ" */
    0x0, 0x0, 0x0, 0x5, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x20, 0x0, 0x0, 0x0, 0x0, 0x1b,
    0xfc, 0x10, 0x0, 0x0, 0x0, 0xaf, 0x9e, 0xa0,
    0x0, 0x0, 0x0, 0xf9, 0x7, 0xf0, 0x0, 0x0,
    0x0, 0xdc, 0x19, 0xf2, 0x3, 0x50, 0x0, 0x5f,
    0xfe, 0xf3, 0xc, 0x90, 0x0, 0x1, 0x24, 0xf1,
    0xf, 0x50, 0x0, 0x0, 0x9, 0xd0, 0x2f, 0x40,
    0x0, 0x0, 0x3f, 0x60, 0xf, 0x70, 0x0, 0x4,
    0xeb, 0x0, 0xb, 0xe5, 0x35, 0xaf, 0xb0, 0x0,
    0x1, 0xdf, 0xff, 0xe7, 0x0, 0x0, 0x0, 0x3,
    0x43, 0x0, 0x0, 0x0,

    /* U+06A8 "ڨ" */
    0x0, 0x0, 0x0, 0x6, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x20, 0x0, 0x0, 0x0, 0x0, 0x5c,
    0x6c, 0x0, 0x0, 0x0, 0x0, 0x2, 0x12, 0x0,
    0x0, 0x0, 0x0, 0x3, 0x74, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0x60, 0x0, 0x0, 0x0, 0xeb,
    0x19, 0xe0, 0x0, 0x0, 0x0, 0xeb, 0x18, 0xf2,
    0x3, 0x50, 0x0, 0x5f, 0xfe, 0xf3, 0xc, 0x90,
    0x0, 0x1, 0x24, 0xf2, 0xf, 0x50, 0x0, 0x0,
    0x9, 0xe0, 0x2f, 0x40, 0x0, 0x0, 0x3f, 0x80,
    0xf, 0x70, 0x0, 0x3, 0xec, 0x0, 0xb, 0xe4,
    0x35, 0xaf, 0xc1, 0x0, 0x1, 0xdf, 0xff, 0xe7,
    0x0, 0x0, 0x0, 0x3, 0x43, 0x0, 0x0, 0x0,

    /* U+06A9 "ک" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x42, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x7d, 0xf4, 0x0, 0x0,
    0x0, 0x2, 0x9f, 0xf9, 0x20, 0x0, 0x0, 0x0,
    0x3f, 0xd6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x20, 0x0,
    0x79, 0x0, 0x0, 0x0, 0x1e, 0xb0, 0x0, 0xe8,
    0x0, 0x0, 0x0, 0x7, 0xf0, 0x0, 0xe9, 0x0,
    0x0, 0x0, 0xb, 0xe0, 0x0, 0x8f, 0x94, 0x22,
    0x49, 0xef, 0x60, 0x0, 0x7, 0xef, 0xff, 0xfd,
    0x92, 0x0, 0x0, 0x0, 0x2, 0x43, 0x10, 0x0,
    0x0, 0x0,

    /* U+06AA "ڪ" */
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xe6, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xcf, 0x91, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xfc, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x4e, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xb1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1d, 0xfe,
    0xc9, 0x74, 0x10, 0x0, 0x0, 0x0, 0x5, 0x8b,
    0xdf, 0xff, 0xfc, 0x91, 0x89, 0x0, 0x0, 0x0,
    0x2, 0x57, 0xaf, 0xce, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xe9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1c, 0xd8, 0xf9, 0x42, 0x11, 0x11, 0x24,
    0x9f, 0xf5, 0x7, 0xef, 0xff, 0xff, 0xff, 0xfd,
    0x81, 0x0, 0x0, 0x24, 0x44, 0x44, 0x31, 0x0,
    0x0,

    /* U+06AB "ګ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x43, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x7e, 0xf4, 0x0, 0x0,
    0x0, 0x3, 0xaf, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0xe2, 0xd0, 0x0, 0x0, 0x0, 0x9f,
    0x45, 0xa2, 0xe0, 0x0, 0x0, 0x0, 0x5f, 0x20,
    0xbe, 0x50, 0x0, 0x0, 0x0, 0x9, 0xd1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcc, 0x0, 0x0,
    0x35, 0x0, 0x0, 0x0, 0x1e, 0x90, 0x0, 0xd9,
    0x0, 0x0, 0x0, 0x7, 0xf0, 0x0, 0xe8, 0x0,
    0x0, 0x0, 0xb, 0xf0, 0x0, 0x9f, 0x94, 0x22,
    0x49, 0xff, 0x70, 0x0, 0x8, 0xef, 0xff, 0xfe,
    0x93, 0x0, 0x0, 0x0, 0x2, 0x43, 0x10, 0x0,
    0x0, 0x0,

    /* U+06AC "ڬ" */
    0x0, 0x0, 0x2f, 0x0, 0xe, 0x90, 0x0, 0x0,
    0x20, 0x0, 0xe9, 0x0, 0x0, 0x0, 0x0, 0xe,
    0x90, 0x0, 0x2, 0x96, 0x0, 0xe9, 0x0, 0x0,
    0x76, 0x0, 0xe, 0x90, 0x0, 0x0, 0x58, 0x0,
    0xe9, 0x0, 0x2, 0xab, 0x20, 0xe, 0x90, 0x0,
    0x0, 0x0, 0x0, 0xe8, 0x54, 0x0, 0x0, 0x0,
    0xf, 0x7d, 0xa0, 0x0, 0x0, 0x9, 0xf3, 0x8f,
    0xb7, 0x55, 0x8e, 0xf8, 0x0, 0x5c, 0xef, 0xfe,
    0xb4, 0x0,

    /* U+06AD "ڭ" */
    0x0, 0x0, 0x2f, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x20, 0x0, 0x0, 0x0, 0x1, 0xf3, 0xf0, 0xe,
    0x90, 0x0, 0x2, 0x2, 0x0, 0xe9, 0x0, 0x0,
    0x0, 0x0, 0xe, 0x90, 0x0, 0x2, 0x96, 0x0,
    0xe9, 0x0, 0x0, 0x76, 0x0, 0xe, 0x90, 0x0,
    0x0, 0x58, 0x0, 0xe9, 0x0, 0x2, 0xab, 0x20,
    0xe, 0x90, 0x0, 0x0, 0x0, 0x0, 0xe8, 0x54,
    0x0, 0x0, 0x0, 0xf, 0x7d, 0xa0, 0x0, 0x0,
    0x9, 0xf3, 0x8f, 0xb7, 0x55, 0x8e, 0xf8, 0x0,
    0x5c, 0xef, 0xfe, 0xb4, 0x0,

    /* U+06AE "ڮ" */
    0x0, 0x0, 0x0, 0x0, 0xe, 0x90, 0x0, 0x0,
    0x0, 0x0, 0xe9, 0x0, 0x0, 0x0, 0x0, 0xe,
    0x90, 0x0, 0x2, 0x96, 0x0, 0xe9, 0x0, 0x0,
    0x76, 0x0, 0xe, 0x90, 0x0, 0x0, 0x58, 0x0,
    0xe9, 0x0, 0x2, 0xab, 0x20, 0xe, 0x90, 0x0,
    0x0, 0x0, 0x0, 0xe8, 0x54, 0x0, 0x0, 0x0,
    0xf, 0x7d, 0xa0, 0x0, 0x0, 0x9, 0xf3, 0x8f,
    0xb7, 0x55, 0x8e, 0xf8, 0x0, 0x5c, 0xef, 0xfe,
    0xb4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4d, 0x5c, 0x0, 0x0, 0x0, 0x0, 0x20,
    0x20, 0x0, 0x0, 0x0, 0x5, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x0, 0x0, 0x0,

    /* U+06AF "گ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x73, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xaf, 0xb2, 0x0, 0x0,
    0x0, 0x6, 0xde, 0x81, 0x42, 0x0, 0x0, 0x0,
    0x6b, 0x51, 0x7d, 0xf4, 0x0, 0x0, 0x0, 0x2,
    0x9f, 0xf9, 0x20, 0x0, 0x0, 0x0, 0x3f, 0xd6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0x20, 0x0, 0x79, 0x0,
    0x0, 0x0, 0x1e, 0xb0, 0x0, 0xe8, 0x0, 0x0,
    0x0, 0x7, 0xf0, 0x0, 0xe9, 0x0, 0x0, 0x0,
    0xb, 0xe0, 0x0, 0x8f, 0x94, 0x22, 0x49, 0xef,
    0x60, 0x0, 0x7, 0xef, 0xff, 0xfd, 0x92, 0x0,
    0x0, 0x0, 0x2, 0x43, 0x10, 0x0, 0x0, 0x0,

    /* U+06B0 "ڰ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x84, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xaf, 0xa2, 0x0, 0x0,
    0x0, 0x17, 0xde, 0x71, 0x43, 0x0, 0x0, 0x0,
    0x6b, 0x41, 0x7e, 0xf4, 0x0, 0x0, 0x0, 0x3,
    0xaf, 0xff, 0x80, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xe2, 0xd0, 0x0, 0x0, 0x0, 0x9f, 0x45, 0xa2,
    0xe0, 0x0, 0x0, 0x0, 0x5f, 0x20, 0xbe, 0x50,
    0x0, 0x0, 0x0, 0x9, 0xd1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcc, 0x0, 0x0, 0x35, 0x0,
    0x0, 0x0, 0x1e, 0x90, 0x0, 0xd9, 0x0, 0x0,
    0x0, 0x7, 0xf0, 0x0, 0xe8, 0x0, 0x0, 0x0,
    0xb, 0xf0, 0x0, 0x9f, 0x94, 0x22, 0x49, 0xff,
    0x70, 0x0, 0x8, 0xef, 0xff, 0xfe, 0x93, 0x0,
    0x0, 0x0, 0x2, 0x43, 0x10, 0x0, 0x0, 0x0,

    /* U+06B1 "ڱ" */
    0x0, 0x0, 0x0, 0xf3, 0xf2, 0x2, 0x84, 0x0,
    0x0, 0x0, 0x20, 0x25, 0xbf, 0xa1, 0x0, 0x0,
    0x0, 0x17, 0xdd, 0x71, 0x53, 0x0, 0x0, 0x0,
    0x6b, 0x41, 0x8e, 0xf4, 0x0, 0x0, 0x0, 0x3,
    0xaf, 0xe8, 0x20, 0x0, 0x0, 0x0, 0x4f, 0xd6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x78, 0x0,
    0x0, 0x0, 0x1e, 0xc0, 0x0, 0xe8, 0x0, 0x0,
    0x0, 0x7, 0xf0, 0x0, 0xe9, 0x0, 0x0, 0x0,
    0x1b, 0xe0, 0x0, 0x8f, 0x94, 0x22, 0x49, 0xff,
    0x60, 0x0, 0x7, 0xef, 0xff, 0xfd, 0x92, 0x0,
    0x0, 0x0, 0x2, 0x43, 0x10, 0x0, 0x0, 0x0,

    /* U+06B2 "ڲ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x94, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xcf, 0x91, 0x0, 0x0,
    0x0, 0x18, 0xed, 0x61, 0x63, 0x0, 0x0, 0x0,
    0x6a, 0x32, 0x8e, 0xf4, 0x0, 0x0, 0x0, 0x3,
    0xbf, 0xe7, 0x10, 0x0, 0x0, 0x0, 0x4f, 0xc5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x8a, 0x0,
    0x0, 0x0, 0x1e, 0xc0, 0x0, 0xe8, 0x0, 0x0,
    0x0, 0x7, 0xf0, 0x0, 0xe9, 0x0, 0x0, 0x0,
    0xb, 0xe0, 0x0, 0x8f, 0x94, 0x22, 0x49, 0xef,
    0x60, 0x0, 0x7, 0xef, 0xff, 0xfd, 0x82, 0x0,
    0x0, 0x0, 0x2, 0x43, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xd5, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x20, 0x20, 0x0, 0x0, 0x0,

    /* U+06B3 "ڳ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x94, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xcf, 0x91, 0x0, 0x0,
    0x0, 0x18, 0xed, 0x61, 0x63, 0x0, 0x0, 0x0,
    0x6a, 0x32, 0x8e, 0xf4, 0x0, 0x0, 0x0, 0x3,
    0xbf, 0xe7, 0x10, 0x0, 0x0, 0x0, 0x4f, 0xc5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x8a, 0x0,
    0x0, 0x0, 0x1e, 0xc0, 0x0, 0xe8, 0x0, 0x0,
    0x0, 0x7, 0xf0, 0x0, 0xe9, 0x0, 0x0, 0x0,
    0xb, 0xe0, 0x0, 0x8f, 0x94, 0x22, 0x49, 0xef,
    0x60, 0x0, 0x7, 0xef, 0xff, 0xfd, 0x82, 0x0,
    0x0, 0x0, 0x2, 0x43, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5d, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5d, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x0, 0x0, 0x0, 0x0,

    /* U+06B4 "ڴ" */
    0x0, 0x0, 0x0, 0xf, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf3, 0xf2, 0x2, 0x84, 0x0, 0x0, 0x0,
    0x20, 0x25, 0xbf, 0xa1, 0x0, 0x0, 0x0, 0x17,
    0xdd, 0x71, 0x53, 0x0, 0x0, 0x0, 0x6b, 0x41,
    0x8e, 0xf4, 0x0, 0x0, 0x0, 0x3, 0xaf, 0xe8,
    0x20, 0x0, 0x0, 0x0, 0x4f, 0xd6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0x20, 0x0, 0x78, 0x0, 0x0, 0x0,
    0x1e, 0xc0, 0x0, 0xe8, 0x0, 0x0, 0x0, 0x7,
    0xf0, 0x0, 0xe9, 0x0, 0x0, 0x0, 0x1b, 0xe0,
    0x0, 0x8f, 0x94, 0x22, 0x49, 0xff, 0x60, 0x0,
    0x7, 0xef, 0xff, 0xfd, 0x92, 0x0, 0x0, 0x0,
    0x2, 0x43, 0x10, 0x0, 0x0, 0x0,

    /* U+06B5 "ڵ" */
    0x0, 0x0, 0x0, 0x34, 0x5, 0x30, 0x0, 0x0,
    0x1, 0xe5, 0xe0, 0x0, 0x0, 0x0, 0x6, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0x30, 0x0, 0x0, 0x0, 0x4, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0x30, 0x0, 0x0, 0x0, 0x4,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x4f, 0x30, 0x0,
    0x0, 0x0, 0x4, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0x30, 0x0, 0x0, 0x0, 0x4, 0xf2, 0x3,
    0x60, 0x0, 0x0, 0x5f, 0x20, 0xcb, 0x0, 0x0,
    0x8, 0xf0, 0xd, 0x90, 0x0, 0x2, 0xeb, 0x0,
    0x9f, 0x61, 0x37, 0xef, 0x20, 0x0, 0xaf, 0xff,
    0xfa, 0x20, 0x0, 0x0, 0x13, 0x30, 0x0, 0x0,
    0x0,

    /* U+06B6 "ڶ" */
    0x0, 0x0, 0x0, 0x2, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xf3, 0x0, 0x0, 0x0, 0x4,
    0xf3, 0x0, 0x0, 0x0, 0x4, 0xf3, 0x0, 0x0,
    0x0, 0x4, 0xf3, 0x0, 0x0, 0x0, 0x4, 0xf3,
    0x0, 0x0, 0x0, 0x4, 0xf3, 0x0, 0x0, 0x0,
    0x4, 0xf3, 0x0, 0x0, 0x0, 0x4, 0xf3, 0x0,
    0x0, 0x0, 0x4, 0xf2, 0x36, 0x0, 0x0, 0x5,
    0xf2, 0xcb, 0x0, 0x0, 0x8, 0xf0, 0xd9, 0x0,
    0x0, 0x2e, 0xb0, 0x9f, 0x61, 0x37, 0xef, 0x20,
    0xa, 0xff, 0xff, 0xa2, 0x0, 0x0, 0x13, 0x30,
    0x0, 0x0,

    /* U+06B7 "ڷ" */
    0x0, 0x0, 0x0, 0x3, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x20, 0x0, 0x0, 0x0, 0x2f, 0x3e, 0x0,
    0x0, 0x0, 0x2, 0x2, 0x0, 0x0, 0x0, 0x4,
    0xf3, 0x0, 0x0, 0x0, 0x4, 0xf3, 0x0, 0x0,
    0x0, 0x4, 0xf3, 0x0, 0x0, 0x0, 0x4, 0xf3,
    0x0, 0x0, 0x0, 0x4, 0xf3, 0x0, 0x0, 0x0,
    0x4, 0xf3, 0x0, 0x0, 0x0, 0x4, 0xf3, 0x0,
    0x0, 0x0, 0x4, 0xf3, 0x0, 0x0, 0x0, 0x4,
    0xf2, 0x36, 0x0, 0x0, 0x5, 0xf2, 0xcb, 0x0,
    0x0, 0x8, 0xf0, 0xd9, 0x0, 0x0, 0x2e, 0xb0,
    0x9f, 0x61, 0x37, 0xef, 0x20, 0xa, 0xff, 0xff,
    0xa2, 0x0, 0x0, 0x13, 0x30, 0x0, 0x0,

    /* U+06B8 "ڸ" */
    0x0, 0x0, 0x0, 0x4, 0xf3, 0x0, 0x0, 0x0,
    0x4, 0xf3, 0x0, 0x0, 0x0, 0x4, 0xf3, 0x0,
    0x0, 0x0, 0x4, 0xf3, 0x0, 0x0, 0x0, 0x4,
    0xf3, 0x0, 0x0, 0x0, 0x4, 0xf3, 0x0, 0x0,
    0x0, 0x4, 0xf3, 0x0, 0x0, 0x0, 0x4, 0xf3,
    0x0, 0x0, 0x0, 0x4, 0xf2, 0x36, 0x0, 0x0,
    0x5, 0xf2, 0xcb, 0x0, 0x0, 0x8, 0xf0, 0xd9,
    0x0, 0x0, 0x2e, 0xb0, 0x9f, 0x61, 0x37, 0xef,
    0x20, 0xa, 0xff, 0xff, 0xa2, 0x0, 0x0, 0x13,
    0x30, 0x0, 0x0, 0x0, 0xb, 0x7b, 0x60, 0x0,
    0x0, 0x1, 0x12, 0x10, 0x0, 0x0, 0x0, 0xb7,
    0x0, 0x0, 0x0, 0x0, 0x11, 0x0, 0x0,

    /* U+06B9 "ڹ" */
    0x0, 0x7, 0xa0, 0x0, 0x0, 0x0, 0x1, 0x10,
    0x4, 0x60, 0x0, 0x0, 0x0, 0x4, 0xf3, 0x58,
    0x0, 0x0, 0x0, 0xf6, 0xcb, 0x0, 0x0, 0x0,
    0xe8, 0xd9, 0x0, 0x0, 0x1, 0xf6, 0xbc, 0x0,
    0x0, 0x9, 0xf1, 0x5f, 0x94, 0x23, 0x9f, 0x80,
    0x6, 0xef, 0xff, 0xe7, 0x0, 0x0, 0x3, 0x42,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8a, 0x0, 0x0, 0x0, 0x0, 0x11, 0x0,
    0x0,

    /* U+06BA "ں" */
    0x0, 0x0, 0x0, 0x1, 0x20, 0x0, 0x0, 0x0,
    0x7, 0xf1, 0x12, 0x0, 0x0, 0x1, 0xf5, 0xbc,
    0x0, 0x0, 0x0, 0xf7, 0xda, 0x0, 0x0, 0x0,
    0xf8, 0xd9, 0x0, 0x0, 0x2, 0xf5, 0xac, 0x0,
    0x0, 0xa, 0xf0, 0x4f, 0xa4, 0x24, 0xaf, 0x60,
    0x5, 0xef, 0xff, 0xe6, 0x0, 0x0, 0x3, 0x42,
    0x0, 0x0,

    /* U+06BB "ڻ" */
    0x0, 0x8, 0x0, 0x0, 0x0, 0x0, 0xc, 0x0,
    0x0, 0x0, 0x0, 0xc, 0x38, 0x20, 0x0, 0x0,
    0xd, 0xb8, 0x80, 0x0, 0x0, 0x5d, 0xcc, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xa0, 0x0, 0x0,
    0x0, 0x3, 0xf4, 0x7a, 0x0, 0x0, 0x0, 0xf7,
    0xca, 0x0, 0x0, 0x0, 0xe8, 0xd9, 0x0, 0x0,
    0x1, 0xf6, 0xbc, 0x0, 0x0, 0x9, 0xf1, 0x5f,
    0xa4, 0x24, 0xaf, 0x80, 0x6, 0xef, 0xff, 0xe6,
    0x0, 0x0, 0x3, 0x42, 0x0, 0x0,

    /* U+06BC "ڼ" */
    0x0, 0x7, 0xa0, 0x0, 0x0, 0x0, 0x1, 0x10,
    0x4, 0x50, 0x0, 0x0, 0x0, 0x5, 0xf2, 0x23,
    0x0, 0x0, 0x1, 0xf6, 0xbc, 0x0, 0x0, 0x0,
    0xf8, 0xda, 0x0, 0x0, 0x0, 0xf8, 0xd9, 0x0,
    0x0, 0x3, 0xf5, 0xac, 0x0, 0x0, 0xa, 0xe0,
    0x4f, 0xa4, 0x24, 0xaf, 0x60, 0x5, 0xef, 0xff,
    0xe5, 0x0, 0x0, 0xa, 0xfd, 0x70, 0x0, 0x0,
    0xa, 0x66, 0xa0, 0x0, 0x0, 0x3, 0xdd, 0x30,
    0x0,

    /* U+06BD "ڽ" */
    0x0, 0x1, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x20,
    0x0, 0x0, 0x0, 0x1f, 0x3f, 0x0, 0x0, 0x0,
    0x2, 0x2, 0x4, 0x50, 0x0, 0x0, 0x0, 0x5,
    0xf2, 0x23, 0x0, 0x0, 0x1, 0xf6, 0xbc, 0x0,
    0x0, 0x0, 0xf8, 0xda, 0x0, 0x0, 0x0, 0xf8,
    0xc9, 0x0, 0x0, 0x3, 0xf5, 0xad, 0x0, 0x0,
    0xa, 0xe0, 0x3f, 0xa4, 0x24, 0xaf, 0x60, 0x5,
    0xef, 0xff, 0xe5, 0x0, 0x0, 0x3, 0x42, 0x0,
    0x0,

    /* U+06BE "ھ" */
    0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0xd, 0xc3,
    0x0, 0x0, 0x0, 0x6, 0xff, 0x70, 0x0, 0x0,
    0xe, 0xbc, 0xf9, 0x0, 0x0, 0x2f, 0x34, 0xff,
    0x60, 0x0, 0xf, 0x57, 0xe7, 0xe0, 0xda, 0xb,
    0xde, 0x92, 0xf2, 0xce, 0x8b, 0xff, 0x99, 0xf1,
    0x2a, 0xfe, 0x8a, 0xee, 0x70,

    /* U+06BF "ڿ" */
    0x0, 0x7, 0xa0, 0x0, 0x0, 0x0, 0x1, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9e,
    0xed, 0xff, 0xe9, 0x0, 0x41, 0x3d, 0xe8, 0x41,
    0x0, 0x3, 0xfa, 0x0, 0x0, 0x0, 0xc, 0xc0,
    0x0, 0x0, 0x0, 0x3f, 0x30, 0xd5, 0xd4, 0x0,
    0x6f, 0x0, 0x20, 0x20, 0x0, 0x5f, 0x10, 0xd,
    0x50, 0x0, 0x1f, 0x80, 0x2, 0x0, 0x0, 0x8,
    0xf9, 0x31, 0x14, 0x94, 0x0, 0x6d, 0xff, 0xff,
    0xc2, 0x0, 0x0, 0x13, 0x31, 0x0,

    /* U+06C6 "ۆ" */
    0x0, 0xd, 0x36, 0xb0, 0x0, 0x4, 0xde, 0x20,
    0x0, 0x0, 0x65, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xed, 0x40, 0x0, 0x6f, 0xac, 0xf1,
    0x0, 0x9e, 0x1, 0xf6, 0x0, 0x6f, 0xa6, 0xf7,
    0x0, 0x8, 0xdf, 0xf7, 0x0, 0x0, 0x4, 0xf4,
    0x0, 0x0, 0x2e, 0xe0, 0x12, 0x48, 0xef, 0x30,
    0xaf, 0xff, 0xa2, 0x0, 0x34, 0x20, 0x0, 0x0,

    /* U+06C7 "ۇ" */
    0x0, 0x2, 0xda, 0x0, 0x0, 0x6, 0x9d, 0x10,
    0x0, 0x1, 0xcf, 0x70, 0x0, 0x5, 0xc2, 0x0,
    0x0, 0xd8, 0x10, 0x0, 0x0, 0x8, 0xed, 0x40,
    0x0, 0x6f, 0xac, 0xf1, 0x0, 0x9e, 0x1, 0xf6,
    0x0, 0x6f, 0xa6, 0xf7, 0x0, 0x8, 0xdf, 0xf7,
    0x0, 0x0, 0x4, 0xf4, 0x0, 0x0, 0x2e, 0xe0,
    0x12, 0x48, 0xef, 0x30, 0xaf, 0xff, 0xa2, 0x0,
    0x34, 0x20, 0x0, 0x0,

    /* U+06C8 "ۈ" */
    0x0, 0x0, 0x52, 0x0, 0x0, 0x0, 0x94, 0x0,
    0x0, 0x0, 0x94, 0x0, 0x0, 0x0, 0x94, 0x0,
    0x0, 0x0, 0x94, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xed, 0x40, 0x0, 0x6f, 0xac, 0xf1,
    0x0, 0x9e, 0x1, 0xf6, 0x0, 0x6f, 0xa6, 0xf7,
    0x0, 0x8, 0xdf, 0xf7, 0x0, 0x0, 0x4, 0xf4,
    0x0, 0x0, 0x2e, 0xe0, 0x12, 0x48, 0xef, 0x30,
    0xaf, 0xff, 0xa2, 0x0, 0x34, 0x20, 0x0, 0x0,

    /* U+06CB "ۋ" */
    0x0, 0x0, 0xa7, 0x0, 0x0, 0x0, 0x11, 0x0,
    0x0, 0xa, 0x8b, 0x70, 0x0, 0x1, 0x11, 0x10,
    0x0, 0x8, 0xed, 0x40, 0x0, 0x6f, 0xac, 0xf1,
    0x0, 0x9e, 0x1, 0xf6, 0x0, 0x6f, 0xa6, 0xf7,
    0x0, 0x8, 0xdf, 0xf7, 0x0, 0x0, 0x4, 0xf4,
    0x0, 0x0, 0x2e, 0xe0, 0x12, 0x48, 0xef, 0x30,
    0xaf, 0xff, 0xa2, 0x0, 0x34, 0x20, 0x0, 0x0,

    /* U+06CC "ی" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6d, 0xfe, 0x70, 0x0, 0x0, 0x4f, 0x94, 0x7f,
    0x40, 0x0, 0x5, 0xf7, 0x0, 0x10, 0x12, 0x0,
    0x9, 0xfe, 0x80, 0xc, 0xa0, 0x0, 0x2, 0x7e,
    0xb0, 0xf7, 0x0, 0x0, 0x0, 0xaf, 0xd, 0xc0,
    0x0, 0x2, 0x9f, 0x90, 0x4f, 0xfc, 0xcf, 0xff,
    0x90, 0x0, 0x28, 0xba, 0x85, 0x10, 0x0,

    /* U+06CE "ێ" */
    0xc, 0x47, 0xa0, 0x0, 0x0, 0x0, 0x2e, 0xe1,
    0x0, 0x0, 0x0, 0x0, 0x21, 0x6, 0xdf, 0xe7,
    0x0, 0x0, 0x4, 0xf9, 0x48, 0xf4, 0x0, 0x0,
    0x5f, 0x60, 0x2, 0x10, 0x10, 0x0, 0xbf, 0xe7,
    0x0, 0xbb, 0x0, 0x0, 0x39, 0xfa, 0xf, 0x70,
    0x0, 0x0, 0xa, 0xf0, 0xeb, 0x0, 0x0, 0x17,
    0xfb, 0x6, 0xfd, 0xab, 0xdf, 0xfc, 0x10, 0x5,
    0xdf, 0xfd, 0x94, 0x0, 0x0,

    /* U+06D0 "ې" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7d, 0xfe, 0x70, 0x0, 0x0, 0x4f, 0x84, 0x7f,
    0x40, 0x0, 0x4, 0xf8, 0x10, 0x10, 0x24, 0x0,
    0x7, 0xff, 0xa2, 0xd, 0xa0, 0x0, 0x0, 0x5d,
    0xc0, 0xf7, 0x0, 0x0, 0x0, 0xbe, 0xb, 0xd2,
    0x0, 0x14, 0xbf, 0x60, 0x1b, 0xfe, 0xff, 0xea,
    0x30, 0x0, 0x1, 0x43, 0x10, 0x0, 0x0, 0x0,
    0x0, 0xe3, 0x0, 0x0, 0x0, 0x0, 0x2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe4, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x0, 0x0, 0x0,

    /* U+06D5 "ە" */
    0x0, 0x10, 0x0, 0x2, 0xef, 0xd5, 0x0, 0xae,
    0x5a, 0xf7, 0xd, 0x90, 0x7, 0xf1, 0xe8, 0x0,
    0x3f, 0x3b, 0xe6, 0x6d, 0xe0, 0x2b, 0xfe, 0x91,
    0x0,

    /* U+06F0 "۰" */
    0x8f, 0x29, 0xf2,

    /* U+06F1 "۱" */
    0xae, 0x0, 0x4f, 0x30, 0xe, 0x90, 0x9, 0xd0,
    0x5, 0xf1, 0x2, 0xf4, 0x0, 0xf6, 0x0, 0xf6,
    0x0, 0xf7, 0x0, 0xf7,

    /* U+06F2 "۲" */
    0x2f, 0x70, 0x0, 0xac, 0xc, 0xf7, 0x26, 0xf7,
    0x6, 0xff, 0xff, 0xc0, 0x2, 0xf7, 0x33, 0x0,
    0x0, 0xea, 0x0, 0x0, 0x0, 0xbc, 0x0, 0x0,
    0x0, 0x9d, 0x0, 0x0, 0x0, 0x8e, 0x0, 0x0,
    0x0, 0x7e, 0x0, 0x0, 0x0, 0x7e, 0x0, 0x0,

    /* U+06F3 "۳" */
    0x3f, 0x40, 0xf6, 0x4f, 0x20, 0xda, 0xf, 0x97,
    0xf1, 0x7, 0xfd, 0xff, 0xfc, 0x0, 0x2f, 0x91,
    0x33, 0x0, 0x0, 0xe9, 0x0, 0x0, 0x0, 0xb,
    0xb0, 0x0, 0x0, 0x0, 0xac, 0x0, 0x0, 0x0,
    0x9, 0xd0, 0x0, 0x0, 0x0, 0x8d, 0x0, 0x0,
    0x0, 0x8, 0xe0, 0x0, 0x0,

    /* U+06F4 "۴" */
    0x2f, 0x60, 0x9e, 0xd1, 0xc, 0xc6, 0xf5, 0x40,
    0x6, 0xff, 0xf4, 0x22, 0x2, 0xfe, 0xff, 0xf8,
    0x0, 0xe7, 0x24, 0x41, 0x0, 0xbb, 0x0, 0x0,
    0x0, 0x9d, 0x0, 0x0, 0x0, 0x8e, 0x0, 0x0,
    0x0, 0x7e, 0x0, 0x0, 0x0, 0x7e, 0x0, 0x0,

    /* U+06F5 "۵" */
    0x0, 0x4, 0x81, 0x0, 0x0, 0x5f, 0xfd, 0x0,
    0x0, 0xe9, 0x2e, 0x80, 0x7, 0xf1, 0x7, 0xf1,
    0xc, 0xb0, 0x1, 0xf6, 0xf, 0x60, 0x0, 0xd9,
    0x1f, 0x40, 0x0, 0xbb, 0x2f, 0x40, 0x0, 0xab,
    0xf, 0x67, 0xd1, 0xca, 0xc, 0xde, 0xfc, 0xf6,
    0x4, 0xed, 0x6f, 0xc0,

    /* U+06F6 "۶" */
    0x0, 0x0, 0x0, 0x0, 0x7, 0xef, 0x80, 0x5,
    0xf7, 0x45, 0x0, 0xac, 0x0, 0x0, 0x9, 0xe1,
    0x0, 0x0, 0x2e, 0xfe, 0xf2, 0x0, 0x2f, 0xe7,
    0x0, 0xc, 0xd1, 0x0, 0x6, 0xf2, 0x0, 0x0,
    0xd9, 0x0, 0x0, 0x3f, 0x30, 0x0, 0x0,

    /* U+06F7 "۷" */
    0x4f, 0x30, 0x0, 0xad, 0x0, 0xda, 0x0, 0x1f,
    0x60, 0x6, 0xf1, 0x7, 0xe0, 0x0, 0x1f, 0x70,
    0xd9, 0x0, 0x0, 0xbc, 0x2f, 0x40, 0x0, 0x5,
    0xf9, 0xf0, 0x0, 0x0, 0x1f, 0xfb, 0x0, 0x0,
    0x0, 0xdf, 0x70, 0x0, 0x0, 0x9, 0xf4, 0x0,
    0x0, 0x0, 0x7f, 0x10, 0x0,

    /* U+06F8 "۸" */
    0x0, 0x7, 0xf1, 0x0, 0x0, 0x0, 0x9f, 0x40,
    0x0, 0x0, 0xd, 0xf7, 0x0, 0x0, 0x1, 0xff,
    0xb0, 0x0, 0x0, 0x5f, 0x9f, 0x0, 0x0, 0xb,
    0xc2, 0xf4, 0x0, 0x1, 0xf6, 0xd, 0x90, 0x0,
    0x6f, 0x10, 0x7e, 0x0, 0xd, 0xa0, 0x1, 0xf6,
    0x4, 0xf3, 0x0, 0xa, 0xd0,

    /* U+06F9 "۹" */
    0x0, 0x0, 0x0, 0x0, 0x2, 0xcf, 0xf7, 0x0,
    0xd, 0xc5, 0xaf, 0x40, 0x2f, 0x40, 0xd, 0x90,
    0xe, 0xb4, 0x2b, 0xc0, 0x3, 0xdf, 0xff, 0xd0,
    0x0, 0x2, 0x49, 0xf0, 0x0, 0x0, 0x5, 0xf2,
    0x0, 0x0, 0x2, 0xf5, 0x0, 0x0, 0x0, 0xf8,
    0x0, 0x0, 0x0, 0xbc,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x49, 0xdc,
    0x0, 0x0, 0x0, 0x0, 0x16, 0xbf, 0xff, 0xff,
    0x0, 0x0, 0x3, 0x8d, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xc7, 0xff,
    0x0, 0x0, 0xff, 0xff, 0xea, 0x51, 0x0, 0xff,
    0x0, 0x0, 0xff, 0x83, 0x0, 0x0, 0x0, 0xff,
    0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x0, 0x0, 0xff, 0x0, 0x0, 0x2b, 0xff, 0xff,
    0x0, 0x0, 0xff, 0x0, 0x0, 0xdf, 0xff, 0xff,
    0x2b, 0xff, 0xff, 0x0, 0x0, 0xdf, 0xff, 0xfd,
    0xdf, 0xff, 0xff, 0x0, 0x0, 0x2b, 0xff, 0xb2,
    0xdf, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2b, 0xff, 0xb2, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F008 "" */
    0xd0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xd,
    0xff, 0xff, 0xc8, 0x88, 0x88, 0x8c, 0xff, 0xff,
    0xf0, 0xf, 0x80, 0x0, 0x0, 0x8, 0xf0, 0xf,
    0xf0, 0xf, 0x80, 0x0, 0x0, 0x8, 0xf0, 0xf,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xf0, 0xf, 0xec, 0xcc, 0xcc, 0xce, 0xf0, 0xf,
    0xf0, 0xf, 0xec, 0xcc, 0xcc, 0xce, 0xf0, 0xf,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xf0, 0xf, 0x80, 0x0, 0x0, 0x8, 0xf0, 0xf,
    0xf0, 0xf, 0x80, 0x0, 0x0, 0x8, 0xf0, 0xf,
    0xff, 0xff, 0xc8, 0x88, 0x88, 0x8c, 0xff, 0xff,
    0xd0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xd,

    /* U+F00B "" */
    0xdf, 0xff, 0x73, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0xff, 0xff, 0xa5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xdf, 0xff, 0x73, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0x73, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0xff, 0xff, 0xa5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xdf, 0xff, 0x73, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0x73, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0xff, 0xff, 0xa5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xdf, 0xff, 0x73, 0xff, 0xff, 0xff, 0xff, 0xfd,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xb1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xc0,
    0x1b, 0xa0, 0x0, 0x0, 0xb, 0xff, 0xfc, 0x0,
    0xcf, 0xfb, 0x0, 0x0, 0xbf, 0xff, 0xc0, 0x0,
    0xbf, 0xff, 0xb0, 0xb, 0xff, 0xfc, 0x0, 0x0,
    0xc, 0xff, 0xfb, 0xbf, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xb0, 0x0, 0x0, 0x0, 0x0,

    /* U+F00D "" */
    0x3, 0x0, 0x0, 0x0, 0x3, 0x8, 0xfc, 0x10,
    0x0, 0x1c, 0xf8, 0xff, 0xfc, 0x10, 0x1c, 0xff,
    0xf5, 0xff, 0xfc, 0x2c, 0xff, 0xf5, 0x5, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x5, 0xff, 0xff, 0xf5,
    0x0, 0x0, 0x1d, 0xff, 0xfd, 0x10, 0x0, 0x1c,
    0xff, 0xff, 0xfc, 0x10, 0x1c, 0xff, 0xf9, 0xff,
    0xfc, 0x1c, 0xff, 0xf5, 0x5, 0xff, 0xfc, 0xdf,
    0xf5, 0x0, 0x5, 0xff, 0xd1, 0xa4, 0x0, 0x0,
    0x4, 0xa1,

    /* U+F011 "" */
    0x0, 0x0, 0x0, 0x4f, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x10, 0x6f, 0xf1, 0x3, 0x10, 0x0,
    0x0, 0x5f, 0xd0, 0x6f, 0xf1, 0x3f, 0xd1, 0x0,
    0x3, 0xff, 0xf1, 0x6f, 0xf1, 0x5f, 0xfd, 0x0,
    0xd, 0xff, 0x40, 0x6f, 0xf1, 0x9, 0xff, 0x70,
    0x4f, 0xf7, 0x0, 0x6f, 0xf1, 0x0, 0xcf, 0xe0,
    0x9f, 0xf0, 0x0, 0x6f, 0xf1, 0x0, 0x5f, 0xf3,
    0xbf, 0xc0, 0x0, 0x6f, 0xf1, 0x0, 0x2f, 0xf5,
    0xbf, 0xc0, 0x0, 0x4f, 0xe0, 0x0, 0x1f, 0xf6,
    0xaf, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf4,
    0x6f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf0,
    0xf, 0xfe, 0x10, 0x0, 0x0, 0x5, 0xff, 0xa0,
    0x6, 0xff, 0xd3, 0x0, 0x0, 0x7f, 0xff, 0x20,
    0x0, 0x9f, 0xff, 0xda, 0xbe, 0xff, 0xf4, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xff, 0xfd, 0x30, 0x0,
    0x0, 0x0, 0x17, 0xbd, 0xca, 0x50, 0x0, 0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x0, 0x8b, 0xb8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x30, 0x6, 0xff, 0xff, 0x60, 0x3, 0x0,
    0x4, 0xfd, 0xdf, 0xff, 0xff, 0xfd, 0xef, 0x40,
    0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0x4f, 0xff, 0xff, 0xf9, 0x9f, 0xff, 0xff, 0xf4,
    0x8, 0xff, 0xff, 0x20, 0x2, 0xff, 0xff, 0x80,
    0x0, 0xff, 0xf9, 0x0, 0x0, 0x9f, 0xff, 0x0,
    0x0, 0xff, 0xf9, 0x0, 0x0, 0x9f, 0xff, 0x0,
    0x8, 0xff, 0xff, 0x20, 0x2, 0xff, 0xff, 0x80,
    0x4f, 0xff, 0xff, 0xf9, 0x9f, 0xff, 0xff, 0xf4,
    0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0x4, 0xfe, 0xdf, 0xff, 0xff, 0xfd, 0xdf, 0x40,
    0x0, 0x30, 0x6, 0xff, 0xff, 0x60, 0x3, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8b, 0xb8, 0x0, 0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x3, 0xdd, 0x30, 0x3f, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xf5, 0x4f,
    0xf4, 0x0, 0x0, 0x0, 0x9, 0xff, 0x99, 0xff,
    0xbf, 0xf4, 0x0, 0x0, 0x1, 0xbf, 0xf6, 0x22,
    0x6f, 0xff, 0xf4, 0x0, 0x0, 0x2d, 0xfe, 0x35,
    0xff, 0x53, 0xef, 0xf4, 0x0, 0x4, 0xff, 0xc1,
    0x8f, 0xff, 0xf8, 0x2d, 0xfe, 0x40, 0x7f, 0xfa,
    0x1a, 0xff, 0xff, 0xff, 0xa1, 0xaf, 0xf7, 0xcf,
    0x82, 0xdf, 0xff, 0xff, 0xff, 0xfd, 0x28, 0xfc,
    0x14, 0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0,
    0x41, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0xf, 0xff, 0xf9, 0x0, 0x8f,
    0xff, 0xf0, 0x0, 0x0, 0xf, 0xff, 0xf8, 0x0,
    0x8f, 0xff, 0xf0, 0x0, 0x0, 0xf, 0xff, 0xf8,
    0x0, 0x8f, 0xff, 0xf0, 0x0, 0x0, 0xe, 0xff,
    0xf6, 0x0, 0x6f, 0xff, 0xe0, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x0, 0xdf, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xfb, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xfc, 0x1b, 0xb1, 0xcf, 0xff, 0xfd,
    0xff, 0xff, 0xff, 0xc2, 0x2c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xe0, 0xff,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,

    /* U+F01C "" */
    0x0, 0x4, 0xef, 0xff, 0xff, 0xff, 0xfe, 0x40,
    0x0, 0x0, 0x1e, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe1, 0x0, 0x0, 0xaf, 0xb0, 0x0, 0x0, 0x0,
    0xb, 0xfa, 0x0, 0x5, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x1, 0xff, 0x50, 0x1e, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xe1, 0xaf, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xfa, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf1, 0x0, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8,

    /* U+F021 "" */
    0x0, 0x0, 0x6, 0xbd, 0xda, 0x50, 0x2, 0xff,
    0x0, 0x5, 0xef, 0xff, 0xff, 0xfe, 0x42, 0xff,
    0x0, 0x7f, 0xff, 0xa7, 0x7b, 0xff, 0xf9, 0xff,
    0x5, 0xff, 0xc1, 0x0, 0x0, 0x2c, 0xff, 0xff,
    0xe, 0xfc, 0x0, 0x0, 0x2, 0x22, 0xdf, 0xff,
    0x5f, 0xf2, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0x8f, 0xb0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0xb, 0xf8,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x2f, 0xf4,
    0xff, 0xfd, 0x22, 0x20, 0x0, 0x0, 0xcf, 0xe0,
    0xff, 0xff, 0xc2, 0x0, 0x0, 0x2c, 0xff, 0x40,
    0xff, 0x9f, 0xff, 0xb7, 0x6a, 0xff, 0xf7, 0x0,
    0xff, 0x24, 0xdf, 0xff, 0xff, 0xfe, 0x50, 0x0,
    0xff, 0x20, 0x5, 0xac, 0xdb, 0x60, 0x0, 0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8d,
    0x0, 0x0, 0x8, 0xff, 0x0, 0x0, 0x8f, 0xff,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xdf, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x8f, 0xff, 0x0, 0x0, 0x8, 0xff,
    0x0, 0x0, 0x0, 0x8d, 0x0, 0x0, 0x0, 0x0,

    /* U+F027 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8d, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0x0, 0x0,
    0xcf, 0xff, 0xff, 0xff, 0x1, 0x50, 0xff, 0xff,
    0xff, 0xff, 0x6, 0xf7, 0xff, 0xff, 0xff, 0xff,
    0x0, 0xbe, 0xff, 0xff, 0xff, 0xff, 0x0, 0xae,
    0xff, 0xff, 0xff, 0xff, 0x5, 0xf8, 0xdf, 0xff,
    0xff, 0xff, 0x2, 0x60, 0x0, 0x0, 0x9f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9e, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xd2, 0x0, 0x0, 0x0, 0x0, 0x8d, 0x0, 0x0,
    0x3, 0xee, 0x10, 0x0, 0x0, 0x8, 0xff, 0x0,
    0xa, 0xb1, 0x2f, 0xb0, 0x0, 0x0, 0x8f, 0xff,
    0x0, 0x5, 0xfc, 0x7, 0xf4, 0xdf, 0xff, 0xff,
    0xff, 0x2, 0x50, 0x5f, 0x60, 0xf9, 0xff, 0xff,
    0xff, 0xff, 0x6, 0xf7, 0xd, 0xc0, 0xbd, 0xff,
    0xff, 0xff, 0xff, 0x0, 0xae, 0x9, 0xf0, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0x0, 0xae, 0x9, 0xf0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0x6, 0xf7, 0xd,
    0xc0, 0xad, 0xdf, 0xff, 0xff, 0xff, 0x2, 0x50,
    0x5f, 0x60, 0xe9, 0x0, 0x0, 0x8f, 0xff, 0x0,
    0x5, 0xfc, 0x6, 0xf4, 0x0, 0x0, 0x8, 0xff,
    0x0, 0xa, 0xb1, 0x2f, 0xb0, 0x0, 0x0, 0x0,
    0x8d, 0x0, 0x0, 0x2, 0xee, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xd2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0x10, 0x0,

    /* U+F03E "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x20, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x0, 0xc, 0xff, 0xff, 0xee, 0xff, 0xff,
    0xff, 0x20, 0x2f, 0xff, 0xfe, 0x22, 0xef, 0xff,
    0xff, 0xfc, 0xff, 0xff, 0xe2, 0x0, 0x2e, 0xff,
    0xff, 0xfe, 0x4e, 0xfe, 0x20, 0x0, 0x2, 0xff,
    0xff, 0xe2, 0x2, 0xc2, 0x0, 0x0, 0x0, 0xff,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,

    /* U+F043 "" */
    0x0, 0x0, 0x4e, 0x40, 0x0, 0x0, 0x0, 0xb,
    0xfb, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0x90, 0x0, 0x0, 0x2f,
    0xff, 0xff, 0x30, 0x0, 0xc, 0xff, 0xff, 0xfc,
    0x0, 0x7, 0xff, 0xff, 0xff, 0xf8, 0x2, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0x9e, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff,
    0x2f, 0xff, 0xff, 0xff, 0xfe, 0xf2, 0xbf, 0xff,
    0xff, 0xfe, 0x9f, 0xa1, 0xbf, 0xff, 0xff, 0x92,
    0xff, 0xa2, 0x2f, 0xff, 0xf2, 0x4, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0x2, 0x9e, 0xfe, 0x92, 0x0,

    /* U+F048 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x30, 0x0,
    0x1, 0xcc, 0xff, 0x40, 0x0, 0x2d, 0xff, 0xff,
    0x40, 0x3, 0xef, 0xff, 0xff, 0x40, 0x3f, 0xff,
    0xff, 0xff, 0x44, 0xff, 0xff, 0xff, 0xff, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0x45, 0xff, 0xff, 0xff, 0xff,
    0x40, 0x4f, 0xff, 0xff, 0xff, 0x40, 0x3, 0xef,
    0xff, 0xff, 0x40, 0x0, 0x2e, 0xff, 0xff, 0x30,
    0x0, 0x1, 0xcc, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04B "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0x91, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xfd,
    0x40, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xfa,
    0x10, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd5,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb2,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb2, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd5, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0xff, 0xff, 0xff, 0xfa, 0x10,
    0x0, 0x0, 0xff, 0xff, 0xfd, 0x40, 0x0, 0x0,
    0x0, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x8e, 0xa1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0xf8, 0x0, 0x8f, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0xff,
    0xff, 0xff, 0x7f, 0xff, 0xf7, 0x0, 0x7f, 0xff,
    0xf7,

    /* U+F04D "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8,

    /* U+F051 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcc, 0x10, 0x0,
    0x3, 0xff, 0xff, 0xd2, 0x0, 0x4, 0xff, 0xff,
    0xfe, 0x30, 0x4, 0xff, 0xff, 0xff, 0xf4, 0x4,
    0xff, 0xff, 0xff, 0xff, 0x54, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0xff, 0xff, 0xff, 0xff, 0x44, 0xff, 0xff,
    0xff, 0xf3, 0x4, 0xff, 0xff, 0xfe, 0x30, 0x4,
    0xff, 0xff, 0xd2, 0x0, 0x4, 0xff, 0xcc, 0x10,
    0x0, 0x3, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F052 "" */
    0x0, 0x0, 0x0, 0x2d, 0xd2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xef, 0xfe, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x1d, 0xff, 0xff, 0xd1, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,

    /* U+F053 "" */
    0x0, 0x0, 0x0, 0x1a, 0x40, 0x0, 0x0, 0x1,
    0xdf, 0xf0, 0x0, 0x0, 0x1d, 0xff, 0xa0, 0x0,
    0x1, 0xdf, 0xfa, 0x0, 0x0, 0x1d, 0xff, 0xa0,
    0x0, 0x1, 0xdf, 0xfa, 0x0, 0x0, 0xc, 0xff,
    0xa0, 0x0, 0x0, 0xd, 0xff, 0x80, 0x0, 0x0,
    0x1, 0xdf, 0xf8, 0x0, 0x0, 0x0, 0x1d, 0xff,
    0x80, 0x0, 0x0, 0x1, 0xdf, 0xf8, 0x0, 0x0,
    0x0, 0x1d, 0xff, 0x80, 0x0, 0x0, 0x1, 0xdf,
    0xf0, 0x0, 0x0, 0x0, 0x1b, 0x50,

    /* U+F054 "" */
    0x4, 0xa1, 0x0, 0x0, 0x0, 0xf, 0xfd, 0x10,
    0x0, 0x0, 0xa, 0xff, 0xd1, 0x0, 0x0, 0x0,
    0xaf, 0xfd, 0x10, 0x0, 0x0, 0xa, 0xff, 0xd1,
    0x0, 0x0, 0x0, 0xaf, 0xfd, 0x10, 0x0, 0x0,
    0xa, 0xff, 0xc0, 0x0, 0x0, 0x8, 0xff, 0xd0,
    0x0, 0x0, 0x8f, 0xfd, 0x10, 0x0, 0x8, 0xff,
    0xd1, 0x0, 0x0, 0x8f, 0xfd, 0x10, 0x0, 0x8,
    0xff, 0xd1, 0x0, 0x0, 0xf, 0xfd, 0x10, 0x0,
    0x0, 0x5, 0xb1, 0x0, 0x0, 0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x4, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0x80, 0x0, 0x0, 0x48, 0x88, 0x8c, 0xff, 0xc8,
    0x88, 0x84, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x48, 0x88, 0x8c, 0xff, 0xc8, 0x88, 0x84, 0x0,
    0x0, 0x8, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x40,
    0x0, 0x0,

    /* U+F068 "" */
    0x14, 0x44, 0x44, 0x44, 0x44, 0x44, 0x41, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x7b, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xb7,

    /* U+F06E "" */
    0x0, 0x0, 0x5, 0xad, 0xff, 0xda, 0x50, 0x0,
    0x0, 0x0, 0x4, 0xdf, 0xfc, 0x88, 0xcf, 0xfd,
    0x40, 0x0, 0x0, 0x7f, 0xfe, 0x40, 0x0, 0x4,
    0xef, 0xf7, 0x0, 0x7, 0xff, 0xf4, 0x0, 0x9e,
    0x80, 0x4f, 0xff, 0x70, 0x4f, 0xff, 0xc0, 0x0,
    0xaf, 0xf8, 0xc, 0xff, 0xf4, 0xdf, 0xff, 0x80,
    0x9a, 0xff, 0xfe, 0x8, 0xff, 0xfd, 0xdf, 0xff,
    0x80, 0xef, 0xff, 0xfe, 0x8, 0xff, 0xfd, 0x4f,
    0xff, 0xc0, 0x8f, 0xff, 0xf8, 0xc, 0xff, 0xf4,
    0x7, 0xff, 0xf4, 0x8, 0xee, 0x80, 0x4f, 0xff,
    0x70, 0x0, 0x7f, 0xfe, 0x40, 0x0, 0x4, 0xef,
    0xf8, 0x0, 0x0, 0x4, 0xdf, 0xfc, 0x88, 0xcf,
    0xfd, 0x40, 0x0, 0x0, 0x0, 0x5, 0xad, 0xff,
    0xda, 0x50, 0x0, 0x0,

    /* U+F070 "" */
    0x8c, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xe4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1b, 0xff, 0x80, 0x49,
    0xdf, 0xfd, 0xa5, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0xff, 0xd8, 0x8c, 0xff, 0xd4, 0x0, 0x0,
    0x0, 0x4, 0xef, 0xf8, 0x0, 0x0, 0x4e, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x1c, 0xff, 0x69, 0xe8,
    0x4, 0xff, 0xf7, 0x0, 0x4, 0xe3, 0x0, 0x9f,
    0xfe, 0xff, 0x80, 0xcf, 0xff, 0x40, 0xd, 0xff,
    0x70, 0x5, 0xff, 0xff, 0xe0, 0x8f, 0xff, 0xd0,
    0xd, 0xff, 0xf7, 0x0, 0x2d, 0xff, 0xe0, 0x8f,
    0xff, 0xd0, 0x4, 0xff, 0xfc, 0x0, 0x0, 0xaf,
    0xf8, 0xcf, 0xff, 0x30, 0x0, 0x7f, 0xff, 0x40,
    0x0, 0x6, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x8,
    0xff, 0xf4, 0x0, 0x0, 0x3e, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x4d, 0xff, 0xc8, 0x82, 0x1, 0xbf,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x5a, 0xdf, 0xfc,
    0x10, 0x8, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4e, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xc8,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x0, 0x2d, 0xd2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xd8, 0x8d,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xa0, 0xa, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xb0, 0xb, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xc0, 0xc, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xd0, 0xd,
    0xff, 0xff, 0x50, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xf9, 0x9f, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x8f,
    0xff, 0xff, 0xe2, 0x2e, 0xff, 0xff, 0xf8, 0x0,
    0x2, 0xff, 0xff, 0xff, 0x90, 0x9, 0xff, 0xff,
    0xff, 0x10, 0xa, 0xff, 0xff, 0xff, 0xe3, 0x3e,
    0xff, 0xff, 0xff, 0xa0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x80,
    0xff, 0xff, 0x70, 0x0, 0x7, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xf6, 0x0, 0x6f, 0xff, 0xff, 0xfd,
    0x78, 0x8e, 0xff, 0x15, 0xff, 0xe8, 0xff, 0xe2,
    0x0, 0x2, 0xe5, 0x4f, 0xfe, 0x20, 0xfe, 0x20,
    0x0, 0x0, 0x13, 0xff, 0xf3, 0x0, 0x52, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0x31, 0x0, 0x52, 0x0,
    0x0, 0x2, 0xef, 0xf4, 0x5e, 0x20, 0xfe, 0x20,
    0x78, 0x8e, 0xff, 0x51, 0xff, 0xe8, 0xff, 0xe2,
    0xff, 0xff, 0xf6, 0x0, 0x6f, 0xff, 0xff, 0xfd,
    0xff, 0xff, 0x70, 0x0, 0x7, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F077 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xdd, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x1d, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x1, 0xdf,
    0xff, 0xfd, 0x10, 0x0, 0x0, 0x1d, 0xff, 0x99,
    0xff, 0xd1, 0x0, 0x1, 0xdf, 0xf9, 0x0, 0x9f,
    0xfd, 0x10, 0x1d, 0xff, 0x90, 0x0, 0x9, 0xff,
    0xd1, 0xbf, 0xf9, 0x0, 0x0, 0x0, 0x9f, 0xfb,
    0x5f, 0x90, 0x0, 0x0, 0x0, 0x9, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F078 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0x90, 0x0, 0x0, 0x0, 0x9, 0xf5, 0xbf, 0xf9,
    0x0, 0x0, 0x0, 0x9f, 0xfb, 0x1d, 0xff, 0x90,
    0x0, 0x9, 0xff, 0xd1, 0x1, 0xdf, 0xf9, 0x0,
    0x9f, 0xfd, 0x10, 0x0, 0x1d, 0xff, 0x99, 0xff,
    0xd1, 0x0, 0x0, 0x1, 0xdf, 0xff, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0x1d, 0xff, 0xd1, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xdd, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F079 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1d, 0xd1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xdf, 0xfd, 0x10,
    0xef, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x1d, 0xff,
    0xff, 0xd1, 0xaf, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0xcf, 0xcf, 0xfc, 0xfc, 0x0, 0x0, 0x0, 0xf,
    0xf0, 0x0, 0x6b, 0x1f, 0xf1, 0xb6, 0x0, 0x0,
    0x0, 0xf, 0xf0, 0x0, 0x0, 0xf, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf0, 0x0, 0x0, 0xf,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x0,
    0x0, 0xf, 0xf0, 0x0, 0x0, 0x0, 0x6b, 0x1f,
    0xf1, 0xb6, 0x0, 0xf, 0xf0, 0x0, 0x0, 0x0,
    0xcf, 0xcf, 0xfc, 0xfc, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xfa, 0x1d, 0xff, 0xff, 0xd1, 0x0, 0xd,
    0xff, 0xff, 0xff, 0xfe, 0x1, 0xdf, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1d,
    0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F07B "" */
    0x8f, 0xff, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,

    /* U+F093 "" */
    0x0, 0x0, 0x0, 0xb, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xf0, 0xdf, 0xfd, 0xf, 0xff, 0xfd,
    0xff, 0xff, 0xf9, 0x0, 0x0, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xe0, 0xff,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xea,
    0x62, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xef, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0x30, 0x0, 0x0, 0x2,
    0x0, 0x0, 0x4f, 0xff, 0x90, 0x0, 0x2, 0x8f,
    0xf3, 0x0, 0x6f, 0xff, 0xd0, 0x0, 0xa, 0xff,
    0xff, 0xe4, 0xbf, 0xff, 0xd1, 0x0, 0x0, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xd1, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xff, 0xfb, 0x30, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xdb, 0x72, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F0C4 "" */
    0x8, 0xee, 0x80, 0x0, 0x0, 0x6, 0x61, 0x8,
    0xff, 0xff, 0x80, 0x0, 0x2d, 0xff, 0xd0, 0xef,
    0x33, 0xfe, 0x0, 0x2e, 0xff, 0xf3, 0xe, 0xf3,
    0x3f, 0xe0, 0x2e, 0xff, 0xf3, 0x0, 0x8f, 0xff,
    0xff, 0x6e, 0xff, 0xf3, 0x0, 0x0, 0x8e, 0xff,
    0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x2, 0xef,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xff,
    0xff, 0x30, 0x0, 0x0, 0x8, 0xef, 0xff, 0xff,
    0xff, 0x30, 0x0, 0x8, 0xff, 0xff, 0xf6, 0xef,
    0xff, 0x30, 0x0, 0xef, 0x33, 0xfe, 0x2, 0xef,
    0xff, 0x30, 0xe, 0xf3, 0x3f, 0xe0, 0x2, 0xef,
    0xff, 0x30, 0x8f, 0xff, 0xf8, 0x0, 0x2, 0xdf,
    0xfd, 0x0, 0x8e, 0xe8, 0x0, 0x0, 0x0, 0x66,
    0x10,

    /* U+F0C5 "" */
    0x0, 0x0, 0xdf, 0xff, 0xff, 0xd, 0x20, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xf, 0xe2, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xf, 0xfd, 0xdf, 0xf0, 0xff,
    0xff, 0xff, 0x20, 0x0, 0xff, 0xf0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xdf, 0xff,
    0xff, 0xff, 0xfd, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x0, 0xdf, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0,

    /* U+F0C7 "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xc2, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x20, 0xff, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xe2, 0xff, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xfc, 0xff, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x11, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x11, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8,

    /* U+F0C9 "" */
    0x12, 0x22, 0x22, 0x22, 0x22, 0x22, 0x21, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x12, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x21, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x12, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x21, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x12, 0x22, 0x22, 0x22, 0x22, 0x22, 0x21, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x12, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x21, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x12, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x21,

    /* U+F0E0 "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90,
    0xd2, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x2d,
    0xff, 0x62, 0xcf, 0xff, 0xff, 0xfc, 0x26, 0xff,
    0xff, 0xfa, 0x18, 0xff, 0xff, 0x81, 0xaf, 0xff,
    0xff, 0xff, 0xe3, 0x4d, 0xd4, 0x3e, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x81, 0x18, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,

    /* U+F0E7 "" */
    0x0, 0xdf, 0xff, 0xfd, 0x0, 0x0, 0x1, 0xff,
    0xff, 0xfc, 0x0, 0x0, 0x3, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xf2, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xd0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xa0, 0xe, 0xff, 0xff, 0xff, 0xff, 0x20,
    0xd, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xe0, 0x0, 0x0, 0x0, 0xe, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x2f, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xa0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x10,
    0x0, 0x0, 0x0, 0x0, 0xd7, 0x0, 0x0, 0x0,

    /* U+F0EA "" */
    0x0, 0x4, 0xee, 0x40, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0x99, 0xff, 0xfd, 0x0, 0x0, 0xff, 0xff,
    0x99, 0xff, 0xff, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xd, 0xff, 0xff,
    0xd, 0x20, 0xff, 0xff, 0xf, 0xff, 0xff, 0xf,
    0xe2, 0xff, 0xff, 0xf, 0xff, 0xff, 0xf, 0xfd,
    0xff, 0xff, 0xf, 0xff, 0xff, 0x20, 0x0, 0xff,
    0xff, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xdf, 0xff, 0xf, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x0, 0xd, 0xff, 0xff, 0xff, 0xfd,

    /* U+F0F3 "" */
    0x0, 0x0, 0x0, 0xcc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0x30, 0x0, 0x0, 0x0, 0x1,
    0xbf, 0xff, 0xfc, 0x20, 0x0, 0x0, 0x1e, 0xff,
    0xff, 0xff, 0xe1, 0x0, 0x0, 0x9f, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xfd, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x3, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0x8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x1e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe1, 0xcf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xee, 0x40, 0x0, 0x0,

    /* U+F11C "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x0, 0xf0, 0xf, 0x0, 0xf0,
    0xf, 0x0, 0xff, 0xff, 0x0, 0xf0, 0xf, 0x0,
    0xf0, 0xf, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x8,
    0x80, 0x88, 0x8, 0x80, 0x8f, 0xff, 0xff, 0xf8,
    0x8, 0x80, 0x88, 0x8, 0x80, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0xf0, 0x0, 0x0, 0x0, 0xf, 0x0,
    0xff, 0xff, 0x0, 0xf0, 0x0, 0x0, 0x0, 0xf,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xaf, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xcf, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xdf, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x17,
    0xef, 0xff, 0xff, 0xff, 0x30, 0x0, 0x0, 0x18,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x2a,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F15B "" */
    0xdf, 0xff, 0xff, 0xf0, 0xd2, 0x0, 0xff, 0xff,
    0xff, 0xf0, 0xfe, 0x20, 0xff, 0xff, 0xff, 0xf0,
    0xff, 0xe2, 0xff, 0xff, 0xff, 0xf0, 0xff, 0xfd,
    0xff, 0xff, 0xff, 0xf2, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xfd,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0x9c, 0xef, 0xfe,
    0xc9, 0x40, 0x0, 0x0, 0x0, 0x7, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x70, 0x0, 0x4, 0xdf,
    0xff, 0xfc, 0xa8, 0x8a, 0xcf, 0xff, 0xfd, 0x40,
    0x6f, 0xff, 0xd5, 0x0, 0x0, 0x0, 0x0, 0x5d,
    0xff, 0xf6, 0xcf, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xfc, 0x1a, 0x30, 0x0, 0x5a,
    0xdf, 0xfd, 0xa5, 0x0, 0x3, 0xa1, 0x0, 0x0,
    0x4d, 0xff, 0xff, 0xff, 0xff, 0xd4, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xfe, 0xa8, 0x8a, 0xef, 0xff,
    0x50, 0x0, 0x0, 0x1, 0xdf, 0x70, 0x0, 0x0,
    0x7, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x12, 0x0,
    0x0, 0x0, 0x0, 0x21, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4e, 0xe4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4e, 0xe4, 0x0, 0x0, 0x0, 0x0,

    /* U+F240 "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfd, 0xff, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf, 0xff,
    0xff, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0xff, 0xff, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0xff, 0xff, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80,

    /* U+F241 "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfd, 0xff, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0xf, 0xff,
    0xff, 0xf, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0xff, 0xff, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0xff, 0xff, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0xf, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80,

    /* U+F242 "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfd, 0xff, 0xf,
    0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xf, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xf, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xf, 0xff, 0xff,
    0xff, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80,

    /* U+F243 "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfd, 0xff, 0xf,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xf, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xf, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xf, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80,

    /* U+F244 "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfd, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xcf, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb9, 0x29, 0xfe, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0x10, 0x2,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xdf, 0x80, 0xa,
    0x90, 0x0, 0x0, 0x0, 0x3, 0x70, 0x0, 0xdf,
    0xff, 0x77, 0xf7, 0x55, 0x55, 0x55, 0x55, 0x8f,
    0xd3, 0xf, 0xff, 0xfd, 0xcc, 0xdf, 0xdc, 0xcc,
    0xcc, 0xcd, 0xff, 0xb0, 0x8f, 0xfe, 0x10, 0x0,
    0xaa, 0x0, 0x0, 0x0, 0x4d, 0x40, 0x0, 0x46,
    0x10, 0x0, 0x1, 0xf2, 0x2, 0x33, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xb1, 0xcf,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x22,
    0x0, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x18, 0xdf, 0xfd, 0x92, 0x0, 0x2, 0xef,
    0xfb, 0xef, 0xff, 0x30, 0xd, 0xff, 0xfa, 0x2e,
    0xff, 0xe0, 0x4f, 0xff, 0xfa, 0x3, 0xff, 0xf5,
    0x9f, 0xfa, 0xfa, 0x35, 0x4f, 0xfa, 0xcf, 0xc0,
    0x8a, 0x3d, 0xb, 0xfd, 0xef, 0xfb, 0x3, 0x12,
    0x8f, 0xfe, 0xff, 0xff, 0xb0, 0x6, 0xff, 0xff,
    0xff, 0xff, 0xd1, 0x8, 0xff, 0xff, 0xef, 0xfd,
    0x11, 0x10, 0x9f, 0xff, 0xdf, 0xd1, 0x59, 0x3b,
    0xb, 0xfd, 0xaf, 0xd7, 0xfa, 0x38, 0x1d, 0xfb,
    0x5f, 0xff, 0xfa, 0x1, 0xdf, 0xf7, 0xd, 0xff,
    0xfa, 0x1d, 0xff, 0xf1, 0x3, 0xef, 0xfc, 0xdf,
    0xff, 0x50, 0x0, 0x18, 0xdf, 0xfe, 0xa3, 0x0,

    /* U+F2ED "" */
    0x0, 0x0, 0x7f, 0xff, 0xf7, 0x0, 0x0, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0xf, 0xf9, 0x9f, 0x99, 0xf9, 0x9f,
    0xf0, 0xf, 0xf8, 0x8f, 0x88, 0xf8, 0x8f, 0xf0,
    0xf, 0xf8, 0x8f, 0x88, 0xf8, 0x8f, 0xf0, 0xf,
    0xf8, 0x8f, 0x88, 0xf8, 0x8f, 0xf0, 0xf, 0xf8,
    0x8f, 0x88, 0xf8, 0x8f, 0xf0, 0xf, 0xf8, 0x8f,
    0x88, 0xf8, 0x8f, 0xf0, 0xf, 0xf8, 0x8f, 0x88,
    0xf8, 0x8f, 0xf0, 0xf, 0xf9, 0x9f, 0x99, 0xf9,
    0x9f, 0xf0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x8a, 0x1d,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfa,
    0x1d, 0xff, 0x70, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xfa, 0x1d, 0x80, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xde, 0xdb, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F55A "" */
    0x0, 0x0, 0x1b, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe4, 0x0, 0x1, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0x1d, 0xff, 0xff,
    0xfa, 0xef, 0xfe, 0xaf, 0xff, 0xff, 0x1, 0xdf,
    0xff, 0xff, 0xa0, 0x2e, 0xe2, 0xa, 0xff, 0xff,
    0x1d, 0xff, 0xff, 0xff, 0xe2, 0x2, 0x20, 0x2e,
    0xff, 0xff, 0xcf, 0xff, 0xff, 0xff, 0xfe, 0x20,
    0x2, 0xef, 0xff, 0xff, 0xcf, 0xff, 0xff, 0xff,
    0xfe, 0x20, 0x2, 0xef, 0xff, 0xff, 0x1d, 0xff,
    0xff, 0xff, 0xe2, 0x2, 0x20, 0x2e, 0xff, 0xff,
    0x1, 0xdf, 0xff, 0xff, 0xa0, 0x2e, 0xe2, 0xa,
    0xff, 0xff, 0x0, 0x1d, 0xff, 0xff, 0xfa, 0xef,
    0xfe, 0xaf, 0xff, 0xff, 0x0, 0x1, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x1b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe4,

    /* U+F7C2 "" */
    0x0, 0x8, 0xff, 0xff, 0xff, 0xe4, 0x0, 0x8f,
    0xff, 0xff, 0xff, 0xfe, 0x8, 0xf8, 0xf, 0xb,
    0x40, 0xff, 0x8f, 0xf8, 0xf, 0xb, 0x40, 0xff,
    0xff, 0xf8, 0xf, 0xb, 0x40, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x4e, 0xff, 0xff, 0xff, 0xff, 0xe4,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xe0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x2,
    0xef, 0x10, 0x0, 0xbf, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf1, 0x0, 0xcf, 0xf1, 0x0, 0x0, 0x0,
    0x7, 0xff, 0x11, 0xcf, 0xff, 0x77, 0x77, 0x77,
    0x77, 0xbf, 0xf1, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x17, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x7, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+FB52 "ﭒ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0x5b, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x9c, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xbd, 0xb0, 0x0, 0x0, 0x0,
    0x2a, 0xf4, 0x5f, 0xd8, 0x66, 0x8a, 0xdf, 0xe5,
    0x0, 0x3a, 0xef, 0xfe, 0xc9, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x89, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x89, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x10, 0x0, 0x0, 0x0,

    /* U+FB53 "ﭓ" */
    0xbb, 0x0, 0x0, 0x0, 0x0, 0x9, 0xc0, 0xf,
    0x70, 0x0, 0x0, 0x0, 0x0, 0xae, 0x0, 0xdb,
    0x0, 0x0, 0x0, 0x2, 0xaf, 0xf1, 0x5, 0xfe,
    0x97, 0x78, 0xad, 0xfe, 0x7f, 0xc6, 0x3, 0xae,
    0xff, 0xec, 0x95, 0x0, 0x4e, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x11,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x11, 0x0,
    0x0, 0x0, 0x0,

    /* U+FB54 "ﭔ" */
    0x0, 0x38, 0x0, 0x6, 0xf1, 0x0, 0x7f, 0x1,
    0x7e, 0xc0, 0x2f, 0xd3, 0x0, 0x0, 0x0, 0x0,
    0x3e, 0x0, 0x0, 0x20, 0x0, 0x3e, 0x0, 0x0,
    0x20,

    /* U+FB55 "ﭕ" */
    0x0, 0x38, 0x0, 0x0, 0x6f, 0x10, 0x0, 0x7f,
    0x20, 0x17, 0xef, 0xc7, 0x2f, 0xc8, 0xef, 0x0,
    0x0, 0x0, 0x0, 0x3e, 0x0, 0x0, 0x2, 0x0,
    0x0, 0x3e, 0x0, 0x0, 0x2, 0x0,

    /* U+FB56 "ﭖ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0x5b, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x9c, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xbd, 0xb0, 0x0, 0x0, 0x0,
    0x2a, 0xf4, 0x5f, 0xd8, 0x66, 0x8a, 0xdf, 0xe5,
    0x0, 0x3a, 0xef, 0xfe, 0xc9, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xa9, 0x90, 0x0, 0x0, 0x0, 0x0, 0x11, 0x11,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x89, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x10, 0x0, 0x0, 0x0,

    /* U+FB57 "ﭗ" */
    0xbb, 0x0, 0x0, 0x0, 0x0, 0x9, 0xc0, 0xf,
    0x70, 0x0, 0x0, 0x0, 0x0, 0xae, 0x0, 0xdb,
    0x0, 0x0, 0x0, 0x2, 0xaf, 0xf1, 0x5, 0xfe,
    0x97, 0x78, 0xad, 0xfe, 0x7f, 0xc6, 0x3, 0xae,
    0xff, 0xec, 0x95, 0x0, 0x4e, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8a,
    0x99, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x11,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x11, 0x0,
    0x0, 0x0, 0x0,

    /* U+FB58 "ﭘ" */
    0x0, 0x38, 0x0, 0x6, 0xf1, 0x0, 0x7f, 0x1,
    0x7e, 0xc0, 0x2f, 0xd3, 0x0, 0x0, 0x0, 0x3,
    0xf4, 0xe0, 0x2, 0x2, 0x0, 0x3e, 0x0, 0x0,
    0x20,

    /* U+FB59 "ﭙ" */
    0x0, 0x38, 0x0, 0x0, 0x6f, 0x10, 0x0, 0x7f,
    0x20, 0x17, 0xef, 0xc7, 0x2f, 0xc8, 0xef, 0x0,
    0x0, 0x0, 0x3, 0xf4, 0xe0, 0x0, 0x20, 0x20,
    0x0, 0x3e, 0x0, 0x0, 0x2, 0x0,

    /* U+FB5A "ﭚ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0x5b, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x9c, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xbd, 0xb0, 0x0, 0x0, 0x0,
    0x2a, 0xf4, 0x5f, 0xd8, 0x66, 0x8a, 0xdf, 0xe5,
    0x0, 0x3a, 0xef, 0xfe, 0xc9, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xa9, 0x90, 0x0, 0x0, 0x0, 0x0, 0x11, 0x11,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xa9, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x11, 0x11, 0x0, 0x0, 0x0,

    /* U+FB5B "ﭛ" */
    0xbb, 0x0, 0x0, 0x0, 0x0, 0x9, 0xc0, 0xf,
    0x70, 0x0, 0x0, 0x0, 0x0, 0xae, 0x0, 0xdb,
    0x0, 0x0, 0x0, 0x2, 0xaf, 0xf1, 0x5, 0xfe,
    0x97, 0x78, 0xad, 0xfe, 0x7f, 0xc6, 0x3, 0xae,
    0xff, 0xec, 0x95, 0x0, 0x4e, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8a,
    0x99, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x11,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8a, 0x99,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x11, 0x10,
    0x0, 0x0, 0x0,

    /* U+FB5C "ﭜ" */
    0x0, 0x38, 0x0, 0x6, 0xf1, 0x0, 0x7f, 0x1,
    0x7e, 0xc0, 0x2f, 0xd3, 0x0, 0x0, 0x0, 0x3,
    0xf4, 0xe0, 0x2, 0x2, 0x3, 0xf4, 0xe0, 0x2,
    0x2,

    /* U+FB5D "ﭝ" */
    0x0, 0x38, 0x0, 0x0, 0x6f, 0x10, 0x0, 0x7f,
    0x20, 0x17, 0xef, 0xc7, 0x2f, 0xc8, 0xef, 0x0,
    0x0, 0x0, 0x3, 0xf4, 0xe0, 0x0, 0x20, 0x20,
    0x3, 0xf4, 0xe0, 0x0, 0x20, 0x20,

    /* U+FB5E "ﭞ" */
    0x0, 0x0, 0x8, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x11, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0x90, 0x0, 0x0, 0x5, 0x70, 0x0, 0x11, 0x0,
    0x0, 0x9c, 0xe8, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xce, 0xa0, 0x0, 0x0, 0x0, 0x7, 0xf7, 0x7f,
    0xd8, 0x66, 0x79, 0xcf, 0xf8, 0x0, 0x4a, 0xef,
    0xfe, 0xc9, 0x61, 0x0,

    /* U+FB5F "ﭟ" */
    0x0, 0x0, 0x8, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x11, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0x90, 0x0, 0x0, 0x0, 0x5, 0x70,
    0x0, 0x11, 0x0, 0x0, 0x9c, 0x0, 0xe8, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xe0, 0xe, 0xa0, 0x0,
    0x0, 0x0, 0x2a, 0xff, 0x10, 0x7f, 0xd9, 0x77,
    0x8a, 0xdf, 0xf8, 0xfc, 0x60, 0x4a, 0xef, 0xfe,
    0xca, 0x51, 0x4, 0xed,

    /* U+FB60 "ﭠ" */
    0x0, 0x3e, 0x0, 0x0, 0x20, 0x0, 0x3e, 0x0,
    0x0, 0x20, 0x0, 0x0, 0x0, 0x4, 0xc0, 0x0,
    0x6f, 0x0, 0x7, 0xf0, 0x18, 0xec, 0x2, 0xfc,
    0x20,

    /* U+FB61 "ﭡ" */
    0x0, 0x3e, 0x0, 0x0, 0x2, 0x0, 0x0, 0x3e,
    0x0, 0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4c, 0x0, 0x0, 0x6f, 0x10, 0x0, 0x7f, 0x20,
    0x18, 0xef, 0xc7, 0x2f, 0xc8, 0xef,

    /* U+FB62 "ﭢ" */
    0x0, 0x0, 0x8a, 0x99, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x11, 0x10, 0x0, 0x0, 0x0, 0x0, 0x8a,
    0x99, 0x0, 0x0, 0x5, 0x70, 0x1, 0x11, 0x10,
    0x0, 0x9c, 0xe8, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xce, 0xa0, 0x0, 0x0, 0x0, 0x7, 0xf7, 0x7f,
    0xd8, 0x66, 0x79, 0xcf, 0xf8, 0x0, 0x4a, 0xef,
    0xfe, 0xc9, 0x61, 0x0,

    /* U+FB63 "ﭣ" */
    0x0, 0x0, 0x8a, 0x99, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x11, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8a, 0x99, 0x0, 0x0, 0x0, 0x5, 0x70,
    0x1, 0x11, 0x10, 0x0, 0x9c, 0x0, 0xe8, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xe0, 0xe, 0xa0, 0x0,
    0x0, 0x0, 0x2a, 0xff, 0x10, 0x7f, 0xd9, 0x77,
    0x8a, 0xdf, 0xf8, 0xfc, 0x60, 0x4a, 0xef, 0xfe,
    0xca, 0x51, 0x4, 0xed,

    /* U+FB64 "ﭤ" */
    0x3, 0xf4, 0xe0, 0x2, 0x2, 0x3, 0xf4, 0xe0,
    0x2, 0x2, 0x0, 0x0, 0x0, 0x4, 0xc0, 0x0,
    0x6f, 0x0, 0x7, 0xf0, 0x18, 0xec, 0x2, 0xfc,
    0x20,

    /* U+FB65 "ﭥ" */
    0x3, 0xf4, 0xe0, 0x0, 0x20, 0x20, 0x3, 0xf4,
    0xe0, 0x0, 0x20, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x4c, 0x0, 0x0, 0x6f, 0x10, 0x0, 0x7f, 0x20,
    0x18, 0xef, 0xc7, 0x2f, 0xc8, 0xef,

    /* U+FB66 "ﭦ" */
    0x0, 0x0, 0x23, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x57,
    0x66, 0x0, 0x0, 0x0, 0x0, 0x5, 0xe7, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0xbb, 0xa6, 0x0, 0x1,
    0x18, 0x90, 0x0, 0x0, 0x0, 0x0, 0x9c, 0xe7,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xce, 0xa0, 0x0,
    0x0, 0x0, 0x18, 0xf6, 0x6f, 0xd8, 0x66, 0x7a,
    0xcf, 0xf7, 0x0, 0x4a, 0xef, 0xfe, 0xc9, 0x61,
    0x0,

    /* U+FB67 "ﭧ" */
    0x0, 0x0, 0x57, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0x75, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5d, 0x7e, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xba, 0x60, 0x0, 0x12, 0x0, 0x89, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xc0, 0xe, 0x70, 0x0,
    0x0, 0x0, 0x0, 0xbe, 0x0, 0xeb, 0x0, 0x0,
    0x0, 0x2, 0xbf, 0xf1, 0x6, 0xfe, 0x97, 0x78,
    0xad, 0xff, 0x7f, 0xc6, 0x3, 0xae, 0xff, 0xec,
    0x95, 0x0, 0x4e, 0xd0,

    /* U+FB68 "ﭨ" */
    0x1, 0xb0, 0x0, 0x1, 0xb3, 0x60, 0x1, 0xe9,
    0xa5, 0x8, 0xec, 0xb1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5d, 0x0, 0x0, 0x6f, 0x0,
    0x0, 0x7f, 0x0, 0x18, 0xeb, 0x0, 0x2f, 0xc2,
    0x0,

    /* U+FB69 "ﭩ" */
    0x1, 0xb0, 0x0, 0x1, 0xb3, 0x60, 0x1, 0xe9,
    0xa5, 0x8, 0xec, 0xb1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5d, 0x0, 0x0, 0x6f, 0x10,
    0x0, 0x7f, 0x20, 0x18, 0xef, 0xc7, 0x2f, 0xc8,
    0xef,

    /* U+FB6A "ﭪ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf3, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x37, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xc1, 0x8f, 0x10, 0x0, 0x0, 0x0,
    0x0, 0xcc, 0x17, 0xf3, 0xbb, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xef, 0x2f, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x26, 0xf0, 0xea, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xfb, 0x7, 0xfa, 0x42, 0x12, 0x35, 0x8d,
    0xfd, 0x10, 0x6, 0xef, 0xff, 0xff, 0xff, 0xb6,
    0x0, 0x0, 0x0, 0x24, 0x44, 0x31, 0x0, 0x0,
    0x0,

    /* U+FB6B "ﭫ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf3, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xaf, 0xc2, 0x0,
    0x11, 0x0, 0x0, 0x0, 0xb, 0xf9, 0xee, 0x0,
    0xca, 0x0, 0x0, 0x0, 0xd, 0xa0, 0x5f, 0x20,
    0xf8, 0x0, 0x0, 0x0, 0x9, 0xe1, 0x6f, 0x0,
    0xaf, 0x83, 0x22, 0x22, 0x35, 0xfd, 0xfd, 0x75,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xdf, 0xfb,
    0x0, 0x3, 0x44, 0x43, 0x32, 0x0, 0x0, 0x0,

    /* U+FB6C "ﭬ" */
    0x0, 0x0, 0xa8, 0x0, 0x0, 0x0, 0x11, 0x0,
    0x0, 0xa, 0x8b, 0x70, 0x0, 0x1, 0x11, 0x10,
    0x0, 0x1, 0x76, 0x0, 0x0, 0x2e, 0xff, 0xc0,
    0x0, 0x8f, 0x24, 0xf6, 0x0, 0x8f, 0x23, 0xf7,
    0x0, 0x1d, 0xff, 0xf6, 0x0, 0x0, 0x27, 0xf3,
    0x17, 0x77, 0xaf, 0xb0, 0x2f, 0xff, 0xe9, 0x10,

    /* U+FB6D "ﭭ" */
    0x0, 0x0, 0x8a, 0x0, 0x0, 0x0, 0x0, 0x11,
    0x0, 0x0, 0x0, 0x8, 0xa8, 0x90, 0x0, 0x0,
    0x1, 0x11, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0x90, 0x0, 0x0, 0x3f,
    0xb9, 0xf4, 0x0, 0x0, 0x5f, 0x20, 0xf7, 0x0,
    0x0, 0x2f, 0x86, 0xf4, 0x0, 0x17, 0x7e, 0xff,
    0xf7, 0x71, 0x2f, 0xfe, 0xba, 0xef, 0xf4,

    /* U+FB6E "ﭮ" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0xf3, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf3, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x37, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xc1, 0x8f, 0x10, 0x0, 0x0, 0x0,
    0x0, 0xcc, 0x17, 0xf3, 0xbb, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xef, 0x2f, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x26, 0xf0, 0xea, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xfb, 0x7, 0xfa, 0x42, 0x12, 0x35, 0x8d,
    0xfd, 0x10, 0x6, 0xef, 0xff, 0xff, 0xff, 0xb6,
    0x0, 0x0, 0x0, 0x24, 0x44, 0x31, 0x0, 0x0,
    0x0,

    /* U+FB6F "ﭯ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf3, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf3, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xaf, 0xc2, 0x0,
    0x11, 0x0, 0x0, 0x0, 0xb, 0xf9, 0xee, 0x0,
    0xca, 0x0, 0x0, 0x0, 0xd, 0xa0, 0x5f, 0x20,
    0xf8, 0x0, 0x0, 0x0, 0x9, 0xe1, 0x6f, 0x0,
    0xaf, 0x83, 0x22, 0x22, 0x35, 0xfd, 0xfd, 0x75,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xdf, 0xfb,
    0x0, 0x3, 0x44, 0x43, 0x32, 0x0, 0x0, 0x0,

    /* U+FB70 "ﭰ" */
    0x0, 0xa, 0x8b, 0x70, 0x0, 0x1, 0x11, 0x10,
    0x0, 0xa, 0x8b, 0x70, 0x0, 0x1, 0x11, 0x10,
    0x0, 0x1, 0x76, 0x0, 0x0, 0x2e, 0xff, 0xc0,
    0x0, 0x8f, 0x24, 0xf6, 0x0, 0x8f, 0x23, 0xf7,
    0x0, 0x1d, 0xff, 0xf6, 0x0, 0x0, 0x27, 0xf3,
    0x17, 0x77, 0xaf, 0xb0, 0x2f, 0xff, 0xe9, 0x10,

    /* U+FB71 "ﭱ" */
    0x0, 0x8, 0xa8, 0x90, 0x0, 0x0, 0x1, 0x11,
    0x10, 0x0, 0x0, 0x8, 0xa8, 0x90, 0x0, 0x0,
    0x1, 0x11, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0x90, 0x0, 0x0, 0x3f,
    0xb9, 0xf4, 0x0, 0x0, 0x5f, 0x20, 0xf7, 0x0,
    0x0, 0x2f, 0x86, 0xf4, 0x0, 0x17, 0x7e, 0xff,
    0xf7, 0x71, 0x2f, 0xfe, 0xba, 0xef, 0xf4,

    /* U+FB72 "ﭲ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9e, 0xed, 0xff,
    0xe9, 0x0, 0x41, 0x3d, 0xe8, 0x41, 0x0, 0x3,
    0xfa, 0x0, 0x0, 0x0, 0xc, 0xc0, 0x0, 0x0,
    0x0, 0x3f, 0x30, 0xe, 0x30, 0x0, 0x6f, 0x0,
    0x2, 0x0, 0x0, 0x5f, 0x10, 0xf, 0x30, 0x0,
    0x1f, 0x80, 0x2, 0x0, 0x0, 0x8, 0xf9, 0x31,
    0x13, 0x83, 0x0, 0x6d, 0xff, 0xff, 0xc2, 0x0,
    0x0, 0x13, 0x31, 0x0,

    /* U+FB73 "ﭳ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7c, 0xff, 0xff,
    0xd8, 0x0, 0x98, 0x5b, 0xff, 0xe7, 0x0, 0x0,
    0xbf, 0x88, 0xd0, 0x0, 0x7, 0xf4, 0x1, 0xf3,
    0x0, 0xf, 0x90, 0x0, 0xac, 0x0, 0x4f, 0x20,
    0xb7, 0x1e, 0xc3, 0x6f, 0x0, 0x11, 0x3, 0xd7,
    0x5f, 0x20, 0xb7, 0x0, 0x0, 0x1f, 0x90, 0x21,
    0x0, 0x0, 0x7, 0xfa, 0x31, 0x14, 0xa4, 0x0,
    0x5d, 0xff, 0xff, 0xc2, 0x0, 0x0, 0x13, 0x31,
    0x0,

    /* U+FB74 "ﭴ" */
    0x0, 0x12, 0x10, 0x0, 0x0, 0x0, 0xcf, 0xfe,
    0xb7, 0x20, 0x0, 0x45, 0x79, 0xdf, 0xf9, 0x0,
    0x0, 0x0, 0x2c, 0xfa, 0x0, 0x0, 0x2, 0xef,
    0x60, 0x0, 0x0, 0x3e, 0xd2, 0x0, 0x17, 0x8b,
    0xfc, 0x10, 0x0, 0x2f, 0xeb, 0x60, 0x0, 0x0,
    0x0, 0x0, 0xe, 0x40, 0x0, 0x0, 0x0, 0x2,
    0x0, 0x0, 0x0, 0x0, 0xe, 0x40, 0x0, 0x0,
    0x0, 0x2, 0x0, 0x0,

    /* U+FB75 "ﭵ" */
    0x0, 0x12, 0x10, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xfe, 0xb7, 0x20, 0x0, 0x0, 0x45, 0x79, 0xcf,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xfa, 0x0,
    0x0, 0x0, 0x2, 0xef, 0xd0, 0x0, 0x0, 0x0,
    0x3e, 0xe9, 0xf5, 0x0, 0x17, 0x8b, 0xfd, 0x20,
    0xcf, 0x83, 0x2f, 0xeb, 0x60, 0x0, 0x1a, 0xf7,
    0x0, 0x0, 0xe, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x0, 0x0, 0x0,

    /* U+FB76 "ﭶ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7c, 0xff, 0xff,
    0xd8, 0x0, 0x98, 0x5b, 0xfe, 0xb6, 0x0, 0x0,
    0xbf, 0x70, 0x0, 0x0, 0x7, 0xf4, 0x0, 0x0,
    0x0, 0xf, 0x80, 0x0, 0x0, 0x0, 0x4f, 0x20,
    0x20, 0x20, 0x0, 0x6f, 0x0, 0xe4, 0xf3, 0x0,
    0x5f, 0x20, 0x0, 0x0, 0x0, 0x1f, 0x90, 0x0,
    0x0, 0x0, 0x7, 0xfa, 0x41, 0x14, 0xa4, 0x0,
    0x5d, 0xff, 0xff, 0xc2, 0x0, 0x0, 0x13, 0x31,
    0x0,

    /* U+FB77 "ﭷ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7c, 0xff, 0xff,
    0xd8, 0x0, 0x97, 0x5b, 0xff, 0xe6, 0x0, 0x0,
    0xce, 0x67, 0xe0, 0x0, 0xa, 0xe2, 0x1, 0xf4,
    0x0, 0x2f, 0x60, 0x0, 0x9d, 0x10, 0x5f, 0x12,
    0x12, 0x2e, 0xe5, 0x6f, 0x1b, 0x7c, 0x63, 0xd7,
    0x2f, 0x70, 0x0, 0x0, 0x0, 0x9, 0xf9, 0x31,
    0x13, 0x83, 0x0, 0x7d, 0xff, 0xff, 0xc2, 0x0,
    0x0, 0x13, 0x31, 0x0,

    /* U+FB78 "ﭸ" */
    0x0, 0x12, 0x10, 0x0, 0x0, 0x0, 0xcf, 0xfe,
    0xb7, 0x20, 0x0, 0x45, 0x79, 0xdf, 0xf9, 0x0,
    0x0, 0x0, 0x2c, 0xfa, 0x0, 0x0, 0x2, 0xef,
    0x60, 0x0, 0x0, 0x3e, 0xd2, 0x0, 0x17, 0x8b,
    0xfc, 0x10, 0x0, 0x2f, 0xeb, 0x60, 0x0, 0x0,
    0x0, 0x0, 0xe4, 0xe4, 0x0, 0x0, 0x0, 0x20,
    0x20, 0x0,

    /* U+FB79 "ﭹ" */
    0x0, 0x12, 0x10, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xfe, 0xb7, 0x20, 0x0, 0x0, 0x45, 0x79, 0xcf,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xfa, 0x0,
    0x0, 0x0, 0x2, 0xef, 0xd0, 0x0, 0x0, 0x0,
    0x3e, 0xe9, 0xf5, 0x0, 0x17, 0x8b, 0xfd, 0x20,
    0xcf, 0x83, 0x2f, 0xeb, 0x60, 0x0, 0x1a, 0xf7,
    0x0, 0x0, 0xe4, 0xe4, 0x0, 0x0, 0x0, 0x0,
    0x20, 0x20, 0x0, 0x0,

    /* U+FB7A "ﭺ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9e, 0xed, 0xff,
    0xe9, 0x0, 0x41, 0x3d, 0xe8, 0x41, 0x0, 0x3,
    0xfa, 0x0, 0x0, 0x0, 0xc, 0xc0, 0x0, 0x0,
    0x0, 0x3f, 0x30, 0xd5, 0xd4, 0x0, 0x6f, 0x0,
    0x20, 0x20, 0x0, 0x5f, 0x10, 0xd, 0x50, 0x0,
    0x1f, 0x80, 0x2, 0x0, 0x0, 0x8, 0xf9, 0x31,
    0x14, 0x94, 0x0, 0x6d, 0xff, 0xff, 0xc2, 0x0,
    0x0, 0x13, 0x31, 0x0,

    /* U+FB7B "ﭻ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7c, 0xff, 0xff,
    0xd8, 0x0, 0xa8, 0x6b, 0xff, 0xe7, 0x0, 0x0,
    0xcf, 0x89, 0xd0, 0x0, 0xa, 0xf3, 0x1, 0xf3,
    0x0, 0x2f, 0x60, 0x0, 0xaa, 0x0, 0x5f, 0x12,
    0x12, 0x2f, 0x71, 0x6f, 0x1c, 0x6d, 0x53, 0xd7,
    0x2f, 0x70, 0xd5, 0x0, 0x0, 0x9, 0xf9, 0x52,
    0x13, 0x73, 0x0, 0x7d, 0xff, 0xff, 0xc2, 0x0,
    0x0, 0x13, 0x31, 0x0,

    /* U+FB7C "ﭼ" */
    0x0, 0x12, 0x10, 0x0, 0x0, 0x0, 0xcf, 0xfe,
    0xb7, 0x20, 0x0, 0x45, 0x79, 0xdf, 0xf9, 0x0,
    0x0, 0x0, 0x2c, 0xfa, 0x0, 0x0, 0x2, 0xef,
    0x60, 0x0, 0x0, 0x3e, 0xd2, 0x0, 0x17, 0x8b,
    0xfc, 0x10, 0x0, 0x2f, 0xeb, 0x60, 0x0, 0x0,
    0x0, 0x0, 0xe4, 0xe4, 0x0, 0x0, 0x0, 0x20,
    0x20, 0x0, 0x0, 0x0, 0xe, 0x40, 0x0, 0x0,
    0x0, 0x2, 0x0, 0x0,

    /* U+FB7D "ﭽ" */
    0x0, 0x12, 0x10, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xfe, 0xb7, 0x20, 0x0, 0x0, 0x45, 0x79, 0xcf,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xfa, 0x0,
    0x0, 0x0, 0x2, 0xef, 0xd0, 0x0, 0x0, 0x0,
    0x3e, 0xe9, 0xf5, 0x0, 0x17, 0x8b, 0xfd, 0x20,
    0xcf, 0x83, 0x2f, 0xeb, 0x60, 0x0, 0x1a, 0xf7,
    0x0, 0x0, 0xe4, 0xe4, 0x0, 0x0, 0x0, 0x0,
    0x20, 0x20, 0x0, 0x0, 0x0, 0x0, 0xe, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x0, 0x0, 0x0,

    /* U+FB7E "ﭾ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9e, 0xed, 0xff,
    0xe9, 0x0, 0x41, 0x3d, 0xe8, 0x41, 0x0, 0x3,
    0xfa, 0x0, 0x0, 0x0, 0xc, 0xc0, 0x0, 0x0,
    0x0, 0x3f, 0x30, 0xd5, 0xd4, 0x0, 0x6f, 0x0,
    0x20, 0x20, 0x0, 0x5f, 0x10, 0xd5, 0xe4, 0x0,
    0x1f, 0x80, 0x21, 0x20, 0x0, 0x8, 0xf9, 0x31,
    0x13, 0x83, 0x0, 0x6d, 0xff, 0xff, 0xc2, 0x0,
    0x0, 0x13, 0x31, 0x0,

    /* U+FB7F "ﭿ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7c, 0xff, 0xff,
    0xd8, 0x0, 0xa8, 0x6b, 0xff, 0xe7, 0x0, 0x0,
    0xcf, 0x89, 0xd0, 0x0, 0xa, 0xf3, 0x1, 0xf3,
    0x0, 0x2f, 0x60, 0x0, 0xaa, 0x0, 0x5f, 0x12,
    0x12, 0x2f, 0x71, 0x6f, 0x1c, 0x6d, 0x53, 0xd7,
    0x2f, 0x7c, 0x6d, 0x50, 0x0, 0x9, 0xfb, 0x43,
    0x23, 0x73, 0x0, 0x7d, 0xff, 0xff, 0xc2, 0x0,
    0x0, 0x13, 0x31, 0x0,

    /* U+FB80 "ﮀ" */
    0x0, 0x12, 0x10, 0x0, 0x0, 0x0, 0xcf, 0xfe,
    0xb7, 0x20, 0x0, 0x45, 0x79, 0xdf, 0xf9, 0x0,
    0x0, 0x0, 0x2c, 0xfa, 0x0, 0x0, 0x2, 0xef,
    0x60, 0x0, 0x0, 0x3e, 0xd2, 0x0, 0x17, 0x8b,
    0xfc, 0x10, 0x0, 0x2f, 0xeb, 0x60, 0x0, 0x0,
    0x0, 0x0, 0xe4, 0xe4, 0x0, 0x0, 0x0, 0x20,
    0x20, 0x0, 0x0, 0x0, 0xd4, 0xe3, 0x0, 0x0,
    0x0, 0x20, 0x20, 0x0,

    /* U+FB81 "ﮁ" */
    0x0, 0x12, 0x10, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xfe, 0xb7, 0x20, 0x0, 0x0, 0x45, 0x79, 0xcf,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xfa, 0x0,
    0x0, 0x0, 0x2, 0xef, 0xd0, 0x0, 0x0, 0x0,
    0x3e, 0xe9, 0xf5, 0x0, 0x17, 0x8b, 0xfd, 0x20,
    0xcf, 0x83, 0x2f, 0xeb, 0x60, 0x0, 0x1a, 0xf7,
    0x0, 0x0, 0xe4, 0xe4, 0x0, 0x0, 0x0, 0x0,
    0x20, 0x20, 0x0, 0x0, 0x0, 0x0, 0xd4, 0xe3,
    0x0, 0x0, 0x0, 0x0, 0x20, 0x20, 0x0, 0x0,

    /* U+FB82 "ﮂ" */
    0x0, 0x2e, 0xa0, 0x0, 0x0, 0x3f, 0x70, 0x0,
    0x0, 0x9e, 0x0, 0x0, 0x5, 0xf2, 0x0, 0x0,
    0x7f, 0x10, 0x96, 0xaf, 0xb0, 0xd, 0xfe, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa8, 0xb7, 0x0,
    0x1, 0x11, 0x10,

    /* U+FB83 "ﮃ" */
    0x0, 0x2e, 0xa0, 0x0, 0x0, 0x0, 0x3f, 0x60,
    0x0, 0x0, 0x0, 0x9d, 0x0, 0x0, 0x0, 0x5,
    0xf2, 0x0, 0x0, 0x0, 0x7f, 0x60, 0x0, 0x66,
    0xaf, 0xef, 0x83, 0xf, 0xfe, 0x80, 0xaf, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa8, 0xb7, 0x0,
    0x0, 0x1, 0x11, 0x10, 0x0,

    /* U+FB84 "ﮄ" */
    0x0, 0xf3, 0xf1, 0x0, 0x2, 0x2, 0x0, 0x0,
    0x13, 0x0, 0x0, 0x1, 0xdc, 0x0, 0x0, 0x2,
    0xf8, 0x0, 0x0, 0x8, 0xe0, 0x0, 0x0, 0x4f,
    0x20, 0x0, 0x8, 0xf1, 0x9, 0x7a, 0xfb, 0x0,
    0xdf, 0xe8, 0x0,

    /* U+FB85 "ﮅ" */
    0x0, 0xf3, 0xf1, 0x0, 0x0, 0x2, 0x2, 0x0,
    0x0, 0x0, 0x12, 0x0, 0x0, 0x0, 0x1, 0xdb,
    0x0, 0x0, 0x0, 0x2, 0xf6, 0x0, 0x0, 0x0,
    0x9, 0xe0, 0x0, 0x0, 0x0, 0x4f, 0x20, 0x0,
    0x0, 0x8, 0xf6, 0x0, 0x6, 0x6a, 0xfe, 0xf8,
    0x30, 0xff, 0xe8, 0xa, 0xf9,

    /* U+FB86 "ﮆ" */
    0x0, 0xf, 0x10, 0x0, 0x0, 0x20, 0x0, 0x0,
    0xf3, 0xf1, 0x0, 0x2, 0x2, 0x0, 0x0, 0x13,
    0x0, 0x0, 0x1, 0xdc, 0x0, 0x0, 0x2, 0xf8,
    0x0, 0x0, 0x8, 0xe0, 0x0, 0x0, 0x4f, 0x20,
    0x0, 0x8, 0xf1, 0x9, 0x7a, 0xfb, 0x0, 0xdf,
    0xe8, 0x0,

    /* U+FB87 "ﮇ" */
    0x0, 0xf, 0x10, 0x0, 0x0, 0x0, 0x20, 0x0,
    0x0, 0x0, 0xf3, 0xf1, 0x0, 0x0, 0x2, 0x2,
    0x0, 0x0, 0x0, 0x12, 0x0, 0x0, 0x0, 0x1,
    0xdb, 0x0, 0x0, 0x0, 0x2, 0xf6, 0x0, 0x0,
    0x0, 0x9, 0xe0, 0x0, 0x0, 0x0, 0x4f, 0x20,
    0x0, 0x0, 0x8, 0xf6, 0x0, 0x6, 0x6a, 0xfe,
    0xf8, 0x30, 0xff, 0xe8, 0xa, 0xf9,

    /* U+FB88 "ﮈ" */
    0x0, 0xc0, 0x0, 0x0, 0xc, 0x26, 0x10, 0x0,
    0xda, 0x98, 0x0, 0x6e, 0xcc, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xb7, 0x0, 0x0, 0x5, 0xf5,
    0x0, 0x0, 0xa, 0xd0, 0x0, 0x0, 0x5f, 0x20,
    0x0, 0x7, 0xf1, 0x9, 0x6a, 0xfc, 0x0, 0xdf,
    0xe9, 0x10,

    /* U+FB89 "ﮉ" */
    0x0, 0xc0, 0x0, 0x0, 0x0, 0xc, 0x26, 0x10,
    0x0, 0x0, 0xda, 0x98, 0x0, 0x0, 0x6e, 0xcc,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xb5, 0x0, 0x0, 0x0, 0x5, 0xf3, 0x0, 0x0,
    0x0, 0xa, 0xc0, 0x0, 0x0, 0x0, 0x5f, 0x10,
    0x0, 0x0, 0x7, 0xf6, 0x0, 0x6, 0x6a, 0xfe,
    0xf8, 0x30, 0xff, 0xe8, 0xa, 0xf9,

    /* U+FB8A "ﮊ" */
    0x0, 0x0, 0x1, 0xf0, 0x0, 0x0, 0x0, 0x2,
    0x0, 0x0, 0x0, 0x1f, 0x3f, 0x0, 0x0, 0x0,
    0x20, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0x70, 0x0, 0x0, 0x0, 0xbb, 0x0,
    0x0, 0x0, 0xa, 0xb0, 0x0, 0x0, 0x0, 0xda,
    0x0, 0x0, 0x0, 0x4f, 0x50, 0x0, 0x0, 0x4f,
    0xd0, 0x1, 0x46, 0xcf, 0xd2, 0x0, 0xaf, 0xfc,
    0x60, 0x0, 0x3, 0x31, 0x0, 0x0, 0x0,

    /* U+FB8B "ﮋ" */
    0x0, 0x0, 0x1, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x20, 0x0, 0x0, 0x0, 0x1f, 0x3f, 0x0, 0x0,
    0x0, 0x2, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe8, 0x0, 0x0, 0x0,
    0x0, 0xbd, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xa7,
    0x0, 0x0, 0x0, 0xdd, 0xef, 0x0, 0x0, 0x5,
    0xf3, 0x0, 0x0, 0x0, 0x4f, 0xb0, 0x0, 0x13,
    0x6b, 0xfb, 0x0, 0x0, 0xaf, 0xfb, 0x50, 0x0,
    0x0, 0x33, 0x0, 0x0, 0x0, 0x0,

    /* U+FB8C "ﮌ" */
    0x0, 0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0xc0,
    0x0, 0x0, 0x0, 0xc, 0x27, 0x10, 0x0, 0x0,
    0xda, 0x97, 0x0, 0x0, 0x7e, 0xcb, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0x70, 0x0, 0x0, 0x0, 0xbb,
    0x0, 0x0, 0x0, 0xa, 0xb0, 0x0, 0x0, 0x0,
    0xd9, 0x0, 0x0, 0x0, 0x4f, 0x50, 0x0, 0x0,
    0x4f, 0xd0, 0x1, 0x46, 0xcf, 0xd1, 0x0, 0xaf,
    0xfc, 0x60, 0x0, 0x3, 0x31, 0x0, 0x0, 0x0,

    /* U+FB8D "ﮍ" */
    0x0, 0x0, 0x6, 0x0, 0x0, 0x0, 0x0, 0xc,
    0x0, 0x0, 0x0, 0x0, 0xc, 0x38, 0x10, 0x0,
    0x0, 0xe, 0xa9, 0x70, 0x0, 0x0, 0x7e, 0xcb,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0xe8, 0x0,
    0x0, 0x0, 0x0, 0xbd, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xa7, 0x0, 0x0, 0x0, 0xdd, 0xef, 0x0,
    0x0, 0x5, 0xf3, 0x0, 0x0, 0x0, 0x5f, 0xb0,
    0x0, 0x13, 0x6b, 0xfb, 0x0, 0x0, 0xaf, 0xfb,
    0x50, 0x0, 0x0, 0x33, 0x0, 0x0, 0x0, 0x0,

    /* U+FB8E "ﮎ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x42, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x7d, 0xf4, 0x0, 0x0,
    0x0, 0x2, 0x9f, 0xf9, 0x20, 0x0, 0x0, 0x0,
    0x3f, 0xd6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x20, 0x0,
    0x79, 0x0, 0x0, 0x0, 0x1e, 0xb0, 0x0, 0xe8,
    0x0, 0x0, 0x0, 0x7, 0xf0, 0x0, 0xe9, 0x0,
    0x0, 0x0, 0xb, 0xe0, 0x0, 0x8f, 0x94, 0x22,
    0x49, 0xef, 0x60, 0x0, 0x7, 0xef, 0xff, 0xfd,
    0x92, 0x0, 0x0, 0x0, 0x2, 0x43, 0x10, 0x0,
    0x0, 0x0,

    /* U+FB8F "ﮏ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x11, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xaf, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x6d, 0xfd, 0x60, 0x0, 0x0,
    0x0, 0x2, 0xef, 0xa3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xe1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xc0, 0x0, 0x0, 0x46, 0x0, 0x0, 0x0,
    0x1e, 0xa0, 0x0, 0xe, 0x80, 0x0, 0x0, 0x0,
    0x7f, 0x60, 0x0, 0xe8, 0x0, 0x0, 0x0, 0xb,
    0xff, 0x30, 0x9, 0xf9, 0x31, 0x24, 0x8e, 0xfa,
    0xde, 0x84, 0x8, 0xef, 0xff, 0xfe, 0xa4, 0x1,
    0xbf, 0xb0, 0x0, 0x24, 0x32, 0x0, 0x0, 0x0,
    0x0,

    /* U+FB90 "ﮐ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xb9, 0x0, 0x1, 0x7d, 0xfc, 0x40, 0x8, 0xff,
    0x93, 0x0, 0x3, 0xf9, 0x10, 0x0, 0x0, 0x3f,
    0x60, 0x0, 0x0, 0x0, 0xaf, 0x30, 0x0, 0x0,
    0x0, 0xce, 0x10, 0x0, 0x0, 0x1, 0xec, 0x0,
    0x0, 0x0, 0x5, 0xf4, 0x0, 0x0, 0x0, 0x3f,
    0x50, 0x1, 0x77, 0x7d, 0xf1, 0x0, 0x2f, 0xff,
    0xd4, 0x0, 0x0,

    /* U+FB91 "ﮑ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4b, 0x90, 0x0, 0x1, 0x7d, 0xfc, 0x40, 0x0,
    0x8f, 0xf9, 0x30, 0x0, 0x3, 0xf9, 0x10, 0x0,
    0x0, 0x3, 0xf6, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0x30, 0x0, 0x0, 0x0, 0xc, 0xe1, 0x0, 0x0,
    0x0, 0x1, 0xec, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xa0, 0x0, 0x0, 0x0, 0x3f, 0xf7, 0x0, 0x17,
    0x77, 0xdf, 0xaf, 0x97, 0x2f, 0xff, 0xd4, 0x8,
    0xef,

    /* U+FB92 "ﮒ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x73, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xaf, 0xb2, 0x0, 0x0,
    0x0, 0x6, 0xde, 0x81, 0x42, 0x0, 0x0, 0x0,
    0x6b, 0x51, 0x7d, 0xf4, 0x0, 0x0, 0x0, 0x2,
    0x9f, 0xf9, 0x20, 0x0, 0x0, 0x0, 0x3f, 0xd6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0x20, 0x0, 0x79, 0x0,
    0x0, 0x0, 0x1e, 0xb0, 0x0, 0xe8, 0x0, 0x0,
    0x0, 0x7, 0xf0, 0x0, 0xe9, 0x0, 0x0, 0x0,
    0xb, 0xe0, 0x0, 0x8f, 0x94, 0x22, 0x49, 0xef,
    0x60, 0x0, 0x7, 0xef, 0xff, 0xfd, 0x92, 0x0,
    0x0, 0x0, 0x2, 0x43, 0x10, 0x0, 0x0, 0x0,

    /* U+FB93 "ﮓ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x42, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x16, 0xde, 0x30, 0x0,
    0x0, 0x0, 0x3, 0x9f, 0xb5, 0x11, 0x0, 0x0,
    0x0, 0x6, 0xe8, 0x23, 0xaf, 0x50, 0x0, 0x0,
    0x0, 0x11, 0x6d, 0xfd, 0x60, 0x0, 0x0, 0x0,
    0x2, 0xef, 0xa3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xe1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xc0, 0x0, 0x0, 0x46, 0x0, 0x0, 0x0, 0x1e,
    0xa0, 0x0, 0xe, 0x80, 0x0, 0x0, 0x0, 0x7f,
    0x60, 0x0, 0xe8, 0x0, 0x0, 0x0, 0xb, 0xff,
    0x30, 0x9, 0xf9, 0x31, 0x24, 0x8e, 0xfa, 0xde,
    0x84, 0x8, 0xef, 0xff, 0xfe, 0xa4, 0x1, 0xbf,
    0xb0, 0x0, 0x24, 0x32, 0x0, 0x0, 0x0, 0x0,

    /* U+FB94 "ﮔ" */
    0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0, 0x17,
    0xd9, 0x0, 0x3, 0xaf, 0xb4, 0x0, 0x1d, 0xe8,
    0x14, 0xb9, 0x1, 0x51, 0x7d, 0xfc, 0x40, 0x8,
    0xff, 0x93, 0x0, 0x3, 0xf9, 0x10, 0x0, 0x0,
    0x3f, 0x60, 0x0, 0x0, 0x0, 0xaf, 0x30, 0x0,
    0x0, 0x0, 0xce, 0x10, 0x0, 0x0, 0x1, 0xec,
    0x0, 0x0, 0x0, 0x5, 0xf4, 0x0, 0x0, 0x0,
    0x3f, 0x50, 0x1, 0x77, 0x7d, 0xf1, 0x0, 0x2f,
    0xff, 0xd4, 0x0, 0x0,

    /* U+FB95 "ﮕ" */
    0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0, 0x1,
    0x7d, 0x90, 0x0, 0x3, 0xaf, 0xb4, 0x0, 0x1,
    0xde, 0x81, 0x4b, 0x90, 0x1, 0x51, 0x7d, 0xfc,
    0x40, 0x0, 0x8f, 0xf9, 0x30, 0x0, 0x3, 0xf9,
    0x10, 0x0, 0x0, 0x3, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0x30, 0x0, 0x0, 0x0, 0xc, 0xe1,
    0x0, 0x0, 0x0, 0x1, 0xec, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xa0, 0x0, 0x0, 0x0, 0x3f, 0xf7,
    0x0, 0x17, 0x77, 0xdf, 0xaf, 0x97, 0x2f, 0xff,
    0xd4, 0x8, 0xef,

    /* U+FB96 "ﮖ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x94, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xcf, 0x91, 0x0, 0x0,
    0x0, 0x18, 0xed, 0x61, 0x63, 0x0, 0x0, 0x0,
    0x6a, 0x32, 0x8e, 0xf4, 0x0, 0x0, 0x0, 0x3,
    0xbf, 0xe7, 0x10, 0x0, 0x0, 0x0, 0x4f, 0xc5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x8a, 0x0,
    0x0, 0x0, 0x1e, 0xc0, 0x0, 0xe8, 0x0, 0x0,
    0x0, 0x7, 0xf0, 0x0, 0xe9, 0x0, 0x0, 0x0,
    0xb, 0xe0, 0x0, 0x8f, 0x94, 0x22, 0x49, 0xef,
    0x60, 0x0, 0x7, 0xef, 0xff, 0xfd, 0x82, 0x0,
    0x0, 0x0, 0x2, 0x43, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5d, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5d, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x0, 0x0, 0x0, 0x0,

    /* U+FB97 "ﮗ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x42, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x16, 0xde, 0x30, 0x0,
    0x0, 0x0, 0x3, 0x9f, 0xb5, 0x11, 0x0, 0x0,
    0x0, 0x6, 0xe8, 0x23, 0xaf, 0x50, 0x0, 0x0,
    0x0, 0x11, 0x6d, 0xfd, 0x60, 0x0, 0x0, 0x0,
    0x2, 0xef, 0xa3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xe1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xc0, 0x0, 0x0, 0x46, 0x0, 0x0, 0x0, 0x1e,
    0xa0, 0x0, 0xe, 0x80, 0x0, 0x0, 0x0, 0x7f,
    0x60, 0x0, 0xe8, 0x0, 0x0, 0x0, 0xb, 0xff,
    0x30, 0x9, 0xf9, 0x31, 0x24, 0x8e, 0xfa, 0xde,
    0x84, 0x8, 0xef, 0xff, 0xfe, 0xa4, 0x1, 0xbf,
    0xb0, 0x0, 0x24, 0x32, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+FB98 "ﮘ" */
    0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0, 0x17,
    0xd9, 0x0, 0x3, 0xaf, 0xb4, 0x0, 0x1d, 0xe8,
    0x14, 0xb9, 0x1, 0x51, 0x7d, 0xfc, 0x40, 0x8,
    0xff, 0x93, 0x0, 0x3, 0xf9, 0x10, 0x0, 0x0,
    0x3f, 0x60, 0x0, 0x0, 0x0, 0xaf, 0x30, 0x0,
    0x0, 0x0, 0xce, 0x10, 0x0, 0x0, 0x1, 0xec,
    0x0, 0x0, 0x0, 0x5, 0xf4, 0x0, 0x0, 0x0,
    0x3f, 0x50, 0x1, 0x77, 0x7d, 0xf1, 0x0, 0x2f,
    0xff, 0xd4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xb0, 0x0, 0x0, 0x0, 0x11, 0x0,
    0x0, 0x0, 0x7, 0xb0, 0x0, 0x0, 0x0, 0x11,
    0x0, 0x0,

    /* U+FB99 "ﮙ" */
    0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0, 0x1,
    0x7d, 0x90, 0x0, 0x3, 0xaf, 0xb4, 0x0, 0x1,
    0xde, 0x81, 0x4b, 0x90, 0x1, 0x51, 0x7d, 0xfc,
    0x40, 0x0, 0x8f, 0xf9, 0x30, 0x0, 0x3, 0xf9,
    0x10, 0x0, 0x0, 0x3, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0x30, 0x0, 0x0, 0x0, 0xc, 0xe1,
    0x0, 0x0, 0x0, 0x1, 0xec, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xa0, 0x0, 0x0, 0x0, 0x3f, 0xf7,
    0x0, 0x17, 0x77, 0xdf, 0xaf, 0x97, 0x2f, 0xff,
    0xd4, 0x8, 0xef, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xb0, 0x0, 0x0, 0x0, 0x1, 0x10,
    0x0, 0x0, 0x0, 0x7, 0xb0, 0x0, 0x0, 0x0,
    0x1, 0x10, 0x0, 0x0,

    /* U+FB9A "ﮚ" */
    0x0, 0x0, 0x0, 0xf3, 0xf2, 0x2, 0x84, 0x0,
    0x0, 0x0, 0x20, 0x25, 0xbf, 0xa1, 0x0, 0x0,
    0x0, 0x17, 0xdd, 0x71, 0x53, 0x0, 0x0, 0x0,
    0x6b, 0x41, 0x8e, 0xf4, 0x0, 0x0, 0x0, 0x3,
    0xaf, 0xe8, 0x20, 0x0, 0x0, 0x0, 0x4f, 0xd6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x78, 0x0,
    0x0, 0x0, 0x1e, 0xc0, 0x0, 0xe8, 0x0, 0x0,
    0x0, 0x7, 0xf0, 0x0, 0xe9, 0x0, 0x0, 0x0,
    0x1b, 0xe0, 0x0, 0x8f, 0x94, 0x22, 0x49, 0xff,
    0x60, 0x0, 0x7, 0xef, 0xff, 0xfd, 0x92, 0x0,
    0x0, 0x0, 0x2, 0x43, 0x10, 0x0, 0x0, 0x0,

    /* U+FB9B "ﮛ" */
    0x0, 0x0, 0x0, 0xf3, 0xf2, 0x2, 0x84, 0x0,
    0x0, 0x0, 0x2, 0x3, 0x6b, 0xf9, 0x10, 0x0,
    0x0, 0x0, 0x18, 0xec, 0x61, 0x63, 0x0, 0x0,
    0x0, 0x5, 0x93, 0x4a, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x7, 0xef, 0xc6, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xfa, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xdb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xe9, 0x0, 0x0, 0x1, 0x20, 0x0, 0x0, 0x3,
    0xf6, 0x0, 0x0, 0xca, 0x0, 0x0, 0x0, 0x8,
    0xf4, 0x0, 0xf, 0x80, 0x0, 0x0, 0x0, 0xaf,
    0xf3, 0x0, 0xaf, 0x83, 0x12, 0x48, 0xef, 0xad,
    0xe8, 0x40, 0x9f, 0xff, 0xff, 0xfa, 0x40, 0x1b,
    0xfb, 0x0, 0x2, 0x43, 0x20, 0x0, 0x0, 0x0,
    0x0,

    /* U+FB9C "ﮜ" */
    0x6, 0xc7, 0xb0, 0x16, 0x70, 0x12, 0x15, 0xaf,
    0xb4, 0x0, 0x6d, 0xe8, 0x24, 0x60, 0x1b, 0x42,
    0x8e, 0xf7, 0x0, 0x3b, 0xfe, 0x81, 0x0, 0x1f,
    0xc5, 0x0, 0x0, 0x4, 0xf5, 0x0, 0x0, 0x0,
    0xc, 0xe1, 0x0, 0x0, 0x0, 0x1e, 0xd0, 0x0,
    0x0, 0x0, 0x2f, 0xa0, 0x0, 0x0, 0x0, 0x5f,
    0x30, 0x0, 0x0, 0x2, 0xf5, 0x0, 0x17, 0x77,
    0xdf, 0x10, 0x2, 0xff, 0xfd, 0x40, 0x0,

    /* U+FB9D "ﮝ" */
    0x6, 0xc7, 0xb0, 0x16, 0x70, 0x1, 0x21, 0x5a,
    0xfb, 0x40, 0x0, 0x6d, 0xe8, 0x24, 0x60, 0x1,
    0xb4, 0x28, 0xef, 0x70, 0x0, 0x3b, 0xfe, 0x81,
    0x0, 0x1, 0xfc, 0x50, 0x0, 0x0, 0x4, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0xce, 0x10, 0x0, 0x0,
    0x0, 0x1e, 0xd0, 0x0, 0x0, 0x0, 0x2, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0x90, 0x0, 0x0,
    0x0, 0x2f, 0xf7, 0x0, 0x17, 0x77, 0xdf, 0xaf,
    0x97, 0x2f, 0xff, 0xd4, 0x8, 0xef,

    /* U+FB9E "ﮞ" */
    0x0, 0x0, 0x0, 0x1, 0x20, 0x0, 0x0, 0x0,
    0x7, 0xf1, 0x12, 0x0, 0x0, 0x1, 0xf5, 0xbc,
    0x0, 0x0, 0x0, 0xf7, 0xda, 0x0, 0x0, 0x0,
    0xf8, 0xd9, 0x0, 0x0, 0x2, 0xf5, 0xac, 0x0,
    0x0, 0xa, 0xf0, 0x4f, 0xa4, 0x24, 0xaf, 0x60,
    0x5, 0xef, 0xff, 0xe6, 0x0, 0x0, 0x3, 0x42,
    0x0, 0x0,

    /* U+FB9F "ﮟ" */
    0x0, 0x0, 0x0, 0x4, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xf2, 0x0, 0x47, 0x0, 0x0, 0x0,
    0xf9, 0x0, 0xbb, 0x0, 0x0, 0x0, 0xef, 0x82,
    0xc9, 0x0, 0x0, 0x0, 0xfe, 0xf5, 0xc9, 0x0,
    0x0, 0x3, 0xf5, 0x0, 0xad, 0x0, 0x0, 0xa,
    0xe0, 0x0, 0x3f, 0xa4, 0x24, 0xaf, 0x40, 0x0,
    0x4, 0xef, 0xff, 0xd4, 0x0, 0x0, 0x0, 0x3,
    0x42, 0x0, 0x0, 0x0,

    /* U+FBA0 "ﮠ" */
    0x0, 0x8, 0x0, 0x0, 0x0, 0x0, 0xc, 0x0,
    0x0, 0x0, 0x0, 0xc, 0x38, 0x20, 0x0, 0x0,
    0xd, 0xb8, 0x80, 0x0, 0x0, 0x5d, 0xcc, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xa0, 0x0, 0x0,
    0x0, 0x3, 0xf4, 0x7a, 0x0, 0x0, 0x0, 0xf7,
    0xca, 0x0, 0x0, 0x0, 0xe8, 0xd9, 0x0, 0x0,
    0x1, 0xf6, 0xbc, 0x0, 0x0, 0x9, 0xf1, 0x5f,
    0xa4, 0x24, 0xaf, 0x80, 0x6, 0xef, 0xff, 0xe6,
    0x0, 0x0, 0x3, 0x42, 0x0, 0x0,

    /* U+FBA1 "ﮡ" */
    0x0, 0xc, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0x16, 0x10, 0x0, 0x0, 0x0, 0xd, 0xa8, 0x80,
    0x0, 0x0, 0x0, 0x5d, 0xcc, 0x33, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xf1, 0x0, 0x35, 0x0,
    0x0, 0x1, 0xf8, 0x0, 0xac, 0x0, 0x0, 0x0,
    0xef, 0x82, 0xca, 0x0, 0x0, 0x0, 0xfe, 0xf5,
    0xc9, 0x0, 0x0, 0x2, 0xf5, 0x0, 0xad, 0x0,
    0x0, 0xa, 0xe0, 0x0, 0x3f, 0xa4, 0x24, 0xaf,
    0x40, 0x0, 0x5, 0xef, 0xff, 0xd4, 0x0, 0x0,
    0x0, 0x3, 0x42, 0x0, 0x0, 0x0,

    /* U+FBA2 "ﮢ" */
    0x1, 0xb0, 0x0, 0x1, 0xb3, 0x60, 0x1, 0xe9,
    0xa5, 0x8, 0xec, 0xb1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5d, 0x0, 0x0, 0x6f, 0x0,
    0x0, 0x7f, 0x0, 0x18, 0xeb, 0x0, 0x2f, 0xc2,
    0x0,

    /* U+FBA3 "ﮣ" */
    0x1, 0xb0, 0x0, 0x1, 0xb3, 0x60, 0x1, 0xe9,
    0xa5, 0x8, 0xec, 0xb1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5d, 0x0, 0x0, 0x6f, 0x10,
    0x0, 0x7f, 0x20, 0x18, 0xef, 0xc7, 0x2f, 0xc8,
    0xef,

    /* U+FBAA "ﮪ" */
    0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0xd, 0xc3,
    0x0, 0x0, 0x0, 0x6, 0xff, 0x70, 0x0, 0x0,
    0xe, 0xbc, 0xf9, 0x0, 0x0, 0x2f, 0x34, 0xff,
    0x60, 0x0, 0xf, 0x57, 0xe7, 0xe0, 0xda, 0xb,
    0xde, 0x92, 0xf2, 0xce, 0x8b, 0xff, 0x99, 0xf1,
    0x2a, 0xfe, 0x8a, 0xee, 0x70,

    /* U+FBAB "ﮫ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xe3, 0x0, 0x0, 0x6, 0xf7, 0xbb, 0x0, 0x0,
    0xd, 0xb0, 0x9c, 0x0, 0xc9, 0xf, 0x75, 0xf8,
    0x0, 0xce, 0x8f, 0xcf, 0xf8, 0x71, 0x2b, 0xff,
    0xff, 0xff, 0xf4, 0x0, 0xf, 0x74, 0xd4, 0x0,
    0x0, 0xb, 0xe3, 0xad, 0x0, 0x0, 0x1, 0xcf,
    0xf7, 0x0, 0x0, 0x0, 0x2, 0x20, 0x0,

    /* U+FBAC "ﮬ" */
    0x0, 0x20, 0x0, 0x0, 0x0, 0x9, 0xe6, 0x0,
    0x0, 0x0, 0x2e, 0xfb, 0x0, 0x0, 0xa, 0xda,
    0xfc, 0x0, 0x0, 0xe7, 0xf, 0xfa, 0x0, 0xd,
    0xa4, 0xf7, 0xf3, 0x0, 0x9f, 0xee, 0xe, 0x71,
    0x7a, 0xff, 0xa5, 0xe6, 0x2f, 0xea, 0xae, 0xfa,
    0x0,

    /* U+FBAD "ﮭ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6e, 0xf8,
    0x0, 0x0, 0x3f, 0x98, 0xf1, 0x0, 0x9, 0xd0,
    0x9e, 0x0, 0x17, 0xdd, 0xbf, 0x97, 0x32, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0xbb, 0x6f, 0x60, 0x0,
    0x9, 0xe0, 0x7f, 0x0, 0x0, 0x3f, 0x77, 0xf0,
    0x0, 0x0, 0x7f, 0xf9, 0x0, 0x0, 0x0, 0x12,
    0x0, 0x0,

    /* U+FBD3 "ﯓ" */
    0x0, 0x0, 0x2f, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x20, 0x0, 0x0, 0x0, 0x1, 0xf3, 0xf0, 0xe,
    0x90, 0x0, 0x2, 0x2, 0x0, 0xe9, 0x0, 0x0,
    0x0, 0x0, 0xe, 0x90, 0x0, 0x2, 0x96, 0x0,
    0xe9, 0x0, 0x0, 0x76, 0x0, 0xe, 0x90, 0x0,
    0x0, 0x58, 0x0, 0xe9, 0x0, 0x2, 0xab, 0x20,
    0xe, 0x90, 0x0, 0x0, 0x0, 0x0, 0xe8, 0x54,
    0x0, 0x0, 0x0, 0xf, 0x7d, 0xa0, 0x0, 0x0,
    0x9, 0xf3, 0x8f, 0xb7, 0x55, 0x8e, 0xf8, 0x0,
    0x5c, 0xef, 0xfe, 0xb4, 0x0,

    /* U+FBD4 "ﯔ" */
    0x0, 0x0, 0x2f, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x20, 0x0, 0x0, 0x0, 0x0, 0x1, 0xf3,
    0xf0, 0xe, 0x90, 0x0, 0x0, 0x2, 0x2, 0x0,
    0xe9, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0x90,
    0x0, 0x0, 0x2, 0x96, 0x0, 0xe9, 0x0, 0x0,
    0x0, 0x76, 0x0, 0xe, 0x90, 0x0, 0x0, 0x0,
    0x58, 0x0, 0xe9, 0x0, 0x0, 0x2, 0xab, 0x20,
    0xe, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe9,
    0x0, 0x22, 0x0, 0x0, 0x0, 0xf, 0x90, 0xd,
    0xa0, 0x0, 0x0, 0x9, 0xfa, 0x0, 0x9f, 0xb7,
    0x55, 0x8e, 0xfd, 0xf8, 0x40, 0x6c, 0xff, 0xfe,
    0xa4, 0x9, 0xfa,

    /* U+FBD5 "ﯕ" */
    0x0, 0x6b, 0x0, 0x0, 0x0, 0x1, 0x20, 0x0,
    0x0, 0x6, 0xc7, 0xb0, 0x0, 0x10, 0x12, 0x11,
    0x5, 0xca, 0x0, 0x1, 0x8e, 0xfa, 0x30, 0x9,
    0xfe, 0x81, 0x0, 0x3, 0xf8, 0x0, 0x0, 0x0,
    0x3f, 0x70, 0x0, 0x0, 0x0, 0x9f, 0x40, 0x0,
    0x0, 0x0, 0xce, 0x20, 0x0, 0x0, 0x1, 0xec,
    0x0, 0x0, 0x0, 0x4, 0xf4, 0x0, 0x0, 0x0,
    0x3f, 0x50, 0x1, 0x77, 0x7d, 0xf1, 0x0, 0x2f,
    0xff, 0xd4, 0x0, 0x0,

    /* U+FBD6 "ﯖ" */
    0x0, 0x6b, 0x0, 0x0, 0x0, 0x0, 0x12, 0x0,
    0x0, 0x0, 0x6, 0xc7, 0xb0, 0x0, 0x10, 0x1,
    0x21, 0x10, 0x5c, 0xa0, 0x0, 0x1, 0x8e, 0xfa,
    0x30, 0x0, 0x9f, 0xe8, 0x10, 0x0, 0x3, 0xf8,
    0x0, 0x0, 0x0, 0x3, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0x30, 0x0, 0x0, 0x0, 0xc, 0xe1,
    0x0, 0x0, 0x0, 0x1, 0xed, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xa0, 0x0, 0x0, 0x0, 0x3f, 0xf7,
    0x0, 0x17, 0x77, 0xdf, 0xaf, 0x97, 0x2f, 0xff,
    0xd4, 0x8, 0xef,

    /* U+FBD7 "ﯗ" */
    0x0, 0x2, 0xda, 0x0, 0x0, 0x6, 0x9d, 0x10,
    0x0, 0x1, 0xcf, 0x70, 0x0, 0x5, 0xc2, 0x0,
    0x0, 0xd8, 0x10, 0x0, 0x0, 0x8, 0xed, 0x40,
    0x0, 0x6f, 0xac, 0xf1, 0x0, 0x9e, 0x1, 0xf6,
    0x0, 0x6f, 0xa6, 0xf7, 0x0, 0x8, 0xdf, 0xf7,
    0x0, 0x0, 0x4, 0xf4, 0x0, 0x0, 0x2e, 0xe0,
    0x12, 0x48, 0xef, 0x30, 0xaf, 0xff, 0xa2, 0x0,
    0x34, 0x20, 0x0, 0x0,

    /* U+FBD8 "ﯘ" */
    0x0, 0x2, 0xda, 0x0, 0x0, 0x0, 0x6, 0x9d,
    0x10, 0x0, 0x0, 0x1, 0xcf, 0x70, 0x0, 0x0,
    0x5, 0xc2, 0x0, 0x0, 0x0, 0xd8, 0x10, 0x0,
    0x0, 0x0, 0x8, 0xed, 0x40, 0x0, 0x0, 0x6f,
    0xac, 0xf1, 0x0, 0x0, 0xae, 0x1, 0xf6, 0x0,
    0x0, 0x6f, 0xa7, 0xfb, 0x73, 0x0, 0x8, 0xef,
    0xff, 0xf6, 0x0, 0x0, 0x4, 0xf3, 0x0, 0x0,
    0x0, 0x2e, 0xc0, 0x0, 0x12, 0x48, 0xee, 0x20,
    0x0, 0xaf, 0xff, 0xa1, 0x0, 0x0, 0x34, 0x20,
    0x0, 0x0, 0x0,

    /* U+FBD9 "ﯙ" */
    0x0, 0xd, 0x36, 0xb0, 0x0, 0x4, 0xde, 0x20,
    0x0, 0x0, 0x65, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xed, 0x40, 0x0, 0x6f, 0xac, 0xf1,
    0x0, 0x9e, 0x1, 0xf6, 0x0, 0x6f, 0xa6, 0xf7,
    0x0, 0x8, 0xdf, 0xf7, 0x0, 0x0, 0x4, 0xf4,
    0x0, 0x0, 0x2e, 0xe0, 0x12, 0x48, 0xef, 0x30,
    0xaf, 0xff, 0xa2, 0x0, 0x34, 0x20, 0x0, 0x0,

    /* U+FBDA "ﯚ" */
    0x0, 0xd, 0x36, 0xb0, 0x0, 0x0, 0x4, 0xde,
    0x20, 0x0, 0x0, 0x0, 0x65, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xee, 0x40,
    0x0, 0x0, 0x6f, 0xac, 0xf1, 0x0, 0x0, 0xae,
    0x1, 0xf6, 0x0, 0x0, 0x6f, 0xa7, 0xfb, 0x73,
    0x0, 0x8, 0xef, 0xff, 0xf6, 0x0, 0x0, 0x4,
    0xf3, 0x0, 0x0, 0x0, 0x2e, 0xc0, 0x0, 0x12,
    0x48, 0xee, 0x20, 0x0, 0xaf, 0xff, 0xa1, 0x0,
    0x0, 0x34, 0x20, 0x0, 0x0, 0x0,

    /* U+FBDB "ﯛ" */
    0x0, 0x0, 0x52, 0x0, 0x0, 0x0, 0x94, 0x0,
    0x0, 0x0, 0x94, 0x0, 0x0, 0x0, 0x94, 0x0,
    0x0, 0x0, 0x94, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xed, 0x40, 0x0, 0x6f, 0xac, 0xf1,
    0x0, 0x9e, 0x1, 0xf6, 0x0, 0x6f, 0xa6, 0xf7,
    0x0, 0x8, 0xdf, 0xf7, 0x0, 0x0, 0x4, 0xf4,
    0x0, 0x0, 0x2e, 0xe0, 0x12, 0x48, 0xef, 0x30,
    0xaf, 0xff, 0xa2, 0x0, 0x34, 0x20, 0x0, 0x0,

    /* U+FBDC "ﯜ" */
    0x0, 0x0, 0x52, 0x0, 0x0, 0x0, 0x0, 0x94,
    0x0, 0x0, 0x0, 0x0, 0x94, 0x0, 0x0, 0x0,
    0x0, 0x94, 0x0, 0x0, 0x0, 0x0, 0x94, 0x0,
    0x0, 0x0, 0x0, 0x11, 0x0, 0x0, 0x0, 0xb,
    0xff, 0x60, 0x0, 0x0, 0x7f, 0xac, 0xf2, 0x0,
    0x0, 0xae, 0x1, 0xf6, 0x0, 0x0, 0x6f, 0xa7,
    0xfb, 0x73, 0x0, 0x8, 0xef, 0xff, 0xf6, 0x0,
    0x0, 0x4, 0xf3, 0x0, 0x0, 0x0, 0x2e, 0xc0,
    0x0, 0x12, 0x48, 0xee, 0x20, 0x0, 0xaf, 0xff,
    0xa1, 0x0, 0x0, 0x34, 0x20, 0x0, 0x0, 0x0,

    /* U+FBDE "ﯞ" */
    0x0, 0x0, 0xa7, 0x0, 0x0, 0x0, 0x11, 0x0,
    0x0, 0xa, 0x8b, 0x70, 0x0, 0x1, 0x11, 0x10,
    0x0, 0x8, 0xed, 0x40, 0x0, 0x6f, 0xac, 0xf1,
    0x0, 0x9e, 0x1, 0xf6, 0x0, 0x6f, 0xa6, 0xf7,
    0x0, 0x8, 0xdf, 0xf7, 0x0, 0x0, 0x4, 0xf4,
    0x0, 0x0, 0x2e, 0xe0, 0x12, 0x48, 0xef, 0x30,
    0xaf, 0xff, 0xa2, 0x0, 0x34, 0x20, 0x0, 0x0,

    /* U+FBDF "ﯟ" */
    0x0, 0x0, 0xa7, 0x0, 0x0, 0x0, 0x0, 0x11,
    0x0, 0x0, 0x0, 0xa, 0x8b, 0x70, 0x0, 0x0,
    0x1, 0x11, 0x10, 0x0, 0x0, 0x4, 0xaa, 0x20,
    0x0, 0x0, 0x5f, 0xac, 0xe1, 0x0, 0x0, 0xae,
    0x1, 0xf6, 0x0, 0x0, 0x6f, 0xa7, 0xfb, 0x73,
    0x0, 0x8, 0xef, 0xff, 0xf6, 0x0, 0x0, 0x4,
    0xf3, 0x0, 0x0, 0x0, 0x2e, 0xc0, 0x0, 0x12,
    0x48, 0xee, 0x20, 0x0, 0xaf, 0xff, 0xa1, 0x0,
    0x0, 0x34, 0x20, 0x0, 0x0, 0x0,

    /* U+FBE4 "ﯤ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7d, 0xfe, 0x70, 0x0, 0x0, 0x4f, 0x84, 0x7f,
    0x40, 0x0, 0x4, 0xf8, 0x10, 0x10, 0x24, 0x0,
    0x7, 0xff, 0xa2, 0xd, 0xa0, 0x0, 0x0, 0x5d,
    0xc0, 0xf7, 0x0, 0x0, 0x0, 0xbe, 0xb, 0xd2,
    0x0, 0x14, 0xbf, 0x60, 0x1b, 0xfe, 0xff, 0xea,
    0x30, 0x0, 0x1, 0x43, 0x10, 0x0, 0x0, 0x0,
    0x0, 0xe3, 0x0, 0x0, 0x0, 0x0, 0x2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe4, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x0, 0x0, 0x0,

    /* U+FBE5 "ﯥ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2e, 0xf8, 0x0, 0xbb, 0x0, 0x0,
    0xc, 0xd7, 0xf4, 0xe, 0x70, 0x0, 0x0, 0xbf,
    0x49, 0xe3, 0xe9, 0x0, 0x0, 0x1, 0xdb, 0xb,
    0x78, 0xf8, 0x31, 0x24, 0x9f, 0x60, 0x0, 0x8,
    0xff, 0xff, 0xfd, 0x60, 0x0, 0x0, 0x0, 0x34,
    0x42, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x0, 0x0, 0x0, 0x0,

    /* U+FBE6 "ﯦ" */
    0x0, 0x38, 0x0, 0x6, 0xf1, 0x0, 0x7f, 0x1,
    0x7e, 0xc0, 0x2f, 0xd3, 0x0, 0x0, 0x0, 0x0,
    0x3e, 0x0, 0x0, 0x20, 0x0, 0x3e, 0x0, 0x0,
    0x20,

    /* U+FBE7 "ﯧ" */
    0x0, 0x38, 0x0, 0x0, 0x6f, 0x10, 0x0, 0x7f,
    0x20, 0x17, 0xef, 0xc7, 0x2f, 0xc8, 0xef, 0x0,
    0x0, 0x0, 0x0, 0x3e, 0x0, 0x0, 0x2, 0x0,
    0x0, 0x3e, 0x0, 0x0, 0x2, 0x0,

    /* U+FBE8 "ﯨ" */
    0x0, 0x38, 0x0, 0x6, 0xf1, 0x0, 0x7f, 0x1,
    0x7e, 0xc0, 0x2f, 0xd3, 0x0,

    /* U+FBE9 "ﯩ" */
    0x0, 0x38, 0x0, 0x0, 0x6f, 0x10, 0x0, 0x7f,
    0x20, 0x17, 0xef, 0xc7, 0x2f, 0xc8, 0xef,

    /* U+FBFC "ﯼ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6d, 0xfe, 0x70, 0x0, 0x0, 0x4f, 0x94, 0x7f,
    0x40, 0x0, 0x5, 0xf7, 0x0, 0x10, 0x12, 0x0,
    0x9, 0xfe, 0x80, 0xc, 0xa0, 0x0, 0x2, 0x7e,
    0xb0, 0xf7, 0x0, 0x0, 0x0, 0xaf, 0xd, 0xc0,
    0x0, 0x2, 0x9f, 0x90, 0x4f, 0xfc, 0xcf, 0xff,
    0x90, 0x0, 0x28, 0xba, 0x85, 0x10, 0x0,

    /* U+FBFD "ﯽ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2e, 0xf8, 0x0, 0xbb, 0x0, 0x0,
    0xc, 0xd7, 0xf4, 0xe, 0x70, 0x0, 0x0, 0xbf,
    0x49, 0xe3, 0xe9, 0x0, 0x0, 0x1, 0xdb, 0xb,
    0x78, 0xf8, 0x31, 0x24, 0x9f, 0x60, 0x0, 0x8,
    0xff, 0xff, 0xfd, 0x60, 0x0, 0x0, 0x0, 0x34,
    0x42, 0x0, 0x0, 0x0,

    /* U+FBFE "ﯾ" */
    0x0, 0x38, 0x0, 0x6, 0xf1, 0x0, 0x7f, 0x1,
    0x7e, 0xc0, 0x2f, 0xd3, 0x0, 0x0, 0x0, 0x3,
    0xf4, 0xe0, 0x2, 0x2,

    /* U+FBFF "ﯿ" */
    0x0, 0x38, 0x0, 0x0, 0x6f, 0x10, 0x0, 0x7f,
    0x20, 0x17, 0xef, 0xc7, 0x2f, 0xc8, 0xef, 0x0,
    0x0, 0x0, 0x3, 0xf4, 0xe0, 0x0, 0x20, 0x20,

    /* U+FE70 "ﹰ" */
    0x0, 0x0, 0x14, 0x8b, 0xd7, 0x85, 0x21, 0x26,
    0xad, 0xc6, 0x63, 0x0, 0x0,

    /* U+FE71 "ﹱ" */
    0x0, 0x0, 0x1, 0x4, 0x8b, 0xd7, 0x8, 0x52,
    0x12, 0x6, 0xad, 0xc6, 0x6, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x17, 0x77, 0x75, 0x2f,
    0xff, 0xfd,

    /* U+FE72 "ﹲ" */
    0x1, 0xdb, 0x0, 0x4a, 0xc2, 0xa0, 0x9f, 0x8b,
    0x1a, 0x50, 0x4d, 0x70, 0x0,

    /* U+FE73 "ﹳ" */
    0x2f, 0x50, 0x0, 0xee, 0x72, 0x3, 0xdf, 0x50,

    /* U+FE74 "ﹴ" */
    0x3, 0x7a, 0x8c, 0xa6, 0x30, 0x25, 0x9c, 0x8a,
    0x84, 0x10,

    /* U+FE76 "ﹶ" */
    0x0, 0x1, 0x26, 0xad, 0xc6, 0x63, 0x0, 0x0,

    /* U+FE77 "ﹷ" */
    0x0, 0x0, 0x12, 0x6, 0xad, 0xc6, 0x6, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x17, 0x77,
    0x75, 0x2f, 0xff, 0xfd,

    /* U+FE78 "ﹸ" */
    0x1, 0xdb, 0x0, 0x5a, 0xc2, 0x0, 0xbf, 0x80,
    0x5d, 0x20, 0xc8, 0x10, 0x0,

    /* U+FE79 "ﹹ" */
    0x0, 0x1d, 0xb0, 0x0, 0x5a, 0xc1, 0x0, 0xc,
    0xe8, 0x0, 0x5c, 0x20, 0xc, 0x81, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x17, 0x77, 0x75, 0x2f,
    0xff, 0xfd,

    /* U+FE7A "ﹺ" */
    0x0, 0x36, 0x6b, 0xda, 0x62, 0x10, 0x0, 0x0,

    /* U+FE7B "ﹻ" */
    0x17, 0x77, 0x75, 0x2f, 0xff, 0xfd, 0x0, 0x3,
    0x66, 0xb, 0xda, 0x62, 0x1, 0x0, 0x0,

    /* U+FE7C "ﹼ" */
    0x0, 0x0, 0x5, 0x4, 0xb, 0xc, 0xb, 0xc,
    0x1c, 0x1c, 0x3e, 0xc8, 0xb, 0xd2, 0x50,

    /* U+FE7D "ﹽ" */
    0x0, 0x5, 0xb, 0xb, 0xc, 0xc, 0x1b, 0x1f,
    0x6a, 0xe, 0xb7, 0xb2, 0x2, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x17, 0x77, 0x75, 0x2f,
    0xff, 0xfd,

    /* U+FE7E "ﹾ" */
    0x2c, 0xe9, 0xb, 0x60, 0xb5, 0xb6, 0xb, 0x52,
    0xce, 0x90,

    /* U+FE7F "ﹿ" */
    0x2, 0xce, 0x90, 0xb, 0x60, 0xb5, 0xb, 0x60,
    0xb5, 0x2, 0xce, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x17, 0x77, 0x75, 0x2f,
    0xff, 0xfd,

    /* U+FE80 "ﺀ" */
    0x0, 0x0, 0x0, 0x9, 0xff, 0x90, 0x6f, 0x95,
    0x40, 0xad, 0x0, 0x0, 0x8f, 0x40, 0x11, 0x1a,
    0xff, 0xf3, 0x5c, 0xfe, 0x70, 0x9a, 0x40, 0x0,

    /* U+FE81 "ﺁ" */
    0x19, 0x40, 0x19, 0x8, 0x5a, 0xdb, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xf0, 0x0, 0x0, 0x7f,
    0x0, 0x0, 0x7, 0xf0, 0x0, 0x0, 0x7f, 0x0,
    0x0, 0x7, 0xf0, 0x0, 0x0, 0x7f, 0x0, 0x0,
    0x7, 0xf0, 0x0, 0x0, 0x7f, 0x0, 0x0, 0x7,
    0xf0, 0x0, 0x0, 0x7f, 0x0, 0x0, 0x7, 0xf0,
    0x0, 0x0, 0x7f, 0x0, 0x0,

    /* U+FE82 "ﺂ" */
    0x19, 0x40, 0x19, 0x8, 0x5a, 0xdb, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xf0, 0x0, 0x0, 0x7f,
    0x0, 0x0, 0x7, 0xf0, 0x0, 0x0, 0x7f, 0x0,
    0x0, 0x7, 0xf0, 0x0, 0x0, 0x7f, 0x0, 0x0,
    0x7, 0xf0, 0x0, 0x0, 0x7f, 0x0, 0x0, 0x7,
    0xf0, 0x0, 0x0, 0x6f, 0x0, 0x0, 0x3, 0xfb,
    0x70, 0x0, 0x7, 0xef, 0x0,

    /* U+FE83 "ﺃ" */
    0x8, 0xc3, 0x1b, 0x0, 0xd, 0x85, 0x2d, 0x93,
    0x0, 0x0, 0x7, 0xf0, 0x7, 0xf0, 0x7, 0xf0,
    0x7, 0xf0, 0x7, 0xf0, 0x7, 0xf0, 0x7, 0xf0,
    0x7, 0xf0, 0x7, 0xf0, 0x7, 0xf0, 0x7, 0xf0,
    0x7, 0xf0,

    /* U+FE84 "ﺄ" */
    0x8, 0xc3, 0x0, 0x1b, 0x0, 0x0, 0xd, 0x85,
    0x0, 0x2d, 0x93, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xf0, 0x0, 0x7, 0xf0, 0x0, 0x7, 0xf0, 0x0,
    0x7, 0xf0, 0x0, 0x7, 0xf0, 0x0, 0x7, 0xf0,
    0x0, 0x7, 0xf0, 0x0, 0x7, 0xf0, 0x0, 0x7,
    0xf0, 0x0, 0x6, 0xf0, 0x0, 0x3, 0xfb, 0x70,
    0x0, 0x7e, 0xf0,

    /* U+FE85 "ﺅ" */
    0x0, 0x5, 0xc7, 0x0, 0x0, 0xc, 0x0, 0x0,
    0x0, 0x9, 0xa7, 0x0, 0x0, 0xb, 0x94, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xed, 0x40,
    0x0, 0x6f, 0xac, 0xf1, 0x0, 0x9e, 0x1, 0xf6,
    0x0, 0x6f, 0xa6, 0xf7, 0x0, 0x8, 0xdf, 0xf7,
    0x0, 0x0, 0x4, 0xf4, 0x0, 0x0, 0x2e, 0xe0,
    0x12, 0x48, 0xef, 0x30, 0xaf, 0xff, 0xa2, 0x0,
    0x34, 0x20, 0x0, 0x0,

    /* U+FE86 "ﺆ" */
    0x0, 0x5, 0xc7, 0x0, 0x0, 0x0, 0xc, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xb8, 0x0, 0x0, 0x0,
    0x8, 0x62, 0x0, 0x0, 0x0, 0x0, 0x22, 0x0,
    0x0, 0x0, 0x1c, 0xff, 0x80, 0x0, 0x0, 0x8f,
    0xac, 0xf2, 0x0, 0x0, 0xae, 0x1, 0xf6, 0x0,
    0x0, 0x6f, 0xa7, 0xfb, 0x73, 0x0, 0x8, 0xef,
    0xff, 0xf6, 0x0, 0x0, 0x4, 0xf3, 0x0, 0x0,
    0x0, 0x2e, 0xc0, 0x0, 0x12, 0x48, 0xee, 0x20,
    0x0, 0xaf, 0xff, 0xa1, 0x0, 0x0, 0x34, 0x20,
    0x0, 0x0, 0x0,

    /* U+FE87 "ﺇ" */
    0x7, 0xf0, 0x7, 0xf0, 0x7, 0xf0, 0x7, 0xf0,
    0x7, 0xf0, 0x7, 0xf0, 0x7, 0xf0, 0x7, 0xf0,
    0x7, 0xf0, 0x7, 0xf0, 0x7, 0xf0, 0x7, 0xf0,
    0x9, 0xc3, 0x1b, 0x0, 0xd, 0xb6, 0x16, 0x20,

    /* U+FE88 "ﺈ" */
    0x7, 0xf0, 0x0, 0x7, 0xf0, 0x0, 0x7, 0xf0,
    0x0, 0x7, 0xf0, 0x0, 0x7, 0xf0, 0x0, 0x7,
    0xf0, 0x0, 0x7, 0xf0, 0x0, 0x7, 0xf0, 0x0,
    0x7, 0xf0, 0x0, 0x6, 0xf0, 0x0, 0x3, 0xfb,
    0x70, 0x0, 0x7e, 0xf0, 0x9, 0xc3, 0x0, 0x1b,
    0x0, 0x0, 0xd, 0xb6, 0x0, 0x16, 0x20, 0x0,

    /* U+FE89 "ﺉ" */
    0x1, 0xbb, 0x0, 0x0, 0x0, 0x0, 0x66, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xeb, 0x20, 0x0, 0x0,
    0x0, 0x35, 0x20, 0x6d, 0xfe, 0x70, 0x0, 0x0,
    0x4f, 0x94, 0x7f, 0x40, 0x0, 0x5, 0xf7, 0x0,
    0x10, 0x12, 0x0, 0x9, 0xfe, 0x80, 0xc, 0xa0,
    0x0, 0x2, 0x7e, 0xb0, 0xf7, 0x0, 0x0, 0x0,
    0xaf, 0xd, 0xc0, 0x0, 0x2, 0x9f, 0x90, 0x4f,
    0xfc, 0xcf, 0xff, 0x90, 0x0, 0x28, 0xba, 0x85,
    0x10, 0x0,

    /* U+FE8A "ﺊ" */
    0x0, 0x3c, 0x90, 0x0, 0x0, 0x0, 0x0, 0xa,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6b, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x8, 0x95, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0x80,
    0xb, 0xb0, 0x0, 0x0, 0xcd, 0x7f, 0x40, 0xe7,
    0x0, 0x0, 0xb, 0xf4, 0x9e, 0x3e, 0x90, 0x0,
    0x0, 0x1d, 0xb0, 0xb7, 0x8f, 0x83, 0x12, 0x49,
    0xf6, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xd6, 0x0,
    0x0, 0x0, 0x3, 0x44, 0x20, 0x0, 0x0, 0x0,

    /* U+FE8B "ﺋ" */
    0x0, 0x8c, 0x50, 0xc, 0x0, 0x0, 0xcb, 0x70,
    0x7, 0x30, 0x0, 0x0, 0x0, 0x4, 0xb0, 0x0,
    0x6f, 0x0, 0x7, 0xf0, 0x17, 0xec, 0x2, 0xfc,
    0x20,

    /* U+FE8C "ﺌ" */
    0x0, 0x8c, 0x50, 0x0, 0xc0, 0x0, 0x0, 0xcb,
    0x70, 0x0, 0x73, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4b, 0x0, 0x0, 0x6f, 0x10, 0x0, 0x7f, 0x20,
    0x17, 0xef, 0xc7, 0x2f, 0xc8, 0xef,

    /* U+FE8D "ﺍ" */
    0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f,
    0x7f, 0x7f, 0x7f, 0x7f,

    /* U+FE8E "ﺎ" */
    0x7f, 0x0, 0x7, 0xf0, 0x0, 0x7f, 0x0, 0x7,
    0xf0, 0x0, 0x7f, 0x0, 0x7, 0xf0, 0x0, 0x7f,
    0x0, 0x7, 0xf0, 0x0, 0x7f, 0x0, 0x6, 0xf0,
    0x0, 0x3f, 0xb7, 0x0, 0x7e, 0xf0,

    /* U+FE8F "ﺏ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0x5b, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x9c, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xbd, 0xb0, 0x0, 0x0, 0x0,
    0x2a, 0xf4, 0x5f, 0xd8, 0x66, 0x8a, 0xdf, 0xe5,
    0x0, 0x3a, 0xef, 0xfe, 0xc9, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x89, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x10,
    0x0, 0x0, 0x0,

    /* U+FE90 "ﺐ" */
    0xbb, 0x0, 0x0, 0x0, 0x0, 0x9, 0xc0, 0xf,
    0x70, 0x0, 0x0, 0x0, 0x0, 0xae, 0x0, 0xdb,
    0x0, 0x0, 0x0, 0x2, 0xaf, 0xf1, 0x5, 0xfe,
    0x97, 0x78, 0xad, 0xfe, 0x7f, 0xc6, 0x3, 0xae,
    0xff, 0xec, 0x95, 0x0, 0x4e, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x89,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x10,
    0x0, 0x0, 0x0, 0x0,

    /* U+FE91 "ﺑ" */
    0x0, 0x38, 0x0, 0x6, 0xf1, 0x0, 0x7f, 0x1,
    0x7e, 0xc0, 0x2f, 0xd3, 0x0, 0x0, 0x0, 0x0,
    0x3e, 0x0, 0x0, 0x20,

    /* U+FE92 "ﺒ" */
    0x0, 0x38, 0x0, 0x0, 0x6f, 0x10, 0x0, 0x7f,
    0x20, 0x17, 0xef, 0xc7, 0x2f, 0xc8, 0xef, 0x0,
    0x0, 0x0, 0x0, 0x3e, 0x0, 0x0, 0x2, 0x0,

    /* U+FE93 "ﺓ" */
    0xf, 0x3f, 0x10, 0x0, 0x20, 0x20, 0x0, 0x0,
    0x10, 0x0, 0x2, 0xef, 0xd5, 0x0, 0xae, 0x5a,
    0xf7, 0xd, 0x90, 0x7, 0xf1, 0xe8, 0x0, 0x3f,
    0x3b, 0xe6, 0x6d, 0xe0, 0x2b, 0xfe, 0x91, 0x0,

    /* U+FE94 "ﺔ" */
    0x4, 0xe4, 0xd0, 0x0, 0x0, 0x20, 0x20, 0x0,
    0x0, 0x3, 0xb1, 0x0, 0x4, 0xcf, 0xf3, 0x0,
    0x5f, 0x82, 0xf6, 0x0, 0xc9, 0x0, 0xda, 0x0,
    0xae, 0xbd, 0xff, 0x85, 0x4, 0x75, 0x8, 0xfb,

    /* U+FE95 "ﺕ" */
    0x0, 0x0, 0x8a, 0x99, 0x0, 0x0, 0x5, 0x70,
    0x1, 0x11, 0x10, 0x0, 0x9c, 0xe8, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xce, 0xa0, 0x0, 0x0, 0x0,
    0x7, 0xf7, 0x7f, 0xd8, 0x66, 0x79, 0xcf, 0xf8,
    0x0, 0x4a, 0xef, 0xfe, 0xc9, 0x61, 0x0,

    /* U+FE96 "ﺖ" */
    0x0, 0x0, 0x8a, 0x99, 0x0, 0x0, 0x0, 0x5,
    0x70, 0x1, 0x11, 0x10, 0x0, 0x9c, 0x0, 0xe8,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xe0, 0xe, 0xa0,
    0x0, 0x0, 0x0, 0x2a, 0xff, 0x10, 0x7f, 0xd9,
    0x77, 0x8a, 0xdf, 0xf8, 0xfc, 0x60, 0x4a, 0xef,
    0xfe, 0xca, 0x51, 0x4, 0xed,

    /* U+FE97 "ﺗ" */
    0x3, 0xf4, 0xe0, 0x2, 0x2, 0x0, 0x0, 0x0,
    0x4, 0xc0, 0x0, 0x6f, 0x0, 0x7, 0xf0, 0x18,
    0xec, 0x2, 0xfc, 0x20,

    /* U+FE98 "ﺘ" */
    0x3, 0xf4, 0xe0, 0x0, 0x20, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x4c, 0x0, 0x0, 0x6f, 0x10, 0x0,
    0x7f, 0x20, 0x18, 0xef, 0xc7, 0x2f, 0xc8, 0xef,

    /* U+FE99 "ﺙ" */
    0x0, 0x0, 0x8, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x11, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8a,
    0x99, 0x0, 0x0, 0x5, 0x70, 0x1, 0x11, 0x10,
    0x0, 0x9c, 0xe8, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xce, 0xa0, 0x0, 0x0, 0x0, 0x7, 0xf7, 0x7f,
    0xd8, 0x66, 0x79, 0xcf, 0xf8, 0x0, 0x4a, 0xef,
    0xfe, 0xc9, 0x61, 0x0,

    /* U+FE9A "ﺚ" */
    0x0, 0x0, 0x8, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x11, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8a, 0x99, 0x0, 0x0, 0x0, 0x5, 0x70,
    0x1, 0x11, 0x10, 0x0, 0x9c, 0x0, 0xe8, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xe0, 0xe, 0xa0, 0x0,
    0x0, 0x0, 0x2a, 0xff, 0x10, 0x7f, 0xd9, 0x77,
    0x8a, 0xdf, 0xf8, 0xfc, 0x60, 0x4a, 0xef, 0xfe,
    0xca, 0x51, 0x4, 0xed,

    /* U+FE9B "ﺛ" */
    0x0, 0x3e, 0x0, 0x0, 0x20, 0x3, 0xf4, 0xe0,
    0x2, 0x2, 0x0, 0x0, 0x0, 0x4, 0xc0, 0x0,
    0x6f, 0x0, 0x7, 0xf0, 0x18, 0xec, 0x2, 0xfc,
    0x20,

    /* U+FE9C "ﺜ" */
    0x0, 0x3e, 0x0, 0x0, 0x2, 0x0, 0x3, 0xf4,
    0xe0, 0x0, 0x20, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x4c, 0x0, 0x0, 0x6f, 0x10, 0x0, 0x7f, 0x20,
    0x18, 0xef, 0xc7, 0x2f, 0xc8, 0xef,

    /* U+FE9D "ﺝ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7c, 0xff, 0xff,
    0xd8, 0x0, 0x98, 0x5b, 0xfe, 0xb6, 0x0, 0x0,
    0xbf, 0x70, 0x0, 0x0, 0x7, 0xf4, 0x0, 0x0,
    0x0, 0xf, 0x80, 0x0, 0x0, 0x0, 0x4f, 0x20,
    0x2, 0x0, 0x0, 0x6f, 0x0, 0xf, 0x30, 0x0,
    0x5f, 0x20, 0x0, 0x0, 0x0, 0x1f, 0x90, 0x0,
    0x0, 0x0, 0x7, 0xfa, 0x41, 0x14, 0xa4, 0x0,
    0x5d, 0xff, 0xff, 0xc2, 0x0, 0x0, 0x13, 0x31,
    0x0,

    /* U+FE9E "ﺞ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7c, 0xff, 0xff,
    0xd8, 0x0, 0x98, 0x6b, 0xff, 0xe6, 0x0, 0x0,
    0xcf, 0x78, 0xe0, 0x0, 0xa, 0xe2, 0x1, 0xf4,
    0x0, 0x2f, 0x60, 0x0, 0x9c, 0x0, 0x5f, 0x10,
    0x11, 0x1e, 0xc3, 0x6f, 0x10, 0x8a, 0x3, 0xd7,
    0x2f, 0x70, 0x0, 0x0, 0x0, 0x9, 0xf9, 0x31,
    0x13, 0x83, 0x0, 0x7d, 0xff, 0xff, 0xc2, 0x0,
    0x0, 0x13, 0x31, 0x0,

    /* U+FE9F "ﺟ" */
    0x0, 0x12, 0x10, 0x0, 0x0, 0x0, 0xcf, 0xfe,
    0xb7, 0x20, 0x0, 0x45, 0x68, 0xbe, 0xf9, 0x0,
    0x0, 0x0, 0x6e, 0xe6, 0x0, 0x0, 0x1a, 0xf9,
    0x0, 0x17, 0x8a, 0xfe, 0x40, 0x0, 0x2f, 0xec,
    0x71, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0x40, 0x0, 0x0, 0x0, 0x2,
    0x0, 0x0,

    /* U+FEA0 "ﺠ" */
    0x0, 0x12, 0x10, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xfe, 0xb7, 0x20, 0x0, 0x0, 0x45, 0x68, 0xbe,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x6e, 0xe6, 0x0,
    0x0, 0x0, 0x1a, 0xfd, 0xf2, 0x0, 0x17, 0x8a,
    0xfe, 0x41, 0xef, 0x83, 0x2f, 0xec, 0x71, 0x0,
    0x2b, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x0, 0x0, 0x0,

    /* U+FEA1 "ﺡ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9e, 0xed, 0xff,
    0xe9, 0x0, 0x41, 0x3d, 0xe8, 0x41, 0x0, 0x3,
    0xfa, 0x0, 0x0, 0x0, 0xc, 0xc0, 0x0, 0x0,
    0x0, 0x3f, 0x30, 0x0, 0x0, 0x0, 0x6f, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0x10, 0x0, 0x0, 0x0,
    0x1f, 0x80, 0x0, 0x0, 0x0, 0x8, 0xf9, 0x31,
    0x14, 0x94, 0x0, 0x6d, 0xff, 0xff, 0xc2, 0x0,
    0x0, 0x13, 0x31, 0x0,

    /* U+FEA2 "ﺢ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7c, 0xff, 0xff,
    0xd8, 0x0, 0x98, 0x5b, 0xff, 0xe6, 0x0, 0x0,
    0xbf, 0x78, 0xe0, 0x0, 0x7, 0xf4, 0x1, 0xf4,
    0x0, 0xf, 0x80, 0x0, 0x9c, 0x0, 0x4f, 0x20,
    0x0, 0x1e, 0xc3, 0x6f, 0x0, 0x0, 0x3, 0xd7,
    0x5f, 0x20, 0x0, 0x0, 0x0, 0x1f, 0x90, 0x0,
    0x0, 0x0, 0x7, 0xfa, 0x41, 0x14, 0xa4, 0x0,
    0x5d, 0xff, 0xff, 0xc2, 0x0, 0x0, 0x13, 0x31,
    0x0,

    /* U+FEA3 "ﺣ" */
    0x0, 0x12, 0x10, 0x0, 0x0, 0x0, 0xcf, 0xfe,
    0xb7, 0x20, 0x0, 0x45, 0x68, 0xbe, 0xf9, 0x0,
    0x0, 0x0, 0x6e, 0xe6, 0x0, 0x0, 0x1a, 0xf9,
    0x0, 0x17, 0x8a, 0xfe, 0x40, 0x0, 0x2f, 0xec,
    0x71, 0x0, 0x0,

    /* U+FEA4 "ﺤ" */
    0x0, 0x12, 0x10, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xfe, 0xb7, 0x20, 0x0, 0x0, 0x45, 0x68, 0xbe,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x6e, 0xe6, 0x0,
    0x0, 0x0, 0x1a, 0xfd, 0xf2, 0x0, 0x17, 0x8a,
    0xfe, 0x41, 0xef, 0x83, 0x2f, 0xec, 0x71, 0x0,
    0x2b, 0xf7,

    /* U+FEA5 "ﺥ" */
    0x0, 0x7, 0xa0, 0x0, 0x0, 0x0, 0x1, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9e,
    0xed, 0xff, 0xe9, 0x0, 0x41, 0x3d, 0xe8, 0x41,
    0x0, 0x3, 0xfa, 0x0, 0x0, 0x0, 0xc, 0xc0,
    0x0, 0x0, 0x0, 0x3f, 0x30, 0x0, 0x0, 0x0,
    0x6f, 0x0, 0x0, 0x0, 0x0, 0x5f, 0x10, 0x0,
    0x0, 0x0, 0x1f, 0x80, 0x0, 0x0, 0x0, 0x8,
    0xf9, 0x31, 0x14, 0x94, 0x0, 0x6d, 0xff, 0xff,
    0xc2, 0x0, 0x0, 0x13, 0x31, 0x0,

    /* U+FEA6 "ﺦ" */
    0x0, 0x7, 0xa0, 0x0, 0x0, 0x0, 0x1, 0x20,
    0x0, 0x0, 0x7c, 0xff, 0xff, 0xd8, 0x0, 0x98,
    0x5b, 0xff, 0xe6, 0x0, 0x0, 0xbf, 0x78, 0xe0,
    0x0, 0x7, 0xf4, 0x1, 0xf4, 0x0, 0xf, 0x80,
    0x0, 0x9c, 0x0, 0x4f, 0x20, 0x0, 0x1e, 0xc3,
    0x6f, 0x0, 0x0, 0x3, 0xd7, 0x5f, 0x20, 0x0,
    0x0, 0x0, 0x1f, 0x90, 0x0, 0x0, 0x0, 0x7,
    0xfa, 0x41, 0x14, 0xa4, 0x0, 0x5d, 0xff, 0xff,
    0xc2, 0x0, 0x0, 0x13, 0x31, 0x0,

    /* U+FEA7 "ﺧ" */
    0x0, 0x0, 0xe, 0x40, 0x0, 0x0, 0x0, 0x2,
    0x0, 0x0, 0x0, 0x12, 0x10, 0x0, 0x0, 0x0,
    0xcf, 0xfe, 0xb7, 0x20, 0x0, 0x45, 0x68, 0xbe,
    0xf9, 0x0, 0x0, 0x0, 0x6e, 0xe6, 0x0, 0x0,
    0x1a, 0xf9, 0x0, 0x17, 0x8a, 0xfe, 0x40, 0x0,
    0x2f, 0xec, 0x71, 0x0, 0x0,

    /* U+FEA8 "ﺨ" */
    0x0, 0x0, 0xe, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x0, 0x0, 0x0, 0x0, 0x12, 0x10, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xfe, 0xb7, 0x20, 0x0,
    0x0, 0x45, 0x68, 0xbe, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x6e, 0xe6, 0x0, 0x0, 0x0, 0x1a, 0xfd,
    0xf2, 0x0, 0x17, 0x8a, 0xfe, 0x41, 0xef, 0x83,
    0x2f, 0xec, 0x71, 0x0, 0x2b, 0xf7,

    /* U+FEA9 "ﺩ" */
    0x0, 0x2e, 0xa0, 0x0, 0x0, 0x3f, 0x70, 0x0,
    0x0, 0x9e, 0x0, 0x0, 0x5, 0xf2, 0x0, 0x0,
    0x7f, 0x10, 0x96, 0xaf, 0xb0, 0xd, 0xfe, 0x90,
    0x0,

    /* U+FEAA "ﺪ" */
    0x0, 0x2e, 0xa0, 0x0, 0x0, 0x0, 0x3f, 0x60,
    0x0, 0x0, 0x0, 0x9d, 0x0, 0x0, 0x0, 0x5,
    0xf2, 0x0, 0x0, 0x0, 0x7f, 0x60, 0x0, 0x66,
    0xaf, 0xef, 0x83, 0xf, 0xfe, 0x80, 0xaf, 0x90,

    /* U+FEAB "ﺫ" */
    0x0, 0x7b, 0x0, 0x0, 0x1, 0x10, 0x0, 0x0,
    0x13, 0x0, 0x0, 0x1, 0xdc, 0x0, 0x0, 0x2,
    0xf8, 0x0, 0x0, 0x8, 0xe0, 0x0, 0x0, 0x4f,
    0x20, 0x0, 0x8, 0xf1, 0x9, 0x7a, 0xfb, 0x0,
    0xdf, 0xe8, 0x0,

    /* U+FEAC "ﺬ" */
    0x0, 0x7b, 0x0, 0x0, 0x0, 0x1, 0x10, 0x0,
    0x0, 0x0, 0x12, 0x0, 0x0, 0x0, 0x1, 0xdb,
    0x0, 0x0, 0x0, 0x2, 0xf6, 0x0, 0x0, 0x0,
    0x9, 0xe0, 0x0, 0x0, 0x0, 0x4f, 0x20, 0x0,
    0x0, 0x8, 0xf6, 0x0, 0x6, 0x6a, 0xfe, 0xf8,
    0x30, 0xff, 0xe8, 0xa, 0xf9,

    /* U+FEAD "ﺭ" */
    0x0, 0x0, 0x0, 0xa5, 0x0, 0x0, 0x0, 0xba,
    0x0, 0x0, 0x0, 0xab, 0x0, 0x0, 0x0, 0xca,
    0x0, 0x0, 0x3, 0xf6, 0x0, 0x0, 0x4e, 0xd0,
    0x14, 0x6c, 0xfd, 0x20, 0xaf, 0xfc, 0x60, 0x0,
    0x33, 0x10, 0x0, 0x0,

    /* U+FEAE "ﺮ" */
    0x0, 0x0, 0x0, 0x73, 0x0, 0x0, 0x0, 0x0,
    0xc9, 0x0, 0x0, 0x0, 0x0, 0xad, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xb7, 0x0, 0x0, 0x0, 0xed,
    0xef, 0x0, 0x0, 0x6, 0xf3, 0x0, 0x0, 0x0,
    0x6f, 0xb0, 0x0, 0x13, 0x6c, 0xfb, 0x0, 0x0,
    0xaf, 0xfb, 0x50, 0x0, 0x0, 0x33, 0x0, 0x0,
    0x0, 0x0,

    /* U+FEAF "ﺯ" */
    0x0, 0x0, 0x1, 0xf0, 0x0, 0x0, 0x0, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc7,
    0x0, 0x0, 0x0, 0xbb, 0x0, 0x0, 0x0, 0xab,
    0x0, 0x0, 0x0, 0xda, 0x0, 0x0, 0x4, 0xf5,
    0x0, 0x0, 0x4f, 0xd0, 0x14, 0x6c, 0xfd, 0x20,
    0xaf, 0xfc, 0x60, 0x0, 0x33, 0x10, 0x0, 0x0,

    /* U+FEB0 "ﺰ" */
    0x0, 0x0, 0x1, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe8, 0x0, 0x0, 0x0, 0x0, 0xbd,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xa7, 0x0, 0x0,
    0x0, 0xdd, 0xef, 0x0, 0x0, 0x5, 0xf3, 0x0,
    0x0, 0x0, 0x4f, 0xb0, 0x0, 0x13, 0x6b, 0xfb,
    0x0, 0x0, 0xaf, 0xfb, 0x50, 0x0, 0x0, 0x33,
    0x0, 0x0, 0x0, 0x0,

    /* U+FEB1 "ﺱ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xf3, 0x0, 0x0, 0x0, 0xd, 0x90, 0x4, 0xf2,
    0x3, 0xf3, 0x0, 0x0, 0x0, 0x9, 0xd0, 0x6,
    0xf3, 0x4, 0xf3, 0x5f, 0x10, 0x0, 0x6, 0xf3,
    0xa, 0xf8, 0x6, 0xf1, 0xcb, 0x0, 0x0, 0x6,
    0xfe, 0xbf, 0xcf, 0xbf, 0xc0, 0xe8, 0x0, 0x0,
    0x9, 0xfc, 0xfa, 0x1a, 0xfb, 0x10, 0xf8, 0x0,
    0x0, 0x3f, 0xa0, 0x0, 0x0, 0x0, 0x0, 0xbe,
    0x52, 0x27, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x2d, 0xff, 0xff, 0xb2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x24, 0x41, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+FEB2 "ﺲ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x7, 0x40, 0x2,
    0x81, 0x3, 0xf3, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xb0, 0x5, 0xf2, 0x4, 0xf3, 0x0, 0x5f, 0x10,
    0x0, 0x7, 0xf1, 0x8, 0xf6, 0x5, 0xf6, 0x0,
    0xbc, 0x0, 0x0, 0x5, 0xfc, 0x8f, 0xde, 0x7d,
    0xff, 0x83, 0xd9, 0x0, 0x0, 0x6, 0xfd, 0xfa,
    0x1b, 0xfc, 0x6c, 0xf9, 0xf7, 0x0, 0x0, 0xa,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe8, 0x0,
    0x0, 0x4f, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0x63, 0x27, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1b, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x13, 0x41, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+FEB3 "ﺳ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xf1, 0x0,
    0x6, 0x40, 0x3, 0x80, 0x5, 0xf1, 0x0, 0xd,
    0x90, 0x7, 0xf0, 0x6, 0xf1, 0x0, 0xf, 0xd0,
    0xa, 0xf4, 0x7, 0xf0, 0x17, 0xcf, 0xfa, 0x9f,
    0xee, 0x7e, 0xa0, 0x2f, 0xe6, 0x7e, 0xe9, 0x2c,
    0xfa, 0x10,

    /* U+FEB4 "ﺴ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xf1, 0x0,
    0x0, 0xd, 0x90, 0x6, 0xf0, 0x5, 0xf1, 0x0,
    0x0, 0xd, 0xa0, 0x8, 0xf1, 0x6, 0xf1, 0x0,
    0x0, 0x2f, 0xe0, 0xc, 0xf6, 0x8, 0xf6, 0x0,
    0x1b, 0xef, 0xfd, 0xdf, 0xcf, 0xbf, 0xff, 0xb4,
    0x2f, 0xe4, 0x4e, 0xe8, 0x1b, 0xfa, 0x3c, 0xf7,

    /* U+FEB5 "ﺵ" */
    0x0, 0x0, 0x0, 0x0, 0x3, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3e, 0x4e,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x2, 0x0, 0x1, 0x61, 0x0, 0x0, 0x0, 0x2,
    0x10, 0x0, 0x20, 0x3, 0xf3, 0x0, 0x0, 0x0,
    0xc, 0x90, 0x5, 0xf2, 0x3, 0xf3, 0x1, 0x0,
    0x0, 0x8, 0xd0, 0x6, 0xf3, 0x4, 0xf2, 0x6f,
    0x10, 0x0, 0x6, 0xf3, 0xa, 0xf9, 0x7, 0xf1,
    0xca, 0x0, 0x0, 0x6, 0xff, 0xbf, 0xcf, 0xbf,
    0xb0, 0xe8, 0x0, 0x0, 0xa, 0xfc, 0xfa, 0x1a,
    0xfb, 0x10, 0xe8, 0x0, 0x0, 0x4f, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0xbe, 0x52, 0x27, 0xfe, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x2d, 0xff, 0xff, 0xb2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x24, 0x41,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+FEB6 "ﺶ" */
    0x0, 0x0, 0x0, 0x0, 0x3, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3e, 0x4e, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x2, 0x0, 0x1, 0x81, 0x0,
    0x0, 0x0, 0x0, 0x6, 0x30, 0x1, 0x61, 0x3,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0xb, 0xb0, 0x5,
    0xf2, 0x3, 0xf3, 0x0, 0x2a, 0x10, 0x0, 0x7,
    0xf1, 0x8, 0xf6, 0x5, 0xf5, 0x0, 0x9d, 0x0,
    0x0, 0x5, 0xfc, 0x8f, 0xee, 0x7d, 0xfe, 0x73,
    0xd9, 0x0, 0x0, 0x6, 0xfd, 0xfb, 0x1b, 0xfc,
    0x6c, 0xf9, 0xf7, 0x0, 0x0, 0xa, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe8, 0x0, 0x0, 0x4f,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0x63,
    0x27, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1c, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x13, 0x41, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+FEB7 "ﺷ" */
    0x0, 0x0, 0x0, 0xe, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe3, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x20, 0x20, 0x2, 0x80, 0x0, 0x5, 0x40, 0x2,
    0x60, 0x5, 0xf1, 0x0, 0xd, 0x90, 0x7, 0xf0,
    0x5, 0xf1, 0x0, 0xf, 0xd0, 0xa, 0xf4, 0x7,
    0xf0, 0x17, 0xcf, 0xfa, 0x9f, 0xee, 0x7e, 0xb0,
    0x2f, 0xe6, 0x7e, 0xe9, 0x2c, 0xfb, 0x10,

    /* U+FEB8 "ﺸ" */
    0x0, 0x0, 0x0, 0xe, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe3, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x20, 0x20, 0x2, 0x60, 0x0,
    0x0, 0x2, 0x10, 0x1, 0x20, 0x5, 0xf1, 0x0,
    0x0, 0xd, 0x90, 0x7, 0xf0, 0x5, 0xf1, 0x0,
    0x0, 0xd, 0xa0, 0x8, 0xf1, 0x6, 0xf1, 0x0,
    0x0, 0x2f, 0xe1, 0xc, 0xf7, 0x9, 0xf7, 0x0,
    0x1b, 0xef, 0xfd, 0xcf, 0xcf, 0xbf, 0xff, 0xb4,
    0x2f, 0xe4, 0x4e, 0xe8, 0x1b, 0xfa, 0x3c, 0xf7,

    /* U+FEB9 "ﺹ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xaf,
    0xfc, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3e,
    0xd6, 0x5c, 0xe0, 0x0, 0x0, 0x0, 0xa, 0x43,
    0xfb, 0x0, 0x5, 0xf1, 0x4e, 0x10, 0x0, 0xc,
    0xce, 0xc0, 0x0, 0x2d, 0xf0, 0xac, 0x0, 0x0,
    0xa, 0xff, 0x87, 0x8b, 0xff, 0x50, 0xd9, 0x0,
    0x0, 0xb, 0xde, 0xff, 0xed, 0x81, 0x0, 0xf7,
    0x0, 0x0, 0xd, 0x90, 0x0, 0x0, 0x0, 0x0,
    0xe8, 0x0, 0x0, 0x5f, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0x63, 0x28, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1c, 0xff, 0xff, 0xa1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x13, 0x41, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+FEBA "ﺺ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1a,
    0xff, 0xc3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3e, 0xd6, 0x5c, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0xa4, 0x3f, 0xb0, 0x0, 0x5f, 0x10, 0x4e, 0x10,
    0x0, 0xc, 0xce, 0xc0, 0x0, 0x2d, 0xf0, 0xa,
    0xc0, 0x0, 0x0, 0xaf, 0xf8, 0x78, 0xbf, 0xff,
    0x95, 0xd9, 0x0, 0x0, 0xb, 0xde, 0xff, 0xed,
    0x83, 0xaf, 0xcf, 0x70, 0x0, 0x0, 0xd9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe8, 0x0, 0x0, 0x5f,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x9, 0xf6, 0x32,
    0x8f, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c,
    0xff, 0xff, 0xa1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x34, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+FEBB "ﺻ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xfe, 0x50, 0x0, 0x4,
    0x30, 0x1d, 0xf7, 0x4a, 0xf2, 0x0, 0xd, 0xa1,
    0xdd, 0x20, 0x1, 0xf5, 0x0, 0x1f, 0xec, 0xe1,
    0x0, 0x1a, 0xf3, 0x17, 0xcf, 0xff, 0xa7, 0x8a,
    0xff, 0x80, 0x2f, 0xe6, 0x6e, 0xff, 0xfd, 0xa3,
    0x0,

    /* U+FEBC "ﺼ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xfd, 0x60, 0x0,
    0x0, 0x4, 0x30, 0x1d, 0xf7, 0x4a, 0xf2, 0x0,
    0x0, 0xd, 0xa1, 0xdd, 0x20, 0x1, 0xf6, 0x0,
    0x0, 0x1f, 0xec, 0xe1, 0x0, 0x1a, 0xf4, 0x0,
    0x17, 0xcf, 0xff, 0xa7, 0x8a, 0xff, 0xfb, 0x70,
    0x2f, 0xe6, 0x6e, 0xff, 0xfd, 0xa4, 0x7e, 0xf0,

    /* U+FEBD "ﺽ" */
    0x0, 0x0, 0x0, 0x0, 0xe, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xaf,
    0xfc, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3e,
    0xd6, 0x5c, 0xe0, 0x0, 0x0, 0x0, 0xa, 0x43,
    0xfb, 0x0, 0x5, 0xf1, 0x4e, 0x10, 0x0, 0xc,
    0xce, 0xc0, 0x0, 0x2d, 0xf0, 0xac, 0x0, 0x0,
    0xa, 0xff, 0x87, 0x8b, 0xff, 0x50, 0xd9, 0x0,
    0x0, 0xb, 0xde, 0xff, 0xed, 0x81, 0x0, 0xf7,
    0x0, 0x0, 0xd, 0x90, 0x0, 0x0, 0x0, 0x0,
    0xe8, 0x0, 0x0, 0x5f, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0x63, 0x28, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1c, 0xff, 0xff, 0xa1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x13, 0x41, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+FEBE "ﺾ" */
    0x0, 0x0, 0x0, 0x0, 0xe, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1a,
    0xff, 0xc3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3e, 0xd6, 0x5c, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0xa4, 0x3f, 0xb0, 0x0, 0x5f, 0x10, 0x4e, 0x10,
    0x0, 0xc, 0xce, 0xc0, 0x0, 0x2d, 0xf0, 0xa,
    0xc0, 0x0, 0x0, 0xaf, 0xf8, 0x78, 0xbf, 0xff,
    0x95, 0xd9, 0x0, 0x0, 0xb, 0xde, 0xff, 0xed,
    0x83, 0xaf, 0xcf, 0x70, 0x0, 0x0, 0xd9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe8, 0x0, 0x0, 0x5f,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x9, 0xf6, 0x32,
    0x8f, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c,
    0xff, 0xff, 0xa1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x34, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+FEBF "ﺿ" */
    0x0, 0x0, 0x4c, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xfe, 0x50, 0x0, 0x4,
    0x30, 0x1d, 0xf7, 0x4a, 0xf2, 0x0, 0xd, 0xa1,
    0xdd, 0x20, 0x1, 0xf5, 0x0, 0x1f, 0xec, 0xe1,
    0x0, 0x1a, 0xf3, 0x17, 0xcf, 0xff, 0xa7, 0x8a,
    0xff, 0x80, 0x2f, 0xe6, 0x6e, 0xff, 0xfd, 0xa3,
    0x0,

    /* U+FEC0 "ﻀ" */
    0x0, 0x0, 0x4c, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xfd, 0x60, 0x0,
    0x0, 0x4, 0x30, 0x1d, 0xf7, 0x4a, 0xf2, 0x0,
    0x0, 0xd, 0xa1, 0xdd, 0x20, 0x1, 0xf6, 0x0,
    0x0, 0x1f, 0xec, 0xe1, 0x0, 0x1a, 0xf4, 0x0,
    0x17, 0xcf, 0xff, 0xa7, 0x8a, 0xff, 0xfb, 0x70,
    0x2f, 0xe6, 0x6e, 0xff, 0xfd, 0xa4, 0x7e, 0xf0,

    /* U+FEC1 "ﻁ" */
    0x0, 0xf, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0x70, 0x6, 0xdf, 0xf9, 0x0, 0x0, 0xf7,
    0xb, 0xfa, 0x46, 0xf7, 0x0, 0xf, 0x7b, 0xf6,
    0x0, 0xc, 0xa0, 0x0, 0xfd, 0xf5, 0x0, 0x6,
    0xf8, 0x67, 0x7f, 0xfd, 0x77, 0x9d, 0xfc, 0x1e,
    0xff, 0xff, 0xff, 0xfe, 0xb5, 0x0,

    /* U+FEC2 "ﻂ" */
    0x0, 0xf, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0x70,
    0x6, 0xdf, 0xf9, 0x0, 0x0, 0x0, 0xf7, 0xb,
    0xfa, 0x46, 0xf7, 0x0, 0x0, 0xf, 0x7b, 0xf6,
    0x0, 0xc, 0xb0, 0x0, 0x0, 0xfd, 0xf5, 0x0,
    0x6, 0xf9, 0x0, 0x67, 0x7f, 0xfd, 0x77, 0x9d,
    0xff, 0xe7, 0x2e, 0xff, 0xff, 0xff, 0xfe, 0xb6,
    0x4d, 0xf5,

    /* U+FEC3 "ﻃ" */
    0x0, 0x1f, 0x60, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0x60, 0x6, 0xef, 0xf9, 0x0, 0x1, 0xf6,
    0xc, 0xf9, 0x47, 0xf6, 0x0, 0x1f, 0x6b, 0xf5,
    0x0, 0xd, 0x90, 0x1, 0xfd, 0xf4, 0x0, 0x7,
    0xf7, 0x17, 0x7f, 0xfd, 0x77, 0x9e, 0xfc, 0x2,
    0xff, 0xff, 0xff, 0xfd, 0xb5, 0x0,

    /* U+FEC4 "ﻄ" */
    0x0, 0x1f, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0x60,
    0x6, 0xef, 0xf9, 0x0, 0x0, 0x1, 0xf6, 0xc,
    0xf9, 0x47, 0xf6, 0x0, 0x0, 0x1f, 0x6b, 0xf5,
    0x0, 0xd, 0xa0, 0x0, 0x1, 0xfd, 0xf4, 0x0,
    0x7, 0xf8, 0x0, 0x17, 0x7f, 0xfd, 0x77, 0x9e,
    0xff, 0xd7, 0x12, 0xff, 0xff, 0xff, 0xfe, 0xb5,
    0x5d, 0xf4,

    /* U+FEC5 "ﻅ" */
    0x0, 0xf, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0x70, 0x5c, 0x0, 0x0,
    0x0, 0x0, 0xf7, 0x1, 0x20, 0x0, 0x0, 0x0,
    0xf, 0x70, 0x6, 0xdf, 0xf9, 0x0, 0x0, 0xf7,
    0xb, 0xfa, 0x46, 0xf7, 0x0, 0xf, 0x7b, 0xf6,
    0x0, 0xc, 0xa0, 0x0, 0xfd, 0xf5, 0x0, 0x6,
    0xf8, 0x67, 0x7f, 0xfd, 0x77, 0x9d, 0xfc, 0x1e,
    0xff, 0xff, 0xff, 0xfe, 0xb5, 0x0,

    /* U+FEC6 "ﻆ" */
    0x0, 0xf, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0x70, 0x5c, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf7,
    0x1, 0x20, 0x0, 0x0, 0x0, 0x0, 0xf, 0x70,
    0x6, 0xdf, 0xf9, 0x0, 0x0, 0x0, 0xf7, 0xb,
    0xfa, 0x46, 0xf7, 0x0, 0x0, 0xf, 0x7b, 0xf6,
    0x0, 0xc, 0xb0, 0x0, 0x0, 0xfd, 0xf5, 0x0,
    0x6, 0xf9, 0x0, 0x67, 0x7f, 0xfd, 0x77, 0x9d,
    0xff, 0xe7, 0x2e, 0xff, 0xff, 0xff, 0xfe, 0xb6,
    0x4d, 0xf5,

    /* U+FEC7 "ﻇ" */
    0x0, 0x1f, 0x60, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0x60, 0x6b, 0x0, 0x0,
    0x0, 0x1, 0xf6, 0x1, 0x20, 0x0, 0x0, 0x0,
    0x1f, 0x60, 0x6, 0xef, 0xf9, 0x0, 0x1, 0xf6,
    0xc, 0xf9, 0x47, 0xf6, 0x0, 0x1f, 0x6b, 0xf5,
    0x0, 0xd, 0x90, 0x1, 0xfd, 0xf4, 0x0, 0x7,
    0xf7, 0x17, 0x7f, 0xfd, 0x77, 0x9e, 0xfc, 0x2,
    0xff, 0xff, 0xff, 0xfd, 0xb5, 0x0,

    /* U+FEC8 "ﻈ" */
    0x0, 0x1f, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0x60, 0x6b, 0x0, 0x0, 0x0, 0x0, 0x1, 0xf6,
    0x1, 0x20, 0x0, 0x0, 0x0, 0x0, 0x1f, 0x60,
    0x6, 0xef, 0xf9, 0x0, 0x0, 0x1, 0xf6, 0xc,
    0xf9, 0x47, 0xf6, 0x0, 0x0, 0x1f, 0x6b, 0xf5,
    0x0, 0xd, 0xa0, 0x0, 0x1, 0xfd, 0xf4, 0x0,
    0x7, 0xf8, 0x0, 0x17, 0x7f, 0xfd, 0x77, 0x9e,
    0xff, 0xd7, 0x12, 0xff, 0xff, 0xff, 0xfe, 0xb5,
    0x5d, 0xf4,

    /* U+FEC9 "ﻉ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xef,
    0x20, 0x0, 0x0, 0xce, 0x75, 0x0, 0x0, 0x4,
    0xf2, 0x0, 0x0, 0x0, 0x4, 0xf4, 0x26, 0xbd,
    0x0, 0x0, 0xaf, 0xff, 0xd9, 0x0, 0x0, 0xbf,
    0x92, 0x0, 0x0, 0x9, 0xf4, 0x0, 0x0, 0x0,
    0xf, 0x80, 0x0, 0x0, 0x0, 0xf, 0x50, 0x0,
    0x0, 0x0, 0xe, 0x90, 0x0, 0x0, 0x0, 0x6,
    0xf9, 0x31, 0x13, 0x95, 0x0, 0x6d, 0xff, 0xff,
    0xd3, 0x0, 0x0, 0x13, 0x31, 0x0,

    /* U+FECA "ﻊ" */
    0x0, 0x1, 0x10, 0x0, 0x0, 0x1, 0xcf, 0xfc,
    0x20, 0x0, 0x8, 0xf9, 0x8f, 0x90, 0x0, 0x4,
    0xfc, 0xdf, 0x50, 0x0, 0x1, 0xdf, 0xf4, 0x0,
    0x0, 0xa, 0xf7, 0xef, 0x97, 0x40, 0xf, 0x80,
    0x18, 0xdf, 0xa0, 0xf, 0x50, 0x0, 0x0, 0x0,
    0xd, 0xa0, 0x0, 0x0, 0x0, 0x6, 0xfa, 0x31,
    0x13, 0x96, 0x0, 0x5d, 0xff, 0xff, 0xd3, 0x0,
    0x0, 0x13, 0x31, 0x0,

    /* U+FECB "ﻋ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x19, 0xef,
    0x20, 0x0, 0xc, 0xe7, 0x40, 0x0, 0x3, 0xf3,
    0x0, 0x0, 0x0, 0x5f, 0x0, 0x0, 0x0, 0x2,
    0xf8, 0x1, 0x8d, 0x0, 0x5, 0xff, 0xfe, 0x61,
    0x79, 0xdf, 0xd5, 0x0, 0x2f, 0xd9, 0x30, 0x0,
    0x0,

    /* U+FECC "ﻌ" */
    0x0, 0x0, 0x10, 0x0, 0x0, 0x3, 0xdf, 0xfb,
    0x10, 0x0, 0xbf, 0x7a, 0xf7, 0x0, 0x5, 0xfa,
    0xde, 0x20, 0x0, 0x8, 0xff, 0x40, 0x1, 0x79,
    0xfd, 0xee, 0x86, 0x2f, 0xe8, 0x2, 0xaf, 0xe0,

    /* U+FECD "ﻍ" */
    0x0, 0xf, 0x10, 0x0, 0x0, 0x0, 0x2, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xef, 0x20, 0x0, 0x0,
    0xce, 0x75, 0x0, 0x0, 0x4, 0xf2, 0x0, 0x0,
    0x0, 0x4, 0xf4, 0x26, 0xbd, 0x0, 0x0, 0xaf,
    0xff, 0xd9, 0x0, 0x0, 0xbf, 0x92, 0x0, 0x0,
    0x9, 0xf4, 0x0, 0x0, 0x0, 0xf, 0x80, 0x0,
    0x0, 0x0, 0xf, 0x50, 0x0, 0x0, 0x0, 0xe,
    0x90, 0x0, 0x0, 0x0, 0x6, 0xf9, 0x31, 0x13,
    0x95, 0x0, 0x6d, 0xff, 0xff, 0xd3, 0x0, 0x0,
    0x13, 0x31, 0x0,

    /* U+FECE "ﻎ" */
    0x0, 0xa, 0x70, 0x0, 0x0, 0x0, 0x1, 0x10,
    0x0, 0x0, 0x0, 0x1, 0x10, 0x0, 0x0, 0x1,
    0xcf, 0xfc, 0x20, 0x0, 0x8, 0xf9, 0x8f, 0x90,
    0x0, 0x4, 0xfc, 0xdf, 0x50, 0x0, 0x1, 0xdf,
    0xf4, 0x0, 0x0, 0xa, 0xf7, 0xef, 0x97, 0x40,
    0xf, 0x80, 0x18, 0xdf, 0xa0, 0xf, 0x50, 0x0,
    0x0, 0x0, 0xd, 0xa0, 0x0, 0x0, 0x0, 0x6,
    0xfa, 0x31, 0x13, 0x96, 0x0, 0x5d, 0xff, 0xff,
    0xd3, 0x0, 0x0, 0x13, 0x31, 0x0,

    /* U+FECF "ﻏ" */
    0x0, 0x0, 0xf1, 0x0, 0x0, 0x0, 0x2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x19,
    0xef, 0x20, 0x0, 0xc, 0xe7, 0x40, 0x0, 0x3,
    0xf3, 0x0, 0x0, 0x0, 0x5f, 0x0, 0x0, 0x0,
    0x2, 0xf8, 0x1, 0x8d, 0x0, 0x5, 0xff, 0xfe,
    0x61, 0x79, 0xdf, 0xd5, 0x0, 0x2f, 0xd9, 0x30,
    0x0, 0x0,

    /* U+FED0 "ﻐ" */
    0x0, 0x0, 0xb7, 0x0, 0x0, 0x0, 0x1, 0x10,
    0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x3, 0xdf,
    0xfb, 0x10, 0x0, 0xbf, 0x7a, 0xf7, 0x0, 0x5,
    0xfa, 0xde, 0x20, 0x0, 0x8, 0xff, 0x40, 0x1,
    0x79, 0xfd, 0xee, 0x86, 0x2f, 0xe8, 0x2, 0xaf,
    0xe0,

    /* U+FED1 "ﻑ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xfd, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xf9, 0xee, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xda, 0x5, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xd1, 0x8f, 0x3b, 0xa0, 0x0, 0x0,
    0x0, 0x4f, 0xfe, 0xf1, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xbc, 0xb, 0xf8, 0x32, 0x12, 0x24,
    0x6a, 0xfe, 0x20, 0x9, 0xff, 0xff, 0xff, 0xff,
    0xc7, 0x0, 0x0, 0x0, 0x24, 0x44, 0x31, 0x0,
    0x0, 0x0,

    /* U+FED2 "ﻒ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xaf, 0xc2, 0x0,
    0x11, 0x0, 0x0, 0x0, 0xb, 0xf9, 0xee, 0x0,
    0xca, 0x0, 0x0, 0x0, 0xd, 0xa0, 0x5f, 0x20,
    0xf8, 0x0, 0x0, 0x0, 0x9, 0xe1, 0x6f, 0x0,
    0xaf, 0x83, 0x22, 0x22, 0x35, 0xfd, 0xfd, 0x75,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xdf, 0xfb,
    0x0, 0x3, 0x44, 0x43, 0x32, 0x0, 0x0, 0x0,

    /* U+FED3 "ﻓ" */
    0x0, 0x0, 0xa7, 0x0, 0x0, 0x0, 0x11, 0x0,
    0x0, 0x1, 0x76, 0x0, 0x0, 0x2e, 0xff, 0xc0,
    0x0, 0x8f, 0x24, 0xf6, 0x0, 0x8f, 0x23, 0xf7,
    0x0, 0x1d, 0xff, 0xf6, 0x0, 0x0, 0x27, 0xf3,
    0x17, 0x77, 0xaf, 0xb0, 0x2f, 0xff, 0xe9, 0x10,

    /* U+FED4 "ﻔ" */
    0x0, 0x0, 0x8a, 0x0, 0x0, 0x0, 0x0, 0x11,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0x90, 0x0, 0x0, 0x3f, 0xb9, 0xf4,
    0x0, 0x0, 0x5f, 0x20, 0xf7, 0x0, 0x0, 0x2f,
    0x86, 0xf4, 0x0, 0x17, 0x7e, 0xff, 0xf7, 0x71,
    0x2f, 0xfe, 0xba, 0xef, 0xf4,

    /* U+FED5 "ﻕ" */
    0x0, 0x0, 0x0, 0x5c, 0x6c, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x12, 0x0, 0x0, 0x0, 0x0, 0x1b,
    0xfc, 0x10, 0x0, 0x0, 0x0, 0xaf, 0x9e, 0xa0,
    0x0, 0x0, 0x0, 0xf9, 0x7, 0xf0, 0x0, 0x0,
    0x0, 0xdc, 0x19, 0xf2, 0x3, 0x50, 0x0, 0x5f,
    0xfe, 0xf3, 0xc, 0x90, 0x0, 0x1, 0x24, 0xf1,
    0xf, 0x50, 0x0, 0x0, 0x9, 0xd0, 0x2f, 0x40,
    0x0, 0x0, 0x3f, 0x60, 0xf, 0x70, 0x0, 0x4,
    0xeb, 0x0, 0xb, 0xe5, 0x35, 0xaf, 0xb0, 0x0,
    0x1, 0xdf, 0xff, 0xe7, 0x0, 0x0, 0x0, 0x3,
    0x43, 0x0, 0x0, 0x0,

    /* U+FED6 "ﻖ" */
    0x0, 0x0, 0x0, 0xf, 0x3f, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1c, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0x9e, 0xa0, 0x0, 0x0, 0x0, 0x0, 0xf8,
    0x6, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xed, 0x33,
    0xf2, 0x0, 0x9, 0x70, 0x0, 0x8f, 0xfe, 0xf8,
    0x73, 0xf, 0x60, 0x0, 0x8, 0xff, 0xff, 0xf8,
    0x2f, 0x40, 0x0, 0x0, 0xc, 0xd0, 0x0, 0xf,
    0x70, 0x0, 0x0, 0x9f, 0x40, 0x0, 0xb, 0xe6,
    0x23, 0x6d, 0xf6, 0x0, 0x0, 0x1, 0xcf, 0xff,
    0xfc, 0x30, 0x0, 0x0, 0x0, 0x2, 0x44, 0x10,
    0x0, 0x0, 0x0,

    /* U+FED7 "ﻗ" */
    0x0, 0xa, 0x8b, 0x70, 0x0, 0x1, 0x11, 0x10,
    0x0, 0x1, 0x76, 0x0, 0x0, 0x2e, 0xff, 0xc0,
    0x0, 0x8f, 0x24, 0xf6, 0x0, 0x8f, 0x23, 0xf7,
    0x0, 0x1d, 0xff, 0xf6, 0x0, 0x0, 0x27, 0xf3,
    0x17, 0x77, 0xaf, 0xb0, 0x2f, 0xff, 0xe9, 0x10,

    /* U+FED8 "ﻘ" */
    0x0, 0x8, 0x99, 0x80, 0x0, 0x0, 0x1, 0x11,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0x90, 0x0, 0x0, 0x3f, 0xb9, 0xf4,
    0x0, 0x0, 0x5f, 0x20, 0xf7, 0x0, 0x0, 0x2f,
    0x86, 0xf4, 0x0, 0x17, 0x7e, 0xff, 0xf7, 0x71,
    0x2f, 0xfe, 0xba, 0xef, 0xf4,

    /* U+FED9 "ﻙ" */
    0x0, 0x0, 0x0, 0x0, 0xe, 0x90, 0x0, 0x0,
    0x0, 0x0, 0xe9, 0x0, 0x0, 0x0, 0x0, 0xe,
    0x90, 0x0, 0x2, 0x96, 0x0, 0xe9, 0x0, 0x0,
    0x76, 0x0, 0xe, 0x90, 0x0, 0x0, 0x58, 0x0,
    0xe9, 0x0, 0x2, 0xab, 0x20, 0xe, 0x90, 0x0,
    0x0, 0x0, 0x0, 0xe8, 0x54, 0x0, 0x0, 0x0,
    0xf, 0x7d, 0xa0, 0x0, 0x0, 0x9, 0xf3, 0x8f,
    0xb7, 0x55, 0x8e, 0xf8, 0x0, 0x5c, 0xef, 0xfe,
    0xb4, 0x0,

    /* U+FEDA "ﻚ" */
    0x0, 0x0, 0x0, 0x0, 0xe, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0x90, 0x0, 0x0, 0x2, 0x96, 0x0,
    0xe9, 0x0, 0x0, 0x0, 0x76, 0x0, 0xe, 0x90,
    0x0, 0x0, 0x0, 0x58, 0x0, 0xe9, 0x0, 0x0,
    0x2, 0xab, 0x20, 0xe, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe9, 0x0, 0x22, 0x0, 0x0, 0x0,
    0xf, 0x90, 0xd, 0xa0, 0x0, 0x0, 0x9, 0xfa,
    0x0, 0x9f, 0xb7, 0x55, 0x8e, 0xfd, 0xf8, 0x40,
    0x6c, 0xff, 0xfe, 0xa4, 0x9, 0xfa,

    /* U+FEDB "ﻛ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xb9, 0x0, 0x1, 0x7d, 0xfc, 0x40, 0x8, 0xff,
    0x93, 0x0, 0x3, 0xf9, 0x10, 0x0, 0x0, 0x3f,
    0x60, 0x0, 0x0, 0x0, 0xaf, 0x30, 0x0, 0x0,
    0x0, 0xce, 0x10, 0x0, 0x0, 0x1, 0xec, 0x0,
    0x0, 0x0, 0x5, 0xf4, 0x0, 0x0, 0x0, 0x3f,
    0x50, 0x1, 0x77, 0x7d, 0xf1, 0x0, 0x2f, 0xff,
    0xd4, 0x0, 0x0,

    /* U+FEDC "ﻜ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4b, 0x90, 0x0, 0x1, 0x7d, 0xfc, 0x40, 0x0,
    0x8f, 0xf9, 0x30, 0x0, 0x3, 0xf9, 0x10, 0x0,
    0x0, 0x3, 0xf6, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0x30, 0x0, 0x0, 0x0, 0xc, 0xe1, 0x0, 0x0,
    0x0, 0x1, 0xec, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xa0, 0x0, 0x0, 0x0, 0x3f, 0xf7, 0x0, 0x17,
    0x77, 0xdf, 0xaf, 0x97, 0x2f, 0xff, 0xd4, 0x8,
    0xef,

    /* U+FEDD "ﻝ" */
    0x0, 0x0, 0x0, 0x4, 0xf3, 0x0, 0x0, 0x0,
    0x4, 0xf3, 0x0, 0x0, 0x0, 0x4, 0xf3, 0x0,
    0x0, 0x0, 0x4, 0xf3, 0x0, 0x0, 0x0, 0x4,
    0xf3, 0x0, 0x0, 0x0, 0x4, 0xf3, 0x0, 0x0,
    0x0, 0x4, 0xf3, 0x0, 0x0, 0x0, 0x4, 0xf3,
    0x0, 0x0, 0x0, 0x4, 0xf2, 0x36, 0x0, 0x0,
    0x5, 0xf2, 0xcb, 0x0, 0x0, 0x8, 0xf0, 0xd9,
    0x0, 0x0, 0x2e, 0xb0, 0x9f, 0x61, 0x37, 0xef,
    0x20, 0xa, 0xff, 0xff, 0xa2, 0x0, 0x0, 0x13,
    0x30, 0x0, 0x0,

    /* U+FEDE "ﻞ" */
    0x0, 0x0, 0x0, 0x4, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x4, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x4, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xf3, 0x0, 0x25, 0x0,
    0x0, 0x5, 0xf4, 0x0, 0xbb, 0x0, 0x0, 0x8,
    0xfd, 0x71, 0xd9, 0x0, 0x0, 0x2e, 0xed, 0xf4,
    0x9f, 0x61, 0x37, 0xef, 0x30, 0x0, 0x1a, 0xff,
    0xff, 0xa2, 0x0, 0x0, 0x0, 0x13, 0x30, 0x0,
    0x0, 0x0,

    /* U+FEDF "ﻟ" */
    0x0, 0x1f, 0x60, 0x1, 0xf6, 0x0, 0x1f, 0x60,
    0x1, 0xf6, 0x0, 0x1f, 0x60, 0x1, 0xf6, 0x0,
    0x1f, 0x60, 0x1, 0xf6, 0x0, 0x1f, 0x60, 0x2,
    0xf5, 0x17, 0xcf, 0x12, 0xfe, 0x60,

    /* U+FEE0 "ﻠ" */
    0x0, 0x1f, 0x60, 0x0, 0x1, 0xf6, 0x0, 0x0,
    0x1f, 0x60, 0x0, 0x1, 0xf6, 0x0, 0x0, 0x1f,
    0x60, 0x0, 0x1, 0xf6, 0x0, 0x0, 0x1f, 0x60,
    0x0, 0x1, 0xf6, 0x0, 0x0, 0x1f, 0x60, 0x0,
    0x2, 0xf7, 0x0, 0x17, 0xcf, 0xe7, 0x32, 0xfe,
    0x8c, 0xf7,

    /* U+FEE1 "ﻡ" */
    0x0, 0x2, 0x87, 0x10, 0x0, 0x2f, 0xff, 0xf3,
    0x0, 0xae, 0x11, 0xea, 0x3, 0xdd, 0x32, 0xea,
    0x5f, 0xbe, 0xff, 0xe3, 0xcb, 0x0, 0x23, 0x0,
    0xea, 0x0, 0x0, 0x0, 0xea, 0x0, 0x0, 0x0,
    0xea, 0x0, 0x0, 0x0, 0xea, 0x0, 0x0, 0x0,

    /* U+FEE2 "ﻢ" */
    0x0, 0x7, 0xee, 0x60, 0x0, 0x0, 0x5f, 0xbb,
    0xf5, 0x0, 0x0, 0xbc, 0x0, 0xdc, 0x0, 0x5,
    0xee, 0x77, 0xff, 0xb7, 0x6f, 0x7a, 0xef, 0xdd,
    0xfd, 0xca, 0x0, 0x0, 0x0, 0x0, 0xea, 0x0,
    0x0, 0x0, 0x0, 0xea, 0x0, 0x0, 0x0, 0x0,
    0xc9, 0x0, 0x0, 0x0, 0x0,

    /* U+FEE3 "ﻣ" */
    0x0, 0x1, 0xbf, 0xd5, 0x0, 0x0, 0xce, 0x9d,
    0xf1, 0x0, 0x1f, 0x50, 0x3f, 0x41, 0x7c, 0xfb,
    0x7b, 0xf2, 0x2f, 0xe9, 0xdf, 0xe6, 0x0,

    /* U+FEE4 "ﻤ" */
    0x0, 0x1, 0xbf, 0xc3, 0x0, 0x0, 0x0, 0xce,
    0x9d, 0xe1, 0x0, 0x0, 0x1f, 0x50, 0x4f, 0x50,
    0x1, 0x7c, 0xfb, 0x7a, 0xfe, 0x72, 0x2f, 0xe9,
    0xdf, 0xfb, 0xef, 0x60,

    /* U+FEE5 "ﻥ" */
    0x0, 0x7, 0xa0, 0x0, 0x0, 0x0, 0x1, 0x10,
    0x4, 0x50, 0x0, 0x0, 0x0, 0x5, 0xf2, 0x23,
    0x0, 0x0, 0x1, 0xf6, 0xbc, 0x0, 0x0, 0x0,
    0xf8, 0xda, 0x0, 0x0, 0x0, 0xf8, 0xc9, 0x0,
    0x0, 0x3, 0xf5, 0xad, 0x0, 0x0, 0xa, 0xe0,
    0x3f, 0xa4, 0x24, 0xaf, 0x60, 0x5, 0xef, 0xff,
    0xe5, 0x0, 0x0, 0x3, 0x42, 0x0, 0x0,

    /* U+FEE6 "ﻦ" */
    0x0, 0x7, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x10, 0x7, 0xf0, 0x0, 0x24, 0x0, 0x0, 0x1,
    0xf7, 0x0, 0xac, 0x0, 0x0, 0x0, 0xff, 0x82,
    0xca, 0x0, 0x0, 0x0, 0xfe, 0xf5, 0xc9, 0x0,
    0x0, 0x2, 0xf5, 0x0, 0xad, 0x0, 0x0, 0x9,
    0xe0, 0x0, 0x3f, 0xa4, 0x24, 0xaf, 0x40, 0x0,
    0x5, 0xef, 0xff, 0xd4, 0x0, 0x0, 0x0, 0x3,
    0x42, 0x0, 0x0, 0x0,

    /* U+FEE7 "ﻧ" */
    0x0, 0x3e, 0x0, 0x0, 0x20, 0x0, 0x0, 0x0,
    0x4, 0xc0, 0x0, 0x6f, 0x0, 0x7, 0xf0, 0x18,
    0xec, 0x2, 0xfc, 0x20,

    /* U+FEE8 "ﻨ" */
    0x0, 0x3e, 0x0, 0x0, 0x2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4c, 0x0, 0x0, 0x6f, 0x10, 0x0,
    0x7f, 0x20, 0x18, 0xef, 0xc7, 0x2f, 0xc8, 0xef,

    /* U+FEE9 "ﻩ" */
    0x0, 0x10, 0x0, 0x2, 0xef, 0xd5, 0x0, 0xae,
    0x5a, 0xf7, 0xd, 0x90, 0x7, 0xf1, 0xe8, 0x0,
    0x3f, 0x3b, 0xe6, 0x6d, 0xe0, 0x2b, 0xfe, 0x91,
    0x0,

    /* U+FEEA "ﻪ" */
    0x0, 0x17, 0xf2, 0x0, 0x7, 0xfe, 0xf3, 0x0,
    0x7f, 0x50, 0xf6, 0x0, 0xd9, 0x0, 0xda, 0x0,
    0x9f, 0xdf, 0xff, 0x95, 0x4, 0x75, 0x8, 0xfb,

    /* U+FEEB "ﻫ" */
    0x0, 0x20, 0x0, 0x0, 0x0, 0x9, 0xe6, 0x0,
    0x0, 0x0, 0x2e, 0xfb, 0x0, 0x0, 0xa, 0xda,
    0xfc, 0x0, 0x0, 0xe7, 0xf, 0xfa, 0x0, 0xd,
    0xa4, 0xf7, 0xf3, 0x0, 0x9f, 0xee, 0xe, 0x71,
    0x7a, 0xff, 0xa5, 0xe6, 0x2f, 0xea, 0xae, 0xfa,
    0x0,

    /* U+FEEC "ﻬ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6e, 0xf8,
    0x0, 0x0, 0x3f, 0x98, 0xf1, 0x0, 0x9, 0xd0,
    0x9e, 0x0, 0x17, 0xdd, 0xbf, 0x97, 0x32, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0xbb, 0x6f, 0x60, 0x0,
    0x9, 0xe0, 0x7f, 0x0, 0x0, 0x3f, 0x77, 0xf0,
    0x0, 0x0, 0x7f, 0xf9, 0x0, 0x0, 0x0, 0x12,
    0x0, 0x0,

    /* U+FEED "ﻭ" */
    0x0, 0x8, 0xed, 0x40, 0x0, 0x6f, 0xac, 0xf1,
    0x0, 0x9e, 0x1, 0xf6, 0x0, 0x6f, 0xa6, 0xf7,
    0x0, 0x8, 0xdf, 0xf7, 0x0, 0x0, 0x4, 0xf4,
    0x0, 0x0, 0x2e, 0xe0, 0x12, 0x48, 0xef, 0x30,
    0xaf, 0xff, 0xa2, 0x0, 0x34, 0x20, 0x0, 0x0,

    /* U+FEEE "ﻮ" */
    0x0, 0x8, 0xee, 0x40, 0x0, 0x0, 0x6f, 0xac,
    0xf1, 0x0, 0x0, 0xae, 0x1, 0xf6, 0x0, 0x0,
    0x6f, 0xa7, 0xfb, 0x73, 0x0, 0x8, 0xef, 0xff,
    0xf6, 0x0, 0x0, 0x4, 0xf3, 0x0, 0x0, 0x0,
    0x2e, 0xc0, 0x0, 0x12, 0x48, 0xee, 0x20, 0x0,
    0xaf, 0xff, 0xa1, 0x0, 0x0, 0x34, 0x20, 0x0,
    0x0, 0x0,

    /* U+FEEF "ﻯ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6d, 0xfe, 0x70, 0x0, 0x0, 0x4f, 0x94, 0x7f,
    0x40, 0x0, 0x5, 0xf7, 0x0, 0x10, 0x12, 0x0,
    0x9, 0xfe, 0x80, 0xc, 0xa0, 0x0, 0x2, 0x7e,
    0xb0, 0xf7, 0x0, 0x0, 0x0, 0xaf, 0xd, 0xc0,
    0x0, 0x2, 0x9f, 0x90, 0x4f, 0xfc, 0xcf, 0xff,
    0x90, 0x0, 0x28, 0xba, 0x85, 0x10, 0x0,

    /* U+FEF0 "ﻰ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2e, 0xf8, 0x0, 0xbb, 0x0, 0x0,
    0xc, 0xd7, 0xf4, 0xe, 0x70, 0x0, 0x0, 0xbf,
    0x49, 0xe3, 0xe9, 0x0, 0x0, 0x1, 0xdb, 0xb,
    0x78, 0xf8, 0x31, 0x24, 0x9f, 0x60, 0x0, 0x8,
    0xff, 0xff, 0xfd, 0x60, 0x0, 0x0, 0x0, 0x34,
    0x42, 0x0, 0x0, 0x0,

    /* U+FEF1 "ﻱ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6d, 0xfe, 0x70, 0x0, 0x0, 0x4f, 0x94, 0x7f,
    0x40, 0x0, 0x4, 0xf8, 0x10, 0x10, 0x24, 0x0,
    0x8, 0xff, 0xa1, 0xc, 0xa0, 0x0, 0x0, 0x5e,
    0xc0, 0xf7, 0x0, 0x0, 0x0, 0xbf, 0xb, 0xd1,
    0x0, 0x4, 0xaf, 0x70, 0x1c, 0xfd, 0xef, 0xfb,
    0x40, 0x0, 0x3, 0x55, 0x30, 0x0, 0x0, 0x0,
    0x1f, 0x3f, 0x0, 0x0, 0x0, 0x0, 0x20, 0x20,
    0x0, 0x0,

    /* U+FEF2 "ﻲ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2e, 0xf8, 0x0, 0xbb, 0x0, 0x0,
    0xc, 0xd7, 0xf4, 0xe, 0x70, 0x0, 0x0, 0xbf,
    0x49, 0xe3, 0xe9, 0x0, 0x0, 0x1, 0xdb, 0xb,
    0x78, 0xf8, 0x31, 0x24, 0x9f, 0x60, 0x0, 0x8,
    0xff, 0xff, 0xfd, 0x60, 0x0, 0x0, 0x0, 0x34,
    0x42, 0x0, 0x0, 0x0, 0x0, 0x1f, 0x3f, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x20, 0x20, 0x0, 0x0,
    0x0,

    /* U+FEF3 "ﻳ" */
    0x0, 0x38, 0x0, 0x6, 0xf1, 0x0, 0x7f, 0x1,
    0x7e, 0xc0, 0x2f, 0xd3, 0x0, 0x0, 0x0, 0x3,
    0xf4, 0xe0, 0x2, 0x2,

    /* U+FEF4 "ﻴ" */
    0x0, 0x38, 0x0, 0x0, 0x6f, 0x10, 0x0, 0x7f,
    0x20, 0x17, 0xef, 0xc7, 0x2f, 0xc8, 0xef, 0x0,
    0x0, 0x0, 0x3, 0xf4, 0xe0, 0x0, 0x20, 0x20,

    /* U+FEF5 "ﻵ" */
    0x1a, 0x30, 0x19, 0x0, 0x0, 0x85, 0xad, 0xb4,
    0x0, 0x0, 0x0, 0x2, 0x0, 0x0, 0xf8, 0x0,
    0x1f, 0x50, 0x0, 0xf8, 0x0, 0x9, 0xc0, 0x0,
    0xf8, 0x0, 0x2, 0xf3, 0x0, 0xf8, 0x0, 0x0,
    0xba, 0x0, 0xf8, 0x0, 0x0, 0x4f, 0x20, 0xf7,
    0x0, 0x0, 0xc, 0x90, 0xf6, 0x0, 0x0, 0x5,
    0xf4, 0xf3, 0x0, 0x0, 0x0, 0xef, 0xe0, 0x0,
    0x0, 0x0, 0xbf, 0x50, 0x0, 0x8, 0x7c, 0xf7,
    0x0, 0x0, 0xb, 0xfc, 0x40, 0x0,

    /* U+FEF6 "ﻶ" */
    0x1a, 0x30, 0x19, 0x0, 0x0, 0x0, 0x85, 0xad,
    0xb4, 0x0, 0x0, 0x0, 0x0, 0x2, 0x0, 0x0,
    0xf8, 0x0, 0x0, 0x1f, 0x50, 0x0, 0xf8, 0x0,
    0x0, 0x9, 0xc0, 0x0, 0xf8, 0x0, 0x0, 0x2,
    0xf3, 0x0, 0xf8, 0x0, 0x0, 0x0, 0xba, 0x0,
    0xf8, 0x0, 0x0, 0x0, 0x4f, 0x20, 0xf8, 0x0,
    0x0, 0x0, 0xc, 0x90, 0xf8, 0x0, 0x0, 0x0,
    0x5, 0xf4, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xcd, 0x0,
    0x0, 0x8, 0x7c, 0xf6, 0x3f, 0xa4, 0x0, 0xb,
    0xfc, 0x40, 0x7, 0xfb,

    /* U+FEF7 "ﻷ" */
    0x9, 0xc3, 0x0, 0x0, 0x2, 0xa0, 0x0, 0x0,
    0x0, 0x1d, 0x52, 0x0, 0x0, 0x2, 0xed, 0x50,
    0x0, 0x0, 0x2, 0x0, 0x0, 0xf, 0x80, 0x2e,
    0x30, 0x0, 0xf8, 0x0, 0xbb, 0x0, 0xf, 0x80,
    0x3, 0xf2, 0x0, 0xf8, 0x0, 0xc, 0x90, 0xf,
    0x80, 0x0, 0x5f, 0x10, 0xf7, 0x0, 0x0, 0xd8,
    0xf, 0x60, 0x0, 0x6, 0xe4, 0xf3, 0x0, 0x0,
    0xe, 0xee, 0x0, 0x0, 0x0, 0xbf, 0x50, 0x0,
    0x87, 0xcf, 0x70, 0x0, 0xb, 0xfc, 0x40, 0x0,

    /* U+FEF8 "ﻸ" */
    0x9, 0xc3, 0x0, 0x0, 0x0, 0x2, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x1d, 0x52, 0x0, 0x0, 0x0,
    0x2, 0xed, 0x50, 0x0, 0x0, 0x0, 0x2, 0x0,
    0x0, 0xf, 0x80, 0x0, 0x2e, 0x30, 0x0, 0xf8,
    0x0, 0x0, 0xbb, 0x0, 0xf, 0x80, 0x0, 0x3,
    0xf2, 0x0, 0xf8, 0x0, 0x0, 0xc, 0x90, 0xf,
    0x80, 0x0, 0x0, 0x5f, 0x10, 0xf8, 0x0, 0x0,
    0x0, 0xd8, 0xf, 0x80, 0x0, 0x0, 0x6, 0xe4,
    0xf8, 0x0, 0x0, 0x0, 0xe, 0xef, 0x90, 0x0,
    0x0, 0x0, 0xbf, 0xdd, 0x0, 0x0, 0x87, 0xcf,
    0x73, 0xfa, 0x40, 0xb, 0xfc, 0x40, 0x7, 0xfb,

    /* U+FEF9 "ﻹ" */
    0x0, 0x0, 0x0, 0xf8, 0x2e, 0x30, 0x0, 0xf8,
    0xb, 0xb0, 0x0, 0xf8, 0x3, 0xf2, 0x0, 0xf8,
    0x0, 0xc9, 0x0, 0xf8, 0x0, 0x5f, 0x10, 0xf7,
    0x0, 0xd, 0x80, 0xf6, 0x0, 0x6, 0xe4, 0xf3,
    0x0, 0x0, 0xee, 0xe0, 0x0, 0x0, 0xbf, 0x50,
    0x8, 0x7c, 0xf7, 0x0, 0xb, 0xfc, 0x40, 0x0,
    0x5c, 0x70, 0x0, 0x0, 0xb0, 0x0, 0x0, 0x0,
    0x9d, 0xa0, 0x0, 0x0, 0x54, 0x0, 0x0, 0x0,

    /* U+FEFA "ﻺ" */
    0x0, 0x0, 0x0, 0xf8, 0x0, 0x2e, 0x30, 0x0,
    0xf8, 0x0, 0xb, 0xb0, 0x0, 0xf8, 0x0, 0x3,
    0xf2, 0x0, 0xf8, 0x0, 0x0, 0xc9, 0x0, 0xf8,
    0x0, 0x0, 0x5f, 0x10, 0xf8, 0x0, 0x0, 0xd,
    0x80, 0xf8, 0x0, 0x0, 0x6, 0xe4, 0xf8, 0x0,
    0x0, 0x0, 0xee, 0xf9, 0x0, 0x0, 0x0, 0xbf,
    0xdd, 0x0, 0x8, 0x7c, 0xf7, 0x3f, 0xa4, 0xb,
    0xfc, 0x40, 0x7, 0xfb, 0x5c, 0x70, 0x0, 0x0,
    0x0, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x9d, 0xa0,
    0x0, 0x0, 0x0, 0x54, 0x0, 0x0, 0x0, 0x0,

    /* U+FEFB "ﻻ" */
    0x0, 0x0, 0x0, 0xf8, 0x2e, 0x30, 0x0, 0xf8,
    0xb, 0xb0, 0x0, 0xf8, 0x3, 0xf2, 0x0, 0xf8,
    0x0, 0xc9, 0x0, 0xf8, 0x0, 0x5f, 0x10, 0xf7,
    0x0, 0xd, 0x80, 0xf6, 0x0, 0x6, 0xe4, 0xf3,
    0x0, 0x0, 0xee, 0xe0, 0x0, 0x0, 0xbf, 0x50,
    0x8, 0x7c, 0xf7, 0x0, 0xb, 0xfc, 0x40, 0x0,

    /* U+FEFC "ﻼ" */
    0x0, 0x0, 0x0, 0xf8, 0x0, 0x2e, 0x30, 0x0,
    0xf8, 0x0, 0xb, 0xb0, 0x0, 0xf8, 0x0, 0x3,
    0xf2, 0x0, 0xf8, 0x0, 0x0, 0xc9, 0x0, 0xf8,
    0x0, 0x0, 0x5f, 0x10, 0xf8, 0x0, 0x0, 0xd,
    0x80, 0xf8, 0x0, 0x0, 0x6, 0xe4, 0xf8, 0x0,
    0x0, 0x0, 0xee, 0xf9, 0x0, 0x0, 0x0, 0xbf,
    0xdd, 0x0, 0x8, 0x7c, 0xf7, 0x3f, 0xa4, 0xb,
    0xfc, 0x40, 0x7, 0xfb,

    /* U+FEFF "﻿" */

};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 81, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 103, .box_w = 2, .box_h = 12, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 12, .adv_w = 118, .box_w = 5, .box_h = 5, .ofs_x = 1, .ofs_y = 7},
    {.bitmap_index = 25, .adv_w = 215, .box_w = 12, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 97, .adv_w = 163, .box_w = 8, .box_h = 15, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 157, .adv_w = 243, .box_w = 15, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 247, .adv_w = 200, .box_w = 11, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 313, .adv_w = 70, .box_w = 2, .box_h = 5, .ofs_x = 1, .ofs_y = 7},
    {.bitmap_index = 318, .adv_w = 100, .box_w = 4, .box_h = 15, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 348, .adv_w = 100, .box_w = 4, .box_h = 15, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 378, .adv_w = 128, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 410, .adv_w = 215, .box_w = 11, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 471, .adv_w = 81, .box_w = 3, .box_h = 4, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 477, .adv_w = 92, .box_w = 5, .box_h = 3, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 485, .adv_w = 81, .box_w = 3, .box_h = 2, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 488, .adv_w = 86, .box_w = 6, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 527, .adv_w = 163, .box_w = 9, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 581, .adv_w = 163, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 629, .adv_w = 163, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 677, .adv_w = 163, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 725, .adv_w = 163, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 785, .adv_w = 163, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 833, .adv_w = 163, .box_w = 9, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 887, .adv_w = 163, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 935, .adv_w = 163, .box_w = 9, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 989, .adv_w = 163, .box_w = 9, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1043, .adv_w = 86, .box_w = 3, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1055, .adv_w = 86, .box_w = 3, .box_h = 10, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 1070, .adv_w = 215, .box_w = 11, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1125, .adv_w = 215, .box_w = 11, .box_h = 6, .ofs_x = 1, .ofs_y = 2},
    {.bitmap_index = 1158, .adv_w = 215, .box_w = 11, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1213, .adv_w = 136, .box_w = 7, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1255, .adv_w = 256, .box_w = 14, .box_h = 15, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 1360, .adv_w = 175, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1426, .adv_w = 176, .box_w = 9, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1480, .adv_w = 179, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1546, .adv_w = 197, .box_w = 11, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1612, .adv_w = 162, .box_w = 9, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1666, .adv_w = 147, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1714, .adv_w = 198, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1786, .adv_w = 193, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1846, .adv_w = 76, .box_w = 3, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1864, .adv_w = 76, .box_w = 5, .box_h = 15, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 1902, .adv_w = 168, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1962, .adv_w = 143, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2010, .adv_w = 221, .box_w = 12, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2082, .adv_w = 192, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2142, .adv_w = 202, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2214, .adv_w = 154, .box_w = 9, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2268, .adv_w = 202, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2352, .adv_w = 178, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2412, .adv_w = 163, .box_w = 9, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2466, .adv_w = 156, .box_w = 11, .box_h = 12, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 2532, .adv_w = 187, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2592, .adv_w = 175, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2658, .adv_w = 253, .box_w = 16, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2754, .adv_w = 175, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2820, .adv_w = 156, .box_w = 11, .box_h = 12, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 2886, .adv_w = 175, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2952, .adv_w = 100, .box_w = 4, .box_h = 15, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 2982, .adv_w = 86, .box_w = 6, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3021, .adv_w = 100, .box_w = 4, .box_h = 15, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 3051, .adv_w = 215, .box_w = 11, .box_h = 5, .ofs_x = 1, .ofs_y = 7},
    {.bitmap_index = 3079, .adv_w = 128, .box_w = 10, .box_h = 2, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 3089, .adv_w = 128, .box_w = 5, .box_h = 3, .ofs_x = 1, .ofs_y = 10},
    {.bitmap_index = 3097, .adv_w = 157, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3138, .adv_w = 163, .box_w = 9, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3192, .adv_w = 141, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3228, .adv_w = 163, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3282, .adv_w = 158, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3323, .adv_w = 90, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3359, .adv_w = 163, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3413, .adv_w = 162, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3461, .adv_w = 71, .box_w = 2, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3473, .adv_w = 71, .box_w = 4, .box_h = 15, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 3503, .adv_w = 148, .box_w = 9, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3557, .adv_w = 71, .box_w = 2, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3569, .adv_w = 249, .box_w = 14, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3632, .adv_w = 162, .box_w = 8, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3668, .adv_w = 157, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3709, .adv_w = 163, .box_w = 9, .box_h = 12, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 3763, .adv_w = 163, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3817, .adv_w = 105, .box_w = 6, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3847, .adv_w = 133, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3883, .adv_w = 100, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3919, .adv_w = 162, .box_w = 8, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3955, .adv_w = 152, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3996, .adv_w = 209, .box_w = 13, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4055, .adv_w = 152, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4096, .adv_w = 152, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 4150, .adv_w = 134, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4186, .adv_w = 163, .box_w = 7, .box_h = 16, .ofs_x = 2, .ofs_y = -4},
    {.bitmap_index = 4242, .adv_w = 86, .box_w = 2, .box_h = 16, .ofs_x = 2, .ofs_y = -4},
    {.bitmap_index = 4258, .adv_w = 163, .box_w = 7, .box_h = 16, .ofs_x = 2, .ofs_y = -4},
    {.bitmap_index = 4314, .adv_w = 215, .box_w = 11, .box_h = 4, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 4336, .adv_w = 171, .box_w = 9, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4377, .adv_w = 148, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4418, .adv_w = 106, .box_w = 7, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4450, .adv_w = 140, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4491, .adv_w = 167, .box_w = 8, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4527, .adv_w = 70, .box_w = 2, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4536, .adv_w = 89, .box_w = 5, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4559, .adv_w = 167, .box_w = 8, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4595, .adv_w = 166, .box_w = 9, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4636, .adv_w = 57, .box_w = 2, .box_h = 6, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 4642, .adv_w = 138, .box_w = 8, .box_h = 12, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 4690, .adv_w = 135, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4726, .adv_w = 146, .box_w = 8, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4774, .adv_w = 170, .box_w = 9, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4815, .adv_w = 174, .box_w = 10, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4860, .adv_w = 70, .box_w = 2, .box_h = 12, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 4872, .adv_w = 103, .box_w = 5, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4895, .adv_w = 166, .box_w = 9, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4936, .adv_w = 160, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4986, .adv_w = 164, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 5034, .adv_w = 160, .box_w = 9, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5075, .adv_w = 138, .box_w = 8, .box_h = 12, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 5123, .adv_w = 152, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5164, .adv_w = 182, .box_w = 10, .box_h = 13, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 5229, .adv_w = 145, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5265, .adv_w = 181, .box_w = 11, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5315, .adv_w = 168, .box_w = 10, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5360, .adv_w = 163, .box_w = 10, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5435, .adv_w = 163, .box_w = 10, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5510, .adv_w = 194, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5560, .adv_w = 250, .box_w = 14, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5630, .adv_w = 83, .box_w = 3, .box_h = 4, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5636, .adv_w = 0, .box_w = 6, .box_h = 5, .ofs_x = 1, .ofs_y = 10},
    {.bitmap_index = 5651, .adv_w = 81, .box_w = 3, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5666, .adv_w = 136, .box_w = 7, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5708, .adv_w = 120, .box_w = 6, .box_h = 8, .ofs_x = 1, .ofs_y = 1},
    {.bitmap_index = 5732, .adv_w = 71, .box_w = 7, .box_h = 15, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 5785, .adv_w = 71, .box_w = 4, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5817, .adv_w = 124, .box_w = 8, .box_h = 15, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 5877, .adv_w = 71, .box_w = 4, .box_h = 16, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 5909, .adv_w = 200, .box_w = 11, .box_h = 12, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 5975, .adv_w = 71, .box_w = 2, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5987, .adv_w = 241, .box_w = 13, .box_h = 9, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 6046, .adv_w = 134, .box_w = 7, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6078, .adv_w = 241, .box_w = 13, .box_h = 6, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6117, .adv_w = 241, .box_w = 13, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6169, .adv_w = 165, .box_w = 10, .box_h = 13, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 6234, .adv_w = 165, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 6294, .adv_w = 165, .box_w = 10, .box_h = 14, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 6364, .adv_w = 114, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6389, .adv_w = 114, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6424, .adv_w = 124, .box_w = 8, .box_h = 9, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 6460, .adv_w = 124, .box_w = 8, .box_h = 12, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 6508, .adv_w = 313, .box_w = 18, .box_h = 10, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 6598, .adv_w = 313, .box_w = 18, .box_h = 14, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 6724, .adv_w = 310, .box_w = 18, .box_h = 12, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 6832, .adv_w = 310, .box_w = 18, .box_h = 12, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 6940, .adv_w = 237, .box_w = 13, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7018, .adv_w = 237, .box_w = 13, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7096, .adv_w = 153, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 7166, .adv_w = 153, .box_w = 10, .box_h = 15, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 7241, .adv_w = 75, .box_w = 6, .box_h = 2, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 7247, .adv_w = 265, .box_w = 15, .box_h = 12, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 7337, .adv_w = 199, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 7421, .adv_w = 211, .box_w = 11, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7487, .adv_w = 186, .box_w = 10, .box_h = 15, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 7562, .adv_w = 159, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 7602, .adv_w = 188, .box_w = 10, .box_h = 11, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 7657, .adv_w = 134, .box_w = 7, .box_h = 7, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7682, .adv_w = 124, .box_w = 8, .box_h = 10, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 7722, .adv_w = 200, .box_w = 11, .box_h = 10, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 7777, .adv_w = 200, .box_w = 11, .box_h = 12, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 7843, .adv_w = 0, .box_w = 6, .box_h = 5, .ofs_x = 1, .ofs_y = 9},
    {.bitmap_index = 7858, .adv_w = 0, .box_w = 6, .box_h = 5, .ofs_x = 1, .ofs_y = 9},
    {.bitmap_index = 7873, .adv_w = 0, .box_w = 6, .box_h = 4, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 7885, .adv_w = 0, .box_w = 6, .box_h = 3, .ofs_x = 1, .ofs_y = 9},
    {.bitmap_index = 7894, .adv_w = 0, .box_w = 6, .box_h = 5, .ofs_x = 1, .ofs_y = 9},
    {.bitmap_index = 7909, .adv_w = 0, .box_w = 6, .box_h = 3, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 7918, .adv_w = 0, .box_w = 6, .box_h = 5, .ofs_x = 1, .ofs_y = 10},
    {.bitmap_index = 7933, .adv_w = 0, .box_w = 6, .box_h = 4, .ofs_x = 1, .ofs_y = 10},
    {.bitmap_index = 7945, .adv_w = 0, .box_w = 6, .box_h = 3, .ofs_x = 1, .ofs_y = 9},
    {.bitmap_index = 7954, .adv_w = 0, .box_w = 4, .box_h = 4, .ofs_x = 2, .ofs_y = 9},
    {.bitmap_index = 7962, .adv_w = 0, .box_w = 4, .box_h = 4, .ofs_x = 2, .ofs_y = -4},
    {.bitmap_index = 7970, .adv_w = 0, .box_w = 6, .box_h = 5, .ofs_x = 1, .ofs_y = 10},
    {.bitmap_index = 7985, .adv_w = 128, .box_w = 6, .box_h = 3, .ofs_x = 1, .ofs_y = 10},
    {.bitmap_index = 7994, .adv_w = 138, .box_w = 3, .box_h = 2, .ofs_x = 3, .ofs_y = 4},
    {.bitmap_index = 7997, .adv_w = 138, .box_w = 4, .box_h = 10, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 8017, .adv_w = 138, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8057, .adv_w = 138, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8102, .adv_w = 138, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 8141, .adv_w = 138, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 8180, .adv_w = 138, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8224, .adv_w = 138, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8269, .adv_w = 138, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8314, .adv_w = 138, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8358, .adv_w = 138, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 8393, .adv_w = 83, .box_w = 5, .box_h = 8, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 8413, .adv_w = 81, .box_w = 3, .box_h = 4, .ofs_x = 1, .ofs_y = 8},
    {.bitmap_index = 8419, .adv_w = 140, .box_w = 9, .box_h = 8, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 8455, .adv_w = 241, .box_w = 13, .box_h = 6, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 8494, .adv_w = 199, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 8566, .adv_w = 0, .box_w = 2, .box_h = 5, .ofs_x = 3, .ofs_y = 10},
    {.bitmap_index = 8571, .adv_w = 75, .box_w = 4, .box_h = 4, .ofs_x = 0, .ofs_y = 10},
    {.bitmap_index = 8579, .adv_w = 241, .box_w = 13, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 8644, .adv_w = 241, .box_w = 13, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 8696, .adv_w = 241, .box_w = 13, .box_h = 11, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 8768, .adv_w = 241, .box_w = 13, .box_h = 9, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 8827, .adv_w = 241, .box_w = 13, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 8879, .adv_w = 241, .box_w = 13, .box_h = 11, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 8951, .adv_w = 241, .box_w = 13, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 9003, .adv_w = 241, .box_w = 13, .box_h = 11, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 9075, .adv_w = 165, .box_w = 10, .box_h = 16, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 9155, .adv_w = 165, .box_w = 10, .box_h = 16, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 9235, .adv_w = 165, .box_w = 10, .box_h = 13, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 9300, .adv_w = 165, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 9360, .adv_w = 165, .box_w = 10, .box_h = 16, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 9440, .adv_w = 165, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 9500, .adv_w = 165, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 9560, .adv_w = 114, .box_w = 7, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9602, .adv_w = 114, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 9637, .adv_w = 114, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 9672, .adv_w = 114, .box_w = 7, .box_h = 15, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 9725, .adv_w = 114, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9760, .adv_w = 114, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 9795, .adv_w = 114, .box_w = 7, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9837, .adv_w = 114, .box_w = 7, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9876, .adv_w = 114, .box_w = 7, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9918, .adv_w = 124, .box_w = 9, .box_h = 16, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 9990, .adv_w = 124, .box_w = 9, .box_h = 14, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 10053, .adv_w = 128, .box_w = 10, .box_h = 9, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 10098, .adv_w = 136, .box_w = 9, .box_h = 9, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 10139, .adv_w = 156, .box_w = 12, .box_h = 9, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 10193, .adv_w = 136, .box_w = 9, .box_h = 8, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 10229, .adv_w = 124, .box_w = 9, .box_h = 12, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 10283, .adv_w = 124, .box_w = 9, .box_h = 14, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 10346, .adv_w = 124, .box_w = 9, .box_h = 14, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 10409, .adv_w = 313, .box_w = 18, .box_h = 12, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 10517, .adv_w = 313, .box_w = 18, .box_h = 10, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 10607, .adv_w = 313, .box_w = 18, .box_h = 14, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 10733, .adv_w = 310, .box_w = 18, .box_h = 12, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 10841, .adv_w = 310, .box_w = 18, .box_h = 14, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 10967, .adv_w = 237, .box_w = 13, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 11045, .adv_w = 153, .box_w = 10, .box_h = 17, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 11130, .adv_w = 265, .box_w = 15, .box_h = 9, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 11198, .adv_w = 265, .box_w = 15, .box_h = 12, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 11288, .adv_w = 265, .box_w = 15, .box_h = 14, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 11393, .adv_w = 265, .box_w = 15, .box_h = 14, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 11498, .adv_w = 265, .box_w = 15, .box_h = 13, .ofs_x = 1, .ofs_y = -6},
    {.bitmap_index = 11596, .adv_w = 265, .box_w = 15, .box_h = 14, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 11701, .adv_w = 199, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 11785, .adv_w = 199, .box_w = 12, .box_h = 16, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 11881, .adv_w = 229, .box_w = 14, .box_h = 14, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 11979, .adv_w = 270, .box_w = 15, .box_h = 14, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 12084, .adv_w = 229, .box_w = 14, .box_h = 14, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 12182, .adv_w = 211, .box_w = 11, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 12248, .adv_w = 211, .box_w = 11, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 12325, .adv_w = 211, .box_w = 11, .box_h = 17, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 12419, .adv_w = 229, .box_w = 14, .box_h = 16, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 12531, .adv_w = 229, .box_w = 14, .box_h = 16, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 12643, .adv_w = 229, .box_w = 14, .box_h = 16, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 12755, .adv_w = 229, .box_w = 14, .box_h = 18, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 12881, .adv_w = 229, .box_w = 14, .box_h = 20, .ofs_x = 1, .ofs_y = -6},
    {.bitmap_index = 13021, .adv_w = 229, .box_w = 14, .box_h = 18, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 13147, .adv_w = 186, .box_w = 11, .box_h = 19, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 13252, .adv_w = 186, .box_w = 10, .box_h = 18, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 13342, .adv_w = 186, .box_w = 10, .box_h = 19, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 13437, .adv_w = 186, .box_w = 10, .box_h = 19, .ofs_x = 1, .ofs_y = -7},
    {.bitmap_index = 13532, .adv_w = 188, .box_w = 10, .box_h = 13, .ofs_x = 1, .ofs_y = -6},
    {.bitmap_index = 13597, .adv_w = 188, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 13647, .adv_w = 188, .box_w = 10, .box_h = 14, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 13717, .adv_w = 188, .box_w = 10, .box_h = 13, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 13782, .adv_w = 188, .box_w = 10, .box_h = 13, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 13847, .adv_w = 179, .box_w = 10, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 13892, .adv_w = 165, .box_w = 10, .box_h = 14, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 13962, .adv_w = 124, .box_w = 8, .box_h = 14, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 14018, .adv_w = 124, .box_w = 8, .box_h = 15, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 14078, .adv_w = 124, .box_w = 8, .box_h = 16, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 14142, .adv_w = 124, .box_w = 8, .box_h = 14, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 14198, .adv_w = 200, .box_w = 11, .box_h = 10, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 14253, .adv_w = 200, .box_w = 11, .box_h = 11, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 14314, .adv_w = 200, .box_w = 11, .box_h = 14, .ofs_x = 1, .ofs_y = -7},
    {.bitmap_index = 14391, .adv_w = 134, .box_w = 7, .box_h = 7, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 14416, .adv_w = 138, .box_w = 3, .box_h = 2, .ofs_x = 3, .ofs_y = 4},
    {.bitmap_index = 14419, .adv_w = 138, .box_w = 4, .box_h = 10, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 14439, .adv_w = 138, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 14479, .adv_w = 138, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 14524, .adv_w = 138, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 14564, .adv_w = 138, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 14608, .adv_w = 138, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 14647, .adv_w = 138, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 14692, .adv_w = 138, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 14737, .adv_w = 138, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 14781, .adv_w = 256, .box_w = 16, .box_h = 17, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 14917, .adv_w = 256, .box_w = 16, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 15013, .adv_w = 256, .box_w = 16, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 15125, .adv_w = 256, .box_w = 16, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 15221, .adv_w = 176, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 15287, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 15415, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 15543, .adv_w = 288, .box_w = 18, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 15669, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 15797, .adv_w = 288, .box_w = 18, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 15905, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 16033, .adv_w = 128, .box_w = 8, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 16089, .adv_w = 192, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 16173, .adv_w = 288, .box_w = 18, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 16317, .adv_w = 256, .box_w = 16, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 16413, .adv_w = 176, .box_w = 11, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 16501, .adv_w = 224, .box_w = 10, .box_h = 16, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 16581, .adv_w = 224, .box_w = 14, .box_h = 18, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 16707, .adv_w = 224, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 16812, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 16910, .adv_w = 224, .box_w = 10, .box_h = 16, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 16990, .adv_w = 224, .box_w = 16, .box_h = 14, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 17102, .adv_w = 160, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 17172, .adv_w = 160, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 17242, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 17340, .adv_w = 224, .box_w = 14, .box_h = 4, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 17368, .adv_w = 288, .box_w = 18, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 17476, .adv_w = 320, .box_w = 20, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 17636, .adv_w = 288, .box_w = 20, .box_h = 16, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 17796, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 17924, .adv_w = 224, .box_w = 14, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 17994, .adv_w = 224, .box_w = 14, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 18064, .adv_w = 320, .box_w = 20, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 18204, .adv_w = 256, .box_w = 16, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 18300, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 18428, .adv_w = 256, .box_w = 17, .box_h = 17, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 18573, .adv_w = 224, .box_w = 15, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 18678, .adv_w = 224, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 18790, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 18888, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 18986, .adv_w = 256, .box_w = 16, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 19082, .adv_w = 160, .box_w = 12, .box_h = 16, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 19178, .adv_w = 224, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 19290, .adv_w = 224, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 19402, .adv_w = 288, .box_w = 18, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 19510, .adv_w = 256, .box_w = 18, .box_h = 18, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 19672, .adv_w = 192, .box_w = 12, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 19768, .adv_w = 320, .box_w = 20, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 19918, .adv_w = 320, .box_w = 20, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 20018, .adv_w = 320, .box_w = 20, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 20118, .adv_w = 320, .box_w = 20, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 20218, .adv_w = 320, .box_w = 20, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 20318, .adv_w = 320, .box_w = 20, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 20418, .adv_w = 320, .box_w = 21, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 20565, .adv_w = 224, .box_w = 12, .box_h = 16, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 20661, .adv_w = 224, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 20773, .adv_w = 256, .box_w = 17, .box_h = 17, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 20918, .adv_w = 320, .box_w = 20, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 21038, .adv_w = 192, .box_w = 12, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 21134, .adv_w = 258, .box_w = 17, .box_h = 11, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 21228, .adv_w = 241, .box_w = 13, .box_h = 11, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 21300, .adv_w = 251, .box_w = 15, .box_h = 10, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 21375, .adv_w = 71, .box_w = 5, .box_h = 10, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 21400, .adv_w = 77, .box_w = 6, .box_h = 10, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 21430, .adv_w = 241, .box_w = 13, .box_h = 11, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 21502, .adv_w = 251, .box_w = 15, .box_h = 10, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 21577, .adv_w = 71, .box_w = 5, .box_h = 10, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 21602, .adv_w = 77, .box_w = 6, .box_h = 10, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 21632, .adv_w = 241, .box_w = 13, .box_h = 11, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 21704, .adv_w = 251, .box_w = 15, .box_h = 10, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 21779, .adv_w = 71, .box_w = 5, .box_h = 10, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 21804, .adv_w = 77, .box_w = 6, .box_h = 10, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 21834, .adv_w = 241, .box_w = 13, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 21886, .adv_w = 251, .box_w = 15, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 21946, .adv_w = 71, .box_w = 5, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 21971, .adv_w = 77, .box_w = 6, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 22001, .adv_w = 241, .box_w = 13, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 22053, .adv_w = 251, .box_w = 15, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 22113, .adv_w = 71, .box_w = 5, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 22138, .adv_w = 77, .box_w = 6, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 22168, .adv_w = 241, .box_w = 13, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 22233, .adv_w = 251, .box_w = 15, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 22301, .adv_w = 71, .box_w = 6, .box_h = 11, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 22334, .adv_w = 77, .box_w = 6, .box_h = 11, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 22367, .adv_w = 265, .box_w = 15, .box_h = 14, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 22472, .adv_w = 265, .box_w = 16, .box_h = 12, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 22568, .adv_w = 122, .box_w = 8, .box_h = 12, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 22616, .adv_w = 130, .box_w = 10, .box_h = 11, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 22671, .adv_w = 265, .box_w = 15, .box_h = 14, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 22776, .adv_w = 265, .box_w = 16, .box_h = 12, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 22872, .adv_w = 122, .box_w = 8, .box_h = 12, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 22920, .adv_w = 130, .box_w = 10, .box_h = 11, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 22975, .adv_w = 165, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 23035, .adv_w = 165, .box_w = 10, .box_h = 13, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 23100, .adv_w = 158, .box_w = 10, .box_h = 12, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 23160, .adv_w = 165, .box_w = 12, .box_h = 12, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 23232, .adv_w = 165, .box_w = 10, .box_h = 13, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 23297, .adv_w = 165, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 23357, .adv_w = 158, .box_w = 10, .box_h = 10, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 23407, .adv_w = 165, .box_w = 12, .box_h = 10, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 23467, .adv_w = 165, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 23527, .adv_w = 165, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 23587, .adv_w = 158, .box_w = 10, .box_h = 12, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 23647, .adv_w = 165, .box_w = 12, .box_h = 12, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 23719, .adv_w = 165, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 23779, .adv_w = 165, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 23839, .adv_w = 158, .box_w = 10, .box_h = 12, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 23899, .adv_w = 165, .box_w = 12, .box_h = 12, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 23971, .adv_w = 114, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 24006, .adv_w = 134, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 24051, .adv_w = 114, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 24086, .adv_w = 134, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 24131, .adv_w = 114, .box_w = 7, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 24173, .adv_w = 134, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 24227, .adv_w = 114, .box_w = 7, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 24269, .adv_w = 134, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 24323, .adv_w = 124, .box_w = 9, .box_h = 14, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 24386, .adv_w = 141, .box_w = 10, .box_h = 14, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 24456, .adv_w = 124, .box_w = 9, .box_h = 16, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 24528, .adv_w = 141, .box_w = 10, .box_h = 16, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 24608, .adv_w = 229, .box_w = 14, .box_h = 14, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 24706, .adv_w = 229, .box_w = 15, .box_h = 14, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 24811, .adv_w = 122, .box_w = 9, .box_h = 13, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 24870, .adv_w = 141, .box_w = 10, .box_h = 13, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 24935, .adv_w = 229, .box_w = 14, .box_h = 16, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 25047, .adv_w = 229, .box_w = 15, .box_h = 16, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 25167, .adv_w = 122, .box_w = 9, .box_h = 15, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 25235, .adv_w = 141, .box_w = 10, .box_h = 15, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 25310, .adv_w = 229, .box_w = 14, .box_h = 20, .ofs_x = 1, .ofs_y = -6},
    {.bitmap_index = 25450, .adv_w = 229, .box_w = 15, .box_h = 21, .ofs_x = 1, .ofs_y = -6},
    {.bitmap_index = 25608, .adv_w = 122, .box_w = 9, .box_h = 20, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 25698, .adv_w = 141, .box_w = 10, .box_h = 20, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 25798, .adv_w = 229, .box_w = 14, .box_h = 16, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 25910, .adv_w = 229, .box_w = 15, .box_h = 15, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 26023, .adv_w = 122, .box_w = 9, .box_h = 14, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 26086, .adv_w = 141, .box_w = 10, .box_h = 14, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 26156, .adv_w = 188, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 26206, .adv_w = 195, .box_w = 12, .box_h = 10, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 26266, .adv_w = 188, .box_w = 10, .box_h = 14, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 26336, .adv_w = 195, .box_w = 12, .box_h = 13, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 26414, .adv_w = 71, .box_w = 6, .box_h = 11, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 26447, .adv_w = 77, .box_w = 6, .box_h = 11, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 26480, .adv_w = 179, .box_w = 10, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 26525, .adv_w = 162, .box_w = 10, .box_h = 11, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 26580, .adv_w = 135, .box_w = 9, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 26621, .adv_w = 118, .box_w = 9, .box_h = 11, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 26671, .adv_w = 211, .box_w = 11, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 26748, .adv_w = 216, .box_w = 13, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 26839, .adv_w = 122, .box_w = 9, .box_h = 15, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 26907, .adv_w = 141, .box_w = 10, .box_h = 15, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 26982, .adv_w = 124, .box_w = 8, .box_h = 15, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 27042, .adv_w = 132, .box_w = 10, .box_h = 15, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 27117, .adv_w = 124, .box_w = 8, .box_h = 14, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 27173, .adv_w = 132, .box_w = 10, .box_h = 14, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 27243, .adv_w = 124, .box_w = 8, .box_h = 16, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 27307, .adv_w = 132, .box_w = 10, .box_h = 16, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 27387, .adv_w = 124, .box_w = 8, .box_h = 14, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 27443, .adv_w = 132, .box_w = 10, .box_h = 14, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 27513, .adv_w = 200, .box_w = 11, .box_h = 14, .ofs_x = 1, .ofs_y = -7},
    {.bitmap_index = 27590, .adv_w = 213, .box_w = 13, .box_h = 12, .ofs_x = 1, .ofs_y = -7},
    {.bitmap_index = 27668, .adv_w = 71, .box_w = 5, .box_h = 10, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 27693, .adv_w = 77, .box_w = 6, .box_h = 10, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 27723, .adv_w = 71, .box_w = 5, .box_h = 5, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 27736, .adv_w = 77, .box_w = 6, .box_h = 5, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 27751, .adv_w = 200, .box_w = 11, .box_h = 10, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 27806, .adv_w = 213, .box_w = 13, .box_h = 8, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 27858, .adv_w = 71, .box_w = 5, .box_h = 8, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 27878, .adv_w = 77, .box_w = 6, .box_h = 8, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 27902, .adv_w = 75, .box_w = 5, .box_h = 5, .ofs_x = 0, .ofs_y = 9},
    {.bitmap_index = 27915, .adv_w = 75, .box_w = 6, .box_h = 14, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 27957, .adv_w = 75, .box_w = 5, .box_h = 5, .ofs_x = 0, .ofs_y = 9},
    {.bitmap_index = 27970, .adv_w = 67, .box_w = 5, .box_h = 3, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 27978, .adv_w = 75, .box_w = 5, .box_h = 4, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 27988, .adv_w = 75, .box_w = 5, .box_h = 3, .ofs_x = 0, .ofs_y = 9},
    {.bitmap_index = 27996, .adv_w = 75, .box_w = 6, .box_h = 12, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 28032, .adv_w = 75, .box_w = 5, .box_h = 5, .ofs_x = 0, .ofs_y = 9},
    {.bitmap_index = 28045, .adv_w = 75, .box_w = 6, .box_h = 14, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 28087, .adv_w = 75, .box_w = 5, .box_h = 3, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 28095, .adv_w = 75, .box_w = 6, .box_h = 5, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 28110, .adv_w = 75, .box_w = 6, .box_h = 5, .ofs_x = -1, .ofs_y = 10},
    {.bitmap_index = 28125, .adv_w = 75, .box_w = 6, .box_h = 14, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 28167, .adv_w = 75, .box_w = 5, .box_h = 4, .ofs_x = 0, .ofs_y = 10},
    {.bitmap_index = 28177, .adv_w = 75, .box_w = 6, .box_h = 14, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 28219, .adv_w = 120, .box_w = 6, .box_h = 8, .ofs_x = 1, .ofs_y = 1},
    {.bitmap_index = 28243, .adv_w = 71, .box_w = 7, .box_h = 15, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 28296, .adv_w = 78, .box_w = 7, .box_h = 15, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 28349, .adv_w = 71, .box_w = 4, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 28383, .adv_w = 78, .box_w = 6, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 28434, .adv_w = 124, .box_w = 8, .box_h = 15, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 28494, .adv_w = 132, .box_w = 10, .box_h = 15, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 28569, .adv_w = 71, .box_w = 4, .box_h = 16, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 28601, .adv_w = 78, .box_w = 6, .box_h = 16, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 28649, .adv_w = 200, .box_w = 11, .box_h = 12, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 28715, .adv_w = 213, .box_w = 13, .box_h = 11, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 28787, .adv_w = 71, .box_w = 5, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 28812, .adv_w = 77, .box_w = 6, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 28842, .adv_w = 71, .box_w = 2, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 28854, .adv_w = 78, .box_w = 5, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 28884, .adv_w = 241, .box_w = 13, .box_h = 9, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 28943, .adv_w = 251, .box_w = 15, .box_h = 9, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 29011, .adv_w = 71, .box_w = 5, .box_h = 8, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 29031, .adv_w = 77, .box_w = 6, .box_h = 8, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 29055, .adv_w = 134, .box_w = 7, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 29087, .adv_w = 137, .box_w = 8, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 29119, .adv_w = 241, .box_w = 13, .box_h = 6, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 29158, .adv_w = 251, .box_w = 15, .box_h = 6, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 29203, .adv_w = 71, .box_w = 5, .box_h = 8, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 29223, .adv_w = 77, .box_w = 6, .box_h = 8, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 29247, .adv_w = 241, .box_w = 13, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 29299, .adv_w = 251, .box_w = 15, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 29359, .adv_w = 71, .box_w = 5, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 29384, .adv_w = 77, .box_w = 6, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 29414, .adv_w = 165, .box_w = 10, .box_h = 13, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 29479, .adv_w = 165, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 29539, .adv_w = 158, .box_w = 10, .box_h = 10, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 29589, .adv_w = 165, .box_w = 12, .box_h = 10, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 29649, .adv_w = 165, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 29709, .adv_w = 165, .box_w = 10, .box_h = 13, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 29774, .adv_w = 158, .box_w = 10, .box_h = 7, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 29809, .adv_w = 165, .box_w = 12, .box_h = 7, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 29851, .adv_w = 165, .box_w = 10, .box_h = 14, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 29921, .adv_w = 165, .box_w = 10, .box_h = 14, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 29991, .adv_w = 158, .box_w = 10, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 30036, .adv_w = 165, .box_w = 12, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 30090, .adv_w = 114, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 30115, .adv_w = 134, .box_w = 9, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 30147, .adv_w = 114, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 30182, .adv_w = 134, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 30227, .adv_w = 124, .box_w = 8, .box_h = 9, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 30263, .adv_w = 141, .box_w = 10, .box_h = 10, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 30313, .adv_w = 124, .box_w = 8, .box_h = 12, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 30361, .adv_w = 141, .box_w = 10, .box_h = 12, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 30421, .adv_w = 313, .box_w = 18, .box_h = 10, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 30511, .adv_w = 326, .box_w = 20, .box_h = 11, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 30621, .adv_w = 215, .box_w = 14, .box_h = 6, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 30663, .adv_w = 228, .box_w = 16, .box_h = 6, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 30711, .adv_w = 313, .box_w = 18, .box_h = 14, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 30837, .adv_w = 326, .box_w = 20, .box_h = 14, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 30977, .adv_w = 215, .box_w = 14, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 31040, .adv_w = 228, .box_w = 16, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 31120, .adv_w = 310, .box_w = 18, .box_h = 12, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 31228, .adv_w = 314, .box_w = 19, .box_h = 12, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 31342, .adv_w = 217, .box_w = 14, .box_h = 7, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 31391, .adv_w = 222, .box_w = 16, .box_h = 7, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 31447, .adv_w = 310, .box_w = 18, .box_h = 12, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 31555, .adv_w = 314, .box_w = 19, .box_h = 12, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 31669, .adv_w = 217, .box_w = 14, .box_h = 7, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 31718, .adv_w = 222, .box_w = 16, .box_h = 7, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 31774, .adv_w = 237, .box_w = 13, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 31852, .adv_w = 243, .box_w = 15, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 31942, .adv_w = 204, .box_w = 13, .box_h = 12, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 32020, .adv_w = 210, .box_w = 15, .box_h = 12, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 32110, .adv_w = 237, .box_w = 13, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 32188, .adv_w = 243, .box_w = 15, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 32278, .adv_w = 204, .box_w = 13, .box_h = 12, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 32356, .adv_w = 210, .box_w = 15, .box_h = 12, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 32446, .adv_w = 153, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 32516, .adv_w = 136, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 32576, .adv_w = 153, .box_w = 9, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 32617, .adv_w = 124, .box_w = 9, .box_h = 7, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 32649, .adv_w = 153, .box_w = 10, .box_h = 15, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 32724, .adv_w = 136, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 32794, .adv_w = 134, .box_w = 9, .box_h = 11, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 32844, .adv_w = 124, .box_w = 9, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 32885, .adv_w = 265, .box_w = 15, .box_h = 12, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 32975, .adv_w = 265, .box_w = 16, .box_h = 10, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 33055, .adv_w = 122, .box_w = 8, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 33095, .adv_w = 130, .box_w = 10, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 33140, .adv_w = 199, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 33224, .adv_w = 214, .box_w = 14, .box_h = 13, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 33315, .adv_w = 122, .box_w = 8, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 33355, .adv_w = 130, .box_w = 10, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 33400, .adv_w = 211, .box_w = 11, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 33466, .adv_w = 216, .box_w = 13, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 33544, .adv_w = 122, .box_w = 9, .box_h = 13, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 33603, .adv_w = 141, .box_w = 10, .box_h = 13, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 33668, .adv_w = 186, .box_w = 10, .box_h = 15, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 33743, .adv_w = 194, .box_w = 12, .box_h = 15, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 33833, .adv_w = 78, .box_w = 5, .box_h = 12, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 33863, .adv_w = 85, .box_w = 7, .box_h = 12, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 33905, .adv_w = 159, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 33945, .adv_w = 170, .box_w = 10, .box_h = 9, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 33990, .adv_w = 137, .box_w = 9, .box_h = 5, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 34013, .adv_w = 148, .box_w = 11, .box_h = 5, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 34041, .adv_w = 188, .box_w = 10, .box_h = 11, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 34096, .adv_w = 195, .box_w = 12, .box_h = 10, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 34156, .adv_w = 71, .box_w = 5, .box_h = 8, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 34176, .adv_w = 77, .box_w = 6, .box_h = 8, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 34200, .adv_w = 134, .box_w = 7, .box_h = 7, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 34225, .adv_w = 137, .box_w = 8, .box_h = 6, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 34249, .adv_w = 135, .box_w = 9, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 34290, .adv_w = 118, .box_w = 9, .box_h = 11, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 34340, .adv_w = 124, .box_w = 8, .box_h = 10, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 34380, .adv_w = 132, .box_w = 10, .box_h = 10, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 34430, .adv_w = 200, .box_w = 11, .box_h = 10, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 34485, .adv_w = 213, .box_w = 13, .box_h = 8, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 34537, .adv_w = 200, .box_w = 11, .box_h = 12, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 34603, .adv_w = 213, .box_w = 13, .box_h = 10, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 34668, .adv_w = 71, .box_w = 5, .box_h = 8, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 34688, .adv_w = 77, .box_w = 6, .box_h = 8, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 34712, .adv_w = 146, .box_w = 10, .box_h = 14, .ofs_x = -2, .ofs_y = 0},
    {.bitmap_index = 34782, .adv_w = 153, .box_w = 12, .box_h = 14, .ofs_x = -2, .ofs_y = 0},
    {.bitmap_index = 34866, .adv_w = 146, .box_w = 9, .box_h = 16, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 34938, .adv_w = 153, .box_w = 11, .box_h = 16, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 35026, .adv_w = 146, .box_w = 8, .box_h = 16, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 35090, .adv_w = 153, .box_w = 10, .box_h = 16, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 35170, .adv_w = 146, .box_w = 8, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 35218, .adv_w = 153, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 35278, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_2[] = {
    0x0, 0x1, 0x3, 0x4, 0x6, 0xf, 0x15, 0x19
};

static const uint8_t glyph_id_ofs_list_5[] = {
    0, 0, 0, 1, 0, 0, 0, 0,
    0, 2, 3, 4, 5, 6, 7, 8,
    9, 10, 11, 12, 13, 14, 15, 16,
    17, 18, 0, 0, 0, 19
};

static const uint16_t unicode_list_7[] = {
    0x0, 0x1, 0x2, 0x5, 0x6, 0x8, 0xa, 0xf,
    0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f, 0x30, 0x31,
    0x32, 0x33, 0xe93b, 0xe942, 0xe945, 0xe946, 0xe947, 0xe94b,
    0xe94d, 0xe94f, 0xe953, 0xe956, 0xe95b, 0xe960, 0xe961, 0xe962,
    0xe978, 0xe97d, 0xe982, 0xe985, 0xe986, 0xe987, 0xe98b, 0xe98c,
    0xe98d, 0xe98e, 0xe9a1, 0xe9a2, 0xe9a8, 0xe9aa, 0xe9ab, 0xe9ae,
    0xe9b1, 0xe9b2, 0xe9b3, 0xe9b5, 0xe9cd, 0xe9cf, 0xe9fe, 0xe9ff,
    0xea01, 0xea03, 0xea1a, 0xea21, 0xea24, 0xea2d, 0xea56, 0xea5e,
    0xea95, 0xeb25, 0xeb7a, 0xeb7b, 0xeb7c, 0xeb7d, 0xeb7e, 0xebc1,
    0xebcd, 0xec27, 0xec3e, 0xee94, 0xf0fc, 0xf1dc
};

static const uint16_t unicode_list_9[] = {
    0x0, 0x1, 0x2, 0x3, 0x29, 0x2a, 0x2b, 0x2c,
    0x2d, 0x2e, 0x2f, 0x30, 0x31, 0x32, 0x34, 0x35,
    0x3a, 0x3b, 0x3c, 0x3d, 0x3e, 0x3f, 0x52, 0x53,
    0x54, 0x55, 0x2c6, 0x2c7, 0x2c8, 0x2c9, 0x2ca
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 1488, .range_length = 27, .glyph_id_start = 96,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 1542, .range_length = 26, .glyph_id_start = 123,
        .unicode_list = unicode_list_2, .glyph_id_ofs_list = NULL, .list_length = 8, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    },
    {
        .range_start = 1569, .range_length = 26, .glyph_id_start = 131,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 1600, .range_length = 22, .glyph_id_start = 157,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 1623, .range_length = 30, .glyph_id_start = 179,
        .unicode_list = NULL, .glyph_id_ofs_list = glyph_id_ofs_list_5, .list_length = 30, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_FULL
    },
    {
        .range_start = 1657, .range_length = 71, .glyph_id_start = 199,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 1734, .range_length = 61917, .glyph_id_start = 270,
        .unicode_list = unicode_list_7, .glyph_id_ofs_list = NULL, .list_length = 78, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    },
    {
        .range_start = 64338, .range_length = 82, .glyph_id_start = 348,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 64426, .range_length = 715, .glyph_id_start = 430,
        .unicode_list = unicode_list_9, .glyph_id_ofs_list = NULL, .list_length = 31, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    },
    {
        .range_start = 65142, .range_length = 135, .glyph_id_start = 461,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 65279, .range_length = 1, .glyph_id_start = 596,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    }
};



/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LV_VERSION_CHECK(8, 0, 0)
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = NULL,
    .kern_scale = 0,
    .cmap_num = 12,
    .bpp = 4,
    .kern_classes = 0,
    .bitmap_format = 0,
#if LV_VERSION_CHECK(8, 0, 0)
    .cache = &cache
#endif
};


/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LV_VERSION_CHECK(8, 0, 0)
const lv_font_t lv_font_dejavu_16_persian_hebrew = {
#else
lv_font_t lv_font_dejavu_16_persian_hebrew = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 24,          /*The maximum line height required by the font*/
    .base_line = 7,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0)
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc           /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
};



#endif /*#if LV_FONT_DEJAVU_16_PERSIAN_HEBREW*/

