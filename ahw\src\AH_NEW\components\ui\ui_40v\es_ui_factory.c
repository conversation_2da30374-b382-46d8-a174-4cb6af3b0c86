#include "es_inc.h"

#if (ES_UI_TYPE == ES_UI_TYPE_K40V_480_800)

// #define ES_UI_FACTORY_DEBUG
#ifdef ES_UI_FACTORY_DEBUG
#define es_ui_factory_debug es_log_info
#define es_ui_factory_error es_log_error
#else
#define es_ui_factory_debug(...)
#define es_ui_factory_error(...)
#endif

#define ES_UI_QRCODE_SIZE           (120)
#define ES_UI_QR_DATA_LEN           (64)

static lv_obj_t *lv_factory_bg = ES_NULL;
static lv_obj_t *lv_qrcode = ES_NULL;
static lv_timer_t *lv_factory_timer = NULL;

static ES_U16 qrcode_x = 0;
static ES_U16 qrcode_y = 0;
static ES_CHAR *factory_note = "Wait for initialization ...";
static ES_CHAR qrcode_data[ES_UI_QR_DATA_LEN];
static ES_U8 factory_got_data = 0;

extern lv_disp_t *cam_disp;

ES_S32 es_ui_factory_qrcode_open(ES_VOID)
{
    lv_obj_clean(lv_factory_bg);
    if (ES_NULL == lv_qrcode) {
        lv_qrcode = lv_qrcode_create(lv_factory_bg, ES_UI_QRCODE_SIZE, lv_color_hex3(0x33f), lv_color_hex3(0xeef));
    }

    lv_obj_align(lv_qrcode, LV_ALIGN_TOP_LEFT, qrcode_x, qrcode_y);
    lv_qrcode_update(lv_qrcode, qrcode_data, strlen(qrcode_data));

    return ES_RET_SUCCESS;
}

ES_S32 es_ui_factory_qrcode_close(ES_VOID)
{
    if (ES_NULL != lv_qrcode) {
        lv_qrcode_delete(lv_qrcode);
        lv_qrcode = ES_NULL;
    }
    return ES_RET_SUCCESS;
}

ES_S32 es_ui_factory_create_widgets(ES_VOID)
{
    lv_obj_t *obj;
    qrcode_x = (ES_UI_WIDTH - ES_UI_QRCODE_SIZE)>>1;
    qrcode_y = (ES_UI_HEIGHT - ES_UI_QRCODE_SIZE)>>1;

    lv_factory_bg = lv_obj_create(lv_disp_get_scr_act(cam_disp));
    lv_obj_remove_style_all(lv_factory_bg);
    lv_obj_set_size(lv_factory_bg, ES_UI_WIDTH, ES_UI_HEIGHT);
    lv_obj_align(lv_factory_bg, LV_ALIGN_TOP_LEFT, 0, 0);

    obj = lv_label_create(lv_factory_bg);
    lv_obj_align(obj, LV_ALIGN_TOP_MID, 0, 0);
    lv_label_set_text_static(obj, factory_note);

    return ES_RET_SUCCESS;
}

static ES_U32 es_ui_factory_init_data(ES_VOID)
{
    ES_BYTE wireless_mac[6] = {0};
    ES_U32 qrcode_data_len;

#if ES_BLE_MODULE_ENABLE
    ES_BYTE ble_mac[6] = {0};
#endif

#if ES_BLE_MODULE_ENABLE
    if (ES_RET_SUCCESS != es_ble_read_mac(ble_mac)) {
        return ES_RET_FAILURE;
    }
#endif

    if (ES_RET_SUCCESS != es_network_get_mac(wireless_mac)) {
        return ES_RET_FAILURE;
    }

    qrcode_data_len = es_snprintf(qrcode_data, ES_UI_QR_DATA_LEN, "{\"mac\":\"%02X%02X%02X%02X%02X%02X\"",
        wireless_mac[0], wireless_mac[1], wireless_mac[2], wireless_mac[3], wireless_mac[4], wireless_mac[5]);

#if ES_BLE_MODULE_ENABLE
    qrcode_data_len += es_snprintf(qrcode_data+qrcode_data_len, ES_UI_QR_DATA_LEN-qrcode_data_len, ",\"ble\":\"AIFACE-%02X%02X\"",
        ble_mac[4], ble_mac[5]);
#endif

    qrcode_data[qrcode_data_len] = '}';
    qrcode_data_len++;
    qrcode_data[qrcode_data_len] = '\0';
    factory_got_data = 1;

    return ES_RET_SUCCESS;
}

static ES_VOID factory_timer_cb(lv_timer_t *timer)
{
    if (0 == factory_got_data) {
        es_ui_factory_init_data();
        return;
    }

    if (ES_NULL == lv_qrcode) {
        es_ui_factory_qrcode_open();
    }
}

ES_S32 es_ui_factory_init(ES_VOID)
{
    es_ui_factory_create_widgets();
    es_ui_factory_show(ES_FALSE);

    lv_factory_timer = lv_timer_create(factory_timer_cb, 1000, NULL);
    if (ES_NULL == lv_factory_timer) {
        return ES_RET_FAILURE;
    }
    lv_timer_pause(lv_factory_timer);

    return ES_RET_SUCCESS;
}

ES_S32 es_ui_factory_show(ES_BOOL show)
{
    if (show) {
        lv_timer_resume(lv_factory_timer);
        lv_obj_clear_flag(lv_factory_bg, LV_OBJ_FLAG_HIDDEN);
    } else {
        lv_timer_pause(lv_factory_timer);
        lv_obj_add_flag(lv_factory_bg, LV_OBJ_FLAG_HIDDEN);
        es_ui_factory_qrcode_close();
    }

    return ES_RET_SUCCESS;
}

#endif