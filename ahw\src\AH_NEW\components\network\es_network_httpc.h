/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_network_mqtt.h
** bef: define the interface for httpc network. 
** auth: lines<<EMAIL>>
** create on 2022.02.26
*/

#ifndef _ES_NETWORK_HTTPC_H_
#define _ES_NETWORK_HTTPC_H_

#ifdef __cplusplus 
extern "C" { 
#endif


#include "es_types.h"

ES_S32 es_network_httpc_init(ES_VOID);

ES_S32 es_network_httpc_upload_pic(const ES_CHAR *url, const ES_BYTE *data, ES_U32 data_len,
   const ES_VOID *headers, ES_U32 header_count);

ES_S32 es_network_httpc_get_file(const ES_CHAR *url);

ES_S32 es_network_httpc_get_model_active(const ES_CHAR *url, const ES_BYTE *data, ES_U32 data_len);

#ifdef __cplusplus 
}
#endif

#endif