/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_utils.h
** bef: define the interface for configure 
** auth: lines<<EMAIL>>
** create on 2020.06.27 
*/

#ifndef _ES_BRD_CONFIG_K28VLTE_H_
#define _ES_BRD_CONFIG_K28VLTE_H_

#ifdef __cplusplus 
extern "C" { 
#endif

// Col is 96
/////////////////////////////////////// system info ///////////////////////////////////////////
#define ES_SYSTEM_INFO
#define ES_SYS_SVER_VAL                     (10000)
#define ES_SYS_SVER_STR                     ("1.0.0")
#define ES_SYS_HVER_VAL                     (10000)
#define ES_SYS_HVER_STR                     ("1.0.0")


/////////////////////////////////////// NFC Module ////////////////////////////////////////////
#define ES_NFC_MODULE_ENABLE                (1)
#define ES_NFC_CHIP_NONE                    (0)
#define ES_NFC_CHIP_WS1850S                 (1)
#define ES_NFC_ID_DATA_LEN                  (10)
#define ES_NFC_GPIO_IRQ_PIN                 (20)
#define ES_NFC_GPIO_RST_PIN                 (21)
#define ES_NFC_GPIO_LED_PIN                 (0)
#define ES_NFC_GPIO_IRQ_HS_NUM              (29)
#define ES_NFC_GPIO_RST_HS_NUM              (30)
#define ES_NFC_GPIO_LED_HS_NUM              (11)
#if ES_NFC_MODULE_ENABLE
#define ES_NFC_CHIP_TYPE                    (ES_NFC_CHIP_WS1850S)
#define ES_NFC_I2C_READ_ADDR                (0x28)
#define ES_NFC_I2C_WRITE_ADDR               (0x28)
#define ES_NFC_I2C_SPEED                    (100 * 1000)
#define WS1850S_ENABLE_GPIO_RST             (1)
#else
#define ES_NFC_CHIP_TYPE                    (ES_NFC_CHIP_NONE)
#endif/*  */


/////////////////////////////////////// LCD ///////////////////////////////////////////////////
#define ES_LCD_DRIVER_TYPE                  (ES_LCD_DRIVER_GC9306)
#define ES_LCD_DCX_HS_NUM                   (5)
#define ES_LCD_RST_HS_NUM                   (6)
#define ES_LCD_BL_HS_NUM                    (16)
#define ES_LCD_SPI_CS_HS_NUM                (17)
#define ES_LCD_CS_PIN                       (36)
#define ES_LCD_RST_PIN                      (37)
#define ES_LCD_DCX_PIN                      (38)
#define ES_LCD_SCK_PIN                      (39)
#define ES_LCD_BL_PIN                       (17)
#define ES_LCD_PWM_DEV_BL                   (PWM_DEVICE_0)
#define ES_LCD_PWDM_CHN_BL                  (PWM_CHANNEL_1)
#define ES_LCD_DIR                          (1) // 0:horizontal, 1:vertical
#define ES_LCD_HMIRROR                      (0)
#define ES_LCD_VFLIP                        (1)
#define ES_LCD_XY_SWAP                      (0)
#define ES_LCD_DIR_PARAM                    (0)
#define ES_LCD_WIDTH                        (240)
#define ES_LCD_HEIGHT                       (320)


/////////////////////////////////////// UI ////////////////////////////////////////////////////
#define ES_UI_WIDTH                         (ES_LCD_WIDTH)
#define ES_UI_HEIGHT                        (ES_LCD_HEIGHT)
#define ES_UI_CAM_WIDTH                     (240)
#define ES_UI_CAM_HEIGHT                    (320)
#define ES_UI_FACE_EDGE_LINE_WIDTH          (2) // pixel
#define ES_UI_FACE_EDGE_LINE_LENGTH         (10) // pixel
#define ES_UI_TYPE                          (ES_UI_TYPE_K28V_240_320)


/////////////////////////////////////// Camera ////////////////////////////////////////////////
#define ES_CAM_WIDTH                        (320)
#define ES_CAM_HEIGHT                       (240)
#define ES_CAM_DIR                          (1) // direction, 0:horizontal, 1:vertical
#define ES_CAM_HMIRROR                      (1) // hmirror
#define ES_CAM_VFLIP                        (0)// vflip
#define ES_CAM_EXP_TIME                     (128)


/////////////////////////////////////// UART //////////////////////////////////////////////////
#define ES_UART_MODULE_ENABLE               (1)
#define ES_UART0_RX_PIN                     (6)
#define ES_UART0_TX_PIN                     (7)
#define ES_UART1_RX_PIN                     (21)
#define ES_UART1_TX_PIN                     (20)
#define ES_UART2_RX_PIN                     (4)
#define ES_UART2_TX_PIN                     (5)

/////////////////////////////////////// KEY ///////////////////////////////////////////////////
#define ES_KEY_MODULE_ENABLE                (1)
#define ES_KEY1_GPIO_PIN                    (8)
#define ES_KEY1_GPIO_HS_NUM                 (0)
#define ES_KEY1_PRESS_VAL                   (0)


/////////////////////////////////////// file system ///////////////////////////////////////////
#define ES_FS_ENABLE                        (0)


/////////////////////////////////////// FACE //////////////////////////////////////////////////
#define ES_FACE_UPLOAD_ENABLE               (1)


/////////////////////////////////////// PASS LOG //////////////////////////////////////////////
#define ES_PASSLOG_ENABLE                   (1)
#define ES_PASSLOG_HDR_MAGIC                (0x12345678)
#define ES_PASSLOG_CACHE_COUNT              (8)
#define ES_PASSLOG_JSON_STR_LEN             (96)
#define ES_PASSLOG_UID_LEN                  (32)


/////////////////////////////////////// 4G LTE ////////////////////////////////////////////////
#define ES_4GLTE_ENABLE                     (1)
#define ES_4GLTE_UART_ID                    (ES_UART_ID_0)
#define ES_4GLTE_UART_BAUD                  (ES_UART_BAUD_115200)
#define ES_4GLTE_MODULE_TYPE                (ES_4GLTE_MODULE_EC800X)
#define ES_4GLTE_POWER_PIN                  (19)
#define ES_4GLTE_POWER_HS_NUM               (28)


#ifdef __cplusplus 
}
#endif
#endif