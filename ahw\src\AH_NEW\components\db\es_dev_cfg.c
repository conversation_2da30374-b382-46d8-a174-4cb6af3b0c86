/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_dev_cfg.c
** bef: implement the interface for device config.
** auth: lines<<EMAIL>>
** create on 2019.05.08 
*/

#include "es_inc.h"

// #define ES_DEV_CFG_DEBUG
#ifdef ES_DEV_CFG_DEBUG
#define es_dev_cfg_debug es_log_info
#define es_dev_cfg_error es_log_error
#else
#define es_dev_cfg_debug(...)
#define es_dev_cfg_error(...)
#endif

#define ES_DEV_CFG_UPDATE_TIME_SEC      (24*3600) // 24*60*60
#define ES_DEV_CFG_UPDATE_FAIL_SEC      (2*60)
#define ES_DEV_CFG_UPDATE_RUN_SEC       (60)//(60)

#define ES_DEV_CFG_UPDATE_WDG           (0)
#define ES_DEV_CFG_UPDATE_433           (0)

#define ES_DEV_CFG_JSON2HEX(json_data, hex, hex_len) do { \
        if (cJSON_String != json_data->type) { \
            es_dev_cfg_error("JSON2HEX json type error(%d)", json_data->type); \
            return ES_RET_FAILURE; \
        } \
        if (ES_NULL == json_data->valuestring) { \
            es_dev_cfg_error("JSON2HEX json value string is NULL"); \
            return ES_RET_FAILURE; \
        } \
        es_memset(hex, 0x00, hex_len); \
        es_utils_hex_str_to_bytes(json_data->valuestring, hex, hex_len); \
    } while (0)


ES_S32 es_dev_cfg_init(ES_VOID)
{
    return ES_RET_SUCCESS;
}


ES_S32 es_dev_cfg_check_nfc_id(const ES_BYTE *nfc_id)
{
#if ES_NFC_MODULE_ENABLE
    es_flash_nfc_t *nfc_list = ES_NULL;
    ES_U32 i = 0;

    es_flash_get_data_handle(ES_FLASH_ID_NFC, &nfc_list);
    if (ES_NULL == nfc_list) {
        return ES_RET_SUCCESS;
    }

    for (i = 0; i < nfc_list->count; i ++) {
        if (0 == es_memcmp(nfc_id, nfc_list->data[i], ES_NFC_ID_DATA_LEN)) {
            return ES_RET_SUCCESS;
        }
    }
#endif
    return ES_RET_FAILURE;
}

ES_S32 es_dev_cfg_update_nfc_list(const cJSON *array, ES_U32 pkg_id, ES_U32 pkg_num)
{
#if ES_NFC_MODULE_ENABLE
    es_flash_nfc_t flash_nfc;
    ES_BYTE nfc_id[ES_NFC_ID_DATA_LEN] = {0};
    ES_U32 i = 0;
    ES_U32 array_size = 0;
    cJSON *item_val;

    if (0 == pkg_id) {
        es_memset(&flash_nfc, 0x00, sizeof(flash_nfc));
    }
    
    array_size = cJSON_GetArraySize(array);
    if (0 == array_size) {
        goto END;
    }

    for (i = 0; i < array_size; i++) {
        item_val = cJSON_GetArrayItem(array, i);
        if (ES_NULL == item_val) {
            return ES_RET_FAILURE;
        }

        ES_DEV_CFG_JSON2HEX(item_val, nfc_id, ES_NFC_ID_DATA_LEN);
        es_memcpy(flash_nfc.data[flash_nfc.count], nfc_id, ES_NFC_ID_DATA_LEN);
        flash_nfc.count++;
    }

#ifdef ES_DEV_CFG_DEBUG
    for (i = 0; i < flash_nfc.count; i++) {
        es_dev_cfg_debug("%d, nfc data:", i);
        es_log_dump_hex(flash_nfc.data[i], ES_NFC_ID_DATA_LEN);
    }
#endif

END:
    if ((pkg_id+1) == pkg_num || pkg_num == 0) {
        if (ES_RET_SUCCESS != es_flash_write(ES_FLASH_ID_NFC, (ES_VOID *)&flash_nfc)) {
            return ES_RET_FAILURE;
        }

        if (ES_RET_SUCCESS != es_flash_sync()) {
            return ES_RET_FAILURE;
        }
    }
    
    dev_cfg_nfc_update_time = es_time_get_timestamp();
#endif
    return ES_RET_SUCCESS;
}

ES_S32 es_dev_cfg_update_ble_mac_list(const cJSON *array)
{
#if ES_BLE_MODULE_ENABLE
#define ES_DEV_CFG_JSON_KEY_BLE_MAC             ("mac")
#define ES_DEV_CFG_JSON_KEY_BLE_DATA            ("data")
#define ES_DEV_CFG_JSON_KEY_BLE_SERV_UUID       ("serv_uuid")
#define ES_DEV_CFG_JSON_KEY_BLE_CHAR_UUID       ("char_uuid")

    es_flash_ble_mac_t flash_ble;
    ES_U32 i = 0;
    ES_BYTE tmp_bytes[ES_BLE_PAYLOAD_DATA_LEN] = {0};
    cJSON *item_val;
    cJSON *json_data;
    ES_U32 array_size = 0;

    es_memset(&flash_ble, 0x00, sizeof(flash_ble));

    array_size = cJSON_GetArraySize(array);
    if (0 == array_size) {
        es_dev_cfg_debug("array size is 0");
        goto END;
    }

    for (i = 0; i < array_size; i++) {
        item_val = cJSON_GetArrayItem(array, i);
        if (ES_NULL == item_val) {
            es_dev_cfg_error("array %d is NULL", i);
            return ES_RET_FAILURE;
        }

        json_data = cJSON_GetObjectItem(item_val, ES_DEV_CFG_JSON_KEY_BLE_MAC);
        if (ES_NULL == json_data) {
            es_dev_cfg_error("ble mac json is NULL");
            return ES_RET_FAILURE;
        }

        ES_DEV_CFG_JSON2HEX(json_data, tmp_bytes, ES_BLE_MAC_LEN);
        es_memcpy(flash_ble.mac[i], tmp_bytes, ES_BLE_MAC_LEN);

        json_data = cJSON_GetObjectItem(item_val, ES_DEV_CFG_JSON_KEY_BLE_DATA);
        if (ES_NULL == json_data) {
            es_dev_cfg_error("ble data json is NULL");
            return ES_RET_FAILURE;
        }
        ES_DEV_CFG_JSON2HEX(json_data, tmp_bytes, ES_BLE_PAYLOAD_DATA_LEN);
        flash_ble.data_len[i] = es_strlen(json_data->valuestring)/2;
        es_memcpy(flash_ble.data[i], tmp_bytes, ES_BLE_PAYLOAD_DATA_LEN);

        json_data = cJSON_GetObjectItem(item_val, ES_DEV_CFG_JSON_KEY_BLE_SERV_UUID);
        if (ES_NULL == json_data) {
            es_dev_cfg_error("ble serv uuid json is NULL");
            return ES_RET_FAILURE;
        }
        ES_DEV_CFG_JSON2HEX(json_data, tmp_bytes, ES_BLE_UUID_LEN);
        es_memcpy(flash_ble.serv_uuid[i], tmp_bytes, ES_BLE_UUID_LEN);

        json_data = cJSON_GetObjectItem(item_val, ES_DEV_CFG_JSON_KEY_BLE_CHAR_UUID);
        if (ES_NULL == json_data) {
            es_dev_cfg_error("ble serv uuid json is NULL");
            return ES_RET_FAILURE;
        }
        ES_DEV_CFG_JSON2HEX(json_data, tmp_bytes, ES_BLE_UUID_LEN);
        es_memcpy(flash_ble.char_uuid[i], tmp_bytes, ES_BLE_UUID_LEN);
    }
    flash_ble.count = array_size;

#ifdef ES_DEV_CFG_DEBUG
    for (i = 0; i < flash_ble.count; i++) {
        es_dev_cfg_debug("%d, ble mac:", i);
        es_log_dump_hex(flash_ble.mac[i], ES_BLE_MAC_LEN);
        es_dev_cfg_debug("ble data:");
        es_log_dump_hex(flash_ble.data[i], ES_BLE_PAYLOAD_DATA_LEN);
    }
#endif

END:
    if (ES_RET_SUCCESS != es_flash_write(ES_FLASH_ID_BLE_MAC, (ES_VOID *)&flash_ble)) {
        return ES_RET_FAILURE;
    }

    if (ES_RET_SUCCESS != es_flash_sync()) {
        return ES_RET_FAILURE;
    }
#endif
    return ES_RET_SUCCESS;
}

ES_S32 es_dev_cfg_update_qrcode(const ES_CHAR *qrcode_str)
{
    es_flash_user_data_t user_data;

    memset(&user_data, 0x00, sizeof(user_data));
    es_flash_read(ES_FLASH_ID_USER_DATA, (ES_VOID *)&user_data);
    strncpy((ES_CHAR *)user_data.qrcode, qrcode_str, ES_FLASH_USER_QRCODE_LEN);
    if (ES_RET_SUCCESS != es_flash_write(ES_FLASH_ID_USER_DATA, (ES_VOID *)&user_data)) {
        return ES_RET_FAILURE;
    }
    if (ES_RET_SUCCESS != es_flash_sync()) {
        return ES_RET_FAILURE;
    }
    return ES_RET_SUCCESS;
}


ES_S32 es_dev_cfg_get_qrcode(ES_CHAR *qrcode_str)
{
    es_flash_user_data_t user_data;

    memset(&user_data, 0x00, sizeof(user_data));
    es_flash_read(ES_FLASH_ID_USER_DATA, (ES_VOID *)&user_data);
    if (0 == user_data.qrcode[0] || 0xFF == user_data.qrcode[0]) {
        return ES_RET_FAILURE;
    }
    strncpy(qrcode_str, (ES_CHAR *)user_data.qrcode, ES_FLASH_USER_QRCODE_LEN);
    return ES_RET_SUCCESS;
}
