#include "facelib_inc.h"
/*****************************************************************************/
// Macro definitions
/*****************************************************************************/


/*****************************************************************************/
// Function definitions
/*****************************************************************************/


/*****************************************************************************/
// Public Var 全局变量
/*****************************************************************************/
mf_facedb_t mf_facedb;
extern mf_facedb_t mf_facedb_flash;

/*****************************************************************************/
// Public Func 全局函数
/*****************************************************************************/
mf_err_t mf_facedb_init(void)
{
	memcpy(&mf_facedb, &mf_facedb_flash, sizeof(mf_facedb_t));
	return MF_ERR_NONE;
}
