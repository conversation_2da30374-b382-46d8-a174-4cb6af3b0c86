#include "es_inc.h"


ES_S32 es_hal_i2c_init(ES_VOID)
{
    fpioa_set_function(ES_I2C_SCL_PIN, FUNC_I2C2_SCLK);
    fpioa_set_function(ES_I2C_SDA_PIN, FUNC_I2C2_SDA);

    return ES_RET_SUCCESS;
}

ES_S32 es_hal_i2c_read_byte(const es_hal_i2c_cfg_t *cfg, ES_U8 reg, ES_BYTE *data)
{
    i2c_init(cfg->i2c_id, cfg->addr, 7, cfg->speed);
    if(0 != i2c_recv_data(cfg->i2c_id, &reg, 1, (uint8_t *)data, 1)) {
        printk("i2c read err\r\n");
        return ES_RET_FAILURE;
    }

    return ES_RET_SUCCESS;
}

ES_S32 es_hal_i2c_write_byte(const es_hal_i2c_cfg_t *cfg, ES_U8 reg, ES_BYTE data)
{
    uint8_t send_data[2] = {reg, data};

    i2c_init(cfg->i2c_id, cfg->addr, 7, cfg->speed);
    if(0 != i2c_send_data(cfg->i2c_id, send_data, 2)) {
        printk("i2c send err\r\n");
        return ES_RET_FAILURE;
    }

    return ES_RET_SUCCESS;
}