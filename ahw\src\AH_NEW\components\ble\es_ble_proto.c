/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_uart.h
** bef: define the interface for ble protocol module.
** auth: lines<<EMAIL>>
** create on 2021.05.18 
*/

#include "es_inc.h"

#if ES_BLE_MODULE_ENABLE

// #define BLE_PROTO_DEBUG
#ifdef BLE_PROTO_DEBUG
#define ble_proto_debug es_log_info
#define ble_proto_error es_log_error
#else
#define ble_proto_debug(...)
#define ble_proto_error(...)
#endif

#define ES_BLE_PROTO_HEAD_IND               (0)
#define ES_BLE_PROTO_HEAD_LEN               (2)
#define ES_BLE_PROTO_HEAD_DATA_0            (0xA5)
#define ES_BLE_PROTO_HEAD_DATA_1            (0xA5)
#define ES_BLE_PROTO_CMD_IND                (ES_BLE_PROTO_HEAD_IND + ES_BLE_PROTO_HEAD_LEN)
#define ES_BLE_PROTO_CMD_LEN                (1)
#define ES_BLE_PROTO_MSGID_IND              (ES_BLE_PROTO_CMD_IND + ES_BLE_PROTO_CMD_LEN)
#define ES_BLE_PROTO_MSGIG_LEN              (1)
#define ES_BLE_PROTO_DLEN_IND               (ES_BLE_PROTO_MSGID_IND + ES_BLE_PROTO_MSGIG_LEN)
#define ES_BLE_PROTO_DLEN_LEN               (1)
#define ES_BLE_PROTO_DATA_IND               (ES_BLE_PROTO_DLEN_IND + ES_BLE_PROTO_DLEN_LEN)
#define ES_BLE_PROTO_DATA_LEN               (0)
#define ES_BLE_PROTO_CRC8_IND               (ES_BLE_PROTO_DATA_IND + ES_BLE_PROTO_DATA_LEN)
#define ES_BLE_PROTO_CRC8_LEN               (1)
#define ES_BLE_PROTO_MIN_LEN                (ES_BLE_PROTO_CRC8_IND+ES_BLE_PROTO_CRC8_LEN)

#define ES_BLE_PROTO_CMD_KEY_VAL            (0x01)
#define ES_BLE_PTOTO_CMD_WIFI_SSID          (0x02)
#define ES_BLE_PTOTO_CMD_WIFI_PWD           (0x03)
#define ES_BLE_PTOTO_CMD_FILE_INFO          (0x04)
#define ES_BLE_PTOTO_CMD_FILE_DATA          (0x05)
#define ES_BLE_PTOTO_CMD_FILE_END           (0x06)
#define ES_BLE_PTOTO_CMD_ACTIVE             (0x07)
#define ES_BLE_PTOTO_CMD_RESET              (0x08)
#define ES_BLE_PTOTO_CMD_SYNC_TIME          (0x09)
#define ES_BLE_PTOTO_CMD_PASSLOG_COUNT      (0x0A)
#define ES_BLE_PTOTO_CMD_PASSLOG_DATA       (0x0B)

#define ES_BLE_PROTO_FILE_OFFSET_SIZE       (2)

#define ES_BLE_PROTO_BUF_LEN                (300)

#define ES_BLE_PROTO_WIFI_SSID_LEN          (15)
#define ES_BLE_PROTO_WIFI_PWS_LEN           (15)

static ES_BYTE ble_proto_buf[ES_BLE_PROTO_BUF_LEN] = {0};
static ES_U32 ble_proto_buf_len = 0;
static ES_CHAR wifi_ssid[ES_BLE_PROTO_WIFI_SSID_LEN+1] = {0};
static ES_CHAR wifi_pwd[ES_BLE_PROTO_WIFI_PWS_LEN+1] = {0};


static unsigned char es_ble_proto_checksum(const ES_BYTE *message, ES_U32 len)
{
    ES_BYTE crc = 0;

    // ble_proto_debug("checksum, data_len:%d", len);
    // es_log_dump_hex(message, len);
    while(len--) {
        crc ^= *message++;
        for(ES_U8 i = 0; i < 8; i++) {
            if(crc & 0x01)
                crc = (crc >> 1) ^ 0x8c;
            else
                crc >>= 1;
        }
    }
    return crc;
}

static ES_S32 es_ble_proto_send_data(ES_BYTE cmd, ES_BYTE msg_id, ES_BYTE *data, ES_U8 dlen)
{
    ES_U8 i = 0;
    ES_BYTE crc8 = 0;
    es_ble_msg_t ble_msg;

    es_memset(&ble_msg, 0x00, sizeof(ble_msg));

    ble_msg.data[i++] = ES_BLE_PROTO_HEAD_DATA_0;
    ble_msg.data[i++] = ES_BLE_PROTO_HEAD_DATA_0;
    /// cmd
    ble_msg.data[i++] = cmd;
    /// msg id
    ble_msg.data[i++] = msg_id;
    /// data length
    ble_msg.data[i++] = dlen;
    /// data
    if (dlen > 0) {
        es_memcpy(&ble_msg.data[i], data, dlen);
        i += dlen;
    }

    crc8 = es_ble_proto_checksum((const ES_BYTE *)ble_msg.data, (ES_U32)i);
    ble_msg.data[i++] = crc8;

    ble_msg.to_slave = 0;
    ble_msg.data_len = (ES_U32)i;
    ble_msg.is_ack = ES_TRUE;
    return es_ble_add_msg(&ble_msg);
}

static ES_S32 es_ble_proto_resp(ES_BYTE cmd, ES_BYTE msg_id, ES_S32 ret)
{
    ES_BYTE data[1] = {0};

    data[0] = 0;
    if (ES_RET_SUCCESS != ret) {
        data[0] = 1;
    }

    return es_ble_proto_send_data(cmd, msg_id, data, 1);
}

static ES_S32 es_ble_proto_parse_key(ES_BYTE *data, ES_U32 len)
{
    if (0 == data[0] && 0 == data[1]) {
        // ble_key_data.ble_key_press = 1;
        ble_proto_debug("key0 press");
        return ES_RET_SUCCESS;
    }

    if (0 == data[0] && 1 == data[1]) {
        // ble_key_data.ble_key_long_press = 1;
        ble_proto_debug("key0 long press");
        return ES_RET_SUCCESS;
    }

    if (1 == data[0] && 0 == data[1]) {
        // ble_key_data.ble_key1_press = 1;
        ble_proto_debug("key1 press");
        return ES_RET_SUCCESS;
    }

    if (1 == data[0] && 1 == data[1]) {
        // ble_key_data.ble_key1_long_press = 1;
        ble_proto_debug("key1 long press");
        return ES_RET_SUCCESS;
    }

    if (2 == data[0] && 0 == data[1]) {
        // ble_key_data.ble_key2_press = 1;
        ble_proto_debug("key2 press");
        return ES_RET_SUCCESS;
    }

    if (2 == data[0] && 1 == data[1]) {
        // ble_key_data.ble_key2_long_press = 1;
        ble_proto_debug("key1 long press");
        return ES_RET_SUCCESS;
    }

    return ES_RET_FAILURE;
}

static ES_S32 es_ble_proto_parse_file(ES_BYTE cmd, ES_BYTE *data, ES_U32 len)
{
    static ES_VOID *cache = ES_NULL;
    static ES_U32 file_type = 0;
    static ES_U32 file_size = 0;
    static ES_BYTE crc8 = 0;
    ES_U32 offset = 0;
    ES_S32 write_len = 0;
    ES_S32 ret = 0;
    ES_BYTE tmp_crc8 = 0;
    const ES_BYTE *cache_buf_addr = ES_NULL;
    es_task_param_t task_param;

    if (cmd == ES_BLE_PTOTO_CMD_FILE_INFO) {
        file_type = (ES_U32)(data[0] & 0xFF);
        file_size = (ES_U32)(data[2] & 0xFF) | (ES_U32)((data[1] & 0xFF) << 8);
        crc8 = data[3] & 0xFF;
        if (cache != ES_NULL) {
            es_cache_destroy(cache);
            cache = ES_NULL;
        }
        cache = es_cache_create(file_size);
        ble_proto_debug("file_type:%d, file_size:%d, crc8:%02x", file_type, file_size, crc8&0xFF);
        return ES_RET_SUCCESS;
    }

    if (cache == ES_NULL) {
        ble_proto_error("cache is NULL");
        return ES_RET_FAILURE;
    }

    ///// write data
    if (cmd == ES_BLE_PTOTO_CMD_FILE_DATA) {
        offset |= (ES_U32)((data[0] & 0xFF) << 8);
        offset |= (ES_U32)(data[1] & 0xFF);

        if (offset >= file_size || len <= ES_BLE_PROTO_FILE_OFFSET_SIZE) {
            ble_proto_error("error, offset:%d, len:%d", offset, len);
            return ES_RET_FAILURE;
        }

        write_len = len - ES_BLE_PROTO_FILE_OFFSET_SIZE;
        ret = es_cache_write(cache, offset, &data[ES_BLE_PROTO_FILE_OFFSET_SIZE], (ES_U32)write_len);
        if (write_len != ret) {
            ble_proto_error("es_cache_write, error, offset:%d, write_len:%d, ret:%d", offset, write_len, ret);
            return ES_RET_FAILURE;
        }
        // ble_proto_debug("data, offset:%d", offset);
        // es_log_dump_hex(&data[ES_BLE_PROTO_FILE_OFFSET_SIZE], (ES_U32)write_len);
        return ES_RET_SUCCESS;
    }

    // end
    cache_buf_addr = es_cache_get_buf_addr(cache);
    tmp_crc8 = es_ble_proto_checksum(cache_buf_addr, (ES_U32)file_size);
    if (crc8 != tmp_crc8) {
        ble_proto_error("crc8 error, %02x:%02x", crc8&0xFF, tmp_crc8&0xFFF);
        // ble_proto_error("data hex:");
        // es_log_dump_hex(cache_buf_addr, file_size);
        // ble_proto_error("data string:\r\n%s", cache_buf_addr);
        es_cache_destroy(cache);
        cache = ES_NULL;
        return ES_RET_FAILURE;
    }

    if (1 == file_type) {
        // es_facedb_file_parse(cache_buf_addr);
        es_memset(&task_param, 0x00, sizeof(task_param));
        task_param.type = ES_TASK_PARSE_FACE_FILE;
        task_param.param = (ES_VOID *)cache_buf_addr;
        task_param.timeout = 0;
        es_task_queue_push_wait((const es_task_param_t *)&task_param);
    } else if (2 == file_type) {
        es_serv_proto_parse((const ES_CHAR *)cache_buf_addr, ES_NULL);
    }

    es_cache_destroy(cache);
    cache = ES_NULL;
    return ES_RET_SUCCESS;
}

static ES_S32 es_ble_proto_parse_sync_time(ES_BYTE *data, ES_U32 len)
{
    es_time_t t;

    // ble_proto_debug("sysn time, len:%d", len);
    if (len != 4) {
        return ES_RET_FAILURE;
    }

    es_memset(&t, 0x00, sizeof(t));
    t.timestamp = (ES_U32)(data[3] & 0xFF) | (ES_U32)((data[2] & 0xFF) << 8) 
                | (ES_U32)((data[1] & 0xFF) << 16) | (ES_U32)((data[0] & 0xFF) << 24);

    // ble_proto_debug("t.timestamp:%d", t.timestamp);
    return es_time_sync(&t);
}

#if ES_PASSLOG_ENABLE
static ES_S32 es_ble_resp_passlog_count(ES_BYTE msg_id)
{
    ES_U32 passlog_count = 0;
    ES_BYTE data[2] = {0};

    passlog_count = es_passlog_get_count();
    data[0] = (ES_BYTE)((passlog_count>>8) & 0xFF);
    data[1] = (ES_BYTE)(passlog_count & 0xFF);
    ble_proto_debug("passlog count, data:%02x, %02x", data[0], data[1]);
    return es_ble_proto_send_data(ES_BLE_PTOTO_CMD_PASSLOG_COUNT, msg_id, data, 2);
}

static ES_S32 es_ble_resp_passlog_data(ES_BYTE msg_id, ES_BYTE is_read)
{
    es_task_param_t task_param;
    ES_CHAR json_str[ES_PASSLOG_JSON_STR_LEN] = {0};
    ES_U32 json_str_len = 0;

    if (0x01 == is_read) {
        es_memset(&task_param, 0x00, sizeof(task_param));
        task_param.type = ES_TASK_PASSLOG_UPLOAD_RESULT;
        task_param.param = (ES_VOID *)(ES_TRUE);
        task_param.timeout = 0;
        return es_task_queue_push_wait((const es_task_param_t *)&task_param);
    }

    json_str_len = es_passlog_read_json_str(json_str, ES_PASSLOG_JSON_STR_LEN-20);
    if (json_str_len < ES_PASSLOG_UID_LEN) {
        json_str_len = 0;
    }
    ble_proto_debug("json_str(len=%d):%s", json_str_len, json_str);
    return es_ble_proto_send_data(ES_BLE_PTOTO_CMD_PASSLOG_DATA, msg_id, (ES_BYTE *)json_str, json_str_len);
}
#endif

static ES_VOID es_ble_proto_save_package(const ES_BYTE *buf, ES_U16 len)
{
    ES_U32 i = 0;

    for (i = 0; i < len; i++) {
        if (ble_proto_buf_len < ES_BLE_PROTO_HEAD_LEN) {
            if (0 == ble_proto_buf_len) {
                if (ES_BLE_PROTO_HEAD_DATA_0 != buf[i]) {
                    continue;
                }
            } else {
                if (ES_BLE_PROTO_HEAD_DATA_1 != buf[i]) {
                    ble_proto_buf_len = 0;
                    continue;
                }
            }
        }

        if (ble_proto_buf_len < ES_BLE_PROTO_BUF_LEN) {
            ble_proto_buf[ble_proto_buf_len++] = buf[i];
        }
    }
}

static ES_S32 es_ble_proto_parse_package(ES_VOID)
{
    es_task_param_t task_param;
    ES_U8 package_len;
    ES_U8 data_len;
    ES_U8 checksum;
    ES_U8 remind_len;
    ES_U32 ret = ES_RET_SUCCESS;

    if (ble_proto_buf_len < ES_BLE_PROTO_MIN_LEN) {
        // ble_proto_error("ble_proto_buf_len(%d) < ES_BLE_PROTO_MIN_LEN(%d)", ble_proto_buf_len, ES_BLE_PROTO_MIN_LEN);
        return ES_RET_FAILURE;
    }

    data_len = ble_proto_buf[ES_BLE_PROTO_DLEN_IND];
    package_len = ES_BLE_PROTO_MIN_LEN + data_len;
    if (ble_proto_buf_len < package_len) {
        // ble_proto_error("ble_proto_buf_len(%d) < package_len(%d)", ble_proto_buf_len, package_len);
        return ES_RET_FAILURE;
    }

    // ble_proto_debug("package_len:%d, data_len:%d, ble_proto_buf_len:%d", package_len, data_len, ble_proto_buf_len);
    checksum = es_ble_proto_checksum((const ES_BYTE *)ble_proto_buf, (ES_U32)(ES_BLE_PROTO_DATA_IND+data_len));
    if (checksum != ble_proto_buf[ES_BLE_PROTO_CRC8_IND+data_len]) {
        ble_proto_buf_len = 0;
        ble_proto_error("checksum error, %02x,%02x", checksum, ble_proto_buf[ES_BLE_PROTO_CRC8_IND+data_len]);
        // es_log_dump_hex(ble_proto_buf, package_len);
        return ES_RET_FAILURE;
    }
    
    // ble_proto_debug("ble cmd:%02x", ble_proto_buf[ES_BLE_PROTO_CMD_IND]&0xFF);
    if (ES_BLE_PROTO_CMD_KEY_VAL == ble_proto_buf[ES_BLE_PROTO_CMD_IND]) {
        es_ble_proto_parse_key(&ble_proto_buf[ES_BLE_PROTO_DATA_IND], data_len);
    } else if (ES_BLE_PTOTO_CMD_WIFI_SSID == ble_proto_buf[ES_BLE_PROTO_CMD_IND]) {
        if (data_len > 0) {
            memset(wifi_ssid, 0x00, sizeof(wifi_ssid));
            memcpy(wifi_ssid, &ble_proto_buf[ES_BLE_PROTO_DATA_IND], data_len);
            ble_proto_debug("ssid:%s", wifi_ssid);
        }
    } else if (ES_BLE_PTOTO_CMD_WIFI_PWD == ble_proto_buf[ES_BLE_PROTO_CMD_IND]) {
        memset(wifi_pwd, 0x00, sizeof(wifi_pwd));
        if (data_len > 0) {
            memcpy(wifi_pwd, &ble_proto_buf[ES_BLE_PROTO_DATA_IND], data_len);
        } else {
            strncpy(wifi_pwd, "12345678", ES_BLE_PROTO_WIFI_PWS_LEN);
        }
        ble_proto_debug("wifi_pwd:%s", wifi_pwd);

        if (wifi_ssid[0] != 0) {
            // mf5_wechat_update_wifi_config(wifi_ssid, wifi_pwd);
        }
    } else if (ES_BLE_PTOTO_CMD_FILE_INFO == ble_proto_buf[ES_BLE_PROTO_CMD_IND]
                || ES_BLE_PTOTO_CMD_FILE_DATA == ble_proto_buf[ES_BLE_PROTO_CMD_IND]
                || ES_BLE_PTOTO_CMD_FILE_END == ble_proto_buf[ES_BLE_PROTO_CMD_IND]) {
        ret = es_ble_proto_parse_file(ble_proto_buf[ES_BLE_PROTO_CMD_IND], 
                    &ble_proto_buf[ES_BLE_PROTO_DATA_IND], data_len);
        es_ble_proto_resp(ble_proto_buf[ES_BLE_PROTO_CMD_IND], ble_proto_buf[ES_BLE_PROTO_MSGID_IND], ret);
        
    } else if (ES_BLE_PTOTO_CMD_ACTIVE == ble_proto_buf[ES_BLE_PROTO_CMD_IND]) {
        task_param.type = ES_TASK_ACTIVE_DEVICE;
        task_param.param = ES_NULL;
        task_param.timeout = 100;
        ret = es_task_queue_push_wait(&task_param);
        es_ble_proto_resp(ble_proto_buf[ES_BLE_PROTO_CMD_IND], ble_proto_buf[ES_BLE_PROTO_MSGID_IND], ret);
    } else if (ES_BLE_PTOTO_CMD_RESET == ble_proto_buf[ES_BLE_PROTO_CMD_IND]) {
        task_param.type = ES_TASK_RESET_DEVICE;
        task_param.param = ES_NULL;
        task_param.timeout = 100;
        ret = es_task_queue_push_wait(&task_param);
        es_ble_proto_resp(ble_proto_buf[ES_BLE_PROTO_CMD_IND], ble_proto_buf[ES_BLE_PROTO_MSGID_IND], ret);
    } else if (ES_BLE_PTOTO_CMD_SYNC_TIME == ble_proto_buf[ES_BLE_PROTO_CMD_IND]) {
        ret = es_ble_proto_parse_sync_time(&ble_proto_buf[ES_BLE_PROTO_DATA_IND], data_len);
        es_ble_proto_resp(ble_proto_buf[ES_BLE_PROTO_CMD_IND], ble_proto_buf[ES_BLE_PROTO_MSGID_IND], ret);
#if ES_PASSLOG_ENABLE
    } if (ES_BLE_PTOTO_CMD_PASSLOG_COUNT == ble_proto_buf[ES_BLE_PROTO_CMD_IND]) {
        es_ble_resp_passlog_count(ble_proto_buf[ES_BLE_PROTO_MSGID_IND]);
    } if (ES_BLE_PTOTO_CMD_PASSLOG_DATA == ble_proto_buf[ES_BLE_PROTO_CMD_IND]) {
        es_ble_resp_passlog_data(ble_proto_buf[ES_BLE_PROTO_MSGID_IND], ble_proto_buf[ES_BLE_PROTO_DATA_IND]);
#endif
    }


    remind_len = ble_proto_buf_len - package_len;
    ble_proto_buf_len = 0;
    if (remind_len > 0) {
        es_ble_proto_save_package(&ble_proto_buf[package_len], remind_len);
    }

    return ES_RET_SUCCESS;
}


ES_S32 es_ble_proto_parse(ES_BYTE *data, ES_U32 data_len)
{
    // ble_proto_debug("es_ble_proto_parse, data_len:%d", data_len);
    // es_log_dump_hex(data, data_len);
    es_ble_proto_save_package(data, data_len);
    if (ES_RET_SUCCESS != es_ble_proto_parse_package()) {
        return ES_RET_FAILURE;
    }

    return ES_RET_SUCCESS;
}

#endif


