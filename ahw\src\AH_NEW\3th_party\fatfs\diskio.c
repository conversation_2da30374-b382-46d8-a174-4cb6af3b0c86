/*-----------------------------------------------------------------------*/
/* Low level disk I/O module SKELETON for FatFs     (C)ChaN, 2019        */
/*-----------------------------------------------------------------------*/
/* If a working storage control module is available, it should be        */
/* attached to the FatFs via a glue function rather than modifying it.   */
/* This is an example of glue functions to attach various exsisting      */
/* storage control modules to the FatFs module with a defined API.       */
/*-----------------------------------------------------------------------*/

#include "ff.h"			/* Obtains integer types */
#include "diskio.h"		/* Declarations of disk functions */
#include "facelib_inc.h"
#include "es_inc.h"

// #define ES_DISKIO_DEBUG
#ifdef ES_DISKIO_DEBUG
#define es_diskio_debug es_log_info
#define es_diskio_error es_log_error
#else
#define es_diskio_debug(...)
#define es_diskio_error(...)
#endif

/* Definitions of physical drive number for each drive */
// #define DEV_RAM		3	/* Example: Map Ramdisk to physical drive 0 */
// #define DEV_MMC		1	/* Example: Map MMC/SD card to physical drive 1 */
// #define DEV_USB		2	/* Example: Map USB MSD to physical drive 2 */
#define SPI_FALSH	0

#define FATFS_FLASH_ADDR			(0xC00000)
#define FATFS_FLASH_SIZE			(1024*1024)
#define FATFS_SECTOR_SIZE			(4096)
#define FATFS_SECTOR_COUNT			((FATFS_FLASH_SIZE)/(FATFS_SECTOR_SIZE))

/*-----------------------------------------------------------------------*/
/* Get Drive Status                                                      */
/*-----------------------------------------------------------------------*/

DSTATUS disk_status (
	BYTE pdrv		/* Physical drive nmuber to identify the drive */
)
{
	// DSTATUS stat;
	// int result;

	// switch (pdrv) {
	// case DEV_RAM :
	// 	result = RAM_disk_status();

	// 	// translate the reslut code here

	// 	return stat;

	// case DEV_MMC :
	// 	result = MMC_disk_status();

	// 	// translate the reslut code here

	// 	return stat;

	// case DEV_USB :
	// 	result = USB_disk_status();

	// 	// translate the reslut code here

	// 	return stat;
	// }
	// return STA_NOINIT;

	// es_diskio_debug("fatfs status, pdrv:%d", pdrv);
	if (SPI_FALSH == pdrv) {
		// es_diskio_debug("fatfs spi flash status ok");
		return RES_OK;
	}

	return RES_PARERR;
}



/*-----------------------------------------------------------------------*/
/* Inidialize a Drive                                                    */
/*-----------------------------------------------------------------------*/

DSTATUS disk_initialize (
	BYTE pdrv				/* Physical drive nmuber to identify the drive */
)
{
	// DSTATUS stat;
	// int result;

	// switch (pdrv) {
	// case DEV_RAM :
	// 	result = RAM_disk_initialize();

	// 	// translate the reslut code here

	// 	return stat;

	// case DEV_MMC :
	// 	result = MMC_disk_initialize();

	// 	// translate the reslut code here

	// 	return stat;

	// case DEV_USB :
	// 	result = USB_disk_initialize();

	// 	// translate the reslut code here

	// 	return stat;
	// }
	// return STA_NOINIT;

	// es_diskio_debug("fatfs init, pdrv:%d", pdrv);
	if (SPI_FALSH == pdrv) {
		// es_diskio_debug("fatfs spi flash init ok");
		if (mf_flash.init_flag) {
			return RES_OK;
		}
		return RES_PARERR;
	}

	return RES_PARERR;
}



/*-----------------------------------------------------------------------*/
/* Read Sector(s)                                                        */
/*-----------------------------------------------------------------------*/

DRESULT disk_read (
	BYTE pdrv,		/* Physical drive nmuber to identify the drive */
	BYTE *buff,		/* Data buffer to store read data */
	LBA_t sector,	/* Start sector in LBA */
	UINT count		/* Number of sectors to read */
)
{
	// DRESULT res;
	// int result;

	// switch (pdrv) {
	// case DEV_RAM :
	// 	// translate the arguments here

	// 	result = RAM_disk_read(buff, sector, count);

	// 	// translate the reslut code here

	// 	return res;

	// case DEV_MMC :
	// 	// translate the arguments here

	// 	result = MMC_disk_read(buff, sector, count);

	// 	// translate the reslut code here

	// 	return res;

	// case DEV_USB :
	// 	// translate the arguments here

	// 	result = USB_disk_read(buff, sector, count);

	// 	// translate the reslut code here

	// 	return res;
	// }

	if (SPI_FALSH == pdrv) {
		// es_diskio_debug("fatfs read, sector:%d, count:%d", sector, count);
		mf_flash.read(FATFS_FLASH_ADDR + (sector*FATFS_SECTOR_SIZE), buff, count*FATFS_SECTOR_SIZE);
		// es_log_dump_hex(buff, count*FATFS_SECTOR_SIZE);
		return RES_OK;
	}

	return RES_PARERR;
}



/*-----------------------------------------------------------------------*/
/* Write Sector(s)                                                       */
/*-----------------------------------------------------------------------*/

#if FF_FS_READONLY == 0

DRESULT disk_write (
	BYTE pdrv,			/* Physical drive nmuber to identify the drive */
	const BYTE *buff,	/* Data to be written */
	LBA_t sector,		/* Start sector in LBA */
	UINT count			/* Number of sectors to write */
)
{
	// DRESULT res;
	// int result;

	// switch (pdrv) {
	// case DEV_RAM :
	// 	// translate the arguments here

	// 	result = RAM_disk_write(buff, sector, count);

	// 	// translate the reslut code here

	// 	return res;

	// case DEV_MMC :
	// 	// translate the arguments here

	// 	result = MMC_disk_write(buff, sector, count);

	// 	// translate the reslut code here

	// 	return res;

	// case DEV_USB :
	// 	// translate the arguments here

	// 	result = USB_disk_write(buff, sector, count);

	// 	// translate the reslut code here

	// 	return res;
	// }

	// es_diskio_debug("fatfs write, pdrv:%d", pdrv);
	if (SPI_FALSH == pdrv) {
		// es_diskio_debug("fatfs write, sector:%d", sector);
		mf_flash.write(FATFS_FLASH_ADDR + (sector*FATFS_SECTOR_SIZE), (uint8_t *)buff, count*FATFS_SECTOR_SIZE);
		return RES_OK;
	}

	return RES_PARERR;
}

#endif


/*-----------------------------------------------------------------------*/
/* Miscellaneous Functions                                               */
/*-----------------------------------------------------------------------*/

DRESULT disk_ioctl (
	BYTE pdrv,		/* Physical drive nmuber (0..) */
	BYTE cmd,		/* Control code */
	void *buff		/* Buffer to send/receive control data */
)
{
	// DRESULT res;
	// int result;

	// switch (pdrv) {
	// case DEV_RAM :

	// 	// Process of the command for the RAM drive

	// 	return res;

	// case DEV_MMC :

	// 	// Process of the command for the MMC/SD card

	// 	return res;

	// case DEV_USB :

	// 	// Process of the command the USB drive

	// 	return res;
	// }

	// es_diskio_debug("fatfs ioctl, pdrv:%d", pdrv);
	if (SPI_FALSH == pdrv) {
		// es_diskio_debug("fatfs ioctl, cmd:%d", cmd);
		if (CTRL_SYNC == cmd) {
			return RES_OK;
		} else if (GET_SECTOR_COUNT == cmd) {
			*(DWORD *)buff = FATFS_SECTOR_COUNT;
			return RES_OK;
		} else if (GET_SECTOR_SIZE == cmd) {
			*(WORD *)buff = FATFS_SECTOR_SIZE;
			return RES_OK;
		} else if (GET_BLOCK_SIZE == cmd) {
			*(DWORD *)buff = 1;
			return RES_OK;
		}
	}

	return RES_PARERR;
}

#if !FF_FS_READONLY && !FF_FS_NORTC
DWORD get_fattime (void)
{
	return 0;
}
#endif

