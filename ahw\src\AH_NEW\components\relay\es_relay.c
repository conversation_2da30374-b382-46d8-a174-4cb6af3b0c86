/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_relay.c
** bef: define the interface for relay.
** auth: lines<<EMAIL>>
** create on 2022.01.08 
*/

#include "es_inc.h"

#if ES_RELAY_ENABLE
#include "facelib_inc.h"

// #define ES_RELAY_DEBUG
#ifdef ES_RELAY_DEBUG
#define es_relay_debug es_log_info
#define es_relay_error es_log_error
#else
#define es_relay_debug(...)
#define es_relay_error(...)
#endif

#define ES_RELAY_TASK_COUNT     (2)

typedef struct {
    ES_U32 start_time;
    ES_U16 continue_ms;
    ES_U16 status; //  0 dile, 1, doing
} es_relay_task_t;

static es_relay_task_t relay_tasks[ES_RELAY_TASK_COUNT];
static struct pt pt_relay;

ES_S32 es_relay_init(ES_VOID)
{
    es_memset(relay_tasks, 0x00, sizeof(relay_tasks));
    fpioa_set_function(ES_RELAY_GPIO_PIN, FUNC_GPIOHS0 + ES_RELAY_GPIO_HS_NUM);
    gpiohs_set_drive_mode(ES_RELAY_GPIO_HS_NUM, GPIO_DM_OUTPUT);
    gpiohs_set_pin(ES_RELAY_GPIO_HS_NUM, !mf_brd.cfg.relay.pol);

    PT_INIT(&pt_relay);

    return ES_RET_SUCCESS;
}

static ES_U8 es_relay_get_task_id(ES_VOID)
{
    ES_U8 i = 0;

    for (i = 0; i < ES_RELAY_TASK_COUNT; i++) {
        if (1 == relay_tasks[i].status && 0 != relay_tasks[i].continue_ms) {
            return i;
        }
    }

    return ES_RELAY_TASK_COUNT;
}

static PT_THREAD(es_relay_task(struct pt *pt))
{
    static ES_U8 task_id = 0;

    PT_BEGIN(pt);
    task_id = es_relay_get_task_id();
    if (task_id == ES_RELAY_TASK_COUNT) {
        goto TASK_END;
    }

    es_relay_debug("relay open");
    gpiohs_set_pin(ES_RELAY_GPIO_HS_NUM, mf_brd.cfg.relay.pol);
    relay_tasks[task_id].start_time = es_time_get_sytem_ms();
    PT_WAIT_UNTIL(pt, ((es_time_get_sytem_ms() - relay_tasks[task_id].start_time) > relay_tasks[task_id].continue_ms));
    gpiohs_set_pin(ES_RELAY_GPIO_HS_NUM, !mf_brd.cfg.relay.pol);
    es_memset(&relay_tasks[task_id], 0x00, sizeof(es_relay_task_t));
    es_relay_debug("relay close");

TASK_END:
    task_id = 0;
    PT_END(pt);
}

ES_VOID es_relay_run(ES_VOID)
{
    es_relay_task(&pt_relay);
}

ES_S32 es_relay_open(ES_U16 doing_ms)
{
    ES_U32 i = 0;

    for (i = 0; i < ES_RELAY_TASK_COUNT; i++) {
        if (0 == relay_tasks[i].status && 0 == relay_tasks[i].continue_ms) {
            relay_tasks[i].status = 1;
            relay_tasks[i].continue_ms = (ES_U16)doing_ms;
            return ES_RET_SUCCESS;
        }
    }

    return ES_RET_FAILURE;
}

ES_S32 es_relay_open_always(ES_VOID)
{
    gpiohs_set_pin(ES_RELAY_GPIO_HS_NUM, mf_brd.cfg.relay.pol);
    return ES_RET_SUCCESS;
}

ES_S32 es_relay_close_always(ES_VOID)
{
    gpiohs_set_pin(ES_RELAY_GPIO_HS_NUM, !mf_brd.cfg.relay.pol);
    return ES_RET_SUCCESS;
}

#endif
