#ifndef _ES_FACECB_H_
#define _ES_FACECB_H_

#ifdef __cplusplus 
extern "C" { 
#endif

#include "es_types.h"

typedef enum {
    ES_FACECB_PASS,  // 0
    ES_FACECB_STRANGER, // 1
    ES_FACECB_FAKE,  // 2
    ES_FACECB_DETECT, // 3
    ES_FACECB_AUTH_FAIL, // 4
    ES_FACECB_CAPTURE, // 5
    ES_FACECB_NONE,
    ES_FACECB_END
} es_facecb_type_e;

ES_U32 es_facecb_get_detect_time_ms(ES_VOID);

ES_VOID es_facecb_update_detect_time(ES_VOID);

#ifdef __cplusplus 
}
#endif
#endif