#ifndef _ES_HAL_LTE_H_
#define _ES_HAL_LTE_H_

#ifdef __cplusplus 
extern "C" { 
#endif

#include "es_types.h"

typedef struct {
    ES_CHAR ci[ES_LTE_CELL_CI_LEN];
    ES_CHAR lac[ES_LTE_CELL_LAC_LEN];
} es_hal_lte_cell_t;

typedef struct {
    ES_CHAR name[16];     // ref ES_HTTP_HEADER_NAME_LEN, FIleName FileType
    ES_CHAR value[32];    // ref ES_HTTP_HEADER_VALUE_LEN, mac-timestamp.jpg, 112233445566-1656406359.jpg
} es_http_header_t;

typedef enum {
    HTTP_EVENT_CONTENT_LEN,
    HTTP_EVENT_BODY_DATA,
    HTTP_EVENT_DOWNLOAD_END
} es_http_event_e;

typedef ES_VOID (*es_hal_lte_data_cb)(es_http_event_e event, const ES_BYTE *data, ES_U32 data_len);
typedef ES_VOID (*es_hal_lte_mqtt_sub_cb)(const ES_CHAR *topic, const ES_BYTE *data, ES_U16 data_len);

ES_S32 es_hal_lte_init(ES_VOID);

ES_VOID es_hal_lte_task(ES_VOID);

ES_BOOL es_hal_lte_is_ready(ES_VOID);

ES_S32 es_hal_lte_mqtt_send(const ES_CHAR *topic, const ES_BYTE *data, ES_U16 data_len);

ES_S32 es_hal_lte_register_mqtt_sub_cb(es_hal_lte_mqtt_sub_cb cb);

ES_S32 es_hal_lte_http_get(const ES_CHAR *url);

ES_S32 es_hal_lte_http_post(const ES_CHAR *url, const ES_BYTE *data, ES_U32 data_len);

ES_S32 es_hal_lte_http_post_jpg(const ES_CHAR *url, const es_http_header_t *headers, 
            ES_U32 header_count, const ES_BYTE *data, ES_U32 data_len);

ES_S32 es_hal_lte_register_http_cb(es_hal_lte_data_cb cb);

ES_S32 es_hal_lte_get_mac(ES_BYTE *mac);

// ref ES_MAC_COLON_STR_LEN
// ref ES_MQTT_CFG_DEV_MAC_FMT
ES_S32 es_hal_lte_get_mqtt_id(ES_CHAR *mqtt_id);

const es_hal_lte_cell_t *es_hal_lte_get_cell_info(ES_VOID);

const ES_CHAR *es_hal_lte_get_iccid(ES_VOID);

const ES_CHAR *es_hal_lte_get_imei(ES_VOID);

ES_U8 es_hal_lte_get_rssi(ES_VOID);

// es_gps_info_t
ES_S32 es_hal_lte_get_gps(ES_VOID *gps);

#ifdef __cplusplus 
}
#endif
#endif
