#ifndef _MF_BRD_H
#define _MF_BRD_H

#include <stdint.h>
#include "mf_constants.h"
/*****************************************************************************/
// Enums & Macro
/*****************************************************************************/
#define PLL0_OUTPUT_FREQ 1000000000UL
#define PLL1_OUTPUT_FREQ 400000000UL

//每一次大的更改配置结构的时候建议修改版本
#define CFG_VERSION (0.2f)
#define CFG_HEADER (0x55AA5550)

/* LCD_SIPEED 使用了timer1来刷屏 */
#define FLASH_BL_LED_PWM_DEV				(PWM_DEVICE_0)

#define FLASH_LED_PWM_CHN					(PWM_CHANNEL_0)
#define LCD_BL_PWDM_CHN						(PWM_CHANNEL_1)

#define ES_CAR_NUM_LEN						(4)

typedef enum 
{
	MF_BOARD_MF0_H		= 0,
	MF_BOARD_MF0_V		= 1,
	MF_BOARD_MF1_H		= 2,
	MF_BOARD_MF1_V		= 3,
	MF_BOARD_MF2_H		= 4,
	MF_BOARD_MF2_V		= 5,
	MF_BOARD_MF3_H		= 6,
	MF_BOARD_MF3_V		= 7,
	MF_BOARD_MF4IPS_V	= 8,
	MF_BOARD_MF5IPS_V	= 9,
	MF_BOARD_MF57_H		= 10,
	MF_BOARD_MF57_V		= 11,

	MF_BOARD_NONE,		/* Invalid */
}mf_brd_type_t;

/* 这里为了减小编译后的固件体积，需要与上边的枚举保持一致 */
#define LIB_UI_TYPE_MF0_H			(0)
#define LIB_UI_TYPE_MF0_V			(1)
#define LIB_UI_TYPE_MF1_H			(2)
#define LIB_UI_TYPE_MF1_V			(3)
#define LIB_UI_TYPE_MF2_H			(4)
#define LIB_UI_TYPE_MF2_V			(5)
#define LIB_UI_TYPE_MF3_H			(6)
#define LIB_UI_TYPE_MF3_V			(7)
#define LIB_UI_TYPE_MF4IPS_V		(8)
#define LIB_UI_TYPE_MF5IPS_V		(9)
#define LIB_UI_TYPE_MF57_H			(10)
#define LIB_UI_TYPE_MF57_V			(11)
#define LIB_UI_TYPE_NONE			(12)

#define LTE_GPS_CACHE_STR_LEN               (128)

/*****************************************************************************/
// Types
/*****************************************************************************/
typedef struct _board_cfg
{
    uint8_t cfg_sha256[32];
    uint32_t header;
    float version;
    uint32_t cfg_right_flag;
	uint8_t dump_flag;
	/********************** HardWare ************************/
	struct
	{
		uint8_t exp_t;
		uint8_t dir     : 1;
		uint8_t vflip   : 1;  //cam_flip
		uint8_t hmirror : 1;  //cam_hmirror
	} cam;
	
	struct
	{
		uint8_t dir     : 1;  //lcd_dir
		uint8_t vflip   : 1;  //lcd_flip
		uint8_t hmirror : 1;  //lcd_hmirror
		uint8_t xyswap  : 1;  //xy swap
		uint8_t inverse : 1;  //inverse color
	} lcd;
	
	struct
	{
		uint8_t port_tx;
		uint8_t port_rx;
		uint8_t log_tx ;
		uint8_t log_rx ;
	} uart;
	
	struct
	{
		uint8_t pin;          //relay_high
		uint8_t pol;   
		uint8_t opent;        //0.1s unit
	} relay;
	
	struct
	{
		uint8_t pin;
		uint8_t pol;          //key_dir
	} key;	
	struct
	{
		uint8_t pin;
		uint8_t pol;          //key_dir
	} key1;	
	struct
	{
		uint8_t pin;
		uint8_t pol;          //key_dir
	} key2;	
	
	struct
	{
		uint8_t pin;
		uint8_t pol;
		float   high;
		float   low;
	} led;	
	
	struct
	{
		uint8_t red;    //21
		uint8_t green;  //22
		uint8_t blue;   //23
		uint8_t pol;    //0
	} rgbled;	
	
	struct
	{
		uint8_t pin;
		uint8_t pol;
	} ir;	
	
	struct
	{
		uint8_t pin;
		uint8_t pol;
	} bl;

	struct
	{
		uint8_t scl;
		uint8_t sda;
	} i2c;	
	
	struct
	{
		uint8_t type;    //0:PT8211; 1:ES8374
		uint8_t pa;
		uint8_t pol;
		uint8_t bck;
		uint8_t lrck;
		uint8_t din;
		uint8_t dout;
		uint8_t mclk;
	} audio;
	
	struct
	{
		uint8_t type;    //0:esp8285; 1:w5500; 255: none
		uint8_t sck;
		uint8_t mosi;
		uint8_t miso;
		uint8_t cs;      //eth pin 24
		uint8_t rst;
	} net;
	
	
	/********************** SoftWare ************************/
	struct
	{
		uint8_t ftr_len;
		uint8_t face_minw;
		uint8_t face_minh;
		uint8_t checkpose;
		float   fd_gate;
		float   fe_gate;	//RGB
		float   fe_gate_ir;
		float   live_gate;
	} model;
	
	struct 
	{
		uint8_t tx_pin;
		uint32_t baud;
	}pic_stream;
	
	struct
	{
		union 
		{//匿名联合体中的匿名结构体吗，直接使用, 不能重名
			struct { //uartp_json使用
				uint8_t  out_fea         : 2;
				uint8_t  auto_out_fea    : 1;
				uint8_t  pkt_fix         : 1;
				uint8_t  notify_en		 : 1;
				uint8_t  notify_outftr	 : 1;
				uint16_t out_interval_ms;
			};	
			struct { //uartp_bin使用
				uint32_t tmp;
				//待添加
			};
		};
		uint32_t port_baud;
	} uartp;
	
	struct
	{
		uint8_t close_lcd; //close lcd bl when no face?
		uint8_t night_gate;        //night lum threshold
		uint8_t open_lcd_n;        //lcd on time, 0 for ever
		uint8_t open_led_n;        //led on n loop when ir have face 
		uint8_t open_ir_n;         //ir on n loop when ir have face
		uint8_t darkface_n;        //n loop ir face while vis no face
		uint8_t ir_period;  //n loop when ir no face
		uint8_t ir_oni;     //turn on i loop in n
	} flow;
	
	// struct
	// {
	// 	uint8_t ssid[8];
	// 	uint8_t passwd[32];
	// } wifi;
	uint8_t gps_cache_str[LTE_GPS_CACHE_STR_LEN]; // 40

	uint8_t car_num[ES_CAR_NUM_LEN];
	uint32_t car_expire;
	uint32_t limit_speed;
	uint32_t working_face_detect;
	uint8_t lock;
	uint8_t posture_detect;
	uint8_t emergency;
	uint8_t safetybelt_detect;
	
	/********************** Others ************************/
    uint8_t user_custom_cfg[420]; // 508
	// uint32_t lcdswapline;
	uint8_t lcdswapline;
	uint8_t fake_upload;
	uint8_t stranger_upload;
	uint8_t factory_flag;

	
} board_cfg_t __attribute__((aligned(8)));


typedef struct
{
	//Private
	//Public
	uint16_t init_flag;
	board_cfg_t cfg;
	const board_cfg_t* default_cfg;
	mf_brd_type_t type;
	uint8_t boot_section;
	uint8_t key_press;
	uint8_t key_long_press;
	uint8_t key1_press;
	uint8_t key1_long_press;
	uint8_t key2_press;
	uint8_t key2_long_press;
	uint32_t relay_start_t;	//in 0.1s unit
	uint8_t relay_flag;
	uint8_t relay_time;
	uint8_t dvp_open_ir;	//cmd to dvp irq open ir
	uint8_t rgb_state;
	uint8_t net_break_flag;
	//Const Public
	mf_err_t (*init)(mf_brd_type_t board_type, const board_cfg_t* cfg);
	mf_err_t (*cfg_save)(void);
	mf_err_t (*reset_default)(const board_cfg_t* cfg);
	void (*print)(void);
	void (*update_key)(void);
	void (*set_IR_LED)(int state);
	void (*set_W_LED)(int state);
	void (*turn_W_LED)(float duty);
	void (*set_RGB)(int state); //b0:R, b1:G, b2:B
	void (*set_relay)(int state, uint8_t time); //0.1s unit
	void (*relay_loop)(void); //check relay to close
} mf_brd_t;


#define UPLOAD_PIC_MIN_TIME_US				(5*1000*1000)
#define NET_BROKEN_TIME_MS					(5*1000*120)

typedef struct {
	uint32_t last_upload_time;
} upload_fs_flag_t;

typedef struct {
	uint32_t net_brk_time;
} wifi_brk_flag_t;

/*****************************************************************************/
// Functions
/*****************************************************************************/


/*****************************************************************************/
// Vars
/*****************************************************************************/
extern mf_brd_t mf_brd;

#endif