#ifndef _MF_MODELIN_H
#define _MF_MODELIN_H

#include "mf_kpu_v1.h"
#include "mf_kpu_v3.h"

#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>


typedef struct
{
    float threshold;
    float nms_value;
    uint32_t coords;
    uint32_t anchor_number;
    float *anchor;
    uint32_t image_width;
    uint32_t image_height;
    uint32_t classes;
    uint32_t net_width;
    uint32_t net_height;
    uint32_t layer_width;
    uint32_t layer_height;
    uint32_t boxes_number;
    uint32_t output_number;
    void *boxes;
    float *input;
    float *output;
    float *probs_buf;
    float **probs;
} region_layer_t;


/*****************************************************************************/
// Enums & Macro
/*****************************************************************************/
enum otp_status_t
{
    OTP_OK = 0,
    OTP_ERROR_TIMEOUT,   /* operation timeout*/
    OTP_ERROR_ADDRESS,   /* invalid address*/
    OTP_ERROR_WRITE,     /* write error*/
    OTP_ERROR_BLANK,     /* blank check error*/
    OTP_ERROR_BISR,      /* bisr error*/
    OTP_ERROR_TESTDEC,   /* testdec error*/
    OTP_ERROR_WRTEST,    /* wrtest error*/
    OTP_ERROR_KEYCOMP,   /* key is wrong*/
    OTP_ERROR_PARAM,     /* param error*/
    OTP_ERROR_NULL,      /* undefine error*/
    OTP_BLOCK_NORMAL,    /* block can be written*/
    OTP_BLOCK_PROTECTED, /* block can not be written*/
    OTP_FUNC_ENABLE,     /* function available*/
    OTP_FUNC_DISABLE,    /* function unavailable*/
    OTP_FLAG_SET,        /* flag set*/
    OTP_FLAG_UNSET,      /* flag unset*/
};

enum otp_data_block_t
{
    COMMON_DATA_BLOCK1 = 0,
    COMMON_DATA_BLOCK2,
    COMMON_DATA_BLOCK3,
    COMMON_DATA_BLOCK4,
    COMMON_DATA_BLOCK5,
    COMMON_DATA_BLOCK6,
    COMMON_DATA_BLOCK7,
    COMMON_DATA_BLOCK8,
    COMMON_DATA_BLOCK9,
    COMMON_DATA_BLOCK10,
    COMMON_DATA_BLOCK11,
    COMMON_DATA_BLOCK12,
    COMMON_DATA_BLOCK13,
    COMMON_DATA_BLOCK14,
    COMMON_DATA_BLOCK15,
    DATA_BLOCK_RESERVE,
    SYSTEM_DATA_BLOCK1,
    SYSTEM_DATA_BLOCK2,
    SYSTEM_DATA_BLOCK3,
    SYSTEM_DATA_BLOCK4,
    SYSTEM_DATA_BLOCK5,
    SYSTEM_DATA_BLOCK6,
    SYSTEM_DATA_BLOCK7,
    SYSTEM_DATA_BLOCK8,
    SYSTEM_DATA_BLOCK9,
    SYSTEM_DATA_BLOCK10,
    SYSTEM_DATA_BLOCK11,
    SYSTEM_DATA_BLOCK12,
    SYSTEM_DATA_BLOCK13,
    SYSTEM_DATA_BLOCK14,
    SYSTEM_DATA_BLOCK15,
    SYSTEM_DATA_BLOCK16,
    SYSTEM_DATA_BLOCK17,
    SYSTEM_DATA_BLOCK18,
    SYSTEM_DATA_BLOCK19,
    SYSTEM_DATA_BLOCK20,
    SYSTEM_DATA_BLOCK21,
    SYSTEM_DATA_BLOCK22,
    SYSTEM_DATA_BLOCK23,
    SYSTEM_DATA_BLOCK24,
    SYSTEM_DATA_BLOCK25,
    SYSTEM_DATA_BLOCK26,
    SYSTEM_DATA_BLOCK27,
    SYSTEM_DATA_BLOCK28,
    SYSTEM_DATA_BLOCK29,
    SYSTEM_DATA_BLOCK30,
    SYSTEM_DATA_BLOCK31,
    SYSTEM_DATA_BLOCK32,
    SYSTEM_DATA_BLOCK33,
    SYSTEM_DATA_BLOCK34,
    SYSTEM_DATA_BLOCK35,
    SYSTEM_DATA_BLOCK36,
    SYSTEM_DATA_BLOCK37,
    SYSTEM_DATA_BLOCK38,
    SYSTEM_DATA_BLOCK39,
    SYSTEM_DATA_BLOCK40,
    SYSTEM_DATA_BLOCK41,
    SYSTEM_DATA_BLOCK42,
    SYSTEM_DATA_BLOCK43,
    SYSTEM_DATA_BLOCK44,
    SYSTEM_DATA_BLOCK45,
    SYSTEM_DATA_BLOCK46,
    SYSTEM_DATA_BLOCK47,
    SYSTEM_DATA_BLOCK48,
    DATA_BLOCK_MAX = 64,
};

enum otp_func_reg_t
{
    BLANK_TEST_DISABLE = 0,
    RAM_BISR_DISABLE,
    AES_WRITE_DISABLE,  //不可写aeskey
    AES_VERIFY_DISABLE, //不可校验aes key
    JTAG_DISABLE,       //彻底锁死
    I2C2AXI_DISABLE,    //彻底锁死
    TEST_EN_DISABLE,
    ISP_DISABLE, //彻底锁死
    OTP_FUNC_FIRMWARE_CIPHER_DISABLE,
    FUNC_REG_MAX = 64,
};
#define OTP_SYSTEM_DATA_ADDR 0x00003AD0U
/* myotp.c */
extern void myotp_init_aaa(uint8_t div, uint32_t a, uint32_t b);
extern size_t end_myotp_init_aaa(void);

extern void otp_key_output_enable(void);
extern void otp_key_output_disable(void);
extern enum otp_status_t otp_status_get(uint32_t flag);

extern enum otp_status_t myotp_write_data(uint32_t addr, uint8_t *data_buf, uint32_t length);
extern size_t end_myotp_write_data(void);

extern enum otp_status_t myotp_read_data(uint32_t addr, uint8_t *data_buf, uint32_t length);
extern size_t end_myotp_read_data(void);

extern enum otp_status_t myotp_key_write(uint8_t *data_buf);
extern size_t end_myotp_key_write(void);

extern enum otp_status_t myotp_key_compare(uint8_t *data_buf);
extern size_t end_myotp_key_compare(void);

extern enum otp_status_t myotp_data_block_protect_set(enum otp_data_block_t block);
extern size_t end_myotp_data_block_protect_set(void);

extern enum otp_status_t myotp_func_reg_disable_set(enum otp_func_reg_t func);
extern size_t end_myotp_func_reg_disable_set(void);

extern enum otp_status_t myotp_data_block_protect_get(enum otp_data_block_t block);
extern size_t end_myotp_data_block_protect_get(void);

extern enum otp_status_t otp_data_block_protect_refresh(enum otp_data_block_t block);

extern uint32_t otp_wrong_address_get(void);


//Note: Address must 4KB align
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//model 3M ~ 8M
#define DETV_MODEL_ADDRESS    (0x300000) //3M --  //default Vertical
#define DETV_MODEL_SIZE       (388784)      //(380 * 1024)

#define KP_MODEL_ADDRESS      (0x364000) //__START__ + 400K
#define KP_MODEL_SIZE         (131824)      //(132 * 1024)

//这里活体往后靠，为以后的V3版KP腾出空间，预留了228KB给V3 KP (目前需要245KB，而V1仅138KB)
#define NOSE_MODEL_ADDRESS    (0x39D000)
#define NOSE_MODEL_SIZE       (33 * 1024)
#define EYE_MODEL_ADDRESS     (0x3A6000)
#define EYE_MODEL_SIZE        (33 * 1024)
#define MOUTH_MODEL_ADDRESS   (0x3AF000)
#define MOUTH_MODEL_SIZE      (33 * 1024)

#define FEATURE_MODEL_ADDRESS (0x3B8000) //__START__ + 400K + 132K + 140K + 64K
#define FEATURE_MODEL_SIZE    (3408 * 1024) //(3406 * 1024)  --> 0x70c000

#define DETH_MODEL_ADDRESS    (0x710000) //det Horizontal
#define DETHV3_MODEL_ADDRESS    (0x525000) //det Horizontal
#define DETH_MODEL_SIZE       (388784)      //(380 * 1024)  --> 0x76F000

/* 192维输出的特征值 */
#define FEATURE_MODEL_V2_ADDRESS    (768 * 1024)
#define FEATURE_MODEL_V2_SIZE       (2350288)

/* 192维输出的特征值V3 */
// #define FEATURE_MODEL_V3_ADDRESS    (9700 * 1024)
#define FEATURE_MODEL_V3_ADDRESS    (0x3B8000 - 4096)
#define FEATURE_MODEL_V3_SIZE       (0xECC48)

// #define FEATURE_MODEL_V3_CONV_ADDR  (0xA65C48)
#define FEATURE_MODEL_V3_CONV_ADDR  (0x4A4C48 - 4096)
#define FEATURE_MODEL_V3_CONV_SIZE  (0x20000) /* 128K */

// #define FEATURE_MODEL_V3_FC_ADDR    (0xA85C48)
#define FEATURE_MODEL_V3_FC_ADDR    (0x4C4C48 - 4096)
#define FEATURE_MODEL_V3_FC_SIZE    (0x60000) /* 384K */

#define FEATURE_MODEL_V4_ADDRESS    (0x3B8000 + 4096)
#define FEATURE_MODEL_V4_SIZE       (1105336)

#define FEATURE_MODEL_V5_ADDRESS    (3690496)  //0x385000
#define FEATURE_MODEL_V5_SIZE       (982952)

#define FEATURE_MODEL_V5_CONV_ADDR  (4714496)
#define FEATURE_MODEL_V5_CONV_SIZE  (131072)

#define FEATURE_MODEL_V5_FC_ADDR    (4845568)
#define FEATURE_MODEL_V5_FC_SIZE    (393216)

/* DMA & anchor */
#define FACE_DMAC_CHANNEL           (DMAC_CHANNEL5)
#define ANCHOR_NUM                  (5)

/*****************************************************************************/
// Types
/*****************************************************************************/


typedef struct
{
	uint8_t ftr_len;
	//key 
	uint8_t key_in_otp[16];
	//fd
	uint8_t* fd_data_original;
	uint8_t* fd_data;
	//ld
	uint8_t* ld_data_original;
	uint8_t* ld_data;
	//fe
	uint8_t* fe_data_original;
	uint8_t* fe_data;
	//live
	uint8_t* eye_data_original;
	uint8_t* eye_data;
	uint8_t* nose_data_original;
	uint8_t* nose_data;
	uint8_t* mouth_data_original;
	uint8_t* mouth_data;
}mf_modelin_t;


/*****************************************************************************/
// Functions
/*****************************************************************************/
extern int region_layer_init(region_layer_t *rl, int width, int height, int channels, int origin_width, int origin_height);
extern void region_layer_run(region_layer_t *rl, face_obj_info_t *obj_info);
extern void region_layer_deinit(region_layer_t *rl);
extern int image_init(image_t *image);
extern void image_deinit(image_t *image);
extern void image_crop(image_t *image_src, image_t *image_dst, uint16_t x_offset, uint16_t y_offset);
extern void image_crop_roate_right90(image_t *image_src, image_t *image_dst, uint16_t x_offset, uint16_t y_offset);
extern void image_crop_roate_left_90(image_t *image_src, image_t *image_dst, uint16_t x_offset, uint16_t y_offset);
extern void image_resize(image_t *image_src, image_t *image_dst);
extern void image_umeyama(float *src, float *dst);
extern void image_similarity(image_t *image_src, image_t *image_dst, float *T);
extern void image_similarity_roate_right90(image_t *image_src, image_t *image_dst, float *T);
extern void image_similarity_roate_left90(image_t *image_src, image_t *image_dst, float *T);
extern void key_point_last_handle(mf_kpu_task_t *task, key_point_t *key_point);
extern uint8_t blur_detect_run(uint8_t *buf_in, uint16_t w, uint16_t h, float scale);

extern float run_eye_model(image_t *img);
extern float run_nose_model(image_t *img);
extern void turn_img_gray(image_t *img, uint8_t dest);
extern int cal_eye_distance(key_point_t *kp);
extern int cal_eye_nose_distance(key_point_t *kp);
/*****************************************************************************/
// Vars
/*****************************************************************************/
extern volatile uint8_t face_lib_model_debug_en;

#endif