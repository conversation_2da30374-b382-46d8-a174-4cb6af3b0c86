/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_mem.c
** bef: define the interface for memory management.
** auth: lines<<EMAIL>>
** create on 2022.01.12 
*/

#include "es_inc.h"

#define ES_MEM_USE_POOL                 (1)
#if ES_MEM_USE_POOL
#define ES_MEM_MAGIC_HEAD               (0xA55A5AA5)
#define ES_MEM_POOL_MAX_SIZE            (64)
#endif

#define ES_MEM_USE_STAT                 (0)
#if ES_MEM_USE_STAT
#define ES_MEM_STAT_MAX_COUNT           (100)
#endif

// #define ES_MEM_DEBUG
#ifdef ES_MEM_DEBUG
#define es_mem_debug es_log_info
#define es_mem_error es_log_error
#else
#define es_mem_debug(...)
#define es_mem_error(...)
#endif


#define ES_MEM_LOCK_INIT()
#define ES_MEM_LOCK() do { \
        corelock_lock(&mem_lock); \
    } while(0)

#define ES_MEM_UNLOCK() do { \
        corelock_unlock(&mem_lock); \
    } while(0)

#if ES_MEM_USE_POOL
typedef struct {
    ES_VOID *mem;
    ES_U16 mem_size;
    ES_U16 unit_size;
} es_mem_pool_t;

static ES_BYTE mem_pool_size_32[1024];
static ES_BYTE mem_pool_size_64[2048];

static es_mem_pool_t mem_pools[] = {
    {.mem = (ES_VOID *)mem_pool_size_32, .mem_size = 1024, .unit_size = 32},
    {.mem = (ES_VOID *)mem_pool_size_64, .mem_size = 2048, .unit_size = 64},
};
#endif


#if ES_MEM_USE_STAT
typedef struct {
    ES_VOID *mem;
    ES_U32 size;
} es_mem_stat_node_t;

typedef struct {
    es_mem_stat_node_t nodes[ES_MEM_STAT_MAX_COUNT];
    ES_U16 used_size;
    ES_U32 free_size;
} es_mem_stat_info_t;

static es_mem_stat_info_t mem_stat_info;
#endif

static corelock_t mem_lock = CORELOCK_INIT;


#if ES_MEM_USE_STAT
static ES_VOID mem_pool_stat_malloc(ES_VOID *mem, ES_U32 size)
{
    ES_U32 i;

    for (i = 0; i < ES_MEM_STAT_MAX_COUNT; i++) {
        if (ES_NULL == mem_stat_info.nodes[i].mem && 0 == mem_stat_info.nodes[i].size) {
            mem_stat_info.nodes[i].mem = mem;
            mem_stat_info.nodes[i].size = size;
            mem_stat_info.used_size += size;
            return;
        }
    }
}

static ES_VOID mem_pool_stat_free(ES_VOID *mem)
{
    ES_U32 i;

    for (i = 0; i < ES_MEM_STAT_MAX_COUNT; i++) {
        if (mem == mem_stat_info.nodes[i].mem) {
            mem_stat_info.nodes[i].mem = ES_NULL;
            mem_stat_info.used_size -= mem_stat_info.nodes[i].size;
            mem_stat_info.free_size += mem_stat_info.nodes[i].size;
            mem_stat_info.nodes[i].size = 0;
            return;
        }
    }
}
#endif

#if ES_MEM_USE_POOL

static ES_VOID mem_pool_reset(es_mem_pool_t *pool)
{
    ES_VOID *p = pool->mem;

    do {
        *(ES_U32 *)p = ES_MEM_MAGIC_HEAD;
        p = (ES_VOID *)(p + pool->unit_size);
    } while (p < (pool->mem + pool->mem_size));
}

static ES_VOID *mem_pool_try_malloc(es_mem_pool_t *pool)
{
    ES_VOID *p = pool->mem;

    do {
        if (ES_MEM_MAGIC_HEAD == *(ES_U32 *)p) {
            *(ES_U32 *)p = 0x00;
            return p;
        }
        p = (ES_VOID *)(p + pool->unit_size);
    } while (p < (pool->mem + pool->mem_size));

    return ES_NULL;
}

static ES_VOID *mem_pools_malloc(ES_U32 size)
{
    ES_U16 i;
    ES_VOID *mem;

    for (i = 0; i < ES_ARRAY_SIZE(mem_pools); i++) {
        if (mem_pools[i].unit_size < size) {
            continue;;
        }

        mem = mem_pool_try_malloc(&mem_pools[i]);
        if (ES_NULL != mem) {
            es_mem_debug("core%d, pool malloc:%p\r\n",  current_coreid(), mem);
            return mem;
        }
    }

    return ES_NULL;
}


static ES_S32 mem_pools_free(ES_VOID *p)
{
    ES_U16 i;
    for (i = 0; i < ES_ARRAY_SIZE(mem_pools); i++) {
        if(p < (mem_pools[i].mem + mem_pools[i].mem_size) && (p >= mem_pools[i].mem)) {
            *(ES_U32 *)p = ES_MEM_MAGIC_HEAD;
            es_mem_debug("core%d, pool free:%p\r\n", current_coreid(), p);
            return ES_RET_SUCCESS;
        }
    }
    return ES_RET_FAILURE;
}

static ES_S32 mem_init_pools(ES_VOID)
{
    ES_U32 i = 0;

    for (i = 0; i < ES_ARRAY_SIZE(mem_pools); i++) {
        es_mem_debug("mem_pools[%d].mem:%p", i, mem_pools[i].mem);
#if 0
        mem_pools[i].mem = (ES_BYTE *)malloc(mem_pools[i].mem_size);
        if (ES_NULL == mem_pools[i].mem) {
            return ES_RET_NO_MEMORY;
        }
#endif
        mem_pool_reset(&mem_pools[i]);
    }
    return ES_RET_SUCCESS;
}

#endif


ES_S32 es_mem_init(ES_VOID)
{
#if ES_MEM_USE_POOL
    if (ES_RET_SUCCESS != mem_init_pools()) {
        return ES_RET_FAILURE;
    }
#endif

#if ES_MEM_USE_STAT
    es_memset(&mem_stat_info, 0x00, sizeof(mem_stat_info));
#endif
    ES_MEM_LOCK_INIT();
    return ES_RET_SUCCESS;
}


#if ES_MEM_WRAPPER_DEBUG
ES_VOID *es_malloc_wrapper(ES_U32 size, char *file, int line)
#else
ES_VOID *es_malloc(ES_U32 size)
#endif
{
    ES_VOID *mem;
    ES_MEM_LOCK();

// #if ES_MEM_WRAPPER_DEBUG
//     if (size < 1024) {
//         es_mem_debug("[%s,%d] size:%d, core%d", file, line, size, current_coreid());
//     }
// #endif

#if ES_MEM_USE_POOL
    if (size <= ES_MEM_POOL_MAX_SIZE) {
        mem = mem_pools_malloc(size);
        if (ES_NULL != mem) {
            ES_MEM_UNLOCK();
    #if ES_MEM_USE_STAT
            mem_pool_stat_malloc(mem, size);
    #endif
            return mem;
    }
    }
#endif
    mem = malloc(size);
    ES_MEM_UNLOCK();

#if ES_MEM_USE_STAT
    mem_pool_stat_malloc(mem, size);
#endif
    return mem;
}

ES_VOID *es_realloc(ES_VOID *ptr, ES_U32 size)
{
    es_free(ptr);
    return es_malloc(size);
}

ES_VOID es_free(ES_VOID *p)
{
    ES_MEM_LOCK();

#if ES_MEM_USE_STAT
    mem_pool_stat_free(p);
#endif

#if ES_MEM_USE_POOL
    if (ES_RET_SUCCESS == mem_pools_free(p)) {
        ES_MEM_UNLOCK();
        return;
    }
#endif

    free(p);
    ES_MEM_UNLOCK();
}

#if ES_MEM_USE_STAT
ES_VOID es_mem_info_print(ES_VOID)
{
    static ES_U32 last_time = 0;
    ES_U32 now_time;

    now_time = es_time_get_sytem_ms();
    if (now_time - last_time < 1000) {
        return;
    }
    last_time = now_time;

    printk("mem used: %d KB\r\n", mem_stat_info.used_size/1024);
}
#endif