/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_uart.h
** bef: define the interface for 433 module.
** auth: lines<<EMAIL>>
** create on 2020.06.26 
*/

#include "es_inc.h"

#if ES_BLE_MODULE_ENABLE

// #define ES_BLE_DEBUG
#ifdef ES_BLE_DEBUG
#define es_ble_debug es_log_info
#define es_ble_error es_log_error
#else
#define es_ble_debug(...)
#define es_ble_error(...)
#endif

#define ES_BLE_MSG_QUEUE_COUNT          (4)
#define ES_BLE_RESP_TIMEOUT_MS          (10*1000)

static struct pt pt_msg_send;
static struct pt_sem pt_sem_msg_resp;
static es_ble_msg_t ble_msg_queue[ES_BLE_MSG_QUEUE_COUNT] = {0};
static ES_U8 resp_fail = 0;
static ES_U32 ble_send_time = 0;

static ES_U8 es_ble_get_msg_id(ES_VOID)
{
    ES_U8 i = 0;

    for (i = 0; i < ES_BLE_MSG_QUEUE_COUNT; i++) {
        if (0 != ble_msg_queue[i].data_len) {
            // es_ble_debug("ble_msg_queue[%d].data_len:%d", i, ble_msg_queue[i].data_len);
            break;
        }
    }

    return i;
}

static ES_VOID es_ble_reset_msg_queue(ES_U8 msg_id)
{
    ble_msg_queue[msg_id].data_len = 0;
}

static ES_S32 es_ble_try_conn(ES_U8 msg_id)
{
    es_ble_conn_param_t param;

    es_ble_debug("try coonn, msg_id:%d, to_slave:%d", msg_id, ble_msg_queue[msg_id].to_slave);

    param.mac = ble_msg_queue[msg_id].mac;
    param.serv_uuid = ble_msg_queue[msg_id].serv_uuid;
    param.char_uuid = ble_msg_queue[msg_id].char_uuid;
    return es_hal_ble_slave_conn(&param);
}

static ES_S32 es_ble_try_send(ES_U8 msg_id)
{
    es_ble_send_data_t data;

    data.mac = ble_msg_queue[msg_id].mac;
    data.data = ble_msg_queue[msg_id].data;
    data.data_len = ble_msg_queue[msg_id].data_len;
    es_ble_debug("ble send msg, msg_id:%d, to_slave:%d", msg_id, ble_msg_queue[msg_id].to_slave);

    if (ble_msg_queue[msg_id].to_slave) {
        return es_hal_ble_slave_send(&data);
    }

    return es_hal_ble_master_send(&data);
}

static PT_THREAD(es_ble_send_msg(struct pt *pt))
{
    static ES_U8 msg_id = 0;
    static ES_U32 conn_wait_time = 0;

    msg_id = es_ble_get_msg_id();
    if (msg_id >= ES_BLE_MSG_QUEUE_COUNT) {
        return 0;
    }

    PT_BEGIN(pt);
    // connect ble
    if (ble_msg_queue[msg_id].to_slave) {
        es_ble_try_conn(msg_id);
        ble_send_time = es_time_get_sytem_ms();
        PT_SEM_INIT(&pt_sem_msg_resp, 0);
        PT_SEM_WAIT(pt, &pt_sem_msg_resp);
        if (resp_fail) {
            goto FUNC_END;
        }
        conn_wait_time = es_time_get_sytem_ms();
        PT_WAIT_UNTIL(pt, ((es_time_get_sytem_ms() - conn_wait_time) > 8*100)); //建议使用指令：D1000-5000-1000
    }

    // send data to ble
    es_ble_try_send(msg_id);
    if (!ble_msg_queue[msg_id].is_ack) {
        ble_send_time = es_time_get_sytem_ms();
        PT_SEM_INIT(&pt_sem_msg_resp, 0);
        PT_SEM_WAIT(pt, &pt_sem_msg_resp);
    }

    // dis connect ble
    if (ble_msg_queue[msg_id].to_slave) {
        es_ble_debug("slave disconn, msg_id:%d, to_slave:%d", msg_id, ble_msg_queue[msg_id].to_slave);
        es_hal_ble_slave_disconn(ble_msg_queue[msg_id].mac);
        ble_send_time = es_time_get_sytem_ms();
        PT_SEM_INIT(&pt_sem_msg_resp, 0);
        PT_SEM_WAIT(pt, &pt_sem_msg_resp);
        if (resp_fail) {
            goto FUNC_END;
        }
    }

FUNC_END:
    es_ble_reset_msg_queue(msg_id);

    PT_END(pt);
}


ES_S32 es_ble_init(void)
{
    if (ES_RET_SUCCESS != es_hal_ble_init()) {
        return ES_RET_SUCCESS;
    }

    PT_INIT(&pt_msg_send);
    PT_SEM_INIT(&pt_sem_msg_resp, 0);
    es_memset(ble_msg_queue, 0x00, sizeof(ble_msg_queue));

    return ES_RET_SUCCESS;
}

ES_S32 es_ble_add_msg(const es_ble_msg_t *msg)
{
    ES_U8 i = 0;

    if (0 == msg->data_len || msg->data_len > ES_BLE_DATA_LEN) {
        es_ble_debug("ble_add_msg fail, data_len:%d", msg->data_len);
        return ES_RET_FAILURE;
    }

    for (i = 0; i < ES_BLE_MSG_QUEUE_COUNT; i++) {
        if (0 == ble_msg_queue[i].data_len) {
            break;
        }
    }

    if (i >= ES_BLE_MSG_QUEUE_COUNT) {
        es_ble_debug("ble_add_msg fail, full");
        return ES_RET_FAILURE;
    }

    es_ble_debug("es_ble_add_msg, i:%d, to_slave:%d, data_len:%d", i, msg->to_slave, msg->data_len);
    es_memcpy(&ble_msg_queue[i], msg, sizeof(es_ble_msg_t));
    return ES_RET_SUCCESS;
}



ES_S32 es_ble_recv(es_ble_recv_data_t *p)
{
    if (ES_NULL == p) {
        es_ble_error("p is NULL, fail");
        return ES_RET_FAILURE;
    }

    if (ES_RET_SUCCESS != es_hal_ble_recv(p)) {
        // es_ble_error("recv fail");
        return ES_RET_FAILURE;
    }

    // es_ble_debug("es_ble_recv, success");
    return ES_RET_SUCCESS;
}

ES_S32 es_ble_send_payload_from_serv(const ES_VOID *json_obj)
{
    cJSON *json_data = ES_NULL;
    const cJSON *data = (const cJSON *)json_obj;
    es_ble_msg_t ble_msg;

    es_memset(&ble_msg, 0x00, sizeof(ble_msg));
    ble_msg.to_slave = 1;
    
    json_data = cJSON_GetObjectItem_Type(data, "mac", cJSON_String);
    if (ES_NULL == json_data) {
        es_ble_error("get josn for mac error.");
        return ES_RET_FAILURE;
    }
    if ((ES_BLE_MAC_LEN<<1) != es_strlen(json_data->valuestring)) {
        es_ble_error("the length of mac error.");
        return ES_RET_FAILURE;
    }
    es_utils_hex_str_to_bytes(json_data->valuestring, ble_msg.mac, ES_BLE_MAC_LEN);
    es_ble_debug("mac:%s", json_data->valuestring);

    json_data = cJSON_GetObjectItem_Type(data, "value", cJSON_String);
    if (ES_NULL == json_data) {
        es_ble_error("get josn for value error.");
        return ES_RET_FAILURE;
    }
    if ((ES_BLE_DATA_LEN<<1) < es_strlen(json_data->valuestring)) {
        es_ble_error("the lenght of payload error");
        return ES_RET_FAILURE;
    }

    es_utils_hex_str_to_bytes(json_data->valuestring, ble_msg.data, ES_BLE_DATA_LEN);
    es_ble_debug("payload:%s", json_data->valuestring);
    ble_msg.data_len = es_strlen(json_data->valuestring)/2;

    json_data = cJSON_GetObjectItem_Type(data, "serv_uuid", cJSON_String);
    if (ES_NULL != json_data) {
        if ((ES_BLE_UUID_LEN<<1) < es_strlen(json_data->valuestring)) {
            es_ble_error("the lenght of serv_uuid error");
            return ES_RET_FAILURE;
        }

        es_utils_hex_str_to_bytes(json_data->valuestring, ble_msg.serv_uuid, ES_BLE_UUID_LEN);
        es_ble_debug("serv_uuid:%s", json_data->valuestring);
    }

    json_data = cJSON_GetObjectItem_Type(data, "char_uuid", cJSON_String);
    if (ES_NULL != json_data) {
        if ((ES_BLE_UUID_LEN<<1) < es_strlen(json_data->valuestring)) {
            es_ble_error("the lenght of char_uuid error");
            return ES_RET_FAILURE;
        }

        es_utils_hex_str_to_bytes(json_data->valuestring, ble_msg.char_uuid, ES_BLE_UUID_LEN);
        es_ble_debug("char_uuid:%s", json_data->valuestring);
    }

    return es_ble_add_msg(&ble_msg);
}


static ES_VOID es_ble_check_resp(ES_VOID)
{
    ES_S32 ret = 0;

    if (0 == ble_send_time) {
        return;
    }

    ret = es_hal_ble_get_resp();
    if (ES_BLE_RESP_NONE != ret) {
        resp_fail = 0;
        if (ES_BLE_RESP_CONN_FAIL == ret || ES_BLE_RESP_SEND_FAIL == ret ||
            ES_BLE_RESP_DISCONN_FAIL == ret) {
            resp_fail = 1;
        }
        ble_send_time = 0;
        PT_SEM_SIGNAL(&pt_msg_send, &pt_sem_msg_resp);
        return;
    }

    // timeout
    if (ES_RET_SUCCESS == es_time_check_timeout_ms(&ble_send_time, ES_BLE_RESP_TIMEOUT_MS)) {
        ble_send_time = 0;
        resp_fail = 1;
        PT_SEM_SIGNAL(&pt_msg_send, &pt_sem_msg_resp);
        return;
    }
}

static ES_VOID es_ble_parse_data(es_ble_recv_data_t *data)
{
    ES_BOOL is_slave = ES_TRUE;
    ES_U32 i = 0;

    // slave mac is FF FF FF FF FF FF
    for (i = 0; i < 6; i++) {
        if (0xff != data->mac[i]) {
            is_slave = ES_FALSE;
            break;
        }
    }

    if (is_slave) {
        es_ble_proto_parse(data->data, data->data_len);
    }

}

ES_VOID es_ble_run(ES_VOID)
{
    es_ble_recv_data_t recv_data;

    memset(&recv_data, 0x00, sizeof(recv_data));
    es_ble_recv(&recv_data);
    if (0 != recv_data.data_len) {
        es_ble_parse_data(&recv_data);
    }

    es_ble_send_msg(&pt_msg_send);
    es_ble_check_resp();
}

ES_S32 es_ble_read_mac(ES_BYTE *mac)
{
    if (ES_NULL == mac) {
        es_ble_error("mac is NULL, fail.");
        return ES_RET_FAILURE;
    }

    if (ES_RET_SUCCESS != es_hal_ble_read_mac(mac)) {
        es_ble_error("recv is NULL");
        return ES_RET_FAILURE;
    }

    return ES_RET_SUCCESS;
}

ES_BOOL es_ble_is_connected(ES_VOID)
{
    return es_hal_ble_is_connected();
}

#endif
