#include "facelib_inc.h"

typedef struct
{
    float x;
    float y;
    float w;
    float h;
} box_t;

static float overlap(float x1, float w1, float x2, float w2)
{
    float l1 = x1 - w1 / 2;
    float l2 = x2 - w2 / 2;
    float left = l1 > l2 ? l1 : l2;
    float r1 = x1 + w1 / 2;
    float r2 = x2 + w2 / 2;
    float right = r1 < r2 ? r1 : r2;

    return right - left;
}

static float box_intersection(box_t a, box_t b)
{
    float w = overlap(a.x, a.w, b.x, b.w);
    float h = overlap(a.y, a.h, b.y, b.h);

    if(w < 0 || h < 0)
        return 0;
    return w * h;
}

static float box_union(box_t a, box_t b)
{
    float i = box_intersection(a, b);
    float u = a.w * a.h + b.w * b.h - i;

    return u;
}

static float box_iou(box_t a, box_t b)
{
    return box_intersection(a, b) / box_union(a, b);
}

float cal_iou(face_obj_t *obj_850, face_obj_t *obj_650)
{
    box_t a, b;
    //a
    a.x = obj_850->x1;
    a.y = obj_850->y1;
    a.w = obj_850->x2 - a.x;
    a.h = obj_850->y2 - a.y;
    //b
    b.x = obj_650->x1;
    b.y = obj_650->y1;
    b.w = obj_650->x2 - a.x;
    b.h = obj_650->y2 - a.y;
    return box_iou(a, b);
}
