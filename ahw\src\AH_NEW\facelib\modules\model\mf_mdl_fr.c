#include "facelib_inc.h"
#include "mf_modelin.h"

/*****************************************************************************/
// Macro definitions
/*****************************************************************************/
#if 1
#define DEBUG_LINE()
#else
#define DEBUG_LINE() \
    do                                             \
    {                                              \
        printk("[mf_mdl_fr] L%d\r\n", __LINE__); \
    } while(0)
#endif
#define _D() //printk("~%d", __LINE__)
/*****************************************************************************/
// Function definitions
/*****************************************************************************/
extern void image_deinit(image_t *image);

/*****************************************************************************/
// Private Var 局部变量
/*****************************************************************************/
static face_obj_info_t detect_info;

/*****************************************************************************/
// Driver 底层驱动
/*****************************************************************************/

/*****************************************************************************/
// Private Func 局部函数
/*****************************************************************************/

//检查人脸大小是否符合限制, 符合返回1
static uint8_t _check_face_size(face_obj_t* obj)
{
	uint32_t width = obj->x2 - obj->x1 + 1;
    uint32_t height = obj->y2 - obj->y1 + 1;
    if(width < mf_model.face_minw || height < mf_model.face_minh) {
        return 0;
	} else {
		return 1;
	}
}

//准备face_ret存储特征值的相关缓存，返回有效人脸数（申请失败返回0）
static uint8_t _prepare_face_ret(fr_ret_t* face_ret, face_obj_info_t* detect_info, uint8_t out_ftr)
{
    uint32_t v_face_number = 0;
    DEBUG_LINE();
    for(uint32_t face_cnt = 0; face_cnt < detect_info->obj_number; face_cnt++)
    {
        if(!_check_face_size(&detect_info->obj[face_cnt]))
        {
            continue;
        }
        if(++v_face_number >= FACE_MAX_NUMBER)
            break;
    }
    DEBUG_LINE();
    // printk("%s\tv_face_number:%d\r\n", ir_or_rgb ? "ir" : "rgb", v_face_number);
    if(v_face_number == 0)
    {
        return 0;
    }
    DEBUG_LINE();
    face_ret->ret = -1;
    fr_result_t *face_result = malloc(sizeof(fr_result_t)); 
    face_ret->result = face_result;

    if(face_result == NULL)
    {
        face_ret->ret = -2;
        return 0;
    }
    memset(face_result, 0, sizeof(fr_result_t));  //obj_number=0
    if(out_ftr)
    {
        int8_t* v_feature = malloc(v_face_number * mf_model.ftr_len * sizeof(int8_t));
        for(uint32_t i = 0; i < v_face_number; i++)
        {
            face_result->face_obj_info.obj[i].feature = v_feature + i * mf_model.ftr_len;
        }
    }
    DEBUG_LINE();
	return v_face_number;
}

static uint8_t _find_biggest_face(face_obj_info_t* detect_info)
{
    /* 这里找出最大的人脸,然后后边的流程就只处理这个人脸的信息 */
    uint16_t w, h, max_w = 0, max_h = 0;
    uint32_t face_idx = 0;
    max_w = detect_info->obj[0].x2 - detect_info->obj[0].x1 + 1;
    max_h = detect_info->obj[0].y2 - detect_info->obj[0].y1 + 1;
    for(uint32_t i = 1; i < detect_info->obj_number; i++)
    {
        w = detect_info->obj[i].x2 - detect_info->obj[i].x1 + 1;
        h = detect_info->obj[i].y2 - detect_info->obj[i].y1 + 1;
        if((w * h) > (max_w * max_h))
        {
            max_w = w;
            max_h = h;
            face_idx = i;
        }
    }
    // printk("max face_idx:%d,w:%d,h:%d\r\n", face_idx, max_w, max_h);
	return face_idx;
}

fr_ret_t _mf_model_run_fr(image_t* kpu_img, uint8_t out_ftr, face_step_t step, uint8_t ir_or_rgb, uint8_t only_biggest)
{_D();
	mf_err_t err = MF_ERR_NONE;
    fr_ret_t face_ret;
    face_ret.ret = 0;
    face_ret.result = NULL;
	image_t simg;
	simg.addr = NULL;
    //先拷贝一次，这样后面可以用cache内存操作，更快
    memcpy(kpu_img->addr + 0x40000000, kpu_img->addr, 320 * 240 * 3);DEBUG_LINE();_D();
	/************************* Face Detect ***********************/
	err = mf_model.face_detect(kpu_img, &detect_info);DEBUG_LINE();_D();
	if(err != MF_ERR_NONE){DEBUG_LINE(); return face_ret;}; 
	//过滤掉小脸后，申请fr_result_t，如果out_ftr则再申请特征值缓存
	uint8_t v_face_number = _prepare_face_ret(&face_ret, &detect_info, out_ftr);_D();
	if(v_face_number == 0){face_ret.result = NULL; return face_ret;};
	fr_result_t *face_result = face_ret.result;
	DEBUG_LINE();_D();
	/************ choose biggest face or all face ****************/
	uint8_t face_idx_min, face_idx_max, face_idx;
	if(only_biggest) {
		face_idx_min = _find_biggest_face(&detect_info);
		face_idx_max = face_idx_min+1;
	} else {
		face_idx_min = 0;
		face_idx_max = detect_info.obj_number;
	}
	DEBUG_LINE();_D();
	/***************** Deal with valid face *********************/
	v_face_number = 0;  
	for(face_idx = face_idx_min; face_idx < face_idx_max; face_idx++)
	{	/******************* Check Face Size ********************/
		face_obj_t* obj = &detect_info.obj[face_idx];
		face_obj_t* obj_res = \
			&face_result->face_obj_info.obj[face_result->face_obj_info.obj_number];
		uint32_t width = obj->x2 - obj->x1 + 1;
		uint32_t height = obj->y2 - obj->y1 + 1;
		if(width < mf_model.face_minw || height < mf_model.face_minh) {
			continue;	                       //face too small
		}
		if(++v_face_number > FACE_MAX_NUMBER)  //exceed max face num
            break;
		DEBUG_LINE();_D();
		/************** Copy fd info to face_result *************/
		obj->key_point.width  = width;
		obj->key_point.height = height;
		memcpy((uint8_t*)obj_res, (uint8_t*)obj, 24); 
		if(step == STEP_DET_FACE)
		{DEBUG_LINE();_D();
			face_result->face_obj_info.obj_number++;
			continue; //only do face detect
		}
		/****************** Landmark & Affine *****************/
		_D();err = mf_model.face_align(kpu_img, obj, &simg);//注意这里申请了simg内存，需释放
		if(err != MF_ERR_NONE) {DEBUG_LINE();_D();
			goto _err_handle;
		};
		_D();
		memcpy(&(obj_res->key_point), &(obj->key_point), sizeof(key_point_t));
        
        /* 如果没有初始化ftr的模型，我们同样只进行人脸检测 */
		if((step == STEP_DET_KP) || \
        (!(mf_model.model_flag & MF_MDL_FACEFTR) && \
        !(mf_model.model_flag & MF_MDL_LAYOUT_V2_FE) && \
        !(mf_model.model_flag & MF_MDL_LAYOUT_V3_FE) && \
        !(mf_model.model_flag & MF_MDL_LAYOUT_V4_FE)))
		{
            image_deinit(&simg);DEBUG_LINE();_D();
			face_result->face_obj_info.obj_number++;
            obj_res->pass = 0;
            obj_res->pose = 0;
            obj_res->score = 0;
            obj_res->true_face = 0;
            obj_res->blur = 0;
            obj_res->feature = NULL;
			continue; //do key point detect
		}
		/***************** Front & Blur Test *****************/
		_D();
        float width_percent = (float)((float)obj->key_point.width / (float)(obj->key_point.point[1].x - obj->key_point.point[0].x));
        
        if(face_lib_model_debug_en)
            printk("width percent:%d\r\n", (int)(width_percent * 100.0f));
		_D();
        if(mf_brd.cfg.model.checkpose == 1) {
            if(mf_model.check_pose(&(obj->key_point)) != 0) { DEBUG_LINE();_D();
                face_result->face_obj_info.obj_number++;
				obj_res->pose = 0;
                obj_res->prob = 0.0f;
				image_deinit(&simg);
                continue; //skip ftr calculate, as pose not right
            }
        }
		obj_res->pose = 1;_D();
        //int blur_res = mf_model.check_blur(simg.addr, width, height, 0.15);_D();
        obj_res->blur = 20; //blur_res;DEBUG_LINE();
		_D();
        //if(face_lib_model_debug_en)
        //    printk("%s->blur_res:%d\r\n", (ir_or_rgb == FTR_850) ? "IR" : "RGB", blur_res);
        
        if((width_percent > 2.6f) || (width_percent < 1.5f)/*  || (blur_res < 19) */) {
                // printk("face angle too bad\r\n");
                face_result->face_obj_info.obj_number++;
				obj_res->pose = 0;
                obj_res->prob = 0.0f;
                obj_res->true_face = 0xAA;
				image_deinit(&simg);
                continue; //skip ftr calculate, as pose not right
        }

		/******************** Living Test *******************/
        if(ir_or_rgb == FTR_850) {DEBUG_LINE();_D();
            float living_score = mf_model.face_living(&simg);
            obj_res->true_face = (living_score > mf_model.live_gate) ? 1 : 0;

            if(face_lib_model_debug_en)
                printk("ir_living score: %d %d\r\n", (uint32_t)(living_score), (uint32_t)mf_model.live_gate);
        } else {DEBUG_LINE();_D();
            obj_res->true_face = 0;

            if(mf_flow.type == MF_FLOW_SINGLE_VIS) {
                /* 单摄没有活体 */
                obj_res->true_face = 1;
            }
        }
        if(step == STEP_LIVING) {DEBUG_LINE();_D();
            image_deinit(&simg);
            face_result->face_obj_info.obj_number++;
            continue;
		}
		/****************** Calculate Ftr ******************/
        DEBUG_LINE();_D();
		int8_t fea_tmp[MAX_FTR_LEN];	//200 is the max ftr len
		err = mf_model.ftr_cal(&simg, fea_tmp);
		if(err != MF_ERR_NONE) {
			goto _err_handle;
		}
		if(out_ftr) {
			memcpy(obj_res->feature, fea_tmp, mf_model.ftr_len);
		}
		image_deinit(&simg);
        face_result->face_obj_info.obj_number++; DEBUG_LINE();_D();
		/***************** Calculate Score ****************/

        /* can only have one face */
        if(0x01 != face_idx_max) {
            obj_res->pass = 0;
            obj_res->score = 0.0f;
            obj_res->index = (-1U);
            obj_res->pose = 0;
            obj_res->true_face = 0;
            continue;
        }

        float score_max;
        int score_index;
		mf_model.db_search(fea_tmp, ir_or_rgb, (uint32_t*)&score_index, &score_max); //选择ir或者rgb数据进行比对
        // printk("score_max:%d\r\n", (int)score_max);
        if(score_index >= 0){
			float gate = (FTR_850 == ir_or_rgb) ? mf_model.fe_gate_ir : mf_model.fe_gate;

            obj_res->score = score_max;
            if(score_max > gate) {
                if(face_lib_model_debug_en)
                    printk("score pass %d %d\r\n", (int)(score_max * 100), (int)(gate * 100));
				DEBUG_LINE();_D();
				obj_res->pass = 1;
				obj_res->index = (uint32_t)score_index;
				face_result->face_compare_info.obj[face_result->face_compare_info.result_number] = obj_res;
				face_result->face_compare_info.result_number++;
                face_ret.ret = 1;
            }
        }
	}
	return face_ret;
_err_handle:
	image_deinit(&simg);_D();
	mf_model.free_fr(face_ret);
	return face_ret;
}

void _mf_model_free_fr(fr_ret_t face_ret)
{
    DEBUG_LINE();
    if(face_ret.result == NULL)
        return;
    DEBUG_LINE();
    if(face_ret.result->face_obj_info.obj[0].feature)
    {	DEBUG_LINE();
        free(face_ret.result->face_obj_info.obj[0].feature);
    }
    DEBUG_LINE();
    free(face_ret.result);
	face_ret.result = NULL;
    DEBUG_LINE();
	return;
}