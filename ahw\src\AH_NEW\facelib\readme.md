# 人脸识别模块

## 概述

提供人脸识别的接口。将face_recognition文件夹拷贝至工作目录下即可。

## 功能描述

每个人脸有196个float的特征值，通过对两个人脸的特征值处理，可以获取两个人脸的相似度（0~100.0）。

## API参考

对应的头文件 `face_recognition.h`

为用户提供以下接口

- face_recognition_run
- face_recognition_free_result
- face_recognition_caldosindistance

### face_recognition_run

#### 描述

人脸识别。

#### 函数原型

```c
face_recognition_ret_t face_recognition_run(const uint8_t *image_src,face_recognition_info_t *face_info, bool output_feature)
```

#### 参数

|    参数名称    |                           描述                            | 输入输出 |
| -------------- | --------------------------------------------------------- | -------- |
| image_src      | 源图像数据分离的320*240 RGB888的数据                      | 输入     |
| face_info      | 输入的参数详细见其结构体说明，如果该值为0，则均使用默认值 | 输入     |
| output_feature | 是否输出特征值                                            | 输入     |

#### 返回值

返回人脸及识别结果，输入的参数有关。返回的内容为动态分配，所以必须使用face_recognition_free_result释放返回的资源。详见结构体说明。

### face_recognition_free_result

#### 描述

释放face_recognition_run返回的资源。

#### 函数原型

```c
void face_recognition_free_result(face_recognition_ret_t face_ret)
```

#### 参数

| 参数名称 |              描述              | 输入输出 |
| -------- | ------------------------------ | -------- |
| face_ret | face_recognition_run的返回结果 | 输入     |

#### 返回值

无。

### face_recognition_caldosindistance

#### 描述

计算两个人脸特征的相似度，范围为0~100.0。越大则越相似。

#### 函数原型

```c
float face_recognition_caldosindistance(float *faceFeature0p, float *faceFeature1p)
```

#### 参数

|   参数名称    |    描述    | 输入输出 |
| ------------- | ---------- | -------- |
| faceFeature0p | 人脸特征值 | 输入     |
| faceFeature1p | 人脸特征值 | 输入     |

#### 返回值

相似度0~100.0。越大则越相似。

## 数据类型

相关数据类型、数据结构定义如下：

- face_recognition_info_t
- face_recognition_ret_t
- face_recognition_result_t
- face_obj_info_t
- face_compare_info_t
- face_obj_t

### face_recognition_info_t

#### 定义

```c
typedef struct
{
    float detect_threshold;
    float detect_nms;
    float compare_threshold;
    dmac_channel_number_t dma_channel;
    int (*callback)(void *ctx);
    int (*get_saved_feature_number)(void);
    int (*get_saved_feature)(float *feature, uint32_t index);
} face_recognition_info_t;
```

#### 成员

|         成员名称         |                                                                        描述                                                                         |
| ------------------------ | --------------------------------------------------------------------------------------------------------------------------------------------------- |
| detect_threshold         | 人脸检测阈值，越大人脸的误检率会越低，如果设置为0则使用默认值0.7，一般情况下用户不必关心，建议设置为0即可。如果人脸的误检率较高，可以适当增大该值。 |
| detect_nms               | 人脸检测nms值，如果设置为0则使用默认值0.3，一般情况下用户不必关心，建议设置为0即可。                                                                |
| compare_threshold        | 人脸比对时相似度的阈值，如果设置为0时使用默认值80.0，如果误识较高可以适当增大此值。                                                                 |
| dma_channel              | DMA通道，1至5，如果其它值默认使用DMA5                                                                                                               |
| callback                 | 回调函数，预留                                                                                                                                      |
| get_saved_feature_number | 获取待对比人脸特征总数，需要用户实现。                                                                                                              |
| get_saved_feature        | 获取人脸特征值，需要用户实现。                                                                                                                      |

```c
int (*get_saved_feature_number)(void);
```

#### 描述

获取待对比人脸特征总数，需要用户实现。如果设置为NULL则，不会执行比对的过程，只会输出人脸特征值。

#### 返回值

| 返回值 |        描述        |
| :----- | :----------------- |
| 负数   | 失败               |
| 非负数 | 待对比人脸特征总数 |

```c
int (*get_saved_feature)(float *feature, uint32_t index);
```

#### 描述

获取待对比人脸特征值，根据index可以获取人脸特征feature。如果设置为NULL则，不会执行比对的过程，只会输出人脸特征值。

#### 参数

| 参数名称 |                            描述                            | 输入输出 |
| -------- | ---------------------------------------------------------- | -------- |
| feature  | 人脸特征值                                                 | 输出     |
| index    | 索引值，用户需要实现根据index值获取对应的人脸特征值feature | 输入     |

#### 返回值

| 返回值 | 描述 |
| :----- | :--- |
| 0      | 成功 |
| 非0    | 失败 |

### face_recognition_ret_t

#### 描述

返回结构体。

#### 定义

```c
typedef struct
{
    int ret;
    face_recognition_result_t *result;
} face_recognition_ret_t;
```

#### 成员

| 成员名称 |                              描述                              |
| -------- | -------------------------------------------------------------- |
| ret      | 返回值，0：无人 1：有人且比对通过 -1：有人且比对未通过，-2异常 |
| result   | 人脸识别返回值，参见其结构体说明                               |

### face_recognition_result_t

#### 定义

```c
typedef struct
{
    face_obj_info_t face_obj_info;
    face_compare_info_t face_compare_info;
} face_recognition_result_t;
```

#### 成员

|      成员名称       |       描述       |
| ------------------- | ---------------- |
| face_obj_info       | 人脸检测相关数据 |
| face_compare_result | 人脸对比相关数据 |

#### face_obj_info_t

#### 定义

```c
typedef struct
{
    uint32_t obj_number;
    face_obj_t obj[FACE_MAX_NUMBER];
} face_obj_info_t;
```

#### 成员

|  成员名称  |   描述   |
| ---------- | -------- |
| obj_number | 人脸数   |
| obj        | 人脸信息 |

### face_compare_info_t

#### 定义

```c
typedef struct
{
    uint32_t result_number;
    face_obj_t *obj[FACE_MAX_NUMBER];
} face_compare_info_t;
```

#### 成员

|   成员名称    |                       描述                       |
| ------------- | ------------------------------------------------ |
| result_number | 人脸比对通过数，可以通过该值判断人脸比对是否通过 |
| obj           | 人脸信息                                         |

### face_obj_t

#### 定义

```c
typedef struct
{
    uint32_t x1;
    uint32_t y1;
    uint32_t x2;
    uint32_t y2;
    uint32_t class_id;
    float prob;
    float *feature;
    uint32_t index;
    float score;
    bool pass;
} face_obj_t;
```

#### 成员

| 成员名称 |                                     描述                                      |
| -------- | ----------------------------------------------------------------------------- |
| x1       | 人脸左上角横坐标                                                              |
| y1       | 人脸左上角竖坐标                                                              |
| x2       | 人脸右下角横坐标                                                              |
| y2       | 人脸右下角竖坐标                                                              |
| class_id | 用户不关心                                                                    |
| prob     | 用户不关心                                                                    |
| feature  | 人脸特征值                                                                    |
| index    | 索引值，用户可以根据此值查找对应的人脸数据，与用户实现的get_saved_feature有关 |
| score    | 人脸比对结果                                                                  |
| pass     | 比对是否通过                                                                  |