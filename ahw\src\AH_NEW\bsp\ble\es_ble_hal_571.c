/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_uart.h
** bef: define the interface for shubbeide module.
** auth: lines<<EMAIL>>
** create on 2020.06.26 
*/

#include "es_inc.h"

#if (ES_BLE_HAL_TYPE == ES_BLE_HAL_571)

// #define BLE_MOD_DEBUG
#ifdef BLE_MOD_DEBUG
#define ble_mod_debug es_log_info
#define ble_mod_error es_log_error
#else
#define ble_mod_debug(...)
#define ble_mod_error(...)
#endif

#define MOD_571_RECV_TIMEOUT_MS                 (500)
#define MOD_571_PROTO_BUF_MAX_LEN               (256)


#define MOD_571_PROTO_HEAD_IND                  (0)
#define MOD_571_PROTO_HEAD_LEN                  (2)
#define MOD_571_PROTO_HEAD_DATA_0               (0xA5)
#define MOD_571_PROTO_HEAD_DATA_1               (0xA5)
#define MOD_571_PROTO_CMD_IND                   (MOD_571_PROTO_HEAD_IND+MOD_571_PROTO_HEAD_LEN)
#define MOD_571_PROTO_CMD_LEN                   (1)
#define MOD_571_PROTO_MSGID_IND                 (MOD_571_PROTO_CMD_IND+MOD_571_PROTO_CMD_LEN)
#define MOD_571_PROTO_MSGID_LEN                 (1)
#define MOD_571_PROTO_MAC_IND                   (MOD_571_PROTO_MSGID_IND+MOD_571_PROTO_MSGID_LEN)
#define MOD_571_PROTO_MAC_LEN                   (6)
#define MOD_571_PROTO_DLEN_IND                  (MOD_571_PROTO_MAC_IND+MOD_571_PROTO_MAC_LEN)
#define MOD_571_PROTO_DLEN_LEN                  (1)
#define MOD_571_PROTO_DATA_IND                  (MOD_571_PROTO_DLEN_IND+MOD_571_PROTO_DLEN_LEN)
#define MOD_571_PROTO_DATA_LEN                  (0)
#define MOD_571_PROTO_SUM_IND                   (MOD_571_PROTO_DATA_IND+MOD_571_PROTO_DATA_LEN)
#define MOD_571_PROTO_SUM_LEN                   (1)

#define MOD_571_PROTO_PACKAGE_MIN_LEN           (MOD_571_PROTO_SUM_IND+MOD_571_PROTO_SUM_LEN)
#define MOD_571_PROTO_DATA_MAX_LEN              (MOD_571_PROTO_BUF_MAX_LEN-(MOD_571_PROTO_PACKAGE_MIN_LEN))

#define MOD_571_PROTO_CMD_CONN                  (0x01)
#define MOD_571_PROTO_CMD_DISCONN               (0x02)
#define MOD_571_PROTO_CMD_DATA_TO_SLAVER        (0x03)
#define MOD_571_PROTO_CMD_DATA_FROM_SLAVER      (0x04)
#define MOD_571_PROTO_CMD_DATA_TO_MASTER        (0x05)
#define MOD_571_PROTO_CMD_DATA_FROM_MASTER      (0x06)
#define MOD_571_PROTO_CMD_READ_MAC              (0x07)


#define BLE_HAL_WAIT_SLEEP_MS                   (10)

#define MOD_571_RX_BUF_LEN                      (64)
#define MOD_571_MAX_CLIENT_COUNT                (4) // 1 master, 3 slave


typedef struct {
    ES_BYTE mac[6];
    ES_VOID *data_buf;
} es_ble_rx_payload_t;

static ES_U8 mod_571_msg_send_id = 0;
// static ES_U8 mod_571_ack_msg_id = 0;
static ES_BYTE mod_571_rx_buf[MOD_571_PROTO_BUF_MAX_LEN] = {0};
static ES_U32 mod_571_rx_buf_len = 0;
static ES_BYTE resp_ack_cmd = 0;
static ES_BYTE resp_ack_ret = 0;
static ES_BYTE ble_mac[6] = {0};
static ES_U8 got_mac = 0;
static ES_U8 msg_resp_value = ES_BLE_RESP_NONE;
static ES_BOOL is_connected = ES_FALSE;

static es_ble_rx_payload_t mod_571_rx_data[MOD_571_MAX_CLIENT_COUNT] = {0};

static ES_VOID es_ble_set_msg_resp_value(ES_BYTE cmd, ES_BYTE ret)
{
    if (MOD_571_PROTO_CMD_CONN == cmd) {
        // ret == 0, success; ret == 1, failure;
        msg_resp_value = ES_BLE_RESP_CONN_OK + ret;
        is_connected = ES_TRUE;
    } if (MOD_571_PROTO_CMD_DISCONN == cmd) {
        // ret == 0, success; ret == 1, failure;
        msg_resp_value = ES_BLE_RESP_DISCONN_OK + ret;
        is_connected = ES_FALSE;
    } if (MOD_571_PROTO_CMD_DATA_TO_SLAVER == cmd || MOD_571_PROTO_CMD_DATA_TO_MASTER == cmd) {
        // ret == 0, success; ret == 1, failure;
        msg_resp_value = ES_BLE_RESP_SEND_OK;
        if (0 != ret) {
            msg_resp_value = ES_BLE_RESP_SEND_FAIL;
        }
    }
}

static es_ble_rx_payload_t *mod_571_rx_data_find_idle(ES_BYTE *mac)
{
    ES_U32 i = 0;
    ES_U32 idle = MOD_571_MAX_CLIENT_COUNT;

    for (i = 0; i < MOD_571_MAX_CLIENT_COUNT; i++) {
        if (0 == memcmp(mod_571_rx_data[i].mac, mac, 6)) {
            return &mod_571_rx_data[i];
        }
        
        if (0 == es_circel_get_data_size(mod_571_rx_data[i].data_buf) && idle == MOD_571_MAX_CLIENT_COUNT) {
            idle = i;
        }
    }

    if (idle < MOD_571_MAX_CLIENT_COUNT) {
        return &mod_571_rx_data[idle];
    }

    return ES_NULL;
}


static unsigned char ble_mod_571_checksum(unsigned char *pBuff, unsigned short len)
{
    unsigned char sum = 0;
    unsigned short i;

    for (i = 0; i < len; i++) {
        sum = sum ^ (pBuff[i]&0xFF);
    }
    return sum;
}

static ES_VOID mod_571_save_package(const ES_BYTE *buf, ES_U16 len)
{
    ES_U32 i = 0;

    for (i = 0; i < len; i++) {
        if (mod_571_rx_buf_len < MOD_571_PROTO_HEAD_LEN) {
            if (0 == mod_571_rx_buf_len) {
                // must been 0xAA
                if (MOD_571_PROTO_HEAD_DATA_0 != buf[i]) {
                    continue;
                }
            } else {
                // must been 0xD5 or 0x56(slave data)
                if (MOD_571_PROTO_HEAD_DATA_1 != buf[i]) {
                    mod_571_rx_buf_len = 0;
                    continue;
                }
            }
        }

        if (mod_571_rx_buf_len < MOD_571_PROTO_BUF_MAX_LEN) {
            mod_571_rx_buf[mod_571_rx_buf_len++] = buf[i];
        }
    }

    // ble_mod_error("mod_571_rx_buf_len:%d", mod_571_rx_buf_len);
}

static ES_VOID mod_571_save_payload_data(ES_U8 data_len)
{
    es_ble_rx_payload_t *rx_payload_data;

    rx_payload_data = mod_571_rx_data_find_idle(&mod_571_rx_buf[MOD_571_PROTO_MAC_IND]);
    if (ES_NULL == rx_payload_data) {
        // ble_mod_error("rx_payload_data is NULL");
        return;
    }

    es_memcpy(rx_payload_data->mac, &mod_571_rx_buf[MOD_571_PROTO_MAC_IND], 6);
    es_circel_write(rx_payload_data->data_buf, &mod_571_rx_buf[MOD_571_PROTO_DATA_IND], data_len);
}

static ES_VOID mod_571_parse_package(ES_VOID)
{
    ES_U8 package_len;
    ES_U8 data_len;
    ES_U8 checksum;
    ES_U8 remind_len;

    data_len = mod_571_rx_buf[MOD_571_PROTO_DLEN_IND];
    if (data_len > MOD_571_PROTO_DATA_MAX_LEN) { // error package
        mod_571_rx_buf_len = 0;
        return;
    }

    package_len = data_len + MOD_571_PROTO_PACKAGE_MIN_LEN;
    if (mod_571_rx_buf_len < package_len) { // not finish
        return;
    }

    if (1 == data_len) {
        resp_ack_ret = mod_571_rx_buf[MOD_571_PROTO_DATA_IND];
    }

    checksum = ble_mod_571_checksum(mod_571_rx_buf, package_len-1);
    if (checksum != mod_571_rx_buf[MOD_571_PROTO_SUM_IND+data_len]) {
        mod_571_rx_buf_len = 0;
        return;
    }

    resp_ack_cmd = mod_571_rx_buf[MOD_571_PROTO_CMD_IND];
    es_ble_set_msg_resp_value(resp_ack_cmd, resp_ack_ret);
    if ((MOD_571_PROTO_CMD_DATA_FROM_SLAVER == resp_ack_cmd || MOD_571_PROTO_CMD_DATA_FROM_MASTER == resp_ack_cmd)
        && data_len > 0) {
        mod_571_save_payload_data(data_len);
    }

    if (MOD_571_PROTO_CMD_READ_MAC == resp_ack_cmd && data_len == 6) {
        got_mac = 1;
        es_memcpy(ble_mac, &mod_571_rx_buf[MOD_571_PROTO_DATA_IND], 6);
        // ble_mod_debug("ble mac:");
        // es_log_dump_hex(ble_mac, 6);
    }

    remind_len = mod_571_rx_buf_len - package_len;
    mod_571_rx_buf_len = 0;
    if (remind_len > 0) {
        mod_571_save_package(&mod_571_rx_buf[package_len], remind_len);
    }

    return;
}

static ES_VOID mod_571_uart_rx_cb(ES_U8 id, const ES_BYTE *buf, ES_U16 len)
{
    // es_log_dump_hex(buf, len);
    mod_571_save_package(buf, len);

    if (MOD_571_PROTO_PACKAGE_MIN_LEN <= mod_571_rx_buf_len) {
        mod_571_parse_package();
    }
}

static ES_S32 ble_mod_571_uart_init(void)
{
    es_uart_param_t uart_param;

	uart_param.baud = ES_UART_BAUD_115200;
	uart_param.data = ES_UART_DATA_8_BIT;
	uart_param.stop = ES_UART_STOP_1_BIT;
	uart_param.parity = ES_UART_PARITY_NONE;
	uart_param.rx_cb = mod_571_uart_rx_cb;
    if (ES_RET_SUCCESS != es_uart_open(ES_BLE_UART_ID, &uart_param)) {
        ble_mod_error("open uart (%d) fail", ES_BLE_UART_ID);
        return ES_RET_FAILURE;
    }

    return ES_RET_SUCCESS;
}

static ES_S32 ble_mod_571_uart_send(const ES_BYTE *data, ES_U32 len)
{
    if (len != es_uart_write(ES_BLE_UART_ID, data, len)) {
        return ES_RET_FAILURE;
    }

    // es_log_dump_hex(data, len);
    return ES_RET_SUCCESS;
}


static ES_S32 ble_mod_571_package_send(ES_BYTE cmd, const ES_BYTE *mac, ES_BYTE *data, ES_U32 data_len)
{
    ES_U32 len = 0;
    ES_U32 i = 0;
    ES_BYTE mod_571_send_buf[MOD_571_PROTO_BUF_MAX_LEN] = {0};
    ES_U8 checksum = 0;

    if (MOD_571_PROTO_DATA_MAX_LEN < data_len) {
        return ES_RET_FAILURE;
    }

    // header
    mod_571_send_buf[len++] = MOD_571_PROTO_HEAD_DATA_0;
    mod_571_send_buf[len++] = MOD_571_PROTO_HEAD_DATA_1;

    // cmd
    mod_571_send_buf[len++] = cmd;

    // msg id
    mod_571_msg_send_id++;
    mod_571_send_buf[len++] = mod_571_msg_send_id;

    // MAC
    for (i = 0; i < ES_BLE_MAC_LEN; i++) {
        mod_571_send_buf[len++] = mac[i];
    }

    // data length
    mod_571_send_buf[len++] = (ES_BYTE)(data_len & 0xFF);

    // DATA
    if (data_len > 0) {
        for (i = 0; i < data_len; i++) {
            mod_571_send_buf[len++] = data[i];
        }
    }

    // calc checksum
    checksum = ble_mod_571_checksum(mod_571_send_buf, len);
    mod_571_send_buf[len++] = checksum;
    msg_resp_value = ES_BLE_RESP_NONE;

    if (ES_RET_SUCCESS != ble_mod_571_uart_send(mod_571_send_buf, len)) {
        return ES_RET_FAILURE;
    }

    return ES_RET_SUCCESS;
}


ES_S32 es_hal_ble_init(ES_VOID)
{
    ES_U8 i = 0;

    if (ES_RET_SUCCESS != ble_mod_571_uart_init()) {
        ble_mod_error("uart init fail.");
        return ES_RET_FAILURE;
    }

    memset(mod_571_rx_data, 0x00, sizeof(mod_571_rx_data));
    for (i = 0; i < MOD_571_MAX_CLIENT_COUNT; i++) {
        mod_571_rx_data[i].data_buf = es_circle_buf_new(MOD_571_PROTO_BUF_MAX_LEN);
        if (ES_NULL == mod_571_rx_data[i].data_buf) {
            return ES_RET_FAILURE;
        }
    }

    return ES_RET_SUCCESS;
}

ES_S32 es_hal_ble_slave_conn(const es_ble_conn_param_t *p)
{
    ES_BYTE data[ES_BLE_UUID_LEN*2] = {0};
    ES_S32 ret = ES_RET_SUCCESS;

    if (ES_NULL == p->serv_uuid || ES_NULL == p->char_uuid) {
        return ES_RET_FAILURE;
    }

    es_memcpy(data, p->serv_uuid, ES_BLE_UUID_LEN);
    es_memcpy(data+ES_BLE_UUID_LEN, p->char_uuid, ES_BLE_UUID_LEN);

    ret = ble_mod_571_package_send(MOD_571_PROTO_CMD_CONN, p->mac, data, ES_BLE_UUID_LEN*2);
    if (ret != ES_RET_SUCCESS) {
        return ES_RET_FAILURE; 
    }

    return ES_RET_SUCCESS;
}

ES_S32 es_hal_ble_slave_send(const es_ble_send_data_t *data)
{
    return ble_mod_571_package_send(MOD_571_PROTO_CMD_DATA_TO_SLAVER, data->mac, data->data, data->data_len);
}


ES_S32 es_hal_ble_master_send(const es_ble_send_data_t *data)
{
    ES_BYTE master_mac[6] = {0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF};
    return ble_mod_571_package_send(MOD_571_PROTO_CMD_DATA_TO_MASTER, master_mac, data->data, data->data_len);
}

ES_S32 es_hal_ble_slave_disconn(const ES_BYTE *mac)
{
    if (ES_RET_SUCCESS != ble_mod_571_package_send(MOD_571_PROTO_CMD_DISCONN, mac, ES_NULL, 0)) {
        return ES_RET_FAILURE;
    }

    return ES_RET_SUCCESS;
}

ES_S32 es_hal_ble_master_disconn(ES_VOID)
{
    return ES_RET_FAILURE;
}

ES_S32 es_hal_ble_get_resp(ES_VOID)
{
    return (ES_S32)msg_resp_value;
}

ES_S32 es_hal_ble_recv(es_ble_recv_data_t *p)
{
    ES_U32 i = 0;
    ES_U32 data_size = 0;

    for (i = 0; i < MOD_571_MAX_CLIENT_COUNT; i++) {
        data_size = es_circel_get_data_size(mod_571_rx_data[i].data_buf);
        if (data_size > 0) {
            break;
        }
    }

    if (i >= MOD_571_MAX_CLIENT_COUNT) {
        // no data
        return ES_RET_FAILURE;
    }

    if (ES_BLE_RECV_DATA_LEN < data_size) {
        data_size = ES_BLE_RECV_DATA_LEN;
    }
    // ble_mod_debug("data_size:%d", data_size);
    memcpy(p->mac, mod_571_rx_data[i].mac, 6);
    es_circel_read(mod_571_rx_data[i].data_buf, p->data, data_size);
    p->data_len = data_size;

    return ES_RET_SUCCESS;
}

ES_S32 es_hal_ble_read_mac(ES_BYTE *mac)
{
    ES_U32 count = 0;
    if (got_mac) {
        es_memcpy(mac, ble_mac, 6);
        return ES_RET_SUCCESS;
    }

again:
    ble_mod_571_package_send(MOD_571_PROTO_CMD_READ_MAC, ble_mac, ES_NULL, 0);
    if (got_mac) {
        es_memcpy(mac, ble_mac, 6);
        return ES_RET_SUCCESS;
    }

    count++;
    if (count < 5) {
        goto again;
    }

    return ES_RET_FAILURE;
}

ES_S32 es_hal_ble_is_connected(ES_VOID)
{
    return is_connected;
}

#endif
