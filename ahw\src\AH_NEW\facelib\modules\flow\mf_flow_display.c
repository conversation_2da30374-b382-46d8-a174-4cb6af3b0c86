#include "es_inc.h"
#include "facelib_inc.h"

#pragma GCC diagnostic ignored "-Wunused-function"
#pragma GCC diagnostic ignored "-Wunused-variable"

/*****************************************************************************/
// Macro definitions
/*****************************************************************************/
#if 1
#define DEBUG_LINE()
#define DBG_HEAP()
#else
#define DEBUG_LINE() do {printk("[mf_flow_vis] L%d\r\n", __LINE__); } while(0)
#define DBG_HEAP() do {printk("[mf_flow_vis] L%d :heap:%ld KB\r\n", __LINE__, get_free_heap_size() / 1024); } while(0)
#endif

/*****************************************************************************/
// Function definitions
/*****************************************************************************/

/*****************************************************************************/
// Private Var 局部变量
/*****************************************************************************/

/*****************************************************************************/
// Driver  底层函数
/*****************************************************************************/
//注册回调函数
static mf_err_t reg_cb_list(flow_cb_list_t* list, flow_cb_t cb)
{
	if(list->idx >= 5) return MF_ERR_LIST_FULL;
	if(cb == NULL) return MF_ERR_CB_NULL;
	list->cbs[list->idx] = cb;
	list->idx ++;
	return MF_ERR_NONE;
}

static mf_err_t _reg_kpubuf_cb(flow_cb_t cb)
{
	return reg_cb_list(&mf_flow.kpubuf_cb_list, cb);
}

static mf_err_t _reg_rgbbuf_cb0(flow_cb_t cb)
{
	return reg_cb_list(&mf_flow.rgbbuf_cb0_list, cb);
}

static mf_err_t _reg_rgbbuf_cb1(flow_cb_t cb)
{
	return reg_cb_list(&mf_flow.rgbbuf_cb1_list, cb);
}

static void _process_cblist(flow_cb_list_t* list)
{
	for(uint8_t i = 0; i < list->idx; i++) {
		if(list->cbs[i])list->cbs[i]();
	}
	return;
}

/* unused func. */
static void fake_ops_cb(mf_dbops_type_t type, uint32_t id, void* _item)
{
	return;
}

/* unused func. */
static mf_err_t _flow_db_search(int8_t *ftr, uint8_t ir_or_rgb, uint32_t* vid, float *score)
{
    int v_id = -1;
    float v_score_max = 0.0f;

    *score = v_score_max;
	*vid = v_id;
    return MF_ERR_NONE;
}
/*****************************************************************************/
// Private Func 局部函数
/*****************************************************************************/
/* unused func. */
static mf_err_t _flow_display_db_hash(uint8_t* sha256)
{
	memset(sha256, 0xFF, 32);
    return MF_ERR_NONE;
}

static mf_err_t _flow_display_init(mf_facecb_t* cb)
{
	mf_flow.cb = cb;
	mf_facedb.ops_cb = fake_ops_cb;
	mf_model.db_search = _flow_db_search;
	return MF_ERR_NONE;
}

static void _flow_display_deinit(void)
{
	return;
}

// //执行界面显示更新
// static void _flow_handle_display(void)
// {
// 	/* 如果使能了背光控制，我们在这里判断是否要进行刷屏 */
// 	if(mf_brd.cfg.flow.close_lcd) {
// 		// if(flow.open_lcd_cnt == 0) {
// 		// 	return;
// 		// }
// 	}

// 	if(mf_flow.cb->pre_display) {
// 		mf_flow.cb->pre_display();
// 	}

// 	mf_ui.refresh();

// 	if(mf_flow.cb->post_display) {
// 		mf_flow.cb->post_display();
// 	}

// 	return;
// }

static mf_err_t _flow_display_loop(void)
{
    // uint64_t t = sysctl_get_time_us();
    mf_cam.lock_rgb();
    // printk("lock %ld us\n", sysctl_get_time_us() - t);
    // t = sysctl_get_time_us();
    // _process_cblist(&mf_flow.kpubuf_cb_list);
    // _process_cblist(&mf_flow.rgbbuf_cb0_list);
    // mf_ui.cam2buf();
    // printk("camubuf %ld us\n", sysctl_get_time_us() - t);
    // t = sysctl_get_time_us();
    // _process_cblist(&mf_flow.rgbbuf_cb1_list);
	// _flow_handle_display();
	es_ui_cam_update(ES_FALSE);
	es_ui_cam2buf();

    // printk("display %ld us\n", sysctl_get_time_us() - t);
    // t = sysctl_get_time_us();
    mf_cam.unlock_rgb();
    // printk("unlock %ld us\n", sysctl_get_time_us() - t);

    return MF_ERR_NONE;
}

/*****************************************************************************/
// Public Var 全局变量
/*****************************************************************************/
mf_flow_t mf_flow_display = {
	.init_flag = 0,
	.type      = MF_FLOW_SINGLE_VIS,
	.enable    = 1,
	.kpubuf_cb_list  = {0},
	.rgbbuf_cb0_list = {0},
	.rgbbuf_cb1_list = {0},
	.init      = _flow_display_init,
	.deinit    = _flow_display_deinit,
	.loop      = _flow_display_loop,
	.hash      = _flow_display_db_hash,
	.reg_kpubuf_cb   = _reg_kpubuf_cb,
	.reg_rgbbuf_cb0  = _reg_rgbbuf_cb0,
	.reg_rgbbuf_cb1  = _reg_rgbbuf_cb1,
};
