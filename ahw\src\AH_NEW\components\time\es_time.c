/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_time.c
** bef: define the interface for time.
** auth: lines<<EMAIL>>
** create on 2021.11.19 
*/

#include "es_inc.h"

// #define ES_TIME_DEBUG
#ifdef ES_TIME_DEBUG
#define es_time_debug es_log_info
#define es_time_error es_log_error
#else
#define es_time_debug(...)
#define es_time_error(...)
#endif

#define ES_TIME_SEC_PER_HOUR                (60 * 60)
#define ES_TIME_SEC_PER_DAY                 ((ES_TIME_SEC_PER_HOUR) * 24)
#define ES_TIME_HOUR_PER_4_YEARS            (1461L * 24L)
#define ES_TIME_DAYS_PER_4_YEARS            (1461) // 365+365+365+366
#define ES_TIME_HOUR_PER_YEAR               (365*24)

static ES_S32 es_time_zone = 8; // default is China.
const ES_U8 es_days_per_month[12] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};


static ES_BOOL es_time_is_leap_year(ES_U16 year)
{
    if (year % 400 == 0 || (year % 4 == 0 && (year % 100 != 0))) {
        return ES_TRUE;
    }

    return ES_FALSE;
}

static ES_U16 es_time_days_in_year(ES_U16 year)
{
    return es_time_is_leap_year(year) ? 366 : 365;
}

static ES_U8 es_time_days_in_month(ES_U16 year, ES_U8 month)
{
    if (month == 2) {
        return es_time_is_leap_year(year) ? 29 : 28;
    }
    if (month == 4 || month == 6 || month == 9 || month == 11) {
        return 30;
    }
    return 31;
}

static ES_U8 es_time_get_wday(ES_U16 year, ES_U8 month, ES_U8 day)
{
    ES_U32 y, c;
    ES_U8 w;

    y = year % 100;
    c = year / 100;

    if (month < 3) {
        y -= 1;
        month += 12;
    }
    w=(ES_U8)((y+y/4+c/4-2*c+26*(month+1)/10+day-1)%7);

    return w;
}


static ES_S32 es_time_time2timestamp(es_time_t *time)
{
    int year, mon, day, hour, min, sec;

    year = (int)time->year;
    mon = (int)time->mon;
    day = (int)time->mday;
    hour = (int)time->hour;
    min = (int)time->min;
    sec = (int)time->sec;

    //January and February are counted as months 13 and 14 of the previous year
    if (mon <= 2) {
        mon += 12;
        year -= 1;
    }

    hour -= (es_time_zone);
    //Convert years to days
    time->timestamp = (365 * year) + (year / 4) - (year / 100) + (year / 400);
    //Convert months to days
    time->timestamp += (30 * mon) + (3 * (mon + 1) / 5) + day;
    //Unix time starts on January 1st, 1970
    time->timestamp -= 719561;
    //Convert days to seconds
    time->timestamp *= 86400;
    //Add hours, minutes and seconds
    time->timestamp += (3600 * hour) + (60 * min) + sec;

    return ES_RET_SUCCESS;
}

static ES_S32 es_time_timestamp2time(es_time_t *time)
{
    ES_U32 tmp_ts = 0;

    if (0 == time->timestamp) {
        es_memset(time, 0x00, sizeof(es_time_t));
        time->year = 1970;
        time->mon = 1;
        time->mday = 1;
        time->wday = 4;
        return ES_RET_SUCCESS;
    }
    tmp_ts = (ES_U32)((ES_S32)time->timestamp + (ES_S32)(es_time_zone*60*60));

    time->sec = tmp_ts % 60;
    tmp_ts /= 60;
    time->min = tmp_ts % 60;
    tmp_ts /= 60;
    time->hour = tmp_ts % 24;
    tmp_ts /= 24;

    // 计算年份
    time->year = 1970;
    while (tmp_ts >= es_time_days_in_year(time->year)) {
        tmp_ts -= es_time_days_in_year(time->year);
        (time->year)++;
    }

    // 计算月份
    time->mon = 1;
    while (tmp_ts >= es_time_days_in_month(time->year, time->mon)) {
        tmp_ts -= es_time_days_in_month(time->year, time->mon);
        (time->mon)++;
    }

    // 计算日期
    time->mday = tmp_ts + 1; // 日期从 1 开始

    time->wday = es_time_get_wday(time->year, time->mon, time->mday);
#if 0
    printf("es_time_timestamp2tm(%d), es_time_zone:%d, %04d-%02d-%02d %02d:%02d:%02d\r\n", 
            (timestamp+es_time_zone*ES_TIME_SEC_PER_HOUR), es_time_zone,
            time->year, time->mon, time->mday,
            time->hour, time->min, time->sec);
#endif

    return ES_RET_SUCCESS;
}

static ES_U8 es_time_calc_weekday(es_time_t *time)
{
    uint8_t m = time->mon;
    uint8_t d = time->mday;
    int y = time->year;

    if(m == 1 || m == 2) {
        m += 12;
        y--;
    }
    return ((d + 2 * m + 3 * (m + 1) / 5 + y + y / 4 - y / 100 + y / 400) % 7) + 1;
}


ES_S32 es_time_init(ES_VOID)
{
    return ES_RET_SUCCESS;
}

ES_S32 es_time_check_timeout_ms(ES_U32 *last, ES_U32 timeout_ms)
{
    ES_U32 t;

    t = es_time_get_sytem_ms();
    if ((t - *last)  < ( timeout_ms)) {
        return ES_RET_FAILURE;
    }

    *last = t;
    return ES_RET_SUCCESS;
}

ES_U32 es_time_get_sytem_ms(ES_VOID)
{
    return (ES_U32)(sysctl_get_time_us()/1000);
}

ES_U64 es_time_get_sytem_us(ES_VOID)
{
    return sysctl_get_time_us();
}

ES_S32 es_time_sync(es_time_t *time)
{
    es_hal_rtc_time_t rtc_time;
    if (0 != time->timestamp) {
        es_time_timestamp2time(time);
    }

    if (0 == time->wday) {
        time->wday = es_time_calc_weekday(time);
    }

    rtc_time.year = time->year;
    rtc_time.mon = time->mon;
    rtc_time.mday = time->mday;
    rtc_time.hour = time->hour;
    rtc_time.min = time->min;
    rtc_time.sec = time->sec;
    rtc_time.wday = time->wday;

    if (rtc_time.year < 2022) {
        es_time_error("sync time fail");
        return ES_RET_FAILURE;
    }

    es_time_debug("sync time start, %d-%d-%d %d:%d:%d\r\n", rtc_time.year, rtc_time.mon, rtc_time.mday, rtc_time.hour, rtc_time.min, rtc_time.sec);
    if (ES_RET_SUCCESS != es_hal_rtc_write_time(&rtc_time)) {
        es_time_error("sync time fail");
        return ES_RET_FAILURE;
    }
    es_time_debug("sync time success");

    return ES_RET_SUCCESS;
}

ES_S32 es_time_get_now(es_time_t *now)
{
    int year, mon, day, hour, min, sec;

    if (0 != rtc_timer_get(&year, &mon, &day, &hour, &min, &sec)) {
        es_time_error("get rtc time fail");
        return ES_RET_FAILURE;
    }

    now->year = (ES_U16)year;
    now->mon = (ES_U8)mon;
    now->mday = (ES_U8)day;
    now->hour = (ES_U8)hour;
    now->min = (ES_U8)min;
    now->sec = (ES_U8)sec;

    now->wday = es_time_calc_weekday(now);
    es_time_time2timestamp(now);

    return ES_RET_SUCCESS;
}

ES_U32 es_time_get_timestamp(ES_VOID)
{
    es_time_t now_time;

    if (ES_RET_SUCCESS != es_time_get_now(&now_time)) {
        return 0;
    }

    return now_time.timestamp;
}