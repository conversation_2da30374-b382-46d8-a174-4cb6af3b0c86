#include "facelib_inc.h"
#include "es_mem.h"
#ifdef FLASH_LAYOUT_V2
    #undef ASCII_FONT_IN_FLASH
    #define ASCII_FONT_IN_FLASH     (0)
#endif

#pragma GCC diagnostic ignored "-Wunused-variable"

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
static mf_imgop_type_t imgop_type = IMGOP_MCU;
void image_op_init(mf_imgop_type_t type)
{
	imgop_type = type;
	return;
}

mf_imgop_type_t get_image_op(void)
{
    return imgop_type;
}
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

// RGB888 顺时针 90 度
// 注意旋转90 后，图像数据的宽高会对调，显示时候自己注意
// !!! 宽度及高度必须是2的倍数
void image_rgb888_roate_right90(uint8_t *out, uint8_t *src, uint16_t w, uint16_t h)
{
    for(uint32_t i = 0; i < w; i++)
    {
        for(uint32_t j = 0; j < (h / 2); j++)
        {
            *(out + (i * h + (j * 2 + 0)) * 3 + 0) = *(src + ((h - 1 - (j * 2 + 0)) * w + i) * 3 + 0);
            *(out + (i * h + (j * 2 + 0)) * 3 + 1) = *(src + ((h - 1 - (j * 2 + 0)) * w + i) * 3 + 1);
            *(out + (i * h + (j * 2 + 0)) * 3 + 2) = *(src + ((h - 1 - (j * 2 + 0)) * w + i) * 3 + 2);

            *(out + (i * h + (j * 2 + 1)) * 3 + 0) = *(src + ((h - 1 - (j * 2 + 1)) * w + i) * 3 + 0);
            *(out + (i * h + (j * 2 + 1)) * 3 + 1) = *(src + ((h - 1 - (j * 2 + 1)) * w + i) * 3 + 1);
            *(out + (i * h + (j * 2 + 1)) * 3 + 2) = *(src + ((h - 1 - (j * 2 + 1)) * w + i) * 3 + 2);
        }
    }
    return;
}

// RGB888 逆时针90度
// 注意旋转90 后，图像数据的宽高会对调，显示时候自己注意
// !!! 宽度及高度必须是2的倍数
void image_rgb888_roate_left90(uint8_t *out, uint8_t *src, uint16_t w, uint16_t h)
{
    for(uint32_t i = 0; i < w; i++)
    {
        for(uint32_t j = 0; j < (h / 2); j++)
        {
            *(out + (i * h + (j * 2 + 0)) * 3 + 0) = *(src + ((j * 2 + 0) * w + i) * 3 + 0);
            *(out + (i * h + (j * 2 + 0)) * 3 + 1) = *(src + ((j * 2 + 0) * w + i) * 3 + 1);
            *(out + (i * h + (j * 2 + 0)) * 3 + 2) = *(src + ((j * 2 + 0) * w + i) * 3 + 2);

            *(out + (i * h + (j * 2 + 1)) * 3 + 0) = *(src + ((j * 2 + 1) * w + i) * 3 + 0);
            *(out + (i * h + (j * 2 + 1)) * 3 + 1) = *(src + ((j * 2 + 1) * w + i) * 3 + 1);
            *(out + (i * h + (j * 2 + 1)) * 3 + 2) = *(src + ((j * 2 + 1) * w + i) * 3 + 2);
        }
    }
    return;
}

void convert_rgb565_order(uint16_t *image, uint16_t w, uint16_t h)
{
    uint16_t tmp;

    if(((uint32_t)image & 0x80000000) == 0)
    {
        image = (uint16_t*)(((uint32_t)image) + 0x40000000);
    }

    for(uint16_t i = 0; i < h; i++)
    {
        for(uint16_t j = 0; j < (w / 2); j++)
        {
            tmp = *(image + i * w + j * 2 + 0);
            *(image + i * w + j * 2 + 0) = *(image + i * w + j * 2 + 1);
            *(image + i * w + j * 2 + 1) = tmp;
        }
    }
}

//FIXME: 需要调用 convert_rgb565_order
// RGB565 顺时针 90 度
// 注意旋转90 后，图像数据的宽高会对调，显示时候自己注意
// !!! 宽度及高度必须是2的倍数
void image_rgb565_roate_right90(uint16_t *out, uint16_t *src, uint16_t w, uint16_t h)
{
    if(((uint32_t)out & 0x80000000) == 0)
    {
        out = (uint16_t*)(((uint32_t)out) + 0x40000000);
    }

    if(((uint32_t)src & 0x80000000) == 0)
    {
        src = (uint16_t*)(((uint32_t)src) + 0x40000000);
    }

    for(uint32_t i = 0; i < w; i++)
    {
        for(uint32_t j = 0; j < (h / 2); j++)
        {
            *(out + (i * h + (j * 2 + 0))) = *(src + ((h - 1 - (j * 2 + 0)) * w + i));

            *(out + (i * h + (j * 2 + 1))) = *(src + ((h - 1 - (j * 2 + 1)) * w + i));
        }
    }
    return;
}

#define LOOP_UNIT()                         \
    {                                       \
        next = h - 1 - i / w + (i % w) * h; \
        tmp = buf[next];                    \
        buf[next] = t;                      \
        t = tmp;                            \
        i = next;                           \
    }

#define I_UNIT(x)    \
    i = x;           \
    cycleBegin = i;  \
    t = buf[i];      \
    do               \
    {                \
        LOOP_UNIT(); \
        LOOP_UNIT(); \
    } while(i != cycleBegin);

void image_rgb565_roate_right90_nobuf(uint16_t *buf, uint16_t w, uint16_t h)
{
    uint16_t t; //暂存一个交换像素
    uint16_t tmp;
    int next;       //下一个像素的地址
    int cycleBegin; //本轮起始地址
    int i;          //计数器

    I_UNIT(0);
    I_UNIT(10);
    return;
}

//FIXME: 需要调用 convert_rgb565_order
// RGB565 逆时针90度
// 注意旋转90 后，图像数据的宽高会对调，显示时候自己注意
// !!! 宽度及高度必须是2的倍数
void image_rgb565_roate_left90(uint16_t *out, uint16_t *src, uint16_t w, uint16_t h)
{
    for(uint32_t i = 0; i < w; i++)
    {
        for(uint32_t j = 0; j < (h / 2); j++)
        {
            *(out + (i * h + (j * 2 + 0))) = *(src + ((j * 2 + 0) * w + i));

            *(out + (i * h + (j * 2 + 1))) = *(src + ((j * 2 + 1) * w + i));
        }
    }
    return;
}

// R8G8B8 顺时针 90 度
// 注意旋转90 后，图像数据的宽高会对调，显示时候自己注意
// !!! 宽度及高度必须是2的倍数
void image_r8g8b8_roate_right90(uint8_t *out, uint8_t *src, uint16_t w, uint16_t h)
{
    for(uint32_t i = 0; i < w; i++)
    {
        for(uint32_t j = 0; j < (h / 2); j++)
        {
            *(out + (i * h + (j * 2 + 0))) = *(src + ((h - 1 - (j * 2 + 0)) * w + i));
            *(out + (i * h + (j * 2 + 1))) = *(src + ((h - 1 - (j * 2 + 1)) * w + i));
        }
    }
    return;
}

// R8G8B8 逆时针90度
// 注意旋转90 后，图像数据的宽高会对调，显示时候自己注意
// !!! 宽度及高度必须是2的倍数
void image_r8g8b8_roate_left90(uint8_t *out, uint8_t *src, uint16_t w, uint16_t h)
{
    for(uint32_t i = 0; i < w; i++)
    {
        for(uint32_t j = 0; j < (h / 2); j++)
        {
            *(out + (i * h + (j * 2 + 0))) = *(src + ((j * 2 + 0) * w + i));
            *(out + (i * h + (j * 2 + 1))) = *(src + ((j * 2 + 1) * w + i));
        }
    }
    return;
}

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

/* clang-format off */
#define RGB565_RED          (0xf800)
#define RGB565_GREEN        (0x07e0)
#define RGB565_BLUE         (0x001f)
/* clang-format on */
void image_rgb8882rgb565(uint16_t *rgb565, uint8_t *rgb888,
                         uint16_t img_w, uint16_t img_h)
{
	uint16_t* pixel;
	uint8_t* r = rgb888;
	uint8_t* g = rgb888+img_w*img_h;
	uint8_t* b = rgb888+img_w*img_h*2;
	uint16_t _r,_g,_b;
    for(uint16_t i = 0; i < img_h; i++)
    {
        for(uint16_t j = 0; j < img_w ; )
        {
			_r = r[i * img_w+ j ]>>3;_g = g[i * img_w+ j ]>>2;_b = b[i * img_w+ j ]>>3;
			*(rgb565 + i * img_w + j) = (_r<<11)|(_g<<5)|(_b<<0);
			j++;
			_r = r[i * img_w+ j ]>>3;_g = g[i * img_w+ j ]>>2;_b = b[i * img_w+ j ]>>3;
			*(rgb565 + i * img_w + j) = (_r<<11)|(_g<<5)|(_b<<0);
			j++;
			_r = r[i * img_w+ j ]>>3;_g = g[i * img_w+ j ]>>2;_b = b[i * img_w+ j ]>>3;
			*(rgb565 + i * img_w + j) = (_r<<11)|(_g<<5)|(_b<<0);
			j++;
			_r = r[i * img_w+ j ]>>3;_g = g[i * img_w+ j ]>>2;_b = b[i * img_w+ j ]>>3;
			*(rgb565 + i * img_w + j) = (_r<<11)|(_g<<5)|(_b<<0);
			j++;
        }
    }
	return;
}


void image_rgb5652rgb888(uint16_t *rgb565, uint8_t *rgb888,
                         uint16_t img_w, uint16_t img_h)
{
    uint16_t pixel = 0;

    for(uint16_t i = 0; i < img_h; i++)
    {
        for(uint16_t j = 0; j < img_w / 2; j++)
        {
            pixel = (uint16_t) * (rgb565 + i * img_w + (j * 2 + 1));
            *(rgb888 + ((j * 2) + i * img_w) * 3 + 0) = (pixel & RGB565_RED) >> 8;
            *(rgb888 + ((j * 2) + i * img_w) * 3 + 1) = (pixel & RGB565_GREEN) >> 3;
            *(rgb888 + ((j * 2) + i * img_w) * 3 + 2) = (pixel & RGB565_BLUE) << 3;

            pixel = (uint16_t) * (rgb565 + i * img_w + (j * 2));
            *(rgb888 + ((j * 2 + 1) + i * img_w) * 3 + 0) = (pixel & RGB565_RED) >> 8;
            *(rgb888 + ((j * 2 + 1) + i * img_w) * 3 + 1) = (pixel & RGB565_GREEN) >> 3;
            *(rgb888 + ((j * 2 + 1) + i * img_w) * 3 + 2) = (pixel & RGB565_BLUE) << 3;
        }
    }
}

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//横屏旋转为竖屏使用
static void image_rgb565_draw_line_h2v(uint16_t *ptr, uint8_t hor_ver,
                                   uint16_t x, uint16_t y,
                                   uint16_t linew, uint16_t lineh,
                                   uint16_t img_w, uint16_t img_h,
                                   uint16_t color)
{
    uint16_t i, j, *addr = NULL;
	uint16_t _i;
	x=(x>>1)<<1;	//对齐处理，防止MCU屏里的交错
	y=(y>>1)<<1;
    {
        for(i = 0; i < (hor_ver ? lineh : linew); i+=1)
        {
			_i = i;
            addr = ((uint16_t *)ptr) + (img_h - (x + _i) - 1) * img_w + y; //竖屏
            for(j = 0; j < (hor_ver ? linew : lineh); j++)
            {
                *addr = color;
                addr++;
            }
        }
    }
    return;
}
//横屏旋转为竖屏使用
#define EDGE_W 2
#define EDGE_H 15 
void image_rgb565_draw_edge_h2v(uint16_t *gram,
                            uint16_t x1, uint16_t y1,
                            uint16_t x2, uint16_t y2,
                            uint16_t color,
                            uint16_t img_w, uint16_t img_h)
{
    if(x1 <= 0)
        x1 = 1;
    if(x2 >= img_w - 1)
        x2 = img_w - 2;
    if(y1 <= 0)
        y1 = 1;
    if(y2 >= img_h - 1)
        y2 = img_h - 2;

    x2 -= 3;
    y2 -= 3;

    //左上
    image_rgb565_draw_line_h2v((uint16_t *)gram, 1, x1, y1, EDGE_W, EDGE_H, img_w, img_h, color);
    image_rgb565_draw_line_h2v((uint16_t *)gram, 0, x1, y1, EDGE_W, EDGE_H, img_w, img_h, color);

    //右上
    image_rgb565_draw_line_h2v((uint16_t *)gram, 1, x2 - 12, y1, EDGE_W, EDGE_H, img_w, img_h, color);
    image_rgb565_draw_line_h2v((uint16_t *)gram, 0, x2, y1, EDGE_W, EDGE_H, img_w, img_h, color);

    //右下
    image_rgb565_draw_line_h2v((uint16_t *)gram, 1, x2 - 12, y2, EDGE_W, EDGE_H, img_w, img_h, color);
    image_rgb565_draw_line_h2v((uint16_t *)gram, 0, x2, y2 - 12, EDGE_W, EDGE_H, img_w, img_h, color);

    //左下
    image_rgb565_draw_line_h2v((uint16_t *)gram, 1, x1, y2, EDGE_W, EDGE_H, img_w, img_h, color);
    image_rgb565_draw_line_h2v((uint16_t *)gram, 0, x1, y2 - 12, EDGE_W, EDGE_H, img_w, img_h, color);
    return;
}
//屏的正常方向
void image_rgb565_draw_edge_normal(uint32_t *gram,
                            uint16_t x1, uint16_t y1,
                            uint16_t x2, uint16_t y2,
                            uint16_t color,
                            uint16_t img_w, uint16_t img_h)
{
    uint32_t data = ((uint32_t)color << 16) | (uint32_t)color;
    uint32_t *addr1, *addr2, *addr3, *addr4;

    if(x1 <= 0)
        x1 = 1;
    if(x2 >= img_w - 1)
        x2 = img_w - 2;
    if(y1 <= 0)
        y1 = 1;
    if(y2 >= img_h - 1)
        y2 = img_h - 2;

    addr1 = gram + (img_w * y1 + x1) / 2;
    addr2 = gram + (img_w * y1 + x2 - 8) / 2;
    addr3 = gram + (img_w * (y2 - 1) + x1) / 2;
    addr4 = gram + (img_w * (y2 - 1) + x2 - 8) / 2;

    for(uint32_t i = 0; i < 4; i++)
    {
        *addr1 = data;
        *(addr1 + img_w / 2) = data;
        *addr2 = data;
        *(addr2 + img_w / 2) = data;
        *addr3 = data;
        *(addr3 + img_w / 2) = data;
        *addr4 = data;
        *(addr4 + img_w / 2) = data;
        addr1++;
        addr2++;
        addr3++;
        addr4++;
    }

    addr1 = gram + (img_w * y1 + x1) / 2;
    addr2 = gram + (img_w * y1 + x2 - 2) / 2;
    addr3 = gram + (img_w * (y2 - 8) + x1) / 2;
    addr4 = gram + (img_w * (y2 - 8) + x2 - 2) / 2;

    for(uint32_t i = 0; i < 8; i++)
    {
        *addr1 = data;
        *addr2 = data;
        *addr3 = data;
        *addr4 = data;
        addr1 += img_w / 2;
        addr2 += img_w / 2;
        addr3 += img_w / 2;
        addr4 += img_w / 2;
    }
    return;
}
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//取模方式： 阴码，逐列式，顺向
// static void image_rgb565_ram_draw_font_mat(uint16_t *ptr, uint8_t zhCN,
//                                            uint8_t *font_mat, uint8_t size,
//                                            uint16_t x, uint16_t y,
//                                            uint16_t color, uint16_t *bg_color,
//                                            uint16_t img_w, uint16_t img_h)
// {
//     uint8_t data;
//     uint16_t *addr = NULL;
//     uint16_t oy = y;

//     uint16_t csize = (size / 8 + ((size % 8) ? 1 : 0)) * (zhCN ? size : (size / 2));

//     for(uint16_t i = 0; i < csize; i++)
//     {
//         data = *(font_mat + i);
//         for(uint8_t j = 0; j < 8; j++)
//         {
// 			if(imgop_type == IMGOP_SIPEED_H2V) {
// 				addr = ((uint16_t *)ptr) + (img_h - x - 1) * img_w + y; //竖屏
// 			} else {
// 				addr = ((uint16_t *)ptr) + y * img_w + x; //横屏
// 			}

//             if(data & 0x80)
//             { //mcu屏需要手工交错显示
// 				if(imgop_type == IMGOP_SIPEED_H2V || imgop_type == IMGOP_SIPEED) {*addr = color;}
// 				else{
// 					if(x & 1)
// 						*(addr - 1) = color;
// 					else
// 						*(addr + 1) = color;
// 				}
//             } else
//             {
//                 if(bg_color)
//                 {
// 					if(imgop_type == IMGOP_SIPEED_H2V || imgop_type == IMGOP_SIPEED) {*addr = *bg_color;}
// 					else{
// 						if(x & 1)
// 							*(addr - 1) = *bg_color;
// 						else
// 							*(addr + 1) = *bg_color;
// 					}
//                 }
//             }
//             data <<= 1;
//             y++;
//             if((y - oy) == size)
//             {
//                 y = oy;
//                 x++;
//                 break;
//             }
//         }
//     }
//     return;
// }


// static void image_rgb565_ram_draw_char(uint16_t *ptr, char c, uint8_t size,
//                                        uint16_t x, uint16_t y,
//                                        uint16_t color, uint16_t *bg_color,
//                                        uint16_t img_w, uint16_t img_h)
// {
//     if(c < ' ' || c > '~')
//     {
//         return;
//     }

//     uint8_t offset = c - ' ';
//     uint16_t csize = (size / 8 + ((size % 8) ? 1 : 0)) * (size / 2);

// #if ASCII_FONT_IN_FLASH
//     uint8_t type = ASCII_MAX;

//     uint8_t font_mat[256]; /* ascii_2448 最大为 144 */

//     switch(size)
//     {
//         case 12:
//             type = ASCII_0612;
//             if (c >= '0' && c <= '9')
//             {
//                 offset = c - '0';
//             }
//             else if (c == 'V')
//             {
//                 offset = 10;
//             }
//             else if (c == '.')
//             {
//                 offset = 11;
//             }
//             else
//             {
//                 offset = 0;
//             }
//             break;
//         case 48:
//             type = ASCII_2448;
//             if(c >= '0' && c <= '9')
//             {
//                 offset = c - '0';
//             } else if(c == ':')
//             {
//                 offset = 10;
//             }
//             break;
//         case 16:
//             type = ASCII_0816;
//             break;
//         case 24:
//             type = ASCII_1224;
//             break;
//         case 32:
//             type = ASCII_1632;
//             break;
//         default:
//             break;
//     }
//     ascii_get_font_mat(type, offset, csize, font_mat);
//     image_rgb565_ram_draw_font_mat(ptr, 0, font_mat, size, x, y, color, bg_color, img_w, img_h);
// #else
//     switch(size)
//     {
//         case 16:
//             image_rgb565_ram_draw_font_mat(ptr, 0, (uint8_t *)(ascii0816 + (offset * csize)), size, x, y, color, bg_color, img_w, img_h);
//             break;
//         case 24:
//             image_rgb565_ram_draw_font_mat(ptr, 0, (uint8_t *)(ascii1224 + (offset * csize)), size, x, y, color, bg_color, img_w, img_h);
//             break;
//         case 32:
//             image_rgb565_ram_draw_font_mat(ptr, 0, (uint8_t *)(ascii1632 + (offset * csize)), size, x, y, color, bg_color, img_w, img_h);
//             break;
//         case 48:
//             if(c >= '0' && c <= '9')
//             {
//                 offset = c - '0';
//                 image_rgb565_ram_draw_font_mat(ptr, 0, (uint8_t *)(ascii2448 + (offset * csize)), size, x, y, color, bg_color, img_w, img_h);
//             } else if(c == ':')
//             {
//                 offset = 10;
//                 image_rgb565_ram_draw_font_mat(ptr, 0, (uint8_t *)(ascii2448 + (offset * csize)), size, x, y, color, bg_color, img_w, img_h);
//             }
//             break;
//         case 12:
//             if (c >= '0' && c <= '9')
//             {
//                 offset = c - '0';
//             }
//             else if (c == 'V')
//             {
//                 offset = 10;
//             }
//             else if (c == '.')
//             {
//                 offset = 11;
//             }
//             else
//             {
//                 offset = 0;
//             }
//             image_rgb565_ram_draw_font_mat(ptr, 0, ascii0612 + (offset * csize), size, x, y, color, bg_color, img_w, img_h);
//             break;
//         default:
//             break;
//     }
// #endif /* ASCII_FONT_IN_FLASH */

//     return;
// }

// void image_rgb565_draw_string(uint16_t *ptr, char *str, uint8_t size,
//                               uint16_t x, uint16_t y,
//                               uint16_t color, uint16_t *bg_color,
//                               uint16_t img_w, uint16_t img_h)
// {
//     uint16_t ox = x;
//     uint16_t oy = y;

//     if(size != 32 && size != 16 && size != 24 && size != 48 && size != 12)
//     {
//         return;
//     }

//     while(*str)
//     {
//         if(x > (ox + img_w - size / 2))
//         {
//             y += size;
//             x = ox;
//         }
//         if(y > (oy + img_h - size))
//             break;

//         if((*str == 0xd) ||
//            (*str == 0xa))
//         {
//             y += size;
//             x = ox;
//             str++;
//         } else
//         {
//             image_rgb565_ram_draw_char(ptr, *str, size, x, y, color, bg_color, img_w, img_h);
//             str++;
//             x += size / 2;
//         }
//     }
//     return;
// }
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

// static void image_rgb565_draw_zhCN_char(uint16_t *ptr, uint8_t *zhCN_char, uint8_t size,
//                                         uint16_t x, uint16_t y,
//                                         uint16_t color, uint16_t *bg_color,
//                                         uint16_t img_w, int16_t img_h,
//                                         get_zhCN_dat get_font_data)
// {
//     uint8_t zhCN_dat[128]; //max 32x32 font

//     memset(zhCN_dat, 0xff, sizeof(zhCN_dat));

//     if(get_font_data)
//     {
//         get_font_data(zhCN_char, zhCN_dat, size);
//     }

//     image_rgb565_ram_draw_font_mat(ptr, 1, zhCN_dat, size, x, y, color, bg_color, img_w, img_h);

//     return;
// }

// void image_rgb565_draw_zhCN_string(uint16_t *ptr, uint8_t *zhCN_string, uint8_t size,
//                                    uint16_t x, uint16_t y,
//                                    uint16_t color, uint16_t *bg_color,
//                                    uint16_t img_w, uint16_t img_h,
//                                    get_zhCN_dat get_font_data)
// {
//     uint8_t have_zhCN = 0;
//     uint16_t ox = x;
//     uint16_t oy = y;

//     if((size != 32 && size != 16 && size != 24) || (get_font_data == NULL))
//     {
//         return;
//     }

//     while(*zhCN_string != 0)
//     {
//         if(have_zhCN == 0)
//         {
//             if(*zhCN_string > 0x80)
//             {
//                 have_zhCN = 1;
//             } else
//             {
//                 if(x > (ox + img_w - size / 2))
//                 {
//                     y += size;
//                     x = ox;
//                 }
//                 if(y > (oy + img_h - size))
//                     break;

//                 if((*zhCN_string == 0xd) ||
//                    (*zhCN_string == 0xa))
//                 {
//                     y += size;
//                     x = ox;
//                     zhCN_string++;
//                 } else
//                 {
//                     image_rgb565_ram_draw_char(ptr, *zhCN_string, size, x, y, color, bg_color, img_w, img_h);
//                 }
//                 zhCN_string++;
//                 x += size / 2;
//             }
//         } else
//         {
//             have_zhCN = 0;
//             if(x > (ox + img_w - size))
//             {
//                 y += size;
//                 x = ox;
//             }
//             if(y > (oy + img_h - size))
//                 break;

//             image_rgb565_draw_zhCN_char(ptr, zhCN_string, size, x, y, color, bg_color, img_w, img_h, get_font_data);
//             zhCN_string += 2;
//             x += size;
//         }
//     }
//     return;
// }

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

static uint16_t fast_blender_alpha(uint32_t x, uint32_t y, uint32_t alpha)
{
    x = (x | (x << 16)) & 0x7E0F81F;
    y = (y | (y << 16)) & 0x7E0F81F;
    uint32_t result = ((((x - y) * alpha) >> 5) + y) & 0x7E0F81F;
    return (uint16_t)((result & 0xFFFF) | (result >> 16));
}

static inline void _mix_pic_line_rgblcd(uint16_t *src_buf, uint16_t *dst_buf, uint16_t dx, uint16_t dw, uint32_t alpha)
{
	for(uint16_t _x = dx; _x < (dx + dw); _x++)
	{
		if ((uint16_t)*(dst_buf + (_x - dx)) == 0xFFFF){continue;};
		*(src_buf + _x) = \
			fast_blender_alpha((uint32_t) * (src_buf + _x),
			(uint32_t) * (dst_buf + (_x - dx)),
			(uint32_t)alpha / 8);
	}
	return;
}

static inline void _mix_pic_line_mculcd(uint16_t *src_buf, uint16_t *dst_buf, uint16_t dx, uint16_t dw, uint32_t alpha)
{
	for(uint16_t _x = dx; _x < (dx + dw); _x+=2)
	{
		if ((uint16_t)*(dst_buf + (_x + 1 - dx)) == 0xFFFF){goto _pix_2;};
		*(src_buf + _x) = \
			fast_blender_alpha((uint32_t) * (src_buf + _x+1),
			(uint32_t) * (dst_buf + (_x + 1 - dx)),
			(uint32_t)alpha / 8);
		_pix_2:
		if ((uint16_t)*(dst_buf + (_x - dx)) == 0xFFFF){continue;};
		*(src_buf + _x + 1) = \
			fast_blender_alpha((uint32_t) * (src_buf + _x),
			(uint32_t) * (dst_buf + (_x - dx)),
			(uint32_t)alpha / 8);
	}
	return;
}


static inline void _mix_pic_line_rgblcd_nodelwhite(uint16_t *src_buf, uint16_t *dst_buf, uint16_t dx, uint16_t dw, uint32_t alpha)
{
	for(uint16_t _x = dx; _x < (dx + dw); _x++)
	{
		*(src_buf + _x) = \
			fast_blender_alpha((uint32_t) * (src_buf + _x),
			(uint32_t) * (dst_buf + (_x - dx)),
			(uint32_t)alpha / 8);
	}
	return;
}

static inline void _mix_pic_line_mculcd_nodelwhite(uint16_t *src_buf, uint16_t *dst_buf, uint16_t dx, uint16_t dw, uint32_t alpha)
{
	for(uint16_t _x = dx; _x < (dx + dw); _x+=2)
	{
		*(src_buf + _x) = \
			fast_blender_alpha((uint32_t) * (src_buf + _x),
			(uint32_t) * (dst_buf + (_x + 1 - dx)),
			(uint32_t)alpha / 8);
		*(src_buf + _x + 1) = \
			fast_blender_alpha((uint32_t) * (src_buf + _x+1),
			(uint32_t) * (dst_buf + (_x - dx)),
			(uint32_t)alpha / 8);
	}
	return;
}

//del_white: 对于全白的，不做混合，透出背景色
void image_rgb565_mix_pic_with_alpha(mix_image_t *img_src, mix_image_t *img_dst, uint32_t alpha, uint8_t del_white)
{
    uint16_t sx, sy, sw, sh;
    uint16_t dx, dy, dw, dh;
	//src 为画布
    sx = img_src->x;
    sy = img_src->y;
    sw = img_src->w;
    sh = img_src->h;
	//dest 为待粘贴上去的图
    dx = img_dst->x;
    dy = img_dst->y;
    dw = img_dst->w;
    dh = img_dst->h;

    if((dx + dw) > (sw + sx) || (dh + dy) > (sh + sy))
    {
        // printk("[image_rgb565_mix_pic_with_alpha]:image invaild\r\n");
        return;
    }
	
    uint16_t *dst_buf;
	uint16_t *src_buf;
	
	if(del_white) {
		if(imgop_type == IMGOP_MCU || imgop_type == IMGOP_MCU_H2V) { // mcu lcd
			for(uint16_t _y = dy; _y < (dy + dh); _y++) {
				dst_buf = img_dst->img_addr + (_y - dy) * dw;
				src_buf = img_src->img_addr + _y * sw;
				_mix_pic_line_mculcd(src_buf, dst_buf, dx, dw, alpha);
			}
			//printk("del_white mcu");
		} else { //rgb lcd
			for(uint16_t _y = dy; _y < (dy + dh); _y++) {
				//读取一行, 这个是快速QIO读取
				dst_buf = img_dst->img_addr + (_y - dy) * dw;
				src_buf = img_src->img_addr + _y * sw;
				_mix_pic_line_rgblcd(src_buf, dst_buf, dx, dw, alpha);
			}
			//printk("del_white rgb");
		}
	} else {
		if(imgop_type == IMGOP_MCU || imgop_type == IMGOP_MCU_H2V) { // mcu lcd
			for(uint16_t _y = dy; _y < (dy + dh); _y++) {
				dst_buf = img_dst->img_addr + (_y - dy) * dw;
				src_buf = img_src->img_addr + _y * sw;
				_mix_pic_line_mculcd_nodelwhite(src_buf, dst_buf, dx, dw, alpha);
			}
			//printk("del_white mcu");
		} else { //rgb lcd
			for(uint16_t _y = dy; _y < (dy + dh); _y++) {
				dst_buf = img_dst->img_addr + (_y - dy) * dw;
				src_buf = img_src->img_addr + _y * sw;
				_mix_pic_line_rgblcd_nodelwhite(src_buf, dst_buf, dx, dw, alpha);
			}
			//printk("del_white rgb");
		}
	}

	//判断放到前面，节省在循环内的耗时
	/*if(del_white) { //with del_white
		for(uint16_t i = dy; i < (dy + dh); i++)
		{
			for(uint16_t j = dx; j < (dx + dw); j++)
			{
				if(del_white)
				{
					if((uint16_t) * (img_dst->img_addr + (i - dy) * dw + (j - dx)) == 0xFFFF)
					{
						continue;
					}
				}
				*(img_src->img_addr + i * sw + j) = \
					fast_blender_alpha((uint32_t) * (img_src->img_addr + i * sw + j),
					(uint32_t) * (img_dst->img_addr + (i - dy) * dw + (j - dx)),
					(uint32_t)alpha / 8);
			}
		}
	} else {	//without del_white
		for(uint16_t i = dy; i < (dy + dh); i++)
		{
			for(uint16_t j = dx; j < (dx + dw); j++)
			{
				*(img_src->img_addr + i * sw + j) = \
					fast_blender_alpha((uint32_t) * (img_src->img_addr + i * sw + j),
					(uint32_t) * (img_dst->img_addr + (i - dy) * dw + (j - dx)),
					(uint32_t)alpha / 8);
			}
		}
	}*/
    return;
}

/* 这个要变成一行一行的进行叠加显示 */
void image_rgb565_mix_flashpic_with_alpha(mix_image_t *img_src, mix_image_t *img_dst, uint32_t alpha, uint8_t del_white)
{
	uint32_t flash_addr = (uint32_t)img_dst->img_addr;
    uint16_t sx, sy, sw, sh;
    uint16_t dx, dy, dw, dh;

    sx = img_src->x;
    sy = img_src->y;
    sw = img_src->w;
    sh = img_src->h;

    dx = img_dst->x;
    dy = img_dst->y;
    dw = img_dst->w;
    dh = img_dst->h;

    if((dx + dw) > (sw + sx) || (dh + dy) > (sh + sy))
    {
        // printk("[image_rgb565_mix_pic_with_alpha]:image invaild\r\n");
        return;
    }

    //申请一行的大小
    uint16_t *dst_buf = es_malloc(dw * 2);
	if(dst_buf == NULL) return;
	uint16_t *src_buf;
	//判断放到循环前，节省时间
	//uint64_t t0 = sysctl_get_time_us();
	if(del_white) {
		if(imgop_type == IMGOP_MCU || imgop_type == IMGOP_MCU_H2V) { // mcu lcd
			for(uint16_t _y = dy; _y < (dy + dh); _y++) {
				//读取一行, 这个是快速QIO读取
				mf_flash.read((uint32_t)(flash_addr + ((_y - dy) * dw * 2)), (uint8_t *)dst_buf, dw * 2);
				src_buf = img_src->img_addr + _y * sw;
				_mix_pic_line_mculcd(src_buf, dst_buf, dx, dw, alpha);
			}
			//printk("del_white mcu");
		} else { //rgb lcd
			for(uint16_t _y = dy; _y < (dy + dh); _y++) {
				//读取一行, 这个是快速QIO读取
				mf_flash.read((uint32_t)(flash_addr + ((_y - dy) * dw * 2)), (uint8_t *)dst_buf, dw * 2);
				src_buf = img_src->img_addr + _y * sw;
				_mix_pic_line_rgblcd(src_buf, dst_buf, dx, dw, alpha);
			}
			//printk("del_white rgb");
		}
	} else { //not del_white
		if(imgop_type == IMGOP_MCU || imgop_type == IMGOP_MCU_H2V) { // mcu lcd
			for(uint16_t _y = dy; _y < (dy + dh); _y++) {
				//读取一行, 这个是快速QIO读取
				mf_flash.read((uint32_t)(flash_addr + ((_y - dy) * dw * 2)), (uint8_t *)dst_buf, dw * 2);
				src_buf = img_src->img_addr + _y * sw;
				_mix_pic_line_mculcd_nodelwhite(src_buf, dst_buf, dx, dw, alpha);
			}
			//printk("no del_white mcu"); //5607us pure, 11275 us with flash read
		} else { //rgb lcd
			for(uint16_t _y = dy; _y < (dy + dh); _y++) {
				//读取一行, 这个是快速QIO读取
				mf_flash.read((uint32_t)(flash_addr + ((_y - dy) * dw * 2)), (uint8_t *)dst_buf, dw * 2);
				src_buf = img_src->img_addr + _y * sw;
				_mix_pic_line_rgblcd_nodelwhite(src_buf, dst_buf, dx, dw, alpha);
			}
			//printk("no del_white rgb"); //6078us pure, 12943 us with flash read
		}
	}
	//uint64_t t1 = sysctl_get_time_us();
	//printk("mix use %ld us\r\n", t1-t0);

    if(dst_buf)
    {
        es_free(dst_buf);
    }

    return;
}


void image_rgb565_paste_img(uint16_t *canvas, uint16_t canvas_w, uint16_t canvas_h,
                            uint16_t *img, uint16_t img_w, uint16_t img_h,
                            int16_t x_oft, int16_t y_oft)
{
    int16_t canvas_x0 = x_oft < 0 ? 0 : x_oft;
    int16_t canvas_y0 = y_oft < 0 ? 0 : y_oft;
    int16_t img_x0 = x_oft < 0 ? -x_oft : 0;
    int16_t img_y0 = y_oft < 0 ? -y_oft : 0;

    int16_t canvas_x1 = x_oft + img_w > canvas_w ? canvas_w : x_oft + img_w;
    int16_t canvas_y1 = y_oft + img_h > canvas_h ? canvas_h : y_oft + img_h;
    //int16_t img_x1 = x_oft + img_w > canvas_w ? canvas_w + x_oft : img_w;
    //int16_t img_y1 = y_oft + img_h > canvas_h ? canvas_h + y_oft : img_h;

    int16_t cpy_w = canvas_x1 - canvas_x0;
    int16_t cpy_h = canvas_y1 - canvas_y0;

    int16_t canvas_y, img_y, dy;

    if(x_oft == 0 && y_oft == 0 && canvas_w == img_w && canvas_h == img_h)
    {
        memcpy(canvas, img, img_w * img_h * 2);
        return;
    }

    for(dy = 0; dy < cpy_h; dy++)
    {
        canvas_y = canvas_y0 + dy;
        img_y = img_y0 + dy;
        memcpy((uint8_t *)canvas + (canvas_y * canvas_w + canvas_x0) * 2,
               (uint8_t *)img + (img_y * img_w + img_x0) * 2,
               cpy_w * 2);
    }
    return;
}

/////////////////////////////////////////////////////////////////////////

/* clang-format off */
#define RGB565_RED          (0xf800)
#define RGB565_GREEN        (0x07e0)
#define RGB565_BLUE         (0x001f)
/* clang-format on */
#if 1
#define _565to888_UNIT()                                                                                  \
    {                                                                                                     \
        pixel = (uint16_t) * (rgb565 + i * img_w + (j * 2 + 1));                                          \
        *(r8g8b8 + (img_w * img_h * 0) + i * img_w + j * 2) = (uint8_t)((pixel & RGB565_RED) >> 8);       \
        *(r8g8b8 + (img_w * img_h * 1) + i * img_w + j * 2) = (uint8_t)((pixel & RGB565_GREEN) >> 3);     \
        *(r8g8b8 + (img_w * img_h * 2) + i * img_w + j * 2) = (uint8_t)((pixel & RGB565_BLUE) << 3);      \
        pixel = (uint16_t) * (rgb565 + i * img_w + (j * 2));                                              \
        *(r8g8b8 + (img_w * img_h * 0) + i * img_w + j * 2 + 1) = (uint8_t)((pixel & RGB565_RED) >> 8);   \
        *(r8g8b8 + (img_w * img_h * 1) + i * img_w + j * 2 + 1) = (uint8_t)((pixel & RGB565_GREEN) >> 3); \
        *(r8g8b8 + (img_w * img_h * 2) + i * img_w + j * 2 + 1) = (uint8_t)((pixel & RGB565_BLUE) << 3);  \
        j++;                                                                                              \
    }
#endif

void image_rgb565_to_r8g8b8(uint16_t *rgb565, uint8_t *r8g8b8, uint16_t img_w, uint16_t img_h)
{
    if((((uint32_t)rgb565) & 0x80000000) == 0)
    {
        rgb565 = (uint16_t*)(((uint32_t)rgb565) + 0x40000000);
    }
    if((((uint32_t)r8g8b8) & 0x80000000) == 0)
    {
        r8g8b8 += 0x40000000;
    }
    uint16_t pixel = 0;
    for(uint16_t i = 0; i < img_h; i++)
    {
        for(uint16_t j = 0; j < img_w / 2;)
        {
            _565to888_UNIT();
            _565to888_UNIT();
            _565to888_UNIT();
            _565to888_UNIT();
        }
    }
}

void image_r8g8b8_to_rgb565(uint16_t *rgb565, uint8_t *r8g8b8, uint16_t img_w, uint16_t img_h)
{
    uint8_t r, g, b;

    for(uint16_t i = 0; i < img_h; i++)
    {
        for(uint16_t j = 0; j < (img_w / 2); j++)
        {
            r = *(r8g8b8 + (img_w * img_h * 0) + i * img_w + j * 2);
            g = *(r8g8b8 + (img_w * img_h * 1) + i * img_w + j * 2);
            b = *(r8g8b8 + (img_w * img_h * 2) + i * img_w + j * 2);
            *(rgb565 + i * img_w + (j * 2 + 1)) = (uint16_t)(((uint16_t)(r << 8) & 0xF800) | ((uint16_t)(g << 3) & 0x7E0) | ((uint16_t)(b >> 3)));

            r = *(r8g8b8 + (img_w * img_h * 0) + i * img_w + j * 2 + 1);
            g = *(r8g8b8 + (img_w * img_h * 1) + i * img_w + j * 2 + 1);
            b = *(r8g8b8 + (img_w * img_h * 2) + i * img_w + j * 2 + 1);
            *(rgb565 + i * img_w + (j * 2 + 0)) = (uint16_t)(((uint16_t)(r << 8) & 0xF800) | ((uint16_t)(g << 3) & 0x7E0) | ((uint16_t)(b >> 3)));
        }
    }
}

#define _RGB565_TO_R8(x) (((x)>>11)<<3)
#define _RGB565_TO_G8(x) ((((x)&0x07e0)>>5)<<2)
#define _RGB565_TO_B8(x) (((x)&0x003f)<<3)

#define IMG565_RESIZE_UNIT()                                                                      \
    {                                                                                     \
        x_src = (x + 0.5f) * w_scale - 0.5f;                                              \
        x1 = (uint16_t)x_src;                                                             \
        x2 = x1 + 1;                                                                      \
        y_src = (y + 0.5f) * h_scale - 0.5f;                                              \
        y1 = (uint16_t)y_src;                                                             \
        y2 = y1 + 1;                                                                      \
        if(x2 >= w_src || y2 >= h_src)                                                    \
        {                                                                                 \
			*(dst + x + y * w_dst) = *(src + x1 + y1 * w_dst); \
        } else                                                                            \
        {   \
			r11 = _RGB565_TO_R8(src[x1+y1*w_src]);  r12 = _RGB565_TO_R8(src[x1+y2*w_src]);\
			r21 = _RGB565_TO_R8(src[x2+y1*w_src]);  r22 = _RGB565_TO_R8(src[x2+y2*w_src]);\
			g11 = _RGB565_TO_G8(src[x1+y1*w_src]);  g12 = _RGB565_TO_G8(src[x1+y2*w_src]);\
			g21 = _RGB565_TO_G8(src[x2+y1*w_src]);  g22 = _RGB565_TO_G8(src[x2+y2*w_src]);\
			b11 = _RGB565_TO_B8(src[x1+y1*w_src]);  b12 = _RGB565_TO_B8(src[x1+y2*w_src]);\
			b21 = _RGB565_TO_B8(src[x2+y1*w_src]);  b22 = _RGB565_TO_B8(src[x2+y2*w_src]);\
            temp1 = (x2 - x_src) * r11 + (x_src - x1) * r21; \
            temp2 = (x2 - x_src) * r12 + (x_src - x1) * r22; \
            dst_r = (uint8_t)((y2 - y_src) * temp1 + (y_src - y1) * temp2);             \
            temp1 = (x2 - x_src) * g11 + (x_src - x1) * g21; \
            temp2 = (x2 - x_src) * g12 + (x_src - x1) * g22; \
            dst_g = (uint8_t)((y2 - y_src) * temp1 + (y_src - y1) * temp2);             \
            temp1 = (x2 - x_src) * b11 + (x_src - x1) * b21; \
            temp2 = (x2 - x_src) * b12 + (x_src - x1) * b22; \
            dst_b = (uint8_t)((y2 - y_src) * temp1 + (y_src - y1) * temp2);             \
			*(dst + x + y * w_dst) = (uint16_t)(((uint16_t)(dst_r << 8) & 0xF800) | ((uint16_t)(dst_g << 3) & 0x7E0) | ((uint16_t)(dst_b >> 3))); \
        }                                                                               \
        x++;                                                                            \
    }
	
#define IMG565_RESIZE_UNIT_FAST()                                                      \
    {                                                                                  \
        x_src = (x + 0.5f) * w_scale - 0.5f;                                           \
        x1 = (uint16_t)(x_src>=0?x_src:0);                                                          \
        y_src = (y + 0.5f) * h_scale - 0.5f;                                           \
        y1 = (uint16_t)(y_src>=0?y_src:0);     \
        {   \
			r11 = _RGB565_TO_R8(src[x1+y1*w_src]);  \
			g11 = _RGB565_TO_G8(src[x1+y1*w_src]);  \
			b11 = _RGB565_TO_B8(src[x1+y1*w_src]); \
			*(dst + x + y * w_dst) = (uint16_t)(((uint16_t)(r11 << 8) & 0xF800) | ((uint16_t)(g11 << 3) & 0x7E0) | ((uint16_t)(b11 >> 3))); \
        }                                                                             \
        x++;                                                                          \
    }


void image_resize_565(image_t *image_src, image_t *image_dst)
{
    uint16_t x1, x2, y1, y2;
    float w_scale, h_scale;
    float temp1, temp2;
    float x_src, y_src;
	uint8_t r11,r21,r12,r22; 
	uint8_t g11,g21,g12,g22; 
	uint8_t b11,b21,b12,b22; 
	uint8_t dst_r, dst_g, dst_b;

    uint16_t *src;
	uint16_t* dst;
    uint16_t w_src, h_src, w_dst, h_dst;
	
	if(image_src->pixel != 2 || image_dst->pixel != 2){
		printk("input/out pixel err!\r\n");
		return;
	}

    w_src = image_src->width;
    h_src = image_src->height;
	src = (uint16_t*)image_src->addr;
	if((uint32_t)src&0x40000000) src= (uint16_t*)((uint32_t)src + 0x40000000);
    //g_src = r_src + w_src * h_src;
    //b_src = g_src + w_src * h_src;
    w_dst = image_dst->width;
    h_dst = image_dst->height;
	dst   = (uint16_t*)image_dst->addr;
	if((uint32_t)dst&0x40000000) dst= (uint16_t*)((uint32_t)dst + 0x40000000);

    w_scale = (float)w_src / w_dst;
    h_scale = (float)h_src / h_dst;

    for(uint16_t y = 0; y < h_dst; y++)
    {
        for(uint16_t x = 0; x < w_dst;)
        {
            //IMG565_RESIZE_UNIT();
			IMG565_RESIZE_UNIT_FAST();
        }
    }
	if((uint32_t)dst&0x40000000) memcpy(image_dst->addr, dst, w_dst * h_dst * 2); //sync cache
}


