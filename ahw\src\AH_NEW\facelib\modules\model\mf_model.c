#include "facelib_inc.h"
#include "es_mem.h"
#include "mf_modelin.h"
#include "mf_mdl_fr.h"
#include "mf_mdl_utils.h"
#include "mf_mdl_enc.h"

#pragma GCC diagnostic ignored "-Wunused-but-set-variable"

/*****************************************************************************/
// Macro definitions
/*****************************************************************************/
#if 1
#define DEBUG_LINE()
#define DBG_HEAP()
#else
#define DEBUG_LINE() do{ printk("[mf_flow_vis2vis] L%d\r\n", __LINE__); } while(0)
#define DBG_HEAP() do{ printk("[mf_flow_vis2vis] L%d :heap:%ld KB\r\n", __LINE__, get_free_heap_size() / 1024); } while(0)
#endif
#define _D() //printk("!%d", __LINE__)
/*****************************************************************************/
// Function definitions
/*****************************************************************************/



/*****************************************************************************/
// Private Var 局部变量
/*****************************************************************************/
#ifndef FLASH_LAYOUT_V2
#ifndef CONFIG_PROJ_MODEL_TEST
#if 1 /* CONFIG_LIVING_MODEL_EMBEED */
#define INCBIN_STYLE INCBIN_STYLE_SNAKE
#define INCBIN_PREFIX
#include "../model_living/incbin.h"

#if 1 /* CONFIG_LIVING_MODEL_V3 */
	// INCBIN(_living_model, "../model_living/face_blur_xor.bin");
	INCBIN(lo6uR1xoa8Keis, "../model_living/face_0616_xor.bin");
#else
	INCBIN(_eye_model, "../model_living/eye_xor.bin");
	INCBIN(_nose_model, "../model_living/nose_xor.bin");
	INCBIN(_mouth_model, "../model_living/mouth_xor.bin");
#endif /* CONFIG_LIVING_MODEL_V3 */
#endif /* CONFIG_LIVING_MODEL_EMBEED */
#endif /* CONFIG_PROJ_MODEL_TEST */
#endif /* FLASH_LAYOUT_V2 */

//隐藏在内部的模型相关信息
static mf_modelin_t mf_mdl = {
//key
	//.key_in_otp
//fd
	.fd_data_original   = NULL,
	.fd_data            = NULL,
//ld                    
	.ld_data_original   = NULL,
	.ld_data            = NULL,
//fe                    
	.fe_data_original   = NULL,
	.fe_data            = NULL,
//live
	.nose_data_original = NULL,
	.nose_data          = NULL,
	.eye_data_original  = NULL,
	.eye_data           = NULL,
	.mouth_data_original= NULL,
	.mouth_data         = NULL,
};

static volatile uint32_t g_ai_done_flag;
/* FD */
static mf_kpu_model_context_t face_detect_task;
static float anchor_ver[ANCHOR_NUM * 2] = {2.5245, 1.889, 3.94056, 2.9465, 5.3658, 3.99987, 6.92275, 5.155437, 9.01025, 6.718375};
static float anchor_hor[ANCHOR_NUM * 2] = {1.889f, 2.5245f, 2.9465f, 3.94056f, 3.99987f, 5.3658f, 5.155437f, 6.92275f, 6.718375f, 9.01025f};
static region_layer_t detect_rl;
/* LD */
static mf_kpu_task_t key_point_task;
static mf_kpu_model_layer_metadata_t *key_point_meta;
/* FE */
static mf_kpu_model_context_t face_fea_model;

static void ai_done(void *ctx)
{
    g_ai_done_flag = 1;
    return;
}
static int ai_done_0(void *ctx)
{
    g_ai_done_flag = 1;
    return 0;
}


/*****************************************************************************/
// Driver 底层驱动
/*****************************************************************************/

/*****************************************************************************/
// Private Func 局部函数
/*****************************************************************************/
/***************************Model Memory Alloc************************/
#define OLD_SDK 1
#if OLD_SDK
static mf_err_t _mf_mdl_init_fd(uint8_t type)
{
	uint32_t kmodel_size = (mf_brd.cfg.cam.dir == 0) ? DETH_MODEL_SIZE : DETV_MODEL_SIZE;
	mf_mdl.fd_data_original = malloc(kmodel_size+255);
	if(mf_mdl.fd_data_original == NULL) {
		return MF_ERR_MDL_NOMEM;
	}
	mf_mdl.fd_data = (uint8_t *)(((uintptr_t)mf_mdl.fd_data_original+255)&(~255))-0x40000000;
	if(mf_brd.cfg.cam.dir == 0) {
		if(0x01 == type) {
			mf_flash.read(DETH_MODEL_ADDRESS, _MODULE_ADDR(mf_mdl.fd_data), DETH_MODEL_SIZE);
		} else if(0x03 == type) {
			mf_flash.read(DETHV3_MODEL_ADDRESS, _MODULE_ADDR(mf_mdl.fd_data), DETH_MODEL_SIZE);
		}
	} else {
		mf_flash.read(DETV_MODEL_ADDRESS, _MODULE_ADDR(mf_mdl.fd_data), DETV_MODEL_SIZE);
	}
	//dec
	enc_kmodel((uint64_t*)_MODULE_ADDR(mf_mdl.fd_data), (4 * 1024), 1);
	memcpy(mf_mdl.fd_data+0x40000000, mf_mdl.fd_data, kmodel_size);
	
    const mf_kpu_kmodel_header_t *header = (const mf_kpu_kmodel_header_t *)mf_mdl.fd_data;
    if(header->version != 3 || header->arch != 0) {
		DEBUG_LINE();
		// while(1){};
		free(mf_mdl.fd_data_original);
		return MF_ERR_MDL;
	}
	
	return MF_ERR_NONE;
}
static void _mf_mdl_deinit_fd(void)
{
	free(mf_mdl.fd_data_original);
	return;
}
static mf_err_t _mf_mdl_init_ld(void)
{
	mf_mdl.ld_data_original = malloc(KP_MODEL_SIZE+255);
	if(mf_mdl.ld_data_original == NULL) {
		return MF_ERR_MDL_NOMEM;
	}
	
	mf_mdl.ld_data = (uint8_t *)(((uintptr_t)mf_mdl.ld_data_original+255)&(~255))-0x40000000;
	mf_flash.read(KP_MODEL_ADDRESS, _MODULE_ADDR(mf_mdl.ld_data), KP_MODEL_SIZE);
	//dec
	enc_kmodel((uint64_t*)_MODULE_ADDR(mf_mdl.ld_data), (4 * 1024), 1);
	memcpy(mf_mdl.ld_data+0x40000000, mf_mdl.ld_data, KP_MODEL_SIZE);
	
    const mf_kpu_kmodel_header_t *header = (const mf_kpu_kmodel_header_t *)mf_mdl.ld_data;
    if(header->version != 1 ) {
		//printk("%x, %x\r\n", header->version, header->arch);
		DEBUG_LINE();
		// while(1){};
		free(mf_mdl.ld_data_original);
		return MF_ERR_MDL;
	}
	
	return MF_ERR_NONE;
}
static void _mf_mdl_deinit_ld(void)
{
	free(mf_mdl.ld_data_original);
	return;
}
static mf_err_t _mf_mdl_init_fe(void)
{
	mf_mdl.fe_data_original = malloc(FEATURE_MODEL_SIZE+255);
	if(mf_mdl.fe_data_original == NULL) {
		return MF_ERR_MDL_NOMEM;
	}
	//uint64_t t0 = sysctl_get_time_us();
	mf_mdl.fe_data = (uint8_t *)(((uintptr_t)mf_mdl.fe_data_original+255)&(~255))-0x40000000;
	mf_flash.read(FEATURE_MODEL_ADDRESS, _MODULE_ADDR(mf_mdl.fe_data), FEATURE_MODEL_SIZE);
	//uint64_t t1 = sysctl_get_time_us();
	//printk("fe load %ld us\r\n", t1-t0);  //119ms@62M; 91ms@82.3M/70M; 63ms@99M(出错)
	//dec
	uint32_t enc_offset;
	enc_kmodel((uint64_t*)_MODULE_ADDR(mf_mdl.fe_data), (4 * 1024), 1);
    enc_offset = 3300 * 1024;
    enc_kmodel((uint64_t*)(mf_mdl.fe_data + enc_offset), (FEATURE_MODEL_SIZE - enc_offset), 1);
	memcpy(mf_mdl.fe_data+0x40000000, mf_mdl.fe_data, FEATURE_MODEL_SIZE);

    const mf_kpu_kmodel_header_t *header = (const mf_kpu_kmodel_header_t *)mf_mdl.fe_data;
    if(header->version != 3 || header->arch != 0) {
		DEBUG_LINE();
		// while(1){};
		free(mf_mdl.fe_data_original);
		return MF_ERR_MDL;
	}
	
	return MF_ERR_NONE;
}

/* 加载小一些的模型 */
static mf_err_t _mf_mdl_init_fe_v2(void)
{
	mf_mdl.fe_data_original = malloc(FEATURE_MODEL_V2_SIZE + 255);

	if(mf_mdl.fe_data_original == NULL) {
		return MF_ERR_MDL_NOMEM;
	}

	mf_mdl.fe_data = (uint8_t *)(((uintptr_t)mf_mdl.fe_data_original+255)&(~255))-0x40000000;
	mf_flash.read(FEATURE_MODEL_V2_ADDRESS, _MODULE_ADDR(mf_mdl.fe_data), FEATURE_MODEL_V2_SIZE);

	enc_kmodel((uint64_t*)_MODULE_ADDR(mf_mdl.fe_data), (4 * 1024), 1);

    uint32_t enc_offset = 2000 * 1024;

    enc_kmodel((uint64_t*)(mf_mdl.fe_data + enc_offset), (FEATURE_MODEL_V2_SIZE - enc_offset), 1);

	memcpy(mf_mdl.fe_data + 0x40000000, mf_mdl.fe_data, FEATURE_MODEL_V2_SIZE);

    const mf_kpu_kmodel_header_t *header = (const mf_kpu_kmodel_header_t *)mf_mdl.fe_data;
    if(header->version != 3 || header->arch != 0) {
		DEBUG_LINE();
		free(mf_mdl.fe_data_original);
		return MF_ERR_MDL;
	}
	
	return MF_ERR_NONE;
}

#define fea_v3_fc_addr 				aequae3aeZ1eiCho
#define fea_v3_conv_addr			teeyiquieWee0bei

static uint8_t *fea_v3_fc_addr = NULL;
static uint8_t *fea_v3_conv_addr = NULL;

static mf_err_t _mf_mdl_init_fe_v3(void)
{
	const uint32_t model_total_size = FEATURE_MODEL_V3_SIZE + \
										FEATURE_MODEL_V3_CONV_SIZE + \
										FEATURE_MODEL_V3_FC_SIZE + 1024; /* 多申请一点 */

	mf_mdl.fe_data_original = malloc(model_total_size + 255);
	if(mf_mdl.fe_data_original == NULL) { return MF_ERR_MDL_NOMEM; }

	mf_mdl.fe_data = (uint8_t *)(((uintptr_t)mf_mdl.fe_data_original + 255) & (~255)) - 0x40000000;
	mf_flash.read(FEATURE_MODEL_V3_ADDRESS, _MODULE_ADDR(mf_mdl.fe_data), FEATURE_MODEL_V3_SIZE);

	// for(int i = 0; i < FEATURE_MODEL_V3_SIZE; i++) { mf_mdl.fe_data[i] ^= 0x58; }

	enc_kmodel((uint64_t*)_MODULE_ADDR(mf_mdl.fe_data), (4 * 1024), 1);

	memcpy(mf_mdl.fe_data + 0x40000000, mf_mdl.fe_data, FEATURE_MODEL_V3_SIZE);

    const mf_kpu_kmodel_header_t *header = (const mf_kpu_kmodel_header_t *)mf_mdl.fe_data;
    if(header->version != 3 || header->arch != 0) { 
		free(mf_mdl.fe_data_original);
		return MF_ERR_MDL;
	}

	fea_v3_conv_addr = mf_mdl.fe_data + 0x40000000 + FEATURE_MODEL_V3_SIZE;
	fea_v3_fc_addr = fea_v3_conv_addr + FEATURE_MODEL_V3_CONV_SIZE;

	mf_flash.read(FEATURE_MODEL_V3_CONV_ADDR, fea_v3_conv_addr, FEATURE_MODEL_V3_CONV_SIZE);
	mf_flash.read(FEATURE_MODEL_V3_FC_ADDR, fea_v3_fc_addr, FEATURE_MODEL_V3_FC_SIZE);

	for(int i = 0; i < FEATURE_MODEL_V3_CONV_SIZE; i++) { fea_v3_conv_addr[i] ^= 0x58; }
	for(int i = 0; i < FEATURE_MODEL_V3_FC_SIZE; i++) { fea_v3_fc_addr[i] ^= 0x58; }

	return MF_ERR_NONE;
}

static mf_err_t _mf_mdl_init_fe_v4(void)
{
	mf_mdl.fe_data_original = malloc(FEATURE_MODEL_V4_SIZE + 255);
	if(NULL == mf_mdl.fe_data_original) {
		return MF_ERR_MDL_NOMEM;
	}
	//uint64_t t0 = sysctl_get_time_us();
	mf_mdl.fe_data = (uint8_t *)(((uintptr_t)mf_mdl.fe_data_original + 255) & (~255)) - 0x40000000;
	mf_flash.read(FEATURE_MODEL_V4_ADDRESS, _MODULE_ADDR(mf_mdl.fe_data), FEATURE_MODEL_V4_SIZE);
	//uint64_t t1 = sysctl_get_time_us();
	//printk("fe load %ld us\r\n", t1-t0);  //119ms@62M; 91ms@82.3M/70M; 63ms@99M(出错)
	//dec
	uint32_t enc_offset;
	enc_kmodel((uint64_t*)_MODULE_ADDR(mf_mdl.fe_data), (4 * 1024), 1);
    enc_offset = FEATURE_MODEL_V4_SIZE - 8192;
    enc_kmodel((uint64_t*)(mf_mdl.fe_data + enc_offset), (FEATURE_MODEL_V4_SIZE - enc_offset), 1);
	memcpy(mf_mdl.fe_data+0x40000000, mf_mdl.fe_data, FEATURE_MODEL_V4_SIZE);

    const mf_kpu_kmodel_header_t *header = (const mf_kpu_kmodel_header_t *)mf_mdl.fe_data;
    if(header->version != 3 || header->arch != 0) {
		DEBUG_LINE();
		// while(1){};
		free(mf_mdl.fe_data_original);
		return MF_ERR_MDL;
	}

	return MF_ERR_NONE;
}

static mf_err_t _mf_mdl_init_fe_v5(void)
{
	const uint32_t model_total_size = FEATURE_MODEL_V5_SIZE + \
										FEATURE_MODEL_V5_CONV_SIZE + \
										FEATURE_MODEL_V5_FC_SIZE + 1024; /* 多申请一点 */

	mf_mdl.fe_data_original = malloc(model_total_size + 255);
	if(mf_mdl.fe_data_original == NULL) { return MF_ERR_MDL_NOMEM; }

	mf_mdl.fe_data = (uint8_t *)(((uintptr_t)mf_mdl.fe_data_original + 255) & (~255)) - 0x40000000;
	mf_flash.read(FEATURE_MODEL_V5_ADDRESS, _MODULE_ADDR(mf_mdl.fe_data), FEATURE_MODEL_V5_SIZE);

	enc_kmodel((uint64_t*)_MODULE_ADDR(mf_mdl.fe_data), (4 * 1024), 1);

	memcpy(mf_mdl.fe_data + 0x40000000, mf_mdl.fe_data, FEATURE_MODEL_V5_SIZE);

    const mf_kpu_kmodel_header_t *header = (const mf_kpu_kmodel_header_t *)mf_mdl.fe_data;
    if(header->version != 3 || header->arch != 0) { 
		free(mf_mdl.fe_data_original);
		return MF_ERR_MDL;
	}

	fea_v3_conv_addr = mf_mdl.fe_data + 0x40000000 + FEATURE_MODEL_V5_SIZE;
	fea_v3_fc_addr = fea_v3_conv_addr + FEATURE_MODEL_V5_CONV_SIZE;

	mf_flash.read(FEATURE_MODEL_V5_CONV_ADDR, fea_v3_conv_addr, FEATURE_MODEL_V5_CONV_SIZE);
	mf_flash.read(FEATURE_MODEL_V5_FC_ADDR, fea_v3_fc_addr, FEATURE_MODEL_V5_FC_SIZE);

	for(int i = 0; i < FEATURE_MODEL_V5_CONV_SIZE; i++) { fea_v3_conv_addr[i] ^= 0x58; }
	for(int i = 0; i < FEATURE_MODEL_V5_FC_SIZE; i++) { fea_v3_fc_addr[i] ^= 0x58; }

	return MF_ERR_NONE;
}

static void _mf_mdl_deinit_fe(void)
{
	free(mf_mdl.fe_data_original);
	return;
}
static mf_err_t _mf_mdl_init_live(void)
{
#if 0 /* !CONFIG_LIVING_MODEL_EMBEED */
	//nose
	mf_mdl.nose_data_original = malloc(NOSE_MODEL_SIZE+255);
	if(mf_mdl.nose_data_original == NULL) {
		return MF_ERR_MDL_NOMEM;
	}
	mf_mdl.nose_data = (uint8_t *)(((uintptr_t)mf_mdl.nose_data_original+255)&(~255))-0x40000000;
	//eye
	mf_mdl.eye_data_original = malloc(EYE_MODEL_SIZE+255);
	if(mf_mdl.eye_data_original == NULL) {
		free(mf_mdl.nose_data_original);
		return MF_ERR_MDL_NOMEM;
	}
	mf_mdl.eye_data = (uint8_t *)(((uintptr_t)mf_mdl.eye_data_original+255)&(~255))-0x40000000;
	//mouth
	mf_mdl.mouth_data_original = malloc(MOUTH_MODEL_SIZE+255);
	if(mf_mdl.mouth_data_original == NULL) {
		free(mf_mdl.nose_data_original);
		free(mf_mdl.eye_data_original);
		return MF_ERR_MDL_NOMEM;
	}
	mf_mdl.mouth_data = (uint8_t *)(((uintptr_t)mf_mdl.mouth_data_original+255)&(~255))-0x40000000;
	
	mf_flash.read(NOSE_MODEL_ADDRESS, _MODULE_ADDR(mf_mdl.nose_data), NOSE_MODEL_SIZE);
	mf_flash.read(EYE_MODEL_ADDRESS, _MODULE_ADDR(mf_mdl.eye_data), EYE_MODEL_SIZE);
	mf_flash.read(MOUTH_MODEL_ADDRESS, _MODULE_ADDR(mf_mdl.mouth_data), MOUTH_MODEL_SIZE);
	enc_kmodel((uint64_t*)_MODULE_ADDR(mf_mdl.nose_data), (4 * 1024), 1);
	memcpy(mf_mdl.nose_data + 0x40000000, mf_mdl.nose_data, NOSE_MODEL_SIZE);
	enc_kmodel((uint64_t*)_MODULE_ADDR(mf_mdl.eye_data), (4 * 1024), 1);
	memcpy(mf_mdl.eye_data + 0x40000000, mf_mdl.eye_data, EYE_MODEL_SIZE);
	enc_kmodel((uint64_t*)_MODULE_ADDR(mf_mdl.mouth_data), (4 * 1024), 1);
	memcpy(mf_mdl.mouth_data + 0x40000000, mf_mdl.mouth_data, MOUTH_MODEL_SIZE);
#else
#ifndef FLASH_LAYOUT_V2
#ifndef CONFIG_PROJ_MODEL_TEST
#if 1 /* CONFIG_LIVING_MODEL_V3 */
	mf_mdl.nose_data  = lo6uR1xoa8Keisdw;

	static volatile uint8_t seed = (0x78 ^ 0xAA);

    for(uint32_t i = 0; i < lo6uR1xoa8Keissw; i++)
    {
        mf_mdl.nose_data[i] ^= (seed ^ 0xAA);
    }

    uint8_t *ptr = NULL;
    ptr = (uint8_t *)((uint32_t)lo6uR1xoa8Keisdw - 0x40000000);
    memcpy(ptr, lo6uR1xoa8Keisdw, lo6uR1xoa8Keissw);

	mf_mdl.nose_data  -= 0x40000000;

	mf_mdl.eye_data   = NULL;
	mf_mdl.mouth_data = NULL;
#else
	mf_mdl.nose_data  = _nose_model_data;
	mf_mdl.eye_data   = _eye_model_data;
	mf_mdl.mouth_data = _mouth_model_data;
    for(uint32_t i = 0; i < _eye_model_size; i++)
    {
        mf_mdl.eye_data[i] ^= 0x78;
    }

    for(uint32_t i = 0; i < _nose_model_size; i++)
    {
        mf_mdl.nose_data[i] ^= 0x78;
    }

    for(uint32_t i = 0; i < _mouth_model_size; i++)
    {
        mf_mdl.mouth_data[i] ^= 0x78;
    }
	//sync
    uint8_t *ptr = NULL;
    ptr = (uint8_t *)((uint32_t)_eye_model_data - 0x40000000);
    memcpy(ptr, _eye_model_data, _eye_model_size);
    // printk("%08X %08X\r\n", (uint32_t)ptr, (uint32_t)new_eye_model_data);
    ptr = (uint8_t *)((uint32_t)_nose_model_data - 0x40000000);
    memcpy(ptr, _nose_model_data, _nose_model_size);
    // printk("%08X %08X\r\n", (uint32_t)ptr, (uint32_t)new_nose_model_data);
    ptr = (uint8_t *)((uint32_t)_mouth_model_data - 0x40000000);
    memcpy(ptr, _mouth_model_data, _mouth_model_size);
	
	mf_mdl.nose_data  -= 0x40000000;
	mf_mdl.eye_data   -= 0x40000000;
	mf_mdl.mouth_data -= 0x40000000;
#endif 
#endif
#endif /* CONFIG_PROJ_MODEL_TEST */
#endif /* FLASH_LAYOUT_V2 */
	return MF_ERR_NONE;
}
static void _mf_mdl_deinit_live(void)
{
#if 0 /* !CONFIG_LIVING_MODEL_EMBEED */
	free(mf_mdl.nose_data_original);
	free(mf_mdl.eye_data_original);
	free(mf_mdl.mouth_data_original);
#else
	mf_mdl.nose_data  = NULL;
	mf_mdl.eye_data   = NULL;
	mf_mdl.mouth_data = NULL;
#endif
	return;
}
///////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////
#else  //New sdk iomem
static mf_err_t _mf_mdl_init_fd(void)
{
	uint32_t kmodel_size = (mf_brd.cfg.cam.dir == 0) ? DETH_MODEL_SIZE : DETV_MODEL_SIZE;
	mf_mdl.fd_data = es_malloc(kmodel_size);
	if(mf_brd.cfg.cam.dir == 0) {
		mf_flash.read(DETH_MODEL_ADDRESS, _MODULE_ADDR(mf_mdl.fd_data), DET_MODELH_SIZE);
	} else {
		mf_flash.read(DETH_MODEL_ADDRESS, _MODULE_ADDR(mf_mdl.fd_data), DET_MODELV_SIZE);
	}
	//dec
	enc_kmodel((uint64_t*)_MODULE_ADDR(mf_mdl.fd_data), (4 * 1024), 1);
	
    const mf_kpu_kmodel_header_t *header = (const mf_kpu_kmodel_header_t *)mf_mdl.fd_data;
    if(header->version != 3 || header->arch != 0) {
		DEBUG_LINE();
		// while(1){};
		return MF_ERR_MDL;
	}
	return MF_ERR_NONE;
}
static void _mf_mdl_deinit_fd(void)
{
	es_free(mf_mdl.fd_data);
	return;
}
static mf_err_t _mf_mdl_init_ld(void)
{
	mf_mdl.fd_data = es_malloc(KP_MODEL_SIZE);
	mf_flash.read(KP_MODEL_ADDRESS, _MODULE_ADDR(mf_mdl.ld_data), KP_MODEL_SIZE);
	//dec
	enc_kmodel((uint64_t*)_MODULE_ADDR(mf_mdl.ld_data), (4 * 1024), 1);
	
    const mf_kpu_kmodel_header_t *header = (const mf_kpu_kmodel_header_t *)mf_mdl.ld_data;
    if(header->version != 1) {
		DEBUG_LINE();
		// while(1){};
		return MF_ERR_MDL;
	}
	return MF_ERR_NONE;
}
static void _mf_mdl_deinit_ld(void)
{
	es_free(mf_mdl.ld_data);
	return;
}
static mf_err_t _mf_mdl_init_fe(void)
{
	mf_mdl.fd_data = es_malloc(FEATURE_MODEL_SIZE);
	mf_flash.read(FEATURE_MODEL_ADDRESS, _MODULE_ADDR(mf_mdl.fe_data), FEATURE_MODEL_SIZE);
	//dec
	uint32_t enc_offset;
	enc_kmodel((uint64_t*)_MODULE_ADDR(mf_mdl.fe_data), (4 * 1024), 1);
    enc_offset = 3300 * 1024;
    enc_kmodel((uint64_t*)_MODULE_ADDR(mf_mdl.fe_data) + enc_offset, (FEATURE_MODEL_SIZE - enc_offset), 1);
	
    const mf_kpu_kmodel_header_t *header = (const mf_kpu_kmodel_header_t *)mf_mdl.fe_data;
    if(header->version != 3 || header->arch != 0) {
		DEBUG_LINE();
		// while(1){};
		return MF_ERR_MDL;
	}
	
	return MF_ERR_NONE;
}
static void _mf_mdl_deinit_fe(void)
{
	es_free(mf_mdl.fe_data);
	return;
}
static mf_err_t _mf_mdl_init_live(void)
{
#if 0 /* !CONFIG_LIVING_MODEL_EMBEED */
	//nose
	mf_mdl.fd_data = es_malloc(NOSE_MODEL_SIZE);
	//eye
	mf_mdl.fd_data = es_malloc(EYE_MODEL_SIZE);
	//mouth
	mf_mdl.fd_data = es_malloc(MOUTH_MODEL_SIZE);
	
	mf_flash.read(NOSE_MODEL_ADDRESS, _MODULE_ADDR(mf_mdl.nose_data), NOSE_MODEL_SIZE);
	mf_flash.read(EYE_MODEL_ADDRESS, _MODULE_ADDR(mf_mdl.eye_data), EYE_MODEL_SIZE);
	mf_flash.read(MOUTH_MODEL_ADDRESS, _MODULE_ADDR(mf_mdl.mouth_data), MOUTH_MODEL_SIZE);
#else
	mf_mdl.nose_data  = _eye_model_data;
	mf_mdl.eye_data   = _nose_model_data;
	mf_mdl.mouth_data = _mouth_model_data;
    for(uint32_t i = 0; i < _eye_model_size; i++)
    {
        mf_mdl.eye_data[i] ^= 0x78;
    }

    for(uint32_t i = 0; i < _nose_model_size; i++)
    {
        mf_mdl.nose_data[i] ^= 0x78;
    }

    for(uint32_t i = 0; i < _mouth_model_size; i++)
    {
        mf_mdl.mouth_data[i] ^= 0x78;
    }
	//sync
    uint8_t *ptr = NULL;
    ptr = (uint8_t *)((uint32_t)_eye_model_data - 0x40000000);
    memcpy(ptr, _eye_model_data, _eye_model_size);
    // printk("%08X %08X\r\n", (uint32_t)ptr, (uint32_t)new_eye_model_data);
    ptr = (uint8_t *)((uint32_t)_nose_model_data - 0x40000000);
    memcpy(ptr, _nose_model_data, _nose_model_size);
    // printk("%08X %08X\r\n", (uint32_t)ptr, (uint32_t)new_nose_model_data);
    ptr = (uint8_t *)((uint32_t)_mouth_model_data - 0x40000000);
    memcpy(ptr, _mouth_model_data, _mouth_model_size);

	mf_mdl.nose_data  -= 0x40000000;
	mf_mdl.eye_data   -= 0x40000000;
	mf_mdl.mouth_data -= 0x40000000;
#endif
	return MF_ERR_NONE;
}
static void _mf_mdl_deinit_live(void)
{
#if 0 /* !CONFIG_LIVING_MODEL_EMBEED */
	es_free(mf_mdl.nose_data);
	es_free(mf_mdl.eye_data);
	es_free(mf_mdl.mouth_data);
#else
	mf_mdl.nose_data  = NULL;
	mf_mdl.eye_data   = NULL;
	mf_mdl.mouth_data = NULL;
#endif
	return;
}

#endif

/*************************** Model Para Config ************************/
static mf_err_t _mf_model_save_cfg(void)
{
	//保存配置
	mf_err_t err = MF_ERR_NONE;
	if(((mf_model.model_flag)&MF_MDL_FACEDETECT) ) {
		// mf_brd.cfg.model.face_minw = mf_model.face_minw;
		// mf_brd.cfg.model.face_minh = mf_model.face_minh;

		if (mf_brd.cfg.cam.dir == 1) {
			mf_model.face_minw = mf_brd.cfg.model.face_minh;
			mf_model.face_minh = mf_brd.cfg.model.face_minw;
		} else {
			mf_model.face_minw = mf_brd.cfg.model.face_minw;
			mf_model.face_minh = mf_brd.cfg.model.face_minh;
		}

		mf_brd.cfg.model.fd_gate = mf_model.fd_gate;
	}
	if(((mf_model.model_flag)&MF_MDL_FACEALIGN) ) {
		//nothing
	}

	if((mf_model.model_flag)&MF_MDL_FACEFTR) {
		mf_brd.cfg.model.ftr_len = mf_model.ftr_len;
		mf_brd.cfg.model.fe_gate = mf_model.fe_gate;
		mf_brd.cfg.model.fe_gate_ir = mf_model.fe_gate_ir;
	}   
	if(((mf_model.model_flag)&MF_MDL_LIVING) ) {
		mf_brd.cfg.model.live_gate = mf_model.live_gate;
	}
	//保存到flash
	err = mf_brd.cfg_save();
	return err;
}

static mf_err_t _mf_model_load_cfg(void)
{
	//加载配置
	mf_err_t err = MF_ERR_NONE;
	if(((mf_model.model_flag)&MF_MDL_FACEDETECT) ) {
		if (mf_brd.cfg.cam.dir == 1) {
			mf_model.face_minw = mf_brd.cfg.model.face_minh;
			mf_model.face_minh = mf_brd.cfg.model.face_minw;
		} else {
			mf_model.face_minw = mf_brd.cfg.model.face_minw;
			mf_model.face_minh = mf_brd.cfg.model.face_minh;
		}
		mf_model.fd_gate = mf_brd.cfg.model.fd_gate;
	}
	if(((mf_model.model_flag)&MF_MDL_FACEALIGN) ) {
		//nothing
	}

	if((mf_model.model_flag)&MF_MDL_FACEFTR) {
		mf_model.ftr_len = mf_brd.cfg.model.ftr_len;
		mf_model.fe_gate = mf_brd.cfg.model.fe_gate;
		if(0.0f != mf_brd.cfg.model.fe_gate_ir) { /* 为0使用默认☞ */
			mf_model.fe_gate_ir = mf_brd.cfg.model.fe_gate_ir;
		}
	}
	if(((mf_model.model_flag)&MF_MDL_LIVING) ) {
		mf_model.live_gate = mf_brd.cfg.model.live_gate;
	}
	return err;
}

static void _mf_model_cfg_fd(uint16_t minw, uint16_t minh, float threshold)
{
	if(((mf_model.model_flag)&MF_MDL_FACEDETECT) ) {
		mf_model.face_minw = minw;
		mf_model.face_minh = minh;
		mf_model.fd_gate = threshold;
	}
	return;
}

static void _mf_model_cfg_fe(uint16_t ftr_len, float threshold, float threshold_ir)
{
	if((mf_model.model_flag)&MF_MDL_FACEFTR) {
		if(ftr_len != 0)   mf_model.ftr_len = ftr_len;
		if(threshold != 0) mf_model.fe_gate = threshold;
		if(0 != threshold_ir) { mf_model.fe_gate_ir = threshold_ir; }
	}
	return;
}

static void _mf_model_cfg_live(float threshold)
{
	if(((mf_model.model_flag)&MF_MDL_LIVING) ) {
		mf_model.live_gate = threshold;
	}
	return;
}

/*************************** Model Method ************************/

static mf_err_t _mf_model_face_detect(image_t* kpu_img, face_obj_info_t* info)
{
	int ret;
	/******************* Model Init *********************/
	DEBUG_LINE();
	//mdl_print_ppm(kpu_img, 8);
	ret = mf_kpu_load_kmodel(&face_detect_task, _MODULE_ADDR(mf_mdl.fd_data));
	if(ret != 0)
	{	DEBUG_LINE();
		return MF_ERR_MDL;
	}
	DEBUG_LINE();
	detect_rl.anchor_number = ANCHOR_NUM;
    detect_rl.anchor = (mf_cam.dir == 1) ? anchor_ver : anchor_hor;
    detect_rl.threshold = (float)(mf_model.fd_gate / 100.0f);
    detect_rl.nms_value = FACE_DETECT_NMS;
	region_layer_init(&detect_rl, 20, 15, 30, 320, 240);
	DEBUG_LINE();
	/******************* Model Run *********************/
    g_ai_done_flag = 0; //人脸检测20ms
	
    mf_kpu_run_kmodel(&face_detect_task, kpu_img->addr, FACE_DMAC_CHANNEL, ai_done, NULL);
    while(!g_ai_done_flag){};DEBUG_LINE();
    float *output;
    size_t output_size;
    mf_kpu_get_output(&face_detect_task, 0, (uint8_t **)&output, &output_size);
    detect_rl.input = output;
    DEBUG_LINE();
    region_layer_run(&detect_rl, info);  //11ms	region处理
    /* 这里直接对矩形进行旋转 */
	if(mf_cam.dir == 1) {
		face_obj_info_roate_right_90(info, 320, 240);
    //face_obj_info_roate_left_90(info, 320, 240);
    
	}
	//face_obj_info_print(info);
	region_layer_deinit(&detect_rl);   DEBUG_LINE();
	mf_kpu_model_free(&face_detect_task); DEBUG_LINE();
	return MF_ERR_NONE;
}

static mf_err_t _mf_model_face_align(image_t* kpu_img, face_obj_t* obj, image_t* simg)
{
	int ret;
	//mdl_print_ppm(kpu_img, 4);
	/************ init key point detect model ***************/
    ret = mf_kpu_lib_model_load_from_buffer(&key_point_task, _MODULE_ADDR(mf_mdl.ld_data), &key_point_meta);
    if(ret < 0)
    {	DEBUG_LINE();
        return MF_ERR_MDL;
    }
	DEBUG_LINE();
	/************ Crop Face & Resize to 128 *****************/
	//_crop_face(kpu_img, obj);
	image_t crop_image;
	crop_image.pixel = 3;
	crop_image.width = obj->x2 - obj->x1 + 1;
	crop_image.height = obj->y2 - obj->y1 + 1;DBG_HEAP();
	ret = image_init(&crop_image);DBG_HEAP();  //
	if(ret < 0) {return MF_ERR_MDL;};
	DEBUG_LINE();
	image_t resize_image = {0, 128, 128, 3};DBG_HEAP();
    ret = image_init(&resize_image);DBG_HEAP();  //52kb
	if(ret < 0) {
		image_deinit(&crop_image); 
		return MF_ERR_MDL;
	};
	if(mf_cam.dir == 1) {
		image_crop_roate_right90(kpu_img, &crop_image, obj->x1, obj->y1);
		//image_crop_roate_left_90(kpu_img, &crop_image, obj->x1, obj->y1);
	} else {
		image_crop(kpu_img, &crop_image, obj->x1, obj->y1);
	}
	//mdl_print_ppm(&crop_image, 2);
	DEBUG_LINE();DBG_HEAP();
	image_resize(&crop_image, &resize_image); //crop+resize=19ms
	DEBUG_LINE();DBG_HEAP();
	
	/****************** Keypoint Detect *********************/
    key_point_task.src      = (uint64_t *)resize_image.addr;
    key_point_task.dma_ch   = FACE_DMAC_CHANNEL;
    key_point_task.callback = ai_done_0;
    mf_kpu_lib_task_init(&key_point_task);DBG_HEAP();
	
	g_ai_done_flag = 0;
	mf_kpu_lib_start(&key_point_task);
	while(!g_ai_done_flag){};
	key_point_last_handle(&key_point_task, &(obj->key_point));DBG_HEAP();
	float matrix_src[5][2], matrix_dst[10];
	for(uint32_t point_cnt = 0; point_cnt < 5; point_cnt++)
	{
		obj->key_point.point[point_cnt].x += obj->x1;
		obj->key_point.point[point_cnt].y += obj->y1;
		matrix_src[point_cnt][0] = obj->key_point.point[point_cnt].x;
		matrix_src[point_cnt][1] = obj->key_point.point[point_cnt].y;
	}
	/******************* Affine Image **********************/
	image_umeyama((float *)&matrix_src, (float *)&matrix_dst);
	DEBUG_LINE();	
	if(mf_cam.dir == 1) { //10ms， 自动申请内存，外部只需要一个指针传入
		image_similarity_roate_right90(kpu_img, simg, matrix_dst); 
	//image_similarity_roate_left90(&kpu_img, simg, matrix_dst);
	} else {
		image_similarity(kpu_img, simg, matrix_dst);
	}
	DBG_HEAP();
	//sync cache
	memcpy(simg->addr, simg->addr + 0x40000000, simg->width * simg->height * 3); 
	DEBUG_LINE();
	mf_kpu_lib_task_deinit(&key_point_task);DBG_HEAP();
    image_deinit(&resize_image);DBG_HEAP();
	image_deinit(&crop_image);DBG_HEAP();
	return MF_ERR_NONE;
}

static mf_err_t _mf_model_ftr_cal(image_t* simg, int8_t* ftr)
{
	int ret;
	//Init Model
    ret = mf_kpu_load_kmodel(&face_fea_model, _MODULE_ADDR(mf_mdl.fe_data));
    if(ret != 0)
    {	DEBUG_LINE();
        return MF_ERR_MDL;
    }
    DEBUG_LINE();
	//Calculate Ftr
	g_ai_done_flag = 0;
	mf_kpu_run_kmodel(&face_fea_model, (simg->addr), FACE_DMAC_CHANNEL, ai_done, NULL); 
	while(!g_ai_done_flag){};
	DEBUG_LINE();
	float *features;
	size_t fea_output_size;
	mf_kpu_get_output(&face_fea_model, 0, (uint8_t **)&features, &fea_output_size);
	lib_compress_feature(features, ftr);
	DEBUG_LINE();
	mf_kpu_model_free(&face_fea_model); DEBUG_LINE();
	return MF_ERR_NONE;
}

static void lib_compress_feature_v4(float* feature, int8_t* compress_feature)
{
	const float gate = 1.0f;
    float temp = 0.0;
    for(uint16_t i = 0; i < mf_model.ftr_len; i++) {
        if(feature[i] > gate) {
            temp = gate;
        } else if(feature[i] < -gate) {
            temp = -gate;
        } else {
            temp = feature[i];
        }
        compress_feature[i] = (int8_t)(temp / gate / 2 * 256);
    }
}

static mf_err_t _mf_model_ftr_calc_v4(image_t* simg, int8_t* ftr)
{
	int ret;
	//Init Model
    ret = mf_kpu_load_kmodel(&face_fea_model, _MODULE_ADDR(mf_mdl.fe_data));

	if(ret != 0) {
		DEBUG_LINE();
        return MF_ERR_MDL;
    }
    DEBUG_LINE();
	//Calculate Ftr
	g_ai_done_flag = 0;

	mf_kpu_run_kmodel(&face_fea_model, (simg->addr), FACE_DMAC_CHANNEL, ai_done, NULL); 
	while(!g_ai_done_flag){};

	DEBUG_LINE();
	float *features;
	size_t fea_output_size;

	mf_kpu_get_output(&face_fea_model, 0, (uint8_t **)&features, &fea_output_size);

	lib_compress_feature_v4(features, ftr);
	DEBUG_LINE();

	mf_kpu_model_free(&face_fea_model); DEBUG_LINE();

	return MF_ERR_NONE;
}

static void quant_ftr(float* ftr, int8_t* ftrq, float quant_scale, int len)
{
	float _scale = (quant_scale / 2.0);

    for(int i = 0; i < len; i++) {
        if(ftr[i] >= _scale) { ftrq[i] = 127; }
		else if(ftr[i] <= -_scale) { ftrq[i] = -128; }
		else { ftrq[i] = (int8_t)(ftr[i] / quant_scale * 256); }
    }
}

static mf_err_t _mf_model_ftr_calc_v3(image_t* simg, int8_t* ftrq)
{
	memset(ftrq, 0, mf_model.ftr_len);

    int ret = mf_kpu_load_kmodel(&face_fea_model, _MODULE_ADDR(mf_mdl.fe_data));
    if(ret != 0) { return MF_ERR_MDL; }
	g_ai_done_flag = 0;
	mf_kpu_run_kmodel(&face_fea_model, (simg->addr), FACE_DMAC_CHANNEL, ai_done, NULL); 
	while(!g_ai_done_flag){};

	float *features;
	size_t fea_output_size;
	mf_kpu_get_output(&face_fea_model, 0, (uint8_t **)&features, &fea_output_size);

	float ftr_tmp[512], ftr[192];

	//gdconv计算
	for(int dim = 0; dim < 512; dim++) {
		ftr_tmp[dim] = 0;
		for(int i = 0; i < 64; i++) {
			uint32_t idx = dim * 64 + i;
			ftr_tmp[dim] = ftr_tmp[dim] + ((float*)fea_v3_conv_addr)[idx] * features[idx];
		}
		// ftr_tmp[dim] -= 1.0;
	}

	//FC计算
	for(int dim = 0; dim < 192; dim++) {
		ftr[dim] = 0;
		for(int i = 0; i < 512; i++) {
			ftr[dim] = ftr[dim] + ftr_tmp[i] * ((float*)fea_v3_fc_addr)[dim * 512 + i];
		}
	}

	const float quant_scale = 24.0;	//精度损失<=0.1%
	quant_ftr(ftr, ftrq, quant_scale, 192);

	mf_kpu_model_free(&face_fea_model); DEBUG_LINE();

	return MF_ERR_NONE;
}

static const float fe_v5_fc_bias[196]={\
-0.27805,0.12080,-0.11537,0.11886,-0.16289,0.13624,0.31679,-0.09714,0.30136,-0.26535,0.00300,-0.07595,0.09770,0.03925,0.01046,-0.06757,0.23964,-0.09941,-0.23995,-0.26580,-0.16621,-0.18016,-0.01175,-0.00087,-0.11221,0.18657,-0.09498,0.09216,-0.20749,0.13424,0.15769,0.22503,0.08272,0.06364,0.36942,-0.16096,0.01012,-0.35752,-0.03635,0.23419,0.12091,0.50769,0.21276,-0.02314,0.01842,0.16383,-0.02036,0.13106,0.19871,0.06647,-0.04608,-0.00220,0.20430,0.09183,-0.18803,0.12895,-0.13384,-0.31472,0.17031,-0.03785,0.06412,0.18992,0.12657,-0.20068,0.09495,0.03701,0.05141,0.14827,0.09728,0.18300,-0.23418,0.22630,-0.02117,-0.20413,-0.40437,0.16806,-0.00733,0.05767,-0.20593,0.12618,-0.12428,-0.19423,0.20736,-0.18729,-0.00583,0.11823,-0.12367,-0.14259,-0.04452,-0.09070,-0.06587,0.13176,-0.03574,0.29693,0.00896,-0.17842,0.16657,-0.10922,-0.03884,-0.10063,0.28430,0.01583,-0.01110,-0.00592,-0.32432,-0.05140,-0.30260,0.00696,0.09632,-0.23784,0.04456,0.20423,0.16992,0.09341,-0.03544,-0.09918,-0.16441,-0.10185,-0.03607,0.03325,0.01146,0.17909,-0.04853,0.17844,-0.09179,0.31290,-0.05035,-0.05468,-0.17375,0.05997,0.05765,0.11673,-0.13996,-0.17768,-0.13974,0.01822,-0.19707,0.07605,-0.35496,0.09559,-0.39605,0.31533,0.21158,-0.24939,-0.01697,0.11062,0.04841,-0.01368,-0.17652,-0.22640,-0.12682,-0.10016,-0.15435,-0.21047,0.20433,0.07102,-0.05131,-0.02276,0.04525,-0.25245,-0.06946,0.24106,0.22819,-0.07149,-0.27604,-0.04059,-0.13531,-0.18357,-0.02170,0.00222,0.29903,-0.18354,0.30043,0.11858,-0.08036,0.04184,-0.18011,0.00700,0.09991,0.09804,-0.07349,-0.13903,0.31595,-0.18721,-0.21993,-0.00252,-0.28268,0.34421,-0.17066,0.02539,-0.01569,0.04223,-0.16543,0.14853,-0.00555,0.04862,};

static mf_err_t _mf_model_ftr_calc_v5(image_t* simg, int8_t* ftrq)
{
	memset(ftrq, 0, mf_model.ftr_len);

    int ret = mf_kpu_load_kmodel(&face_fea_model, _MODULE_ADDR(mf_mdl.fe_data));
    if(ret != 0) { return MF_ERR_MDL; }
	g_ai_done_flag = 0;
	mf_kpu_run_kmodel(&face_fea_model, (simg->addr), FACE_DMAC_CHANNEL, ai_done, NULL);
	while(!g_ai_done_flag){};

	float *features;
	size_t fea_output_size;
	mf_kpu_get_output(&face_fea_model, 0, (uint8_t **)&features, &fea_output_size);

	float ftr_tmp[512], ftr[196];

	//gdconv计算
	for(int dim = 0; dim < 512; dim++) {
		ftr_tmp[dim] = 0;
		for(int i = 0; i < 64; i++) {
			uint32_t idx = dim * 64 + i;
			ftr_tmp[dim] = ftr_tmp[dim] + ((float*)fea_v3_conv_addr)[idx] * features[idx];
		}
	}

	//FC计算
	for(int dim = 0; dim < 196; dim++) {
		ftr[dim] = 0;
		for(int i = 0; i < 512; i++) {
			ftr[dim] = ftr[dim] + ftr_tmp[i] * ((float*)fea_v3_fc_addr)[dim * 512 + i];
		}
		ftr[dim] += fe_v5_fc_bias[dim];
	}

	const float quant_scale = 24.0;	//精度损失<=0.1%
	quant_ftr(ftr, ftrq, quant_scale, 196);

	mf_kpu_model_free(&face_fea_model); DEBUG_LINE();

	return MF_ERR_NONE;
}

#if 1 /* 新的计算分数函数 */
static const float cos_tbl[257] = { \
 0.00,  0.20,  0.39,  0.59,  0.79,  0.99,  1.19,  1.39, 
 1.59,  1.79,  1.99,  2.20,  2.40,  2.61,  2.81,  3.02, 
 3.23,  3.44,  3.65,  3.86,  4.07,  4.29,  4.50,  4.71,   
 4.93,  5.15,  5.37,  5.59,  5.81,  6.03,  6.25,  6.47, 
 6.70,  6.92,  7.15,  7.38,  7.61,  7.84,  8.07,  8.31, 
 8.54,  8.78,  9.02,  9.25,  9.50,  9.74,  9.98, 10.23, 
10.47, 10.72, 10.97, 11.22, 11.47, 11.73, 11.98, 12.24, 
12.50, 12.76, 13.02, 13.29, 13.56, 13.83, 14.10, 14.37, 
14.64, 14.92, 15.20, 15.48, 15.77, 16.05, 16.34, 16.63, 
16.93, 17.22, 17.52, 17.83, 18.13, 18.44, 18.75, 19.06, 
19.38, 19.70, 20.03, 20.35, 20.68, 21.02, 21.36, 21.70, 
22.05, 22.40, 22.76, 23.12, 23.48, 23.85, 24.23, 24.61, 
25.00, 25.39, 25.79, 26.20, 26.61, 27.04, 27.47, 27.90, 
28.35, 28.81, 29.27, 29.75, 30.24, 30.74, 31.25, 31.78, 
32.32, 32.88, 33.46, 34.07, 34.69, 35.34, 36.02, 36.74, 
37.50, 38.31, 39.17, 40.12, 41.16, 42.35, 43.75, 45.58, 
50.00, 54.42, 56.25, 57.65, 58.84, 59.88, 60.83, 61.69, 
62.50, 63.26, 63.98, 64.66, 65.31, 65.93, 66.54, 67.12, 
67.68, 68.22, 68.75, 69.26, 69.76, 70.25, 70.73, 71.19, 
71.65, 72.10, 72.53, 72.96, 73.39, 73.80, 74.21, 74.61, 
75.00, 75.39, 75.77, 76.15, 76.52, 76.88, 77.24, 77.60, 
77.95, 78.30, 78.64, 78.98, 79.32, 79.65, 79.97, 80.30, 
80.62, 80.94, 81.25, 81.56, 81.87, 82.17, 82.48, 82.78, 
83.07, 83.37, 83.66, 83.95, 84.23, 84.52, 84.80, 85.08, 
85.36, 85.63, 85.90, 86.17, 86.44, 86.71, 86.98, 87.24, 
87.50, 87.76, 88.02, 88.27, 88.53, 88.78, 89.03, 89.28, 
89.53, 89.77, 90.02, 90.26, 90.50, 90.75, 90.98, 91.22, 
91.46, 91.69, 91.93, 92.16, 92.39, 92.62, 92.85, 93.08, 
93.30, 93.53, 93.75, 93.97, 94.19, 94.41, 94.63, 94.85, 
95.07, 95.29, 95.50, 95.71, 95.93, 96.14, 96.35, 96.56, 
96.77, 96.98, 97.19, 97.39, 97.60, 97.80, 98.01, 98.21, 
98.41, 98.61, 98.81, 99.01, 99.21, 99.41, 99.61, 99.80, 
100.0
};

static float _mf_model_ftr_compare(int8_t* ftr0, int8_t* ftr1)
{
    int64_t sumcorr = 0;
    int64_t sumftr0 = 0;
    int64_t sumftr1 = 0;

    for(uint8_t i = 0; i < mf_model.ftr_len;)
    {
        sumftr0 += ftr0[i] * ftr0[i];
        sumftr1 += ftr1[i] * ftr1[i];
        sumcorr += ftr0[i] * ftr1[i];
        i++;
        sumftr0 += ftr0[i] * ftr0[i];
        sumftr1 += ftr1[i] * ftr1[i];
        sumcorr += ftr0[i] * ftr1[i];
        i++;
        sumftr0 += ftr0[i] * ftr0[i];
        sumftr1 += ftr1[i] * ftr1[i];
        sumcorr += ftr0[i] * ftr1[i];
        i++;
        sumftr0 += ftr0[i] * ftr0[i];
        sumftr1 += ftr1[i] * ftr1[i];
        sumcorr += ftr0[i] * ftr1[i];
        i++;
    }
    int tmp = abs(sumcorr) * sumcorr * 128 / sumftr0 / sumftr1 + 128;
    //printk("%ld, %ld, %ld, tmp=%d\r\n",sumcorr,sumftr0,sumftr1, tmp);
    return cos_tbl[tmp];
}
#else
static float _mf_model_ftr_compare(int8_t* ftr0, int8_t* ftr1)
{
    int64_t sumcorr = 0;
    int64_t sumftr0 = 0;
    int64_t sumftr1 = 0;

    for(uint8_t i = 0; i < mf_model.ftr_len; i++)
    {
        sumftr0 += ftr0[i] * ftr0[i];
        sumftr1 += ftr1[i] * ftr1[i];
        sumcorr += ftr0[i] * ftr1[i];
    }
    return (0.5 + 0.5 * sumcorr / sqrt(sumftr0 * sumftr1)) * 100;
}
#endif

#define DEBUG_SIM_IMG	(0)

#if DEBUG_SIM_IMG
	uint16_t global_sim_image[128 *128];
#else
	uint16_t *global_sim_image = NULL;
#endif

static void img_r8g8b8_togray(image_t *simg, image_t *out_simg, uint16_t *rgb565)
{
	uint16_t w = simg->width;
	uint16_t h = simg->height;

	if((out_simg->width != w) || (out_simg->height != h) || \
		(out_simg->pixel != 1) || (simg->pixel != 3) || \
		(out_simg->addr == NULL)) {
		printk("err in convert\r\n");
		return;
	}

	uint8_t *ptr_r = (uint8_t*)(simg->addr + w * h * 0);
	uint8_t *ptr_g = (uint8_t*)(simg->addr + w * h * 1);
	uint8_t *ptr_b = (uint8_t*)(simg->addr + w * h * 2);

	uint8_t gray = 0, r, g, b;

	// uint64_t t = sysctl_get_time_us();

	for(uint32_t i = 0; i < (w * h); i++) {
		r = (uint8_t)*(ptr_r + i);
		g = (uint8_t)*(ptr_g + i);
		b = (uint8_t)*(ptr_b + i);
		
		/* Gray = (R*38 + G*75 + B*15) >> 7 */
		gray = (uint8_t)((r * 38 + g * 75 + b * 15) >> 7);

		// *(ptr_r + i) = gray;
		out_simg->addr[i] = gray;

#if DEBUG_SIM_IMG
		rgb565[i] = (uint16_t)(((uint16_t)(r << 8) & 0xF800) | ((uint16_t)(g << 3) & 0x7E0) | ((uint16_t)(b >> 3)));
#endif
	}

	// printk("%ld us\r\n", (sysctl_get_time_us() - t));

	return;
}

static inline uint8_t cal_avg(uint8_t* pic, uint8_t w, uint8_t h)
{
    uint16_t pixelCount = w * h;
 	uint32_t sum = 0;
    
	for (int i = 0; i < pixelCount; i++) {
        sum += pic[i];
 	}

	uint8_t avg = sum / pixelCount;
 	
	return avg;
}

#if 1 /* CONFIG_LIVING_MODEL_V3 */
static float _cal_living_item(image_t* simage,uint8_t* mdl_data)
{	
	int kpu_ret = 0xffff;
	float *result, ret = 0.0f;
    size_t result_size;

#if CONFIG_PROJ_KALMAN /* 目前来说,Kalman的需要开启 */
	/* 这里需要判断室内还是室外,如果是室外就不再进行检测,检测最近的10帧,求平均 */
#define PIC_EX_LEN					(8)
#define PIC_EX_OUTDOOR_TH			(130)

	static uint8_t pic_ex_idx = 0, pic_avg_ex[PIC_EX_LEN] = {0};

	uint8_t avg = 0;
	
	avg = cal_avg(simage->addr, 128, 128);

	pic_avg_ex[pic_ex_idx] = avg;
    pic_ex_idx = (pic_ex_idx + 1) % PIC_EX_LEN;

	/* 计算平均值,判断是否为室外 */
	int sum = 0, th = 0;

	for(uint8_t i = 0; i < PIC_EX_LEN; i++) {
		sum += pic_avg_ex[i];
	}
	th = sum / PIC_EX_LEN;

	printk("avg: %d, tavg:%d\r\n\r\n", avg, th);

	if(th < PIC_EX_OUTDOOR_TH) {
		if(avg > 150) {
			printk("pic is Overexplosion\r\n");
			return 0.0f;
		}
	} else {
		printk("pic is outdoor\r\n");
	}
#else
    if(cal_avg(simage->addr, 128, 128) > 150) {
		// printk("pic is Overexplosion\r\n");
		return 0.0f;
    }
#endif

    mf_kpu_model_context_t living;
    kpu_ret = mf_kpu_load_kmodel(&living, mdl_data);
    if(kpu_ret != 0)
    {	DEBUG_LINE();
        while(1) {};
    }
    g_ai_done_flag = 0;
    mf_kpu_run_kmodel(&living, simage->addr - 0x40000000, FACE_DMAC_CHANNEL, ai_done, NULL);
    while(!g_ai_done_flag){};
    mf_kpu_get_output(&living, 0, (uint8_t **)&result, &result_size);

	/* copy result. */
	ret = result[0]; /* result[1] */

    mf_kpu_model_free(&living);
	return ret;
}

uint8_t global_living_pass_twice_flag = 0;

float global_living_score = 0.0f;

static uint8_t g_simg_buf[128 * 128];
static image_t g_simg = {.addr = g_simg_buf, .width = 128, .height = 128, .pixel = 1};

static float _mf_model_face_living(image_t* img)
{
#ifdef CONFIG_PROJ_MODEL_TEST
	return 100.0f;
#endif

	/* NOTE: must convert to gray */
	img_r8g8b8_togray(img, &g_simg, global_sim_image);

	static float last_living_res_0 = 0.0f;
	static float last_living_res_1 = 0.0f;
	static uint64_t last_recong_time = 0;

	float fret = 0.0f;
	float living_res = _cal_living_item(&g_simg, mf_mdl.nose_data);

	living_res *= 100.0f;

	if(face_lib_model_debug_en)
    	printk("LIVING: %d\r\n", (int)(living_res * 100));

	///////////////////////////////////////////////////////////////////////////
	static uint8_t pass_cnt = 0, run_cnt = 0;
	static uint64_t last_living_pass_tim = 0;

	uint8_t pass_flag = 0;
	uint64_t t = sysctl_get_time_us();;

	if(global_living_pass_twice_flag) {
		/* 两次通过时间大于300ms，认定为非法,防止刚好最后一次识别是通过的，下一次来识别立马通过 */
		if((t - last_living_pass_tim) > (300 * 1000)) {
			pass_cnt = 0;
		}

		/* 是否大于设定的阈值 */
		if(living_res > mf_model.live_gate) {
			pass_cnt ++;

			if(pass_cnt >= 2) {
				pass_flag = 1;
				pass_cnt = 0;
			}
			last_living_pass_tim = t;
		}

		/* 识别3次之后就清空标志位 */
		run_cnt++;
		if(run_cnt >= 3) {
			run_cnt = 0;
			pass_cnt = 0;
		}
	} else {
		pass_flag = 1;
	}

	// fret = pass_flag ? (( living_res + last_living_res_0 + last_living_res_1) / 3.0f) : (-living_res);
	fret = pass_flag ? (( living_res + last_living_res_0 ) / 2.0f) : (-living_res);
	///////////////////////////////////////////////////////////////////////////

	/* 如果超过500ms没有识别，就清空保存的结果 */
	if((t - last_recong_time) > (500 *1000)) {
		last_living_res_1 = 0.0f;
		last_living_res_0 = 0.0f;
	}
	last_recong_time = t;

	/* 保存最多3次的结果 */
	last_living_res_1 = last_living_res_0;
	last_living_res_0 = living_res;

	global_living_score = living_res;

	return fret;
}
#elif 0 /* 旧的活体模型，一共3个 */
/* 在全部都升级到最新的活体模型之前，这个error不允许屏蔽 */
// #error "Please comfirm use which version model"

#define CONFIG_LIVING_MODEL_V2				(1)	/* 新的活体模型，V126之后发布的 */

const static uint16_t patch_x[3] = {44, 44, 44};

#if CONFIG_LIVING_MODEL_V2
	const static uint16_t patch_y[3] = {44, 74, 86};
#else
	const static uint16_t patch_y[3] = {40, 74, 86};
#endif

static float _cal_living_item(image_t* simage, image_t* patch_img, uint8_t* mdl_data, uint8_t idx)
{	
	int kpu_ret = 0xffff;
	float *result;
    size_t result_size;
    mf_kpu_model_context_t living;
    image_crop(simage, patch_img, patch_x[idx], patch_y[idx]);
    kpu_ret = mf_kpu_load_kmodel(&living, mdl_data);
    if(kpu_ret != 0)
    {	DEBUG_LINE();
        while(1) {};
    }
    g_ai_done_flag = 0;
    mf_kpu_run_kmodel(&living, patch_img->addr, FACE_DMAC_CHANNEL, ai_done, NULL);
    while(!g_ai_done_flag){};
    mf_kpu_get_output(&living, 0, (uint8_t **)&result, &result_size);
	//char str[32];
    //sprintf(str, "%.3f", result[1]);
    //printk("##### living[%d]  %s\r\n", idx, str);
    mf_kpu_model_free(&living);
	return result[1];
}

uint8_t global_living_pass_twice_flag = 0;

float global_living_res[3];

// float global_living_weights[3] = {0.160f, 0.560f, 0.280f};

// float global_living_weights[3] = {0.194f, 0.161f, 0.645f};

float global_living_weights[3] = {0.526f, 0.211f, 0.263f};

float global_living_chk_th[3] = {0,0,0};//{0.65f, 0.85f,  0.75f};

float global_living_score = 0.0f;

static float _mf_model_face_living(image_t* img)
{
    /* 先初始化模型，再初始化图像，最后进行运算 */
    float as_result[3];

#if CONFIG_LIVING_MODEL_V2
	/* NOTE: must convert to gray */
	img_r8g8b8_togray(img);
#endif

    image_t patch_img = {.pixel = 3, .width = 40, .height = 24};DBG_HEAP();
    int ret = image_init(&patch_img);	DBG_HEAP();
	if(ret < 0) return 0;
	as_result[0] = _cal_living_item(img, &patch_img, mf_mdl.eye_data, 0);
	as_result[1] = _cal_living_item(img, &patch_img, mf_mdl.nose_data, 1);
	as_result[2] = _cal_living_item(img, &patch_img, mf_mdl.mouth_data, 2);
    image_deinit(&patch_img);

	global_living_res[0] = as_result[0];
	global_living_res[1] = as_result[1];
	global_living_res[2] = as_result[2];

#if !CONFIG_LIVING_MODEL_V2

	float fc_weights[3] = {3.4909396, -0.8304184 ,1.5224441};

	/* 归一化到100分 */
    float living_res = (float)((fc_weights[0] * as_result[0] + fc_weights[1] * as_result[1] + fc_weights[2] * as_result[2]) * 25.0f);

	char str[64];
    sprintf(str, "res:%.3f, %.3f, %.3f, %.3f", living_res, as_result[0], as_result[1], as_result[2]);
    printk("##### %s\r\n", str);

	global_living_score = living_res;

	return living_res;
#else
	/* 满分100 */
    float living_res = (float)((global_living_weights[0] * as_result[0] + global_living_weights[1] * as_result[1] + global_living_weights[2] * as_result[2]) * 100.0f);

	char str[64];
    sprintf(str, "res:%.3f, %.3f, %.3f, %.3f", living_res, as_result[0], as_result[1], as_result[2]);
    printk("##### %s\r\n", str);

	if(((as_result[0] >= global_living_chk_th[0]) && (as_result[1] >= global_living_chk_th[1]) && (as_result[2] >= global_living_chk_th[2])) == false) {
		global_living_score = (-living_res);
		return (-living_res);
	}

    printk("@@@@\r\n");

	static uint8_t pass_cnt = 0, run_cnt = 0;
	static uint64_t last_living_pass_tim = 0;

	uint8_t pass_flag = 0;
	uint64_t t = 0;

	if(global_living_pass_twice_flag) {
		t = sysctl_get_time_us();

		/* 两次通过时间大于300ms，认定为非法,防止刚好最后一次识别是通过的，下一次来识别立马通过 */
		if((t - last_living_pass_tim) > (300 * 1000)) {
			pass_cnt = 0;
		}

		/* 是否大于设定的阈值 */
		if(living_res > mf_model.live_gate) {
			pass_cnt ++;

			if(pass_cnt >= 2) {
				pass_flag = 1;
				pass_cnt = 0;
			}
			last_living_pass_tim = t;
		}

		/* 识别3次之后就清空标志位 */
		run_cnt++;
		if(run_cnt >= 3) {
			run_cnt = 0;
			pass_cnt = 0;
		}
	} else {
		pass_flag = 1;
	}

	/* Debug */
	global_living_score = pass_flag ? living_res : (-living_res);

    return pass_flag ? living_res : (-living_res);
#endif
}
#endif
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////

//初始化需要的模型
//MF_MDL_FACEDETECT | MF_MDL_FACEALIGN | MF_MDL_FACEFTR | MF_MDL_LIVING
//注意：这里没有处理申请失败之后的释放操作（因为已经初始化的不会重复初始化）
static mf_err_t _mf_model_init_real(uint16_t model_type, uint8_t init_otp)
{
	if(init_otp) {
		enum otp_status_t result;
		myotp_init_aaa(21, 0x50000000U, 0x420000U);
		result = myotp_func_reg_disable_set(JTAG_DISABLE);
		if(result != OTP_OK) {
			return MF_ERR_MDL_INIT1;
		}

		result = myotp_read_data(OTP_SYSTEM_DATA_ADDR, mf_mdl.key_in_otp, 16);
		if(result != OTP_OK) {
			return MF_ERR_MDL_INIT2;
		}
	}
	
	//加载模型
	mf_err_t err = MF_ERR_NONE;
	///////////////////////////////////////////////////////////////////////////
	///////////////////////////////////////////////////////////////////////////
	///////////////////////////////////////////////////////////////////////////
	/* 人脸检测相关模型初始化 */
	if((model_type&MF_MDL_FACEDETECT) && \
		(((mf_model.model_flag)&MF_MDL_FACEDETECT) == 0)) {
		err = _mf_mdl_init_fd(1);
		if(err != MF_ERR_NONE) goto mf_mdl_init_err;
		mf_model.model_flag |= MF_MDL_FACEDETECT;
	}

	if((model_type&MF_MDL_LAYOUT_V3_FD) && \
		(((mf_model.model_flag)&MF_MDL_FACEDETECT) == 0)) {
		err = _mf_mdl_init_fd(3);
		if(err != MF_ERR_NONE) goto mf_mdl_init_err;
		mf_model.model_flag |= MF_MDL_FACEDETECT;
	}

	if((model_type&MF_MDL_LAYOUT_V2_FD) && \
		(((mf_model.model_flag)&MF_MDL_LAYOUT_V2_FD) == 0)) {
		uint8_t tmp = mf_brd.cfg.cam.dir;
		mf_brd.cfg.cam.dir = 1;

		/* 借用DETV的地址 */
		err = _mf_mdl_init_fd(1);

		mf_brd.cfg.cam.dir = tmp;

		if(err != MF_ERR_NONE) { goto mf_mdl_init_err; }

		mf_model.model_flag |= MF_MDL_FACEDETECT;
	}
	///////////////////////////////////////////////////////////////////////////
	///////////////////////////////////////////////////////////////////////////
	///////////////////////////////////////////////////////////////////////////
	/* 人脸关键点检测模型 */
	if((model_type&MF_MDL_FACEALIGN) && \
		(((mf_model.model_flag)&MF_MDL_FACEALIGN) == 0)) {
		err = _mf_mdl_init_ld();
		if(err != MF_ERR_NONE) goto mf_mdl_init_err;
		mf_model.model_flag |= MF_MDL_FACEALIGN;
	}
	
	if((model_type & MF_MDL_LAYOUT_V2_KP) && \
		(((mf_model.model_flag) & MF_MDL_LAYOUT_V2_KP) == 0)) {
		err = _mf_mdl_init_ld();

		if(err != MF_ERR_NONE) goto mf_mdl_init_err;
		mf_model.model_flag |= MF_MDL_FACEALIGN;
	}
	///////////////////////////////////////////////////////////////////////////
	///////////////////////////////////////////////////////////////////////////
	///////////////////////////////////////////////////////////////////////////
	/* 活体判断模型 */
	if((model_type&MF_MDL_LIVING) && \
		(((mf_model.model_flag)&MF_MDL_LIVING) == 0)) {
		err = _mf_mdl_init_live();
		if(err != MF_ERR_NONE) goto mf_mdl_init_err;
		mf_model.model_flag |= MF_MDL_LIVING;
	}
	///////////////////////////////////////////////////////////////////////////
	///////////////////////////////////////////////////////////////////////////
	///////////////////////////////////////////////////////////////////////////
	/* 人脸特征值提取模型 */
	if((model_type&MF_MDL_FACEFTR) && \
		(((mf_model.model_flag)&MF_MDL_FACEFTR) == 0)) {
		err = _mf_mdl_init_fe();
		if(err != MF_ERR_NONE) goto mf_mdl_init_err;
		mf_model.model_flag |= MF_MDL_FACEFTR;
	}

	if((model_type & MF_MDL_LAYOUT_V2_FE) && \
		(((mf_model.model_flag) & MF_MDL_LAYOUT_V2_FE) == 0)) {
			err = _mf_mdl_init_fe_v2();

			if(err != MF_ERR_NONE) goto mf_mdl_init_err;
			mf_model.ftr_len = 192;
			mf_model.model_flag |= MF_MDL_LAYOUT_V2_FE | MF_MDL_FACEFTR;
	}

	if((model_type & MF_MDL_LAYOUT_V3_FE) && \
		(((mf_model.model_flag) & MF_MDL_LAYOUT_V3_FE) == 0)) {
			err = _mf_mdl_init_fe_v3();

			if(err != MF_ERR_NONE) goto mf_mdl_init_err;
			mf_model.model_flag |= MF_MDL_LAYOUT_V3_FE | MF_MDL_FACEFTR;

			mf_model.ftr_cal = _mf_model_ftr_calc_v3;
	}

	if((model_type & MF_MDL_LAYOUT_V4_FE) && \
		(((mf_model.model_flag) & MF_MDL_LAYOUT_V4_FE) == 0)) {
			err = _mf_mdl_init_fe_v4();

			if(err != MF_ERR_NONE) goto mf_mdl_init_err;
			mf_model.model_flag |= MF_MDL_LAYOUT_V4_FE | MF_MDL_FACEFTR;

			mf_model.ftr_cal = _mf_model_ftr_calc_v4;
	}

	if((model_type & MF_MDL_LAYOUT_V5_FE) && \
		(((mf_model.model_flag) & MF_MDL_LAYOUT_V5_FE) == 0)) {
			err = _mf_mdl_init_fe_v5();

			if(err != MF_ERR_NONE) goto mf_mdl_init_err;
			mf_model.model_flag |= MF_MDL_LAYOUT_V5_FE | MF_MDL_FACEFTR;

			mf_model.ftr_cal = _mf_model_ftr_calc_v5;
	}

	//加载配置
	_mf_model_load_cfg();

mf_mdl_init_err:

	if(err != MF_ERR_NONE) {
		char out[13];
		static char err_model[] = {'M' ^ 0xD9, 'o' ^ 0xD9, 'd' ^ 0xD9, 'e' ^ 0xD9, 'l' ^ 0xD9, ',' ^ 0xD9, 'e' ^ 0xD9, 'r' ^ 0xD9, 'r' ^ 0xD9, 'o' ^ 0xD9, 'r' ^ 0xD9, 0x0a ^ 0xd9, 0x00  ^ 0xD9};
		static volatile uint8_t seed = 0xd9;
		for(int i = 0; i < sizeof(err_model); i++) { out[i] = err_model[i] ^ seed; }
		printk("%s", out);
	}

	if(init_otp)
		sysctl_clock_disable(SYSCTL_CLOCK_OTP);

	return err;
}

static mf_err_t _mf_model_init(uint16_t model_type)
{
	return _mf_model_init_real(model_type, 1);
}

static inline void _hex2str(const uint8_t *inchar, const uint16_t len, uint8_t *outtxt)
{
	uint16_t i = 0;
    uint8_t hbit, lbit;

    for(i = 0; i < len; i++) {
        hbit = (*(inchar + i) & 0xf0) >> 4;
        lbit = *(inchar + i) & 0x0f;
        if(hbit > 9)
            outtxt[2 * i] = 'A' + hbit - 10;
        else
            outtxt[2 * i] = '0' + hbit;
        if(lbit > 9)
            outtxt[2 * i + 1] = 'A' + lbit - 10;
        else
            outtxt[2 * i + 1] = '0' + lbit;
    }
    outtxt[2 * i] = 0;
}

/**
 * @brief 激活指定地址的模型及长度
 * 
 * @param addr: 模型地址
 * @param len: 模型长度
 * @param model_type: 0:模型头部;1: 模型其他位置
 * @param model_ver: 1: V1; 3: V3
 * 
 * @return 0: succ 1: fail
 */
static inline int act_model(uint32_t addr, uint32_t len, uint8_t model_type, uint8_t model_ver)
{
	if((120 * 1024) < len) {
		printk("model len err %d\r\n", len);
		return 1;
	}

	uint8_t version = 0; /* 1: V1; 3: V3 */
	uint8_t type = model_type & 0x01; /* 0:模型头部;1: 模型其他位置 */

	if((0x01 != model_ver) && (0x03 != model_ver)) {
		printk("model version err %d\r\n", model_ver);
		return 1;
	}
	version = model_ver;

	uint8_t *model_data_org = malloc(len + 512);

	if(NULL == model_data_org) {
		printk("OOM %s\r\n", __func__);
		return 1;
	}

	uint8_t *model_data = (uint8_t *)(((uintptr_t)model_data_org + 255) & (~255));

	mf_flash.read(addr, model_data, len);

// printk("%x %x %d\r\n", (uint32_t)addr, (uint32_t)model_data, len);

	if(0x00 == type) { /* 模型头部 */
		mf_kpu_kmodel_header_t *hdr = NULL;

		hdr = (mf_kpu_kmodel_header_t *)model_data;

// printk("ver %d, arch %d\r\n", hdr->version, hdr->arch);

		if(0x03 == version) {
			if((0x03 == hdr->version) && (0x00 == hdr->arch)) {
				/* 未激活 */
			} else {
				free(model_data_org);
				return 0;
			}
		} else if(0x01 == version) {
			if(0x01 == hdr->version) {
				/* 未激活 */
			} else {
				free(model_data_org);
				return 0;
			}
		} else {
			/* 需要进行激活操作 */
			printk("unk err %s\r\n", __func__);
		}
	} else { /* 模型其他位置 */
		/* 目前就特征值模型有加密非头部数据 */
		if((FEATURE_MODEL_ADDRESS + 3300 * 1024) != addr) {
			printk("unk err %s\r\n", __func__);
			free(model_data_org);
			return 1;
		}

		// for(int i = 0; i < 8; i++) {
		// 	printk("0x%02X,", model_data[i]);
		// }

		/* 检查是否为原始数据 */
		static const uint8_t org_data[8] = {0x68,0x77,0xAF,0xCF,0x66,0x63,0x79,0xC1};

		if(0x00 != memcmp(org_data, model_data, 8)) {
			free(model_data_org);
			return 0;
		}
	}

// printk("%s->%d\r\n", __func__, __LINE__);

// printk("%x %x %d\r\n", (uint32_t)addr, (uint32_t)model_data, len);

	enc_kmodel((uint64_t*)model_data, len, 0);

	mf_flash.write(addr, (uint8_t *)model_data, len);

	free(model_data_org);

	return 0;
}

/**
 * @brief 激活用到的模型
 * 
 * @param model_type: 模型类型
 * 
 * @return 0:succ; 1: fail
 */
static int active_models(uint16_t model_type)
{
	/* 人脸检测相关模型初始化 */
	if((model_type & MF_MDL_FACEDETECT)) {
		if(0x00 != act_model(DETH_MODEL_ADDRESS, 4096, 0, 3)) {
			return 1;
		}
		if(0x00 != act_model(DETV_MODEL_ADDRESS, 4096, 0, 3)) {
			return 1;
		}
	}

	if((model_type & MF_MDL_LAYOUT_V3_FD)) {
		printk("Unsupport model type %x\r\n", MF_MDL_LAYOUT_V3_FD);
		return 1;
	}

	if((model_type & MF_MDL_LAYOUT_V2_FD)) {
		printk("Unsupport model type %x\r\n", MF_MDL_LAYOUT_V2_FD);
		return 1;
	}
	///////////////////////////////////////////////////////////////////////////
	///////////////////////////////////////////////////////////////////////////
	///////////////////////////////////////////////////////////////////////////
	/* 人脸关键点检测模型 */
	if((model_type & MF_MDL_FACEALIGN)) {
		if(0x00 != act_model(KP_MODEL_ADDRESS, 4096, 0, 1)) {
			return 1;
		}
	}

	if((model_type & MF_MDL_LAYOUT_V2_KP)) {
		printk("Unsupport model type %x\r\n", MF_MDL_LAYOUT_V2_KP);
		return 1;
	}
	///////////////////////////////////////////////////////////////////////////
	///////////////////////////////////////////////////////////////////////////
	///////////////////////////////////////////////////////////////////////////
	/* 活体判断模型 */
	if((model_type & MF_MDL_LIVING)) {
		/* 这个不是存储在flash中的 */
	}
	///////////////////////////////////////////////////////////////////////////
	///////////////////////////////////////////////////////////////////////////
	///////////////////////////////////////////////////////////////////////////
	/* 人脸特征值提取模型 */
	if((model_type & MF_MDL_FACEFTR)) {
		if(0x00 != act_model(FEATURE_MODEL_ADDRESS, 4096, 0, 3)) {
			return 1;
		}

		if(0x00 != act_model(FEATURE_MODEL_ADDRESS + 3300 * 1024, (FEATURE_MODEL_SIZE - 3300 * 1024), 1, 3)) {
			return 1;
		}
	}

	if((model_type & MF_MDL_LAYOUT_V2_FE)) {
		printk("Unsupport model type %x\r\n", MF_MDL_LAYOUT_V2_FE);
		return 1;
	}

	if((model_type & MF_MDL_LAYOUT_V3_FE)) {
		printk("Unsupport model type %x\r\n", MF_MDL_LAYOUT_V3_FE);
		return 1;
	}

	if((model_type & MF_MDL_LAYOUT_V4_FE)) {
		printk("Unsupport model type %x\r\n", MF_MDL_LAYOUT_V4_FE);
		return 1;
	}

	if((model_type & MF_MDL_LAYOUT_V5_FE)) {
		printk("Unsupport model type %x\r\n", MF_MDL_LAYOUT_V5_FE);
		return 1;
	}

	return 0;
}

/**
	@brief 激活模型

	@param [in] model_type: 模型类型
	@param [in] key: 秘钥，服务器通过设备的uniqid计算获得

	@return `mf_err_t`
 */
mf_err_t mf_model_act_models(uint16_t model_type, uint8_t key[32])
{
	uint8_t uniq_id[16],uniq_id_str[64], uniq_sha2[32];

	/* 读取芯片的uniq_id */
	for(uint8_t i = 0; i < 16; i ++) {
		uniq_id[i] = mf_mdl.key_in_otp[i];
	}

	_hex2str(uniq_id, 16, uniq_id_str);

	/* 服务器计算 */
	uniq_id_str[32 + 0] = 'A';
	uniq_id_str[32 + 1] = 'i';
	uniq_id_str[32 + 2] = 'H';
	uniq_id_str[32 + 3] = 'a';
	uniq_id_str[32 + 4] = 'r';
	uniq_id_str[32 + 5] = 'd';
	uniq_id_str[32 + 6] = 'W';
	uniq_id_str[32 + 7] = 'a';
	uniq_id_str[32 + 8] = 'r';
	uniq_id_str[32 + 9] = 'e';
	uniq_id_str[32 + 10] = '2';
	uniq_id_str[32 + 11] = '0';
	uniq_id_str[32 + 12] = '2';
	uniq_id_str[32 + 13] = '1';
	uniq_id_str[32 + 14] = '@';
	uniq_id_str[32 + 15] = '#';
	uniq_id_str[32 + 16] = '>';
	uniq_id_str[32 + 17] = '\0';

	/* NOTE: 这里不应该调用这个函数，但是由于只是内部使用，就不改了 */
	sha256_hard_calculate(uniq_id_str, 49, uniq_sha2);

	// for(int i = 0; i < 32; i++) {
	// 	printk("%02X", uniq_sha2[i]);
	// }

	if(0x00 != memcmp(uniq_sha2, key, 32)) {
		printk("key is error\r\n");
		return MF_ERR_MODEL_ACT_ERR_KEY;
	}

	/* key比对通过，则进行激活 */

	myotp_init_aaa(21, 0x50000000U, 0x420000U);

	if(0x00 == active_models(model_type)) {
		mf_model.deinit(model_type);

		mf_err_t err =  _mf_model_init(model_type);

		sysctl_clock_disable(SYSCTL_CLOCK_OTP);

		return err;
	}

	sysctl_clock_disable(SYSCTL_CLOCK_OTP);

	return MF_ERR_TODO;
}


//清理不需要的模型
static void _mf_model_deinit(uint16_t model_type)
{
	if((model_type&MF_MDL_FACEDETECT) && \
		((mf_model.model_flag)&MF_MDL_FACEDETECT)) {
		_mf_mdl_deinit_fd();
		mf_model.model_flag &= ~MF_MDL_FACEDETECT;
	}
	if((model_type&MF_MDL_FACEALIGN) && \
		((mf_model.model_flag)&MF_MDL_FACEALIGN)) {
		_mf_mdl_deinit_ld();
		mf_model.model_flag &= ~MF_MDL_FACEALIGN;
	}

	if((model_type&MF_MDL_LIVING) && \
		((mf_model.model_flag)&MF_MDL_LIVING)) {
		_mf_mdl_deinit_live();
		mf_model.model_flag &= ~MF_MDL_LIVING;
	}

	if((model_type&MF_MDL_LAYOUT_V2_FE) && \
		((mf_model.model_flag)&MF_MDL_LAYOUT_V2_FE)) {
		_mf_mdl_deinit_fe();
		mf_model.model_flag &= ~MF_MDL_LAYOUT_V2_FE;
		mf_model.model_flag &= ~MF_MDL_FACEFTR;
	}

	if((model_type&MF_MDL_LAYOUT_V3_FE) && \
		((mf_model.model_flag)&MF_MDL_LAYOUT_V3_FE)) {
		_mf_mdl_deinit_fe();
		mf_model.model_flag &= ~MF_MDL_LAYOUT_V3_FE;
		mf_model.model_flag &= ~MF_MDL_FACEFTR;
	}

	if((model_type&MF_MDL_LAYOUT_V4_FE) && \
		((mf_model.model_flag)&MF_MDL_LAYOUT_V4_FE)) {
		_mf_mdl_deinit_fe();
		mf_model.model_flag &= ~MF_MDL_LAYOUT_V4_FE;
		mf_model.model_flag &= ~MF_MDL_FACEFTR;
	}

	/* 最后判断是否为FE_V1 */
	if((model_type&MF_MDL_FACEFTR) && \
		((mf_model.model_flag)&MF_MDL_FACEFTR)) {
		_mf_mdl_deinit_fe();
		mf_model.model_flag &= ~MF_MDL_FACEFTR;
	}

	return;
}

static void _mf_model_get_cpuid(uint8_t *id)
{
	for(uint8_t i = 0; i < 16; i ++) {
		*(id + i) = mf_mdl.key_in_otp[i];
	}
	return;
}
/*****************************************************************************/
// Public Var 全局变量
/*****************************************************************************/
volatile uint8_t face_lib_model_debug_en = 0;

mf_model_t mf_model = {
	.model_flag   = 0,
	.face_minw    = FACE_WIDTH_MIN,
	.face_minh    = FACE_HEIGHT_MIN,
	.fd_gate      = FACE_DETECT_THRESHOLD,
	.ftr_len      = FACE_FTR_LEN,
	.fe_gate      = FE_GATE,
	.fe_gate_ir   = FE_GATE_IR,
	.live_gate    = LIVE_GATE,
	.is_check_pose= 1,
	.db_search    = NULL,	//待flow配置
	.init         = _mf_model_init,
	.deinit       = _mf_model_deinit,
	.load_cfg     = _mf_model_load_cfg,
	.save_cfg     = _mf_model_save_cfg,
	.cfg_fd       = _mf_model_cfg_fd,
	.cfg_fe       = _mf_model_cfg_fe,
	.cfg_live     = _mf_model_cfg_live,
	.face_detect  = _mf_model_face_detect,
	.face_align   = _mf_model_face_align,
	.ftr_cal      = _mf_model_ftr_cal,
	.ftr_compare  = _mf_model_ftr_compare,
	.face_living  = _mf_model_face_living,
	.run_fr       = _mf_model_run_fr,
	.free_fr      = _mf_model_free_fr,
	.check_pose   = _mf_model_check_pose,
	.check_blur   = blur_detect_run,
	.resize       = image_resize,
	.cpuid		  = _mf_model_get_cpuid,
};

