#ifndef _MF_MODEL_H
#define _MF_MODEL_H

#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include "image_op.h"

/*****************************************************************************/
// Enums & Macro
/*****************************************************************************/
#define MF_MDL_FACEDETECT           (0x01)
#define MF_MDL_FACEALIGN            (0x02)
#define MF_MDL_FACEFTR              (0x04)
#define MF_MDL_LIVING               (0x08)

#define MF_MDL_LAYOUT_V2_FD         (0x10)
#define MF_MDL_LAYOUT_V2_KP         (0x20)
#define MF_MDL_LAYOUT_V2_FE         (0x40)

#define MF_MDL_LAYOUT_V3_FE         (0x80)

#define MF_MDL_LAYOUT_V3_FD         (0x100)

#define MF_MDL_LAYOUT_V4_FE         (0x200)

#define MF_MDL_LAYOUT_V5_FE         (0x400)

#define MF_MDL_ALL                  (MF_MDL_FACEDETECT|MF_MDL_FACEALIGN|MF_MDL_FACEFTR|MF_MDL_LIVING)
#define MF_MDL_ALL_V3               (MF_MDL_LAYOUT_V3_FD|MF_MDL_FACEALIGN|MF_MDL_LAYOUT_V3_FE|MF_MDL_LIVING)
#define MF_MDL_ALL_V4               (MF_MDL_LAYOUT_V3_FD|MF_MDL_FACEALIGN|MF_MDL_LAYOUT_V4_FE|MF_MDL_LIVING)
#define MF_MDL_ALL_V5               (MF_MDL_LAYOUT_V3_FD|MF_MDL_FACEALIGN|MF_MDL_LAYOUT_V5_FE|MF_MDL_LIVING)

#define FTR_850                 (1)
#define FTR_650                 (0)

#define FACE_MAX_NUMBER         (5)
#define FACE_DETECT_THRESHOLD 	(70.0f)
#define FACE_DETECT_NMS 		(0.3f)
#define FACE_WIDTH_MIN		 	(60)
#define FACE_HEIGHT_MIN 		(80)
#define FACE_FTR_LEN 			(196)	//192 //128 //64
#define FE_GATE					(80.0f)
#define FE_GATE_IR              (88.0f)
#define LIVE_GATE				(70.0f)
#define FR_SCORE_MAX            (95.0f)

/*****************************************************************************/
// Types
/*****************************************************************************/
typedef struct
{
    uint32_t width;
    uint32_t height;
    struct
    {
        uint32_t x;
        uint32_t y;
    } point[5];
} key_point_t;

/*typedef struct
{
    uint8_t *addr;
    uint16_t width;
    uint16_t height;
    uint16_t pixel;
} image_t; */

typedef enum face_step
{
    STEP_DET_FACE = 0,
    STEP_DET_KP,
    STEP_LIVING,
    STEP_FETURE,
} face_step_t;

typedef struct
{
    uint32_t x1;
    uint32_t y1;
    uint32_t x2;
    uint32_t y2;
    uint32_t class_id;
    float prob;
    key_point_t key_point;
    int8_t *feature;
    uint32_t index;
    float score;
    uint8_t pass;
    uint8_t blur;
	uint8_t pose;
    uint8_t true_face; /* 0:fake, 1:true, 0xAA:illgal */
    uint8_t ir_rgb; /* 0:rgb,1:ir; only used in VIS2IR*/
} face_obj_t;

typedef struct
{
    uint32_t obj_number;
    face_obj_t obj[FACE_MAX_NUMBER];
} face_obj_info_t;

typedef struct
{
    uint32_t result_number;
    face_obj_t *obj[FACE_MAX_NUMBER];
} face_compare_info_t;

typedef struct
{
    face_obj_info_t face_obj_info;
    face_compare_info_t face_compare_info;
} fr_result_t;  // face recognition result 

typedef struct
{
    int ret;
    fr_result_t *result;
} fr_ret_t;  // face recognition return 

typedef struct
{
	//Private
	//Public
	uint16_t model_flag;
	uint16_t ftr_len;
	uint16_t face_minw;
	uint16_t face_minh;
	float    fd_gate;
	float    fe_gate;
    float    fe_gate_ir;
	float    live_gate;
	uint16_t is_check_pose;
	mf_err_t (*db_search)(int8_t *ftr, uint8_t ir_or_rgb, uint32_t* vid, float *score);
	//Const Public
	mf_err_t (*init)(uint16_t model_type);
	void     (*deinit)(uint16_t model_type);
	mf_err_t (*load_cfg)(void);
	mf_err_t (*save_cfg)(void);
	void     (*cfg_fd)(uint16_t minw, uint16_t minh, float threshold);
	void     (*cfg_fe)(uint16_t ftr_len, float threshold, float threshold_ir);
	void     (*cfg_live)(float threshold);
	//algorithm interface
	mf_err_t (*face_detect)(image_t* kpu_img, face_obj_info_t* info);
	mf_err_t (*face_align)(image_t* kpu_img, face_obj_t* obj, image_t* simg);
	mf_err_t (*ftr_cal)(image_t* img, int8_t* ftr);
	float    (*ftr_compare)(int8_t* ftr0, int8_t* ftr1);
	float    (*face_living)(image_t* img);
	fr_ret_t (*run_fr)(image_t* kpu_img, uint8_t out_ftr, face_step_t step, uint8_t ir_or_rgb, uint8_t only_biggest);
	void     (*free_fr)(fr_ret_t face_ret);
	uint8_t  (*check_pose)(key_point_t *kp);
	//128x128 image in, w,h is crop size, scale is about 0.15
	uint8_t  (*check_blur)(uint8_t * buf_in, uint16_t w, uint16_t h, float scale); 
	void     (*resize)(image_t *src, image_t *dst);
    void     (*cpuid)(uint8_t *id);
} mf_model_t;


/*****************************************************************************/
// Functions
/*****************************************************************************/


/*****************************************************************************/
// Vars
/*****************************************************************************/
extern mf_model_t mf_model;

void mf_model_use_dummy(void);

#endif
