/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_key.c
** bef: define the interface for key.
** auth: lines<<EMAIL>>
** create on 2021.12.21 
*/

#include "es_inc.h"

#if ES_KEY_MODULE_ENABLE

// #define ES_KEY_DEBUG
#ifdef ES_KEY_DEBUG
#define es_key_debug es_log_info
#define es_key_error es_log_error
#else
#define es_key_debug(...)
#define es_key_error(...)
#endif


static ES_U32 key1_last_time = 0;

static ES_VOID es_key_gpio_init(ES_VOID)
{
    fpioa_set_function(ES_KEY1_GPIO_PIN, FUNC_GPIOHS0 + ES_KEY1_GPIO_HS_NUM);
    gpiohs_set_drive_mode(ES_KEY1_GPIO_HS_NUM, GPIO_DM_INPUT);
}

static ES_VOID es_key_reset_default(ES_VOID)
{
    es_task_param_t task_param;
    task_param.type = ES_TASK_RESET_DEVICE;
    task_param.param = ES_NULL;
    task_param.timeout = 0;
    es_task_queue_push_wait(&task_param);
}

static ES_VOID es_key_del_facedb_all(ES_VOID)
{
    es_task_param_t task_param;
    task_param.type = ES_TASK_DEL_FACEDB_ALL;
    task_param.param = ES_NULL;
    task_param.timeout = 0;
    es_task_queue_push_wait(&task_param);
}

ES_S32 es_key_init(ES_VOID)
{
    es_key_gpio_init();
    return ES_RET_SUCCESS;
}

ES_VOID es_key_task(ES_VOID)
{
    ES_U32 now_time = 0;

    if (ES_KEY1_PRESS_VAL != gpiohs_get_pin(ES_KEY1_GPIO_HS_NUM)) {
        if (0 == key1_last_time) {
            return;
        }

        now_time = es_time_get_sytem_ms();
        if (now_time - key1_last_time < 200) {
            es_key_debug("press too fast");
        } else if (now_time - key1_last_time < 1000) {
            // press key, release
            es_key_debug("press key");
        // } else if (now_time - key1_last_time > 3000) {
        //     // long press key, release
        //     es_key_reset_default();
        //     es_key_debug("press long time");
        }
        key1_last_time = 0;
        return;
        // press key, release
    }

    now_time = es_time_get_sytem_ms();
    if (0 == key1_last_time) {
        key1_last_time = es_time_get_sytem_ms();
    } else if (now_time - key1_last_time > 3000) {
        es_key_del_facedb_all();
        es_key_reset_default();
        es_key_debug("press long time");
        key1_last_time = 0;
    }
}

#endif
