/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_network_mqtt.c
** bef: define the interface for mqtt network.
** auth: lines<<EMAIL>>
** create on 2022.05.08 
*/

#include "es_inc.h"
#include "facelib_inc.h"
#if ES_MQTT_ENABLE

#define NETWORK_MQTT_DEBUG
#ifdef NETWORK_MQTT_DEBUG
#define network_mqtt_debug es_log_info
#define network_mqtt_error es_log_error
#else
#define network_mqtt_debug(...)
#define network_mqtt_error(...)
#endif

#define MQTT_ENABLE_FACE_FEA_SHA256         (0)
#define MQTT_SEND_JSON_STR_LEN              (1024)

typedef enum {
    ES_MQTT_STATUS_SYNC_INIT,
    ES_MQTT_STATUS_READY,
} es_mqtt_status_e;

static ES_U8 mqtt_status = ES_MQTT_STATUS_SYNC_INIT;
static ES_U32 last_heartbeat_time = 0;
static ES_U32 last_upload_status_time = 0;

static struct pt pt_mqtt_send;
static struct pt_sem sem_mqtt_resp;
static ES_CHAR mqtt_id[ES_MAC_COLON_STR_LEN] = {0};
static ES_U16 mqtt_wait_resp = ES_PROTO_SERV_DEV_MAX;
static ES_U16 mqtt_now_resp = ES_PROTO_SERV_DEV_MAX;
static ES_U32 mqtt_send_timeout_ms = 0;
static ES_U32 mqtt_send_time = 0;
static ES_BOOL mqtt_doing_send = ES_FALSE;
static ES_U8 mqtt_no_resp_count = 0;
static ES_CHAR mqtt_send_json_str[MQTT_SEND_JSON_STR_LEN] = {0};
static ES_U32 upload_event_count = 0;

static ES_VOID network_mqtt_reset_send_param(ES_U32 wait_resp, ES_U32 timeout_ms)
{
    mqtt_wait_resp = wait_resp;
    mqtt_now_resp = ES_PROTO_SERV_DEV_MAX;
    mqtt_send_timeout_ms = timeout_ms;
    mqtt_send_time = es_time_get_sytem_ms();
}

static ES_VOID network_mqtt_send_sem_check(ES_VOID)
{
    ES_BOOL got_resp_or_timeout = ES_FALSE;

    if (0 == mqtt_send_time) {
        return;
    }

    if (mqtt_now_resp == mqtt_wait_resp) {
        got_resp_or_timeout = ES_TRUE;
        mqtt_no_resp_count = 0;
        network_mqtt_debug("got resp %d", mqtt_now_resp);
    }

    if (ES_RET_SUCCESS == es_time_check_timeout_ms(&mqtt_send_time, mqtt_send_timeout_ms)) {
        got_resp_or_timeout = ES_TRUE;
        mqtt_no_resp_count++;
        network_mqtt_debug("wait resp timeout, no resp_count:%d, %d, %d", mqtt_no_resp_count, mqtt_now_resp, mqtt_wait_resp);
    }

    if (got_resp_or_timeout) {
        mqtt_send_time = 0;
        PT_SEM_SIGNAL(pt, &sem_mqtt_resp);
    }

    if (mqtt_no_resp_count > 3) {
        mqtt_no_resp_count = 0;
        mqtt_status = ES_MQTT_STATUS_SYNC_INIT;
    }
}

static ES_S32 network_mqtt_send(const ES_BYTE *data, ES_U16 data_len)
{
    network_mqtt_debug("mqtt send:%s", data);
    return es_hal_lte_mqtt_send(ES_MQTT_CFG_DEV_PUB_TOPIC, data, data_len);
}

static ES_S32 network_mqtt_get_msg_header(ES_CHAR *json_buf)
{
    return es_sprintf(json_buf, "{\"f\":\"%s\",\"t\":\"server\"", mqtt_id);
}

static ES_S32 network_mqtt_get_jpg_name(ES_CHAR *filename, ES_U32 timestamp)
{
    if (0 == mqtt_id[0]) {
        return ES_RET_FAILURE;
    }

    snprintf(filename, ES_HTTP_HEADER_VALUE_LEN, "%s-%d.jpg", mqtt_id, timestamp);
    return ES_RET_SUCCESS;
}


static ES_S32 network_mqtt_send_sync_time(ES_VOID)
{
    ES_S32 json_len = 0;

    json_len = network_mqtt_get_msg_header(mqtt_send_json_str);
    json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len, ",\"c\":%d,\"mi\":\"0\",\"m\":{}}",
            ES_PROTO_DEV_SYNC_TIME);
    return network_mqtt_send((const ES_BYTE *)mqtt_send_json_str, (ES_U16)json_len);
}


static ES_S32 network_mqtt_send_status(es_gps_info_t *gps_info)
{
    ES_S32 json_len = 0;
    
    memset(mqtt_send_json_str, 0x00, sizeof(mqtt_send_json_str));
    json_len = network_mqtt_get_msg_header(mqtt_send_json_str);
    json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len, 
            ",\"c\":%d,\"mi\":\"%d\",\"m\":{\"vaild\":1,\"net_type\":\"EC800M\",\"time\":%d,\"longitude\":%s,\"latitude\":%s,\"altitude\":%s",
            ES_PROTO_DEV_UPLOAD_STATUS,
            es_time_get_timestamp(),
            es_time_get_timestamp(),
            gps_info->longitude, 
            gps_info->latitude,
            gps_info->altitude);

    if (0 == gps_info->spkm[0]) {
        json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len, ",\"%s\":0", "spkm");
    } else {
        json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len, ",\"%s\":%s", "spkm", gps_info->spkm);
    }

    if (0 == gps_info->cog[0]) {
        json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len, ",\"%s\":0", "cog");
    } else {
        json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len, ",\"%s\":%s", "cog", gps_info->cog);
    }

    if (0 == gps_info->fix[0]) {
        json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len, ",\"%s\":0", "fix");
    } else {
        json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len, ",\"%s\":%s", "fix", gps_info->fix);
    }
    mqtt_send_json_str[json_len] = '}';
    json_len++;
    mqtt_send_json_str[json_len] = '}';
    json_len++;

    mqtt_send_json_str[json_len] = 0;
    network_mqtt_debug("upload status");

    if (ES_RET_SUCCESS != network_mqtt_send((const ES_BYTE *)mqtt_send_json_str, (ES_U16)json_len)) {
        return ES_RET_FAILURE;
    }
    
    return ES_RET_SUCCESS;
}

static PT_THREAD(network_mqtt_upload_status(struct pt *pt))
{
    static es_gps_info_t gps_info;
    ES_U32 limit_speed = 0;
    ES_U32 now_speed = 0;

    PT_BEGIN(pt);

    if ((es_time_get_timestamp() - last_upload_status_time) < ES_MQTT_UPLOAD_STATUS_TIME_SEC) {
        goto END;
    }

    memset(&gps_info, 0x00, sizeof(gps_info));
    if (ES_RET_SUCCESS != es_hal_lte_get_gps(&gps_info)) {
        last_upload_status_time =last_upload_status_time + (ES_MQTT_UPLOAD_STATUS_TIME_SEC>>1);
        goto END;
    }

    network_mqtt_debug("upload status");
    mqtt_doing_send = ES_TRUE;
    network_mqtt_reset_send_param(ES_PROTO_DEV_UPLOAD_STATUS, 5*1000);
    if (ES_RET_SUCCESS != network_mqtt_send_status(&gps_info)) {
        mqtt_status = ES_MQTT_STATUS_SYNC_INIT;
        goto END;
    }

    PT_SEM_INIT(&sem_mqtt_resp, 0);
    PT_SEM_WAIT(pt, &sem_mqtt_resp);
    if (mqtt_wait_resp != mqtt_now_resp) {
        mqtt_status = ES_MQTT_STATUS_SYNC_INIT;
        goto END;
    }
    last_upload_status_time = es_time_get_timestamp();

    limit_speed = es_spec_car_get_limit_speed();
    if (limit_speed > 0 && 0 != gps_info.spkm[0]) {
        now_speed = atoi(gps_info.spkm);
        if (limit_speed <= now_speed) {
            if (upload_event_count < 3) {
                upload_event_count++;
                // play over speed
                es_voice_play(ES_VOICE_21_OVER_SPEED);
                // upload 
                es_network_mqtt_upload_ai_event(AI_EVENT_OVER_SPEED, 0, ES_NULL, gps_info.spkm);
            }
        }
        else
            upload_event_count = 0;
    }

END:
    mqtt_doing_send = ES_FALSE;
    PT_END(pt);
}


#define CAR_BASE_TIME (4102415999) /* 2099-12-31 23:59:59 */
static ES_S32 network_mqtt_send_upload_cfg(ES_VOID)
{
    ES_S32 json_len = 0;
#if ES_4GLTE_ENABLE
    // const es_hal_lte_cell_t *cell_info = ES_NULL;
    const ES_CHAR *iccid = ES_NULL;
    const ES_CHAR *imei = ES_NULL;
    // ES_S32 rssi;
#endif

    json_len = network_mqtt_get_msg_header(mqtt_send_json_str);
    json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len, ",\"c\":%d,\"resp\":1,\"mi\":\"%d\"",
            ES_PROTO_DEV_UPLOAD_DEVICE_INFO, es_time_get_timestamp());
    json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len, ",\"m\":{");
    json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len, "\"%s\":%d", "fmver", ES_SYS_SVER_VAL);
    json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len, ",\"%s\":%d", "bind", mf_brd.cfg.factory_flag?0:1);
    json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len, ",\"%s\":%d", "brd_type", ES_BRD_TYPE);
    if (0 == mf_brd.cfg.car_num[0]) {
        json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len, ",\"%s\":\"\"", "car_num");
        json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len, ",\"%s\":%ld", "car_expire", CAR_BASE_TIME);
    } else {
        json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len, ",\"%s\":\"%s\"", "car_num", mf_brd.cfg.car_num);
        json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len, ",\"%s\":%d", "car_expire", mf_brd.cfg.car_expire);
    }
#if ES_4GLTE_ENABLE
    // cell_info = es_hal_lte_get_cell_info();
    // if (ES_NULL == cell_info) {
    //     json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len, ",\"%s\":\"%s\"", "cell_lac", "0");
    //     json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len, ",\"%s\":\"%s\"", "cell_ci", "0");
    // } else {
    //     json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len, ",\"%s\":\"%s\"", "cell_lac", cell_info->lac);
    //     json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len, ",\"%s\":\"%s\"", "cell_ci", cell_info->ci);
    // }

    iccid = es_hal_lte_get_iccid();
    if (ES_NULL == iccid) {
        json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len, ",\"%s\":\"\"", "iccid");
    } else {
        json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len, ",\"%s\":\"%s\"", "iccid", iccid);
    }

    imei = es_hal_lte_get_imei();
    if (ES_NULL == imei) {
        json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len, ",\"%s\":\"\"", "imei");
    } else {
        json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len, ",\"%s\":\"%s\"", "imei", imei);
    }

    // rssi = es_network_get_rssi()&0xFF;
    // if (0 == rssi) {
    //     json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len, ",\"%s\":\"\"", "rssi");
    // } else {
    //     json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len, ",\"%s\":\"%d\"", "rssi", rssi);
    // }

    json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len, ",\"%s\":%d", "relay_open_vol", mf_brd.cfg.relay.pol);
#endif

    json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len, ",\"%s\":%d", "safety_detect", mf_brd.cfg.safetybelt_detect);
    json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len, ",\"%s\":%d", "face_detect", mf_brd.cfg.working_face_detect);
    json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len, ",\"%s\":%d", "position_detect",  mf_brd.cfg.posture_detect);

    json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len, "}}");
    return network_mqtt_send((const ES_BYTE *)mqtt_send_json_str, (ES_U16)json_len);
}


#if MQTT_ENABLE_FACE_FEA_SHA256
static ES_S32 network_mqtt_send_upload_face_feature(ES_VOID)
{
    ES_BYTE sha256[32] = {0};
    ES_CHAR str_sha256[64 + 1];
    ES_S32 json_len = 0;

    es_task_param_t task_param;

    task_param.type = ES_TASK_FACE_FEATRUE_GET_SHA256;
    task_param.param = (ES_VOID *)sha256;
    task_param.timeout = 0;
    es_task_queue_push_wait(&task_param);
    es_utils_byte_to_hex_str(str_sha256, sha256, 32);

    json_len = network_mqtt_get_msg_header(mqtt_send_json_str);
    json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len, ",\"c\":%d,\"resp\":1,\"mi\":\"%d\"",
            ES_SERV_PROTO_UPLOAD_FACE_FEA_SHA256, es_time_get_timestamp());
    json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len, ",\"m\":{");
    json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len, "\"%s\":%d", "code", 1);
    json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len, ",\"%s\":%d", "version", 2);
    json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len, ",\"%s\":\"%s\"", "mac", mqtt_id);
    json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len, ",\"%s\":\"%s\"", "sha256", str_sha256);
    json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len, "}}");
    return network_mqtt_send((const ES_BYTE *)mqtt_send_json_str, (ES_U16)json_len);
}
#endif

static ES_S32 network_mqtt_send_resp(ES_U32 cmd, ES_U32 mi)
{
    ES_S32 json_len = 0;

    json_len = network_mqtt_get_msg_header(mqtt_send_json_str);
    json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len, 
            ",\"c\":%d,\"mi\":\"%d\",\"m\":{\"ret\":0,\"des\":\"success\"}}",
            cmd, mi);
    return network_mqtt_send((const ES_BYTE *)mqtt_send_json_str, (ES_U16)json_len);
}

static ES_VOID network_mqtt_sub_cb(const ES_CHAR *topic, const ES_BYTE *data, ES_U16 data_len)
{
    ES_S32 cmd;
    ES_U32 mi;

    cmd = es_serv_proto_parse((const ES_CHAR *)data, &mi);
    if (ES_PROTO_SERV_DEV_MAX == cmd) {
        return;
    }
    mqtt_now_resp = (ES_U16)cmd;
    if (ES_PROTO_SERV_PUSH_CAR_INFO == cmd || 
        ES_PROTO_SERV_PUSH_ADD_DRIVER_INFO == cmd ||
        ES_PROTO_SERV_PUSH_CTL_LOCK == cmd ||
        ES_PROTO_SERV_PUSH_DEL_DRIVER_INFO == cmd ||
        ES_PROTO_SERV_PUSH_UPDATE_DRIVER_INFO == cmd ||
        ES_PROTO_SERV_PUSH_IMAGE_CAPTRUE == cmd ||
        ES_PROTO_SERV_PUSH_DYNAMIC_QR_CODE == cmd ||
        ES_PROTO_SERV_PUSH_EMERHENCY_CONTROL == cmd ||
        ES_PROTO_SERV_PLAY_VOICE == cmd) {
        network_mqtt_send_resp((ES_U32)cmd, mi);
    }

    network_mqtt_debug("mqtt_now_resp:%d", mqtt_now_resp);
}


static PT_THREAD(network_mqtt_init(struct pt *pt))
{
    static ES_U32 last_time = 0;

    PT_BEGIN(pt);
    mqtt_status = ES_MQTT_STATUS_SYNC_INIT;

    if (ES_RET_SUCCESS != es_time_check_timeout_ms(&last_time, 1000)) {
        goto END;
    }

    if (ES_OTA_STATUS_IDLE != es_ota_get_status()) {
        goto END;
    }

    mqtt_doing_send = ES_TRUE;
    
    // 1 sync time from server
    network_mqtt_reset_send_param(ES_PROTO_DEV_SYNC_TIME, 5*1000);
    if (ES_RET_SUCCESS != network_mqtt_send_sync_time()) {
        goto END;
    }

    PT_SEM_INIT(&sem_mqtt_resp, 0);
    PT_SEM_WAIT(pt, &sem_mqtt_resp);
    if (mqtt_wait_resp != mqtt_now_resp) {
        network_mqtt_error("sync time fail, mqtt_wait_resp:%d,mqtt_now_resp:%d", mqtt_wait_resp, mqtt_now_resp);
        goto END;
    }
    last_heartbeat_time = es_time_get_timestamp();

    // 2 upload device config
    network_mqtt_reset_send_param(ES_PROTO_DEV_UPLOAD_DEVICE_INFO, 3000);
    if (ES_RET_SUCCESS != network_mqtt_send_upload_cfg()) {
        goto END;
    }

    PT_SEM_INIT(&sem_mqtt_resp, 0);
    PT_SEM_WAIT(pt, &sem_mqtt_resp);
    if (mqtt_wait_resp != mqtt_now_resp) {
        network_mqtt_error("upload device config fail, mqtt_wait_resp:%d,mqtt_now_resp:%d", mqtt_wait_resp, mqtt_now_resp);
        goto END;
    }

    mqtt_status = ES_MQTT_STATUS_READY;

END:
    mqtt_doing_send = ES_FALSE;
    PT_END(pt);
}


ES_S32 es_network_mqtt_init(ES_VOID)
{
    PT_INIT(&pt_mqtt_send);
    PT_SEM_INIT(&sem_mqtt_resp, 0);
    mqtt_status = ES_MQTT_STATUS_SYNC_INIT;

    if (ES_RET_SUCCESS != es_hal_lte_register_mqtt_sub_cb(network_mqtt_sub_cb)) {
        return ES_RET_SUCCESS;
    }

    return ES_RET_SUCCESS;
}

ES_VOID es_network_mqtt_task(ES_VOID)
{
    if (ES_NETWORK_CONNECTED != es_network_get_status()) {
        return;
    }

    if (0 == mqtt_id[0]) {
        es_hal_lte_get_mqtt_id(mqtt_id);
    } else if (mqtt_status < ES_MQTT_STATUS_READY) {
        network_mqtt_init(&pt_mqtt_send);
    } else {
        // ready,
        network_mqtt_upload_status(&pt_mqtt_send);
    }

    network_mqtt_send_sem_check();
}

ES_S32 es_network_mqtt_send(const ES_BYTE *data, ES_U16 data_len)
{
    if (ES_NETWORK_CONNECTED != es_network_get_status()) {
        return ES_RET_FAILURE;
    }

    return ES_RET_SUCCESS;
}

ES_S32 es_network_mqtt_upload_pass_log(const ES_BYTE *uid, ES_U32 timestamp, ES_U8 type, ES_U32 expire, ES_U32 status)
{
    ES_CHAR img_url[48] = {0};
    ES_S32 json_len = 0;
    ES_CHAR uid_str[FACEDB_UID_LEN+1] = {0};

    if (0 == mqtt_id[0]) {
        return ES_RET_FAILURE;
    }
    network_mqtt_get_jpg_name(img_url, timestamp);
    es_utils_byte_to_hex_str(uid_str, uid, FACEDB_UID_LEN>>1);

    json_len = network_mqtt_get_msg_header(mqtt_send_json_str);

    if (0 == mf_brd.cfg.car_num[0]) {
        json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len, 
                ",\"c\":%d,\"mi\":\"%d\",\"m\":{\"uid\":\"%s\",\"mac\":\"%s\",\"url\":\"%s\",\"car_num\":\"\",\"expire\":0,\"status\":2}}",
            ES_PROTO_DEV_UPLOAD_DRIVER_INFO,
            es_time_get_timestamp(),
            uid_str,
            mqtt_id,
            img_url
            );
    } else {
        json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len, 
                ",\"c\":%d,\"mi\":\"%d\",\"m\":{\"uid\":\"%s\",\"mac\":\"%s\",\"url\":\"%s\",\"car_num\":\"%s\",\"expire\":%d,\"status\":%d}}",
            ES_PROTO_DEV_UPLOAD_DRIVER_INFO,
            es_time_get_timestamp(),
            uid_str,
            mqtt_id,
            img_url,
            mf_brd.cfg.car_num,
            expire,
            status
            );
    }

    return network_mqtt_send((const ES_BYTE *)mqtt_send_json_str, (ES_U16)json_len);
}

ES_S32 es_network_mqtt_upload_offline_log(const ES_CHAR *json_data, ES_U32 json_data_len)
{
#if ES_PASSLOG_ENABLE
    ES_S32 json_len = 0;

    if (0 == mqtt_id[0]) {
        return ES_RET_FAILURE;
    }

    json_len = network_mqtt_get_msg_header(mqtt_send_json_str);
    json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len, 
                ",\"c\":%d,\"mi\":\"%d\",\"m\":{\"records\":%s}}",
            ES_PROTO_DEV_UPLOAD_OFFLINE_RECORDS,
            es_time_get_timestamp(),
            json_data);

    return network_mqtt_send((const ES_BYTE *)mqtt_send_json_str, (ES_U16)json_len);
#else
    return ES_RET_FAILURE;
#endif
}

ES_S32 es_network_mqtt_upload_ai_event(ES_U32 event, ES_U32 cam_id, const ES_CHAR *pic_url, const ES_CHAR *speed)
{
    ES_S32 json_len = 0;
    // ES_CHAR car_num[ES_FLASH_CAR_NUM_LEN] = {0};

    if (0 == mqtt_id[0]) {
        network_mqtt_debug("mqtt id is null");
        return ES_RET_FAILURE;
    }

    memset(mqtt_send_json_str, 0x00, sizeof(mqtt_send_json_str));
    json_len = network_mqtt_get_msg_header(mqtt_send_json_str);
    // es_flash_dev_cfg_get_vechicle_num(car_num);

    json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len,
                            ",\"c\":%d,\"mi\":\"%d\",\"m\":{\"imei\":\"%s\",\"event\":%d,\"cam_id\":%d",
                            ES_PROTO_DEV_UPLOAD_AI_EVENT,
                            es_time_get_timestamp(),
                            mqtt_id,
                            event,
                            cam_id);

    json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len,
                                ",\"car_num\":\"\"");

    if (ES_NULL == speed) {
        json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len,
                                ",\"speed\":\"\"");
    } else {
        json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len,
                                ",\"speed\":\"%s\"", speed);
    }

    if (ES_NULL == pic_url) {
        json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len,
                                ",\"pic\":\"\"");
    } else {
        json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len,
                                ",\"pic\":\"%s\"", pic_url);
    }
    json_len += es_snprintf((ES_CHAR *)(mqtt_send_json_str+json_len), MQTT_SEND_JSON_STR_LEN-json_len,
                                "}}");


    return network_mqtt_send((const ES_BYTE *)mqtt_send_json_str, (ES_U16)json_len);
}


#endif
