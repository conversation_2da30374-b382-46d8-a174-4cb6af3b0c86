#include "es_inc.h"

#if (ES_UI_TYPE == ES_UI_TYPE_K35V_240_320_GPS)
#include "facelib_inc.h"

// #define ES_UI_CAM_DEBUG
#ifdef ES_UI_CAM_DEBUG
#define es_ui_cam_debug es_log_info
#define es_ui_cam_error es_log_error
#else
#define es_ui_cam_debug(...)
#define es_ui_cam_error(...)
#endif

#if 1//(ES_CAM_WIDTH == ES_CAM_WIDTH) && (ES_CAM_HEIGHT == ES_UI_CAM_HEIGHT)
#define CAM_IMAGE_RESIZE            (0)
#else
#define CAM_IMAGE_RESIZE            (1)
#endif

#define cam_image   (mf_cam.rgb_image[mf_cam.rgb_buf_index])

static lv_obj_t *lv_img_cam = ES_NULL;
static ES_BYTE *cam_display_image = ES_NULL;
static lv_img_dsc_t cam_dsc = {
        .header.always_zero = 0,
        .header.w = ES_UI_CAM_WIDTH,
        .header.h = ES_UI_CAM_HEIGHT,
        .data_size = (ES_UI_CAM_WIDTH * ES_UI_CAM_HEIGHT) * (LV_COLOR_DEPTH / 8),
        .header.cf = LV_IMG_CF_TRUE_COLOR,
    };

extern lv_disp_t *cam_disp;

ES_S32 es_ui_cam_init(ES_VOID)
{
    ES_BYTE *mem_ptr = ES_NULL;
    mem_ptr = (ES_BYTE *)es_malloc(ES_UI_CAM_WIDTH*ES_UI_CAM_HEIGHT*2+255);
    if (NULL == mem_ptr) {
        es_ui_cam_debug("no memory for cam display");
        return ES_RET_FAILURE;
    }
    cam_display_image = (uint8_t*)(((uintptr_t)mem_ptr+255)&(~255));

    // lv_img_cam = lv_img_create(lv_scr_act());
    lv_img_cam = lv_img_create(lv_disp_get_scr_act(cam_disp));
    lv_obj_align(lv_img_cam, LV_ALIGN_TOP_LEFT, 0, 0);
    
    return ES_RET_SUCCESS;
}


ES_S32 es_ui_cam_update(ES_BOOL only_update)
{
#if CAM_IMAGE_RESIZE
    image_t image_src =  {.pixel = 2, .width = ES_CAM_WIDTH, .height = ES_CAM_HEIGHT, .addr = cam_image};
	image_t image_dst =  {.pixel = 2, .width = ES_UI_CAM_WIDTH, .height = ES_UI_CAM_HEIGHT, .addr = cam_display_image};
#endif

    if (!only_update) {
        convert_rgb565_order((uint16_t *)cam_image, MF_CAM_W, MF_CAM_H);
        image_rgb565_roate_right90_nobuf((uint16_t *)cam_image, MF_CAM_W, MF_CAM_H);
#if CAM_IMAGE_RESIZE
        image_resize_565(&image_src, &image_dst);
#else
        memcpy(cam_display_image, cam_image, (ES_CAM_WIDTH*ES_CAM_HEIGHT)<<1);
#endif
    }
    cam_dsc.data = cam_display_image;
    lv_img_set_src(lv_img_cam, &cam_dsc);
    return ES_RET_SUCCESS;
}


ES_S32 es_ui_cam_show(ES_BOOL show)
{
    if (show) {
        lv_obj_clear_flag(lv_img_cam, LV_OBJ_FLAG_HIDDEN);
    } else {
        lv_obj_add_flag(lv_img_cam, LV_OBJ_FLAG_HIDDEN);
    }
    return ES_RET_SUCCESS;
}

ES_S32 es_ui_cam2buf(ES_VOID)
{
#if ES_CAM_DIR
    // convert_rgb565_order((uint16_t *)cam_image, ES_CAM_WIDTH, ES_CAM_HEIGHT); //1.1ms
    // image_rgb565_roate_right90_nobuf((uint16_t*)cam_image, ES_CAM_WIDTH, ES_CAM_HEIGHT); //7.3ms
    // convert_rgb565_order((uint16_t *)cam_image, ES_CAM_WIDTH, ES_CAM_HEIGHT); //1.1ms
#endif
    return ES_RET_SUCCESS;
}

#endif