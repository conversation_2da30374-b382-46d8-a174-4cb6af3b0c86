/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_network_lte.h
** bef: define the interface for lte network. 
** auth: lines<<EMAIL>>
** create on 2020.12.21
*/

#ifndef _ES_NETWORK_LTE_H_
#define _ES_NETWORK_LTE_H_

#ifdef __cplusplus 
extern "C" { 
#endif

#include "es_types.h"


ES_S32 es_network_lte_init(ES_VOID);

ES_VOID es_network_lte_task(ES_VOID);

ES_S32 es_network_lte_get_status(ES_VOID);

#ifdef __cplusplus 
}
#endif

#endif