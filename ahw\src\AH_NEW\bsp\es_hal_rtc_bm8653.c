#include "es_inc.h"

#define BM8563_I2C_ADDR         (0xA2)
#define RTC_BASE_YEAR           (2000)

#define ES_RET_RESET_YEAR       (2021)

#if ES_RTC_ENABLE
static const es_hal_i2c_cfg_t bm8563_cfg = {
    .i2c_id = I2C_DEVICE_2,
    .addr = BM8563_I2C_ADDR >> 1,
    .speed = 100 * 1000,
};
#endif

static void bm8563_write_byte(uint8_t addr, uint8_t data)
{
#if ES_RTC_ENABLE
    es_hal_i2c_write_byte(&bm8563_cfg, addr, data);
#endif
    return;
}

static uint8_t bm8563_read_byte(uint8_t addr)
{
#if ES_RTC_ENABLE
    uint8_t data;
    es_hal_i2c_read_byte(&bm8563_cfg, addr, &data);
    return data;
#else
    return 0;
#endif
}


ES_S32 es_hal_rtc_init(ES_VOID)
{
    es_hal_rtc_time_t rtc_time;
    ES_BOOL need_reset = ES_FALSE;

    es_memset(&rtc_time, 0x00, sizeof(rtc_time));
    if (ES_RET_SUCCESS != es_hal_rtc_read_time(&rtc_time)) {
        need_reset = ES_TRUE;
    }

    // printk("rtc init, year:%d\r\n", rtc_time.year);
    // printk("rtc init, mon:%d\r\n", rtc_time.mon);
    // printk("rtc init, mday:%d\r\n", rtc_time.mday);
    // printk("rtc init, hour:%d\r\n", rtc_time.hour);
    // printk("rtc init, min:%d\r\n", rtc_time.min);
    // printk("rtc init, sec:%d\r\n", rtc_time.sec);

    if (rtc_time.year < ES_RET_RESET_YEAR) {
        need_reset = ES_TRUE;
    }

    if (need_reset) {
        rtc_time.year = ES_RET_RESET_YEAR;
        rtc_time.mon = 1;
        rtc_time.mday = 1;
        rtc_time.hour = 20;
        rtc_time.min = 10;
        rtc_time.sec = 10;
    }

    rtc_timer_set(rtc_time.year, rtc_time.mon, rtc_time.mday, rtc_time.hour, rtc_time.min, rtc_time.sec);
    return ES_RET_SUCCESS;
}

ES_S32 es_hal_rtc_read_time(es_hal_rtc_time_t *time)
{
    uint8_t buf[7];
    uint8_t vl = 0, tmp;

    for(uint8_t i = 0; i < 7; i++) {
        buf[i] = bm8563_read_byte(0x02 + i);
    }

    vl = (buf[0] & 0x80) >> 7;
    if(vl) {
        printk("detect rtc vl\r\n");
        bm8563_write_byte(0x02, buf[0] & 0x7F);
    }

    tmp = buf[0] & 0x7f;
    time->sec = ((tmp & 0xf0) >> 4) * 10 + (tmp & 0x0f);

    tmp = buf[1] & 0x7f;
    time->min = ((tmp & 0xf0) >> 4) * 10 + (tmp & 0x0f);

    tmp = buf[2] & 0x3f;
    time->hour = ((tmp & 0xf0) >> 4) * 10 + (tmp & 0x0f);

    tmp = buf[3] & 0x3f;
    time->mday = ((tmp & 0xf0) >> 4) * 10 + (tmp & 0x0f);

    tmp = (buf[4] & 0x07) + 1; /* [0-6] --> [1-7] */
    time->wday = ((tmp & 0xf0) >> 4) * 10 + (tmp & 0x0f);

    tmp = buf[5] & 0x1f;
    time->mon = ((tmp & 0xf0) >> 4) * 10 + (tmp & 0x0f);

    tmp = (buf[6] & 0xff);
    time->year = (((tmp & 0xf0) >> 4) * 10 + (tmp & 0x0f)) + ((buf[5] & 0x80) ? 1900 : 2000);
    return ES_RET_SUCCESS;
}

ES_S32 es_hal_rtc_write_time(const es_hal_rtc_time_t *time)
{
    uint8_t weekday, year, high, low, tmp[11];

    if (time->sec > 59) {
        return ES_RET_FAILURE;
    }

    if (time->min > 59) {
        return ES_RET_FAILURE;
    }

    if (time->hour > 23) {
        return ES_RET_FAILURE;
    }

    if (time->mday < 1 || time->mday > 31) {
        return ES_RET_FAILURE;
    }

    if (time->mon < 1 || time->mon > 12) {
        return ES_RET_FAILURE;
    }

    if (time->wday < 1 || time->wday > 7) {
        return ES_RET_FAILURE;
    }

    year = (uint8_t)(time->year - RTC_BASE_YEAR);
    if (year > 99) {
        return ES_RET_FAILURE;
    }

    weekday = time->wday - 1; /* BM8563 is diff to GM1302 */
    ///////////////////////////////////////////////////////////////////////////////
    high = time->sec / 10;
    low = time->sec % 10;
    tmp[0] = (0 << 7) | ((high & 0x7) << 4) | (low & 0x0f);

    high = time->min / 10;
    low = time->min % 10;
    tmp[1] = ((high & 0x7) << 4) | (low & 0x0f);

    high = time->hour / 10;
    low = time->hour % 10;
    tmp[2] = ((high & 0x3) << 4) | (low & 0x0f);

    high = time->mday / 10;
    low = time->mday % 10;
    tmp[3] = ((high & 0x3) << 4) | (low & 0x0f);

    tmp[4] = (weekday & 0x0f);

    high = time->mon / 10;
    low = time->mon % 10;
    tmp[5] = (0 << 7) | ((high & 0x1) << 4) | (low & 0x0f);

    high = year / 10;
    low = year % 10;
    tmp[6] = ((high & 0x0f) << 4) | (low & 0x0f);

    /* ALARM not use */
    tmp[7] = 0x80;
    tmp[8] = 0x80;
    tmp[9] = 0x80;
    tmp[10] = 0x80;
    ///////////////////////////////////////////////////////////////////////////////
    for(uint8_t i = 0; i < 11; i++) {
        bm8563_write_byte(0x02 + i, tmp[i]);
    }

    rtc_timer_set(time->year, time->mon, time->mday, time->hour, time->min, time->sec);
    return ES_RET_SUCCESS;
}

