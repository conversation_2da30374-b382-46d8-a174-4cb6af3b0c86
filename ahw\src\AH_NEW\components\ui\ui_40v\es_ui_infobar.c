#include "es_inc.h"

#if (ES_UI_TYPE == ES_UI_TYPE_K40V_480_800)
#include "es_ui_img_res.h"
#include "facelib_inc.h"

// #define ES_UI_INFOBAR_DEBUG
#ifdef ES_UI_INFOBAR_DEBUG
#define es_ui_infobar_debug es_log_info
#define es_ui_infobar_error es_log_error
#else
#define es_ui_infobar_debug(...)
#define es_ui_infobar_error(...)
#endif


static lv_obj_t *lv_infobar_bg = ES_NULL;
static lv_timer_t *infobar_timer = NULL;

static ES_CHAR sver_str_buf[20] = {0};
static ES_CHAR time_str_buf[32] = {0};
static ES_CHAR date_str_buf[48] = {0};
static ES_CHAR face_num_str_buf[16] = {0};
static ES_CHAR netowrk_str_buf[32] = {0};

// static ES_CHAR *wday_str[7] = {"Mon", "Tu<PERSON>", "Wed", "Thu", "Fri", "Sat", "Sun"};

extern lv_disp_t *bar_disp;

static ES_VOID es_ui_infobar_create_widgets(ES_VOID)
{
    // info bar 
    // lv_infobar_bg = lv_obj_create(lv_scr_act());
    lv_infobar_bg = lv_obj_create(lv_disp_get_scr_act(bar_disp));
    lv_obj_remove_style_all(lv_infobar_bg);
    lv_obj_set_size(lv_infobar_bg, ES_UI_BAR_WIDTH, ES_UI_BAR_HEIGHT);
    lv_obj_align(lv_infobar_bg, LV_ALIGN_TOP_LEFT, 0, 0);
    // lv_obj_set_style_bg_color(lv_infobar_bg, lv_palette_main(LV_PALETTE_GREEN), 0);
}


static void infobar_update(void)
{
    lv_obj_t *obj;
    ES_U8 network_status = 0;
    es_time_t ui_date_time;

    lv_obj_clean(lv_infobar_bg);
    es_time_get_now(&ui_date_time);

#if ES_OTA_ENABLE
    if (ES_OTA_STATUS_IDLE != es_ota_get_status()) {
        obj = lv_label_create(lv_infobar_bg);
        lv_obj_add_style(obj, (lv_style_t *)es_ui_font_get_time(), 1);
        lv_obj_align(obj, LV_ALIGN_TOP_MID, 0, 10);
        lv_label_set_recolor(obj, ES_TRUE);
        es_snprintf(time_str_buf, sizeof(time_str_buf), "#FF0000 OTA %d ...#",  es_ota_get_percent());
        lv_label_set_text(obj, time_str_buf);
        return;
    }
#endif
    obj = lv_label_create(lv_infobar_bg);
    lv_obj_add_style(obj, (lv_style_t *)es_ui_font_get_common(), 0);
    lv_obj_align(obj, LV_ALIGN_BOTTOM_RIGHT, 0, 0);
    lv_label_set_recolor(obj, ES_TRUE);
    es_snprintf(sver_str_buf, sizeof(sver_str_buf), "#FFFFFF %s#", ES_SYS_SVER_STR);
    lv_label_set_text_static(obj, sver_str_buf);

    obj = lv_label_create(lv_infobar_bg);
    lv_obj_add_style(obj, (lv_style_t *)es_ui_font_get_common(), 0);
    lv_obj_align(obj, LV_ALIGN_TOP_MID, 0, 10);
    lv_label_set_recolor(obj, ES_TRUE);
    es_snprintf(time_str_buf, sizeof(time_str_buf), "#FFFFFF %02d:%02d:%02d#", ui_date_time.hour, ui_date_time.min, ui_date_time.sec);
    lv_label_set_text_static(obj, time_str_buf);
    // es_ui_infobar_debug("time:%s", time_str_buf);
    

    obj = lv_label_create(lv_infobar_bg);
    lv_obj_add_style(obj, (lv_style_t *)es_ui_font_get_common(), 0);
    lv_obj_align(obj, LV_ALIGN_TOP_MID, 0, 50);
    lv_label_set_recolor(obj, ES_TRUE);
    es_snprintf(date_str_buf, sizeof(date_str_buf), "#FFFFFF %04d.%02d.%02d#", 
            ui_date_time.year, ui_date_time.mon, ui_date_time.mday);
    lv_label_set_text_static(obj, date_str_buf);

    if (!mf_brd.cfg.factory_flag) {
        obj = lv_label_create(lv_infobar_bg);
        lv_obj_add_style(obj, (lv_style_t *)es_ui_font_get_common(), 0);
        lv_obj_align(obj, LV_ALIGN_BOTTOM_LEFT, 0, 0);
        lv_label_set_recolor(obj, ES_TRUE);
        es_snprintf(face_num_str_buf, sizeof(face_num_str_buf), "#FFFFFF %04d#", mf_facedb.num());
        lv_label_set_text_static(obj, face_num_str_buf);

        obj = lv_label_create(lv_infobar_bg);
        lv_obj_add_style(obj, (lv_style_t *)es_ui_font_get_common(), 1);
        lv_obj_align(obj, LV_ALIGN_BOTTOM_MID, 0, 0);
        lv_label_set_recolor(obj, ES_TRUE);
        network_status = es_network_get_status();
        if (ES_NETWORK_INIT == network_status) {
            es_snprintf(netowrk_str_buf, sizeof(netowrk_str_buf), "#FF0000 Initializing#");
        } else if (ES_NETWORK_CONNECTING == network_status) {
            es_snprintf(netowrk_str_buf, sizeof(netowrk_str_buf), "#FF0000 Connecting %d#", es_network_get_rssi()&0xFF);
        } else if (ES_NETWORK_CONNECTED == network_status) {
            es_snprintf(netowrk_str_buf, sizeof(netowrk_str_buf), "#008000 Connected %d#", es_network_get_rssi()&0xFF);
        } else {
            es_snprintf(netowrk_str_buf, sizeof(netowrk_str_buf), "#FF0000 Newtork Bad %d#", es_network_get_rssi()&0xFF);
        }
        lv_label_set_text_static(obj, netowrk_str_buf);
    }

}

static void infobar_timer_cb(lv_timer_t *timer)
{
    infobar_update();
}


ES_S32 es_ui_infobar_init(ES_VOID)
{
    es_ui_infobar_create_widgets();
    infobar_timer = lv_timer_create(infobar_timer_cb, 1000, NULL);
    if (ES_NULL == infobar_timer) {
        return ES_RET_FAILURE;
    }
    return ES_RET_SUCCESS;
}

ES_S32 es_ui_infobar_show(ES_BOOL show)
{
    lv_obj_clear_flag(lv_infobar_bg, LV_OBJ_FLAG_HIDDEN);
    return ES_RET_SUCCESS;
}

#endif
