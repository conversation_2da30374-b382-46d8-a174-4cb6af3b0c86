#include "es_inc.h"

#define WDT_RESET_NOW           (1)

static int wdt_irq_core1(void *ctx)
{
#if WDT_RESET_NOW
    printk("wdt_irq_core1\r\n");
    es_hal_sys_reset();
#else
    static int wdt1_irq_cnt = 0;
    wdt1_irq_cnt++;
    if(wdt1_irq_cnt < 2) {
        printk("wdt_irq_core1 1\r\n");
        wdt_clear_interrupt(1);
    } else {
        printk("wdt_irq_core1 2\r\n");
        es_hal_sys_reset();
    }
#endif
    return 0;
}

static int wdt_irq_core0(void *ctx)
{
#if WDT_RESET_NOW
    printk("wdt_irq_core0\r\n");
    es_hal_sys_reset();
#else
    static int s_wdt_irq_cnt = 0;
    s_wdt_irq_cnt++;
    if(s_wdt_irq_cnt < 2) {
        printk("wdt_irq_core0 1\r\n");
        wdt_clear_interrupt(0);
    } else {
        printk("wdt_irq_core0 2\r\n");
        es_hal_sys_reset();
    }
#endif
    return 0;
}

ES_VOID es_wdg_init(ES_U8 id, ES_U32 sec)
{
    if(id == 0)
    {
        wdt_start(0, sec * 1000, wdt_irq_core0);
    } else
    {
        wdt_start(1, sec * 1000, wdt_irq_core1);
    }
}

ES_VOID es_wdg_feed(ES_U8 id)
{
    wdt_feed(id);
}