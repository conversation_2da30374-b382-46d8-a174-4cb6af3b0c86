/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_crc.h
** bef: define the interface for crc. 
** auth: lines<<EMAIL>>
** create on 2022.01.15
*/

#ifndef _ES_CRC_H_
#define _ES_CRC_H_

#ifdef __cplusplus 
extern "C" { 
#endif

#include "es_types.h"

ES_U16 es_crc16(const ES_BYTE *data, ES_U32 len);

ES_U16 es_crc16_xmodem(const ES_BYTE *data, ES_U32 len);

ES_U8 es_crc8(const ES_BYTE *data, ES_U32 len);

ES_U32 es_crc32(const ES_BYTE *data, ES_U32 len);

#ifdef __cplusplus 
}
#endif

#endif
