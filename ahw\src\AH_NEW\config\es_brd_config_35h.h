/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_utils.h
** bef: define the interface for configure 
** auth: lines<<EMAIL>>
** create on 2020.06.27 
*/

#ifndef _ES_BRD_CONFIG_Q28_H_
#define _ES_BRD_CONFIG_Q28_H_

#ifdef __cplusplus 
extern "C" { 
#endif

// Col is 96
/////////////////////////////////////// Temperature Module ////////////////////////////////////
#define ES_TEMPERATURE_MODULE_ENABLE        (0)
#define ES_TEMPERATURE_READ_TIMEOUT_MS      (3*1000)


/////////////////////////////////////// Wireless 433 Module ///////////////////////////////////
#define ES_433_MODULE_ENABLE                (1)
#define ES_433_ENABLE_DINGDONG_DOORBELL     (1)
#if ES_433_MODULE_ENABLE
#define ES_433_MODULE_SEND_PIN              (13)
#define ES_433_MODULE_READ_PIN              (27)
#define ES_433_MODULE_SEND_HS_NUM           (28)
#define ES_433_MODULE_READ_HS_NUM           (13)
#endif


/////////////////////////////////////// door app //////////////////////////////////////////////
#define ES_APP_MODULE_SBD_ENABLE            (0)
#define ES_APP_DOOR_JWZH_ENABLE             (0)


/////////////////////////////////////// Cloud Protocol ////////////////////////////////////////
#define ES_CLOUD_PROTOCOL_MODULE_ENABLE     (1)


/////////////////////////////////////// Motor Module //////////////////////////////////////////
#define ES_MOTOR_MODULE_ENABLE              (0)
#if ES_MOTOR_MODULE_ENABLE
#define ES_MOTOR_AN1_GPIO_PIN               (27)
#define ES_MOTOR_AN2_GPIO_PIN               (28)
#define ES_MOTOR_AN1_HS_NUM                 (29)
#define ES_MOTOR_AN2_HS_NUM                 (30)
#endif


/////////////////////////////////////// NFC Module ////////////////////////////////////////////
#define ES_NFC_MODULE_ENABLE                (1)
#define ES_NFC_CHIP_NONE                    (0)
#define ES_NFC_CHIP_WS1850S                 (1)
#define ES_NFC_ID_DATA_LEN                  (10)
#define ES_NFC_GPIO_IRQ_PIN                 (20)
#define ES_NFC_GPIO_RST_PIN                 (21)
#define ES_NFC_GPIO_LED_PIN                 (0)
#define ES_NFC_GPIO_IRQ_HS_NUM              (29)
#define ES_NFC_GPIO_RST_HS_NUM              (30)
#define ES_NFC_GPIO_LED_HS_NUM              (11)
#if ES_NFC_MODULE_ENABLE
#define ES_NFC_CHIP_TYPE                    (ES_NFC_CHIP_WS1850S)
#define ES_NFC_I2C_READ_ADDR                (0x28)
#define ES_NFC_I2C_WRITE_ADDR               (0x28)
#define ES_NFC_I2C_SPEED                    (100 * 1000)
#define WS1850S_ENABLE_GPIO_RST             (1)
#else
#define ES_NFC_CHIP_TYPE                    (ES_NFC_CHIP_NONE)
#endif/*  */


/////////////////////////////////////// BLE Module ////////////////////////////////////////////
#define ES_BLE_MODULE_ENABLE                (1)

#define ES_BLE_HAL_WS8100                   (1) /* WS8100 */
#define ES_BLE_HAL_JWZH                     (2) /* JWZH module. */
#define ES_BLE_HAL_571                      (3)

#if ES_BLE_MODULE_ENABLE
#define ES_BLE_HAL_TYPE                     (ES_BLE_HAL_571)
#else
#define ES_BLE_HAL_TYPE                     (0)
#endif

#define ES_BLE_UART_ID                      (ES_UART_ID_1)

#define ES_BLE_PAYLOAD_GPIO_PIN             (26)
#define ES_BLE_PAYLOAD_GPIO_HS_NUM          (29)


/////////////////////////////////////// test module ///////////////////////////////////////////
#define ES_TEST_MODULE_ENABLE               (0)


/////////////////////////////////////// LCD ///////////////////////////////////////////////////
#define ES_LCD_DRIVER_TYPE                  (ES_LCD_DRIVER_ILI9488)
#define ES_LCD_DCX_HS_NUM                   (5)
#define ES_LCD_RST_HS_NUM                   (6)
#define ES_LCD_BL_HS_NUM                    (16)
#define ES_LCD_SPI_CS_HS_NUM                (17)
#define ES_LCD_CS_PIN                       (36)
#define ES_LCD_RST_PIN                      (37)
#define ES_LCD_DCX_PIN                      (38)
#define ES_LCD_SCK_PIN                      (39)
#define ES_LCD_BL_PIN                       (17)
#define ES_LCD_PWM_DEV_BL                   (PWM_DEVICE_0)
#define ES_LCD_PWDM_CHN_BL                  (PWM_CHANNEL_1)
#define ES_LCD_HMIRROR                      (1)
#define ES_LCD_VFLIP                        (1)
#define ES_LCD_XY_SWAP                      (1)
#define ES_LCD_DIR_PARAM                    ((ES_LCD_HMIRROR<<7) | (ES_LCD_VFLIP<<6) | (ES_LCD_XY_SWAP<<5))
#define ES_LCD_WIDTH                        (480)
#define ES_LCD_HEIGHT                       (320)

/////////////////////////////////////// Redefine Wechat config ////////////////////////////////
#undef CONFIG_ENABLE_EC200X
#define CONFIG_ENABLE_EC200X                (0)
#undef CONFIG_ENABLE_WIFI
#define CONFIG_ENABLE_WIFI                  (1)
#undef CONFIG_ENABLE_ETH5500
#define CONFIG_ENABLE_ETH5500				(0)


/////////////////////////////////////// IR LED ////////////////////////////////////////////////
#define ES_IR_PIN                           (9)
#define ES_IR_HS_NUM                        (1)
#define ES_IR_IO_OPEN_VAL                   (1)

/////////////////////////////////////// FLASH LED ////////////////////////////////////////////////
#define ES_LED_FLASH_PIN                    (10)
#define ES_LED_FLASH_IO_OPEN_VAL            (1)
#define ES_LED_FLASH_PWM_CHANNEL            (PWM_CHANNEL_0)
#define ES_LED_FLASH_PWM_DEV                (PWM_DEVICE_0)
#define ES_LED_FLASH_PWM_STRONG_DUTY        (0.5)
#define ES_LED_FLASH_PWM_WEAK_DUTY          (0.05)

/////////////////////////////////////// UI ////////////////////////////////////////////////////
#define ES_UI_WIDTH                         (ES_LCD_WIDTH)
#define ES_UI_HEIGHT                        (ES_LCD_HEIGHT)
#define ES_UI_CAM_WIDTH                     (372)
#define ES_UI_CAM_HEIGHT                    (320)
#define ES_UI_FACE_EDGE_LINE_WIDTH          (2) // pixel
#define ES_UI_FACE_EDGE_LINE_LENGTH         (10) // pixel


/////////////////////////////////////// Camera ////////////////////////////////////////////////
#define ES_CAM_WIDTH                        (320)
#define ES_CAM_HEIGHT                       (240)
#define ES_CAM_DIR                          (0) // direction
#define ES_CAM_HMIRROR                      (0) // hmirror
#define ES_CAM_VFLIP                        (0)// vflip


/////////////////////////////////////// Tuya Module ///////////////////////////////////////////
#define ES_TUYA_MODULE_ENABLE               (0)
#define ES_TUYA_UART_ID                     (ES_UART_ID_1)


#ifdef __cplusplus 
}
#endif
#endif