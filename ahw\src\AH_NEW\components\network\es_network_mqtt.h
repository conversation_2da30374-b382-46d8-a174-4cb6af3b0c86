/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_network_mqtt.h
** bef: define the interface for mqtt network. 
** auth: lines<<EMAIL>>
** create on 2022.02.26
*/

#ifndef _ES_NETWORK_MQTT_H_
#define _ES_NETWORK_MQTT_H_

#ifdef __cplusplus 
extern "C" { 
#endif

#include "es_types.h"


ES_S32 es_network_mqtt_init(ES_VOID);

ES_VOID es_network_mqtt_task(ES_VOID);

ES_S32 es_network_mqtt_send(const ES_BYTE *data, ES_U16 data_len);

ES_S32 es_network_mqtt_upload_pass_log(const ES_BYTE *uid, ES_U32 timestamp, ES_U8 type, ES_U32 expire, ES_U32 status);

ES_S32 es_network_mqtt_upload_offline_log(const ES_CHAR *json_data, ES_U32 json_len);

ES_S32 es_network_mqtt_upload_ai_event(ES_U32 event, ES_U32 cam_id, const ES_CHAR *pic_url, const ES_CHAR *speed);

#ifdef __cplusplus 
}
#endif

#endif