#ifndef _MF_FLOW_H
#define _MF_FLOW_H

#include <stdint.h>
#include "mf_constants.h"
#include "mf_facecb.h"


/*****************************************************************************/
// Enums & Macro
/*****************************************************************************/
#define kpu_buf (_IOMEM_ADDR(mf_cam.kpu_image[mf_cam.kpu_buf_index]))
#define rgb_buf (mf_cam.rgb_image[mf_cam.rgb_buf_index]) /* 在dualcam_irq中设置摄像头输出到cache段 */

typedef enum 
{
	MF_FLOW_DUALCAM_VIS2VIS=0,
	MF_FLOW_DUALCAM_VIS2IR,
	MF_FLOW_DUALCAM_IR2IR,
	MF_FLOW_SINGLE_VIS,
	MF_FLOW_SINGLE_IR,
	MF_FLOW_SINGLE_TRACK,
	MF_FLOW_NONE //not support
}mf_flow_type_t;

typedef void (*flow_cb_t)(void);

typedef struct 
{
	uint8_t idx;
	flow_cb_t cbs[5];
} flow_cb_list_t;
//
/*****************************************************************************/
// Types
/*****************************************************************************/

typedef struct
{
	//Private
	//Public var
	uint16_t init_flag;
	uint16_t type;
	uint16_t enable;         //1,全流程; 0,仅刷屏 
	uint32_t last_pass_t;    //0.1s unit
	mf_facecb_t* cb;
	flow_cb_list_t kpubuf_cb_list;
	flow_cb_list_t rgbbuf_cb0_list;	//旋转前
	flow_cb_list_t rgbbuf_cb1_list;	//旋转后
	//Const Public
	mf_err_t (*init)(mf_facecb_t* cb);
	void     (*deinit)(void);
	mf_err_t (*loop)(void);
	mf_err_t (*hash)(uint8_t* sha256); //数据库人脸hash
	mf_err_t (*reg_kpubuf_cb)(flow_cb_t cb);
	mf_err_t (*reg_rgbbuf_cb0)(flow_cb_t cb);
	mf_err_t (*reg_rgbbuf_cb1)(flow_cb_t cb);
} mf_flow_t;




/*****************************************************************************/
// Functions
/*****************************************************************************/
mf_err_t mf_flow_choose(mf_flow_type_t type);

/*****************************************************************************/
// Vars
/*****************************************************************************/
extern mf_flow_t mf_flow;

extern volatile uint8_t face_lib_flow_debug_en;

#endif