/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_resource.c
** bef: define the interface for resource.
** auth: lines<<EMAIL>>
** create on 2022.12.16
*/

#include "es_inc.h"

#if ES_RESOURCE_ENABLE
#include "mf_flash.h"

#define ES_RESOURCE_DEBUG
#ifdef ES_RESOURCE_DEBUG
#define es_resource_debug es_log_info
#define es_resource_error es_log_error
#else
#define es_resource_debug(...)
#define es_resource_error(...)
#endif


typedef struct {
    ES_U32 addr;
    ES_U32 size;
    ES_U32 crc32;
    ES_BYTE *data;
    ES_U32 timestramp;
} es_res_node_t;

typedef struct {
    ES_U32 magic;
    es_res_node_t node[ES_RESOURCE_MAX_COUNT];
} es_res_head_t;

static es_res_head_t resource_head;


ES_S32 es_resource_init(ES_VOID)
{
    es_memset(&resource_head, 0x00, sizeof(resource_head));
    mf_flash.read(ES_FLASH_CFG_ADDR, (uint8_t *)&resource_head, sizeof(resource_head));
    if (ES_RESOURCE_MAGIC != resource_head.magic) {
        es_memset(&resource_head, 0x00, sizeof(resource_head));
        resource_head.magic = ES_RESOURCE_MAGIC;
        if (0 != mf_flash.write(ES_FLASH_CFG_ADDR, (uint8_t *)&resource_head, sizeof(resource_head))) {
            es_resource_error("reset resource fail");
            return ES_RET_FAILURE;
        }
        es_resource_debug("resource reset");
    }

    es_resource_debug("resource init success");
    return ES_RET_SUCCESS;
}

ES_S32 es_resource_read(es_resource_type_t type, es_resource_data_t *res)
{
    
    return ES_RET_FAILURE;
}

ES_S32 es_resource_write(es_resource_type_t type, es_resource_data_t *res)
{
    return ES_RET_FAILURE;
}

#endif