/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_uart.h
** bef: define the interface ble hardware channel.
** auth: lines<<EMAIL>>
** create on 2020.05.06
*/

#ifndef _ES_BLE_HAL_H_
#define _ES_BLE_HAL_H_

#ifdef __cplusplus 
extern "C" { 
#endif

#include "es_types.h"

#define ES_BLE_RECV_DATA_LEN                (256)

typedef enum {
    ES_BLE_RESP_NONE,
    ES_BLE_RESP_CONN_OK,
    ES_BLE_RESP_CONN_FAIL,
    ES_BLE_RESP_SEND_OK,
    ES_BLE_RESP_SEND_FAIL,
    ES_BLE_RESP_DISCONN_OK,
    ES_BLE_RESP_DISCONN_FAIL,
} es_ble_hal_resp_e;

typedef struct {
    ES_BYTE *mac;
    ES_BYTE *serv_uuid;
    ES_BYTE *char_uuid;
} es_ble_conn_param_t;

typedef struct {
    ES_BYTE *mac;
    ES_BYTE *data;
    ES_U32 data_len;
} es_ble_send_data_t;

typedef struct {
    ES_BYTE mac[6];
    ES_BYTE data[ES_BLE_RECV_DATA_LEN];
    ES_U32 data_len;
} es_ble_recv_data_t;

ES_S32 es_hal_ble_init(ES_VOID);
ES_S32 es_hal_ble_slave_conn(const es_ble_conn_param_t *p);
ES_S32 es_hal_ble_slave_send(const es_ble_send_data_t *data);
ES_S32 es_hal_ble_master_send(const es_ble_send_data_t *data);
ES_S32 es_hal_ble_slave_disconn(const ES_BYTE *mac);
ES_S32 es_hal_ble_master_disconn(ES_VOID);  
// return value is es_ble_hal_resp_e.
ES_S32 es_hal_ble_get_resp(ES_VOID);
ES_S32 es_hal_ble_recv(es_ble_recv_data_t *p);
ES_S32 es_hal_ble_read_mac(ES_BYTE *mac);
ES_S32 es_hal_ble_is_connected(ES_VOID);

#ifdef __cplusplus 
}
#endif

#endif