/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_ota_dl.h
** bef: define the interface for ota download. 
** auth: lines<<EMAIL>>
** create on 2022.01.12
*/

#ifndef _ES_OTA_DL_H_
#define _ES_OTA_DL_H_

#ifdef __cplusplus 
extern "C" { 
#endif

#include "es_types.h"

ES_S32 es_ota_dl_init(ES_VOID);

ES_S32 es_ota_dl_set_url(const ES_CHAR *url);

ES_S32 es_ota_dl_set_md5(const ES_CHAR *md5);

ES_S32 es_ota_dl_set_version(const ES_CHAR *version);

ES_S32 es_ota_dl_set_version_with_number(ES_U32 version);

ES_VOID es_ota_dl_run(ES_VOID);

#ifdef __cplusplus 
}
#endif

#endif
