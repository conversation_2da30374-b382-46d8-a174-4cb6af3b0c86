**-----------------------------------Revised Version---------------------------------------
此文件用户无需关心（Users of this file do not need to care）



** 版  本: v2.5.6
** 日　期: 2020年12月16日
** 修改人员: 周猛
** 协议版本: v1.2.3
** 描　  述: 1：修改 mcu_get_dp_download_enum 和 mcu_get_dp_download_value 注释
			2：在 my_strcpy 函数中，增加入参判空检查
			3：在 wifi_protocol_init 函数中对 stop_update_flag 赋初值 DISABLE
			4：支持多个字节数据一次性存入串口接收缓存
			5：增加天气conditionNum参数
			6：增加免责声明



** 版  本: v2.5.5
** 日　期: 2020年5月28日
** 修改人员: 周猛
** 协议版本: v1.2.3
** 描　  述: 1：将一些与队列描述相关的变量和函数名改为串口接收描述
			2：修改了流服务处理相关函数
			3：增加语音模组VWXR2扩展功能，暂支持“播放/暂停” “蓝牙开关”功能
			4：增加产品信息包红外功能和低功耗开关选择字段
			5：增加主动获取天气服务的命令
			6：增加重置状态通知的命令



** 版  本: v2.5.4
** 日　期: 2020年4月15日
** 修改人员: 周猛
** 协议版本: v1.2.0
** 描　  述: 1：新增对多地图数据流数据传输的支持
			2：新增文件包下载功能
			3：配网模式的默认选择更改为特殊配网（防误触工作模式）
			4：修复data_handle函数中结果变量定义的问题
			5：修复了一些不一致的函数定义和声明
			6：修复了OTA数据处理程序中未初始化的长度变量
			7：修改wifi_uart_write_frame函数以添加传递的帧版本号参数
			8：修改天气服务字段以支持天气预报功能
			9：新增语音模块相关协议
			10：新增模块扩展服务
			11：新增蓝牙功能
			12：新增产品信息mt和n字段
			13：新增网络状态7
			14：新增代码注释格式，改成英文



** 版  本: v2.5.3 
** 日　期: 2019年11月26日
** 修改人员: 周猛
** 协议版本: v1.1.6
** 描　  述: 1:增加红外状态通知
           2:增加红外进入收发产测
           3:修复串口数据接收函数uart_receive_input的BUG(不能判断出其中一种缓存区满的情况)
           4:修改串口数据缓存区的数组名称和长度宏定义的名称

** 版  本: v2.5.2
** 日　期: 2019年7月5日
** 描　述: 1:增加WiFi功能性测试（连接指定路由）
           2:简化流服务过程，用户只需要调用流服务传输接口即可使用流服务
           3:增加同步上报指令
           4:增加获取当前wifi联网状态指令
           5:增加获取格林时间指令
           6:优化ota升级流程，启动ota升级时用户可以选择传输包大小
           7:增加获取模块mac地址指令

** 版  本: v2.5.1
** 日　期: 2018年10月27日
** 描　述: 1:默认关闭流服务功能
           2:增加03协议wifi状态宏定义
		   3:更新与修改部分函数注释

** 版  本: v2.5.0
** 日　期: 2018年4月18日
** 描　述: 1:协议版本改为0x03
           2:增加WIFI模组心跳关闭功能
           3:增加天气功能

** 版  本: v2.3.8
** 日　期: 2018年1月17日
** 描　述: 1:变量添加volatile防止编译器优化
           2:添加#error提示

** 版  本: v2.3.7
** 日　期: 2017年4月18日
** 描　述: 1:优化串口队列接收处理
		   
** 版  本: v2.3.6
** 日　期: 2016年7月21日
** 描　述: 1:修复获取本地时间错误
           2:添加hex_to_bcd转换函数
		   
** 版  本: v2.3.5
** 日　期: 2016年6月3日
** 描　述: 1:修改返回协议版本为0x01
           2:固件升级数据偏移量修改为4字节

** 版  本: v2.3.4
** 日　期: 2016年5月26日
** 描　述: 1:优化串口解析函数
           2:优化编译器兼容性,取消enum类型定义

** 版  本: v2.3.3
** 日　期: 2016年5月24日
** 描　述: 1:修改mcu获取本地时间函数
           2:添加wifi功能测试

** 版  本: v2.3.2
** 日　期: 2016年4月23日
** 描　述: 1:优化串口数据解析
           2:优化MCU固件升级流程
           3:优化上报流程

** 版  本: v2.3.1
** 日　期: 2016年4月15日
** 描　述: 1:优化串口数据解析

** 版  本: v2.3
** 日　期: 2016年4月14日
** 描　述: 1:支持MCU固件在线升级

** 版  本: v2.2
** 日　期: 2016年4月11日
** 描　述: 1:修改串口数据接收方式

** 版  本: v2.1
** 日　期: 2016年4月8日
** 描　述: 1:加入某些编译器不支持函数指针兼容选项

** 版  本: v2.0
** 日　期: 2016年3月29日
** 描　述: 1:优化代码结构
           2:节省RAM空间
**
**-----------------------------------------------------------------------------
******************************************************************************/