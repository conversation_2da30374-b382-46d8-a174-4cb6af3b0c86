/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_serv_proto.h
** bef: define the interface for server protocol. 
** auth: lines<<EMAIL>>
** create on 2022.01.11
*/

#ifndef _ES_SERV_PROTO_H_
#define _ES_SERV_PROTO_H_

#ifdef __cplusplus 
extern "C" { 
#endif

#include "es_types.h"

typedef enum {
    ES_PROTO_SERV_PUSH_CAR_INFO             = (1),
    ES_PROTO_SERV_PUSH_ADD_DRIVER_INFO      = (2),
    ES_PROTO_SERV_PUSH_CTL_LOCK             = (4),
    ES_PROTO_SERV_PUSH_DEL_DRIVER_INFO      = (5),
    ES_PROTO_SERV_PUSH_UPDATE_DRIVER_INFO   = (6),
    ES_PROTO_SERV_PUSH_IMAGE_CAPTRUE        = (7),
    ES_PROTO_SERV_PUSH_DYNAMIC_QR_CODE      = (9),
    ES_PROTO_SERV_PUSH_EMERHENCY_CONTROL    = (10),
    ES_PROTO_SERV_PUSH_RELAY_OPEN_VOL       = (11),
    ES_PROTO_SERV_PUSH_SAFETY_DETECT        = (12),
    ES_PROTO_SERV_PUSH_WORK_FACE_DETECT     = (13),
    ES_PROTO_SERV_PUSH_SPEED_DETECT         = (14),
    ES_PROTO_SERV_PUSH_POSTURE_DETECT       = (15),
    ES_PROTO_SERV_UPDATE_FLASH_RESOURCE     = (16),
    ES_PROTO_DEV_SYNC_TIME                  = (20),
    ES_PROTO_DEV_UPLOAD_STATUS              = (21),
    ES_PROTO_DEV_UPLOAD_DRIVER_INFO         = (22),
    ES_PROTO_DEV_UPLOAD_DEVICE_INFO         = (23),
    ES_PROTO_SERV_PLAY_VOICE                = (25),
    ES_PROTO_DEV_UPLOAD_AI_EVENT            = (26),
    ES_PROTO_SERV_RTSP_CTL                  = (27),
    ES_PROTO_DEV_UPLOAD_OFFLINE_RECORDS     = (30),
    ES_PROTO_SERV_DEV_MAX
} es_serv_dev_proto_type_e;

#define ES_SERV_MSG_EVENT_CB_COUNT          (4)

typedef ES_VOID(*es_serv_proto_msg_event_cb)(ES_U32 proto_type, ES_S32 ret);

ES_S32 es_serv_proto_init(ES_VOID);

// return cmd, mi
ES_S32 es_serv_proto_parse(const ES_CHAR *json_str, ES_U32 *mi);

ES_S32 es_serv_proto_reg_msg_event(es_serv_proto_msg_event_cb event_cb);

// need to free data
ES_S32 es_serv_proto_get_model_active(const ES_BYTE *cpu_id, const ES_BYTE *mac, ES_CHAR **data);

ES_S32 es_serv_proto_model_active_decode(const ES_BYTE *data, ES_U32 data_len);

#ifdef __cplusplus 
}
#endif

#endif
