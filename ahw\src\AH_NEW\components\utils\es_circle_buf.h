/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_circle_buffer.h
** bef: define the interface for circle buffer. 
** auth: lines<<EMAIL>>
** create on 2019.11.16 
*/

#ifndef _ES_CIRCLE_BUF_H_
#define _ES_CIRCLE_BUF_H_

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */


#include "es_types.h"

// the size must be 2^n, such 64/128/256/1024 ...
ES_VOID *es_circle_buf_new(ES_U16 size);

ES_S32 es_circel_write(ES_VOID *b, const ES_BYTE *data, ES_U16 len);

ES_S32 es_circel_write_byte(ES_VOID *b, ES_BYTE c);

ES_S32 es_circel_read(ES_VOID *b, const ES_BYTE *data, ES_U16 len);

ES_S32 es_circel_read_byte(ES_VOID *b, ES_BYTE *c);

ES_U32 es_circel_get_data_size(ES_VOID *b);

ES_S32 es_circle_buf_reset(ES_VOID *b);

ES_S32 es_circle_buf_free(ES_VOID *b);


#ifdef __cplusplus
}
#endif /* __cplusplus */
#endif


