/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_uart.h
** bef: define the interface for utils 
** auth: lines<<EMAIL>>
** create on 2019.05.08 
*/

#ifndef _ES_UTILS_H_
#define _ES_UTILS_H_

#ifdef __cplusplus 
extern "C" { 
#endif

#include "es_types.h"

ES_BYTE es_utils_char2hex(ES_BYTE c);

ES_CHAR es_utils_hex2char(ES_BYTE h);

ES_S32 es_utils_hex_str_to_bytes(const ES_CHAR *string, ES_BYTE *hex_bytes, ES_U32 hex_len);

ES_S32 es_utils_byte_to_hex_str(ES_CHAR *string, const ES_BYTE *hex_bytes, ES_U32 hex_len);

ES_BOOL es_utils_check_is_odd(const ES_BYTE byte);

ES_BOOL es_utils_check_timeout(ES_U32 last_time, ES_U32 now_time, ES_U32 timeout);

ES_BOOL es_utils_array_is_all(const ES_BYTE *array, ES_U32 array_len, ES_BYTE val);

#ifdef __cplusplus 
}
#endif
#endif
