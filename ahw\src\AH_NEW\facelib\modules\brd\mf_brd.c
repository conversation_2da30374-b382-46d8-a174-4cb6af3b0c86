#include "es_inc.h"
#include "facelib_inc.h"


#pragma GCC diagnostic ignored "-Wunused-function"
#pragma GCC diagnostic ignored "-Wunused-variable"

#define BOARD_CFG_LEN                               (2 * 1024)
#define BOARD_CFG_ADDR                              (0x7FF000)  //8M-4K
#define DBG_BAUDRATE								(1500000)

#define MF_BRD_DEBUG                                (0)
#if MF_BRD_DEBUG
#define mf_brd_debug								printk
#else
#define mf_brd_debug(...)
#endif


/*****************************************************************************/
// Macro definitions
/*****************************************************************************/
#define KEY_PRESS_MIN_MS			(100)
#define KEY2_PRESS_MIN_MS			(300)

/*****************************************************************************/
// Function definitions
/*****************************************************************************/


/*****************************************************************************/
// Private Var 局部变量
/*****************************************************************************/
const board_cfg_t board_default_conf = { 
    .dump_flag=0,
    /********************** HardWare ************************/
    .cam  = {.exp_t  = 0x80,.dir   = ES_CAM_DIR,  .vflip = ES_CAM_VFLIP,   .hmirror = ES_CAM_HMIRROR},
    .lcd  = {.dir    = ES_LCD_DIR,   .vflip = ES_LCD_VFLIP,  .hmirror = ES_LCD_HMIRROR, .xyswap  = ES_LCD_XY_SWAP, .inverse=0},	//3.5寸横屏

    .uart = {.port_tx=255,.port_rx =255, .log_tx  = 5, .log_rx  = 4},
    .relay= {.pin    = 32,  .pol   = 1,  .opent   = 5},  //1s unit
    .key  = {.pin    = 12,  .pol   = 0},                  //press to low
    .key1 = {.pin    =255,  .pol   = 0},                  //press to low
    .key2 = {.pin    = 22,  .pol   = 0},                  //press to low
    .led  = {.pin    = 10,  .pol   = 1,  .high    = ES_LED_FLASH_PWM_STRONG_DUTY,.low    = ES_LED_FLASH_PWM_WEAK_DUTY},
	.rgbled={.red    =255,  .green =255, .blue    =255, .pol    = 0},
    .ir   = {.pin    = 9,   .pol   = 1,},
    .bl   = {.pin    = 17,  .pol   = 0,},
	//TODO edit
	.i2c  = {.scl    = 30,  .sda   = 31,},
    .audio= {.type   = 0,   .pa    = 11, .pol     = 1,  .bck    = 35,  \
	         .lrck   = 33,  .din   =255, .dout    = 18, .mclk   =255,},
	.net  = {.type   = 0,   .sck   = 1,  .mosi    = 3,  .miso   = 2, .cs =0},
    /********************** SoftWare ************************/
    .model= {.ftr_len   = 196, .face_minw   = 80,  .face_minh = 60, \
			 .checkpose = 0,
             .fd_gate = ES_FACE_MODEL_FD_GATE, .fe_gate = ES_FACE_MODEL_FE_GATE, .live_gate = ES_FACE_MODEL_LIVE_GATE},
    .uartp= {.out_fea   = 0,   .auto_out_fea= 0,   .pkt_fix   = 0,  \
             .out_interval_ms=100,                 .port_baud =115200},
    .flow = {.close_lcd = 1,   .night_gate  = 60,  .open_lcd_n= 15,  \
             .open_led_n= 10,  .open_ir_n   = 50,  .darkface_n= 10, \
             .ir_period = 3,   .ir_oni      = 1,},
};

/*****************************************************************************/
// Driver 底层驱动
/*****************************************************************************/
#if MF_BRD_DEBUG
static void _mf_cfg_print(void)
{
    mf_brd_debug("dump board cfg:\r\n");
	mf_brd_debug("version = %d\r\n", (int)(mf_brd.cfg.version * 100)); 
	mf_brd_debug("-----------------------------------------------------------------");
	mf_brd_debug("\r\ncam   || ");
    mf_brd_debug("%-7s:%3d | ", "exp_t",     mf_brd.cfg.cam.exp_t);
	mf_brd_debug("%-7s:%3d | ", "dir",       mf_brd.cfg.cam.dir);
	mf_brd_debug("%-7s:%3d | ", "vflip",     mf_brd.cfg.cam.vflip);
	mf_brd_debug("%-7s:%3d | ", "hmirror",   mf_brd.cfg.cam.hmirror);
    mf_brd_debug("\r\nlcd   || ");                       
    mf_brd_debug("%-7s:%3d | ", "dir",       mf_brd.cfg.lcd.dir);
	mf_brd_debug("%-7s:%3d | ", "vflip",     mf_brd.cfg.lcd.vflip);
	mf_brd_debug("%-7s:%3d | ", "hmirror",   mf_brd.cfg.lcd.hmirror);
	mf_brd_debug("%-7s:%3d | ", "xyswap",    mf_brd.cfg.lcd.xyswap);
	mf_brd_debug("\r\n      || ");
	mf_brd_debug("%-7s:%3d | ", "inverse",   mf_brd.cfg.lcd.inverse);
    mf_brd_debug("\r\nuart  || ");                      
    mf_brd_debug("%-7s:%3d | ", "port_tx",   mf_brd.cfg.uart.port_tx);
	mf_brd_debug("%-7s:%3d | ", "port_rx",   mf_brd.cfg.uart.port_rx);
	mf_brd_debug("%-7s:%3d | ", "log_tx",    mf_brd.cfg.uart.log_tx);
	mf_brd_debug("%-7s:%3d | ", "log_rx",    mf_brd.cfg.uart.log_rx);
    mf_brd_debug("\r\nrelay || ");                     
    mf_brd_debug("%-7s:%3d | ", "pin",       mf_brd.cfg.relay.pin);
	mf_brd_debug("%-7s:%3d | ", "pol",       mf_brd.cfg.relay.pol);
	mf_brd_debug("%-7s:%3d | ", "opent",     mf_brd.cfg.relay.opent);
    mf_brd_debug("\r\nkey   || ");                       
    mf_brd_debug("%-7s:%3d | ", "pin",       mf_brd.cfg.key.pin);
	mf_brd_debug("%-7s:%3d | ", "pol",       mf_brd.cfg.key.pol);
    mf_brd_debug("\r\nkey1  || ");                       
    mf_brd_debug("%-7s:%3d | ", "pin",       mf_brd.cfg.key1.pin);
	mf_brd_debug("%-7s:%3d | ", "pol",       mf_brd.cfg.key1.pol);
    mf_brd_debug("\r\nkey2  || ");                       
    mf_brd_debug("%-7s:%3d | ", "pin",       mf_brd.cfg.key2.pin);
	mf_brd_debug("%-7s:%3d | ", "pol",       mf_brd.cfg.key2.pol);
    mf_brd_debug("\r\nled   || ");                       
    mf_brd_debug("%-7s:%3d | ", "pin",       mf_brd.cfg.led.pin);
	mf_brd_debug("%-7s:%3d | ", "pol",       mf_brd.cfg.led.pol);
    mf_brd_debug("%-7s:0.%02d| ", "high",  (int)(mf_brd.cfg.led.high*100));
	mf_brd_debug("%-7s:0.%02d| ", "low",   (int)(mf_brd.cfg.led.low*100));
    mf_brd_debug("\r\nir    || ");                        
    mf_brd_debug("%-7s:%3d | ", "pin",       mf_brd.cfg.ir.pin);
	mf_brd_debug("%-7s:%3d | ", "pol",       mf_brd.cfg.ir.pol);
    mf_brd_debug("\r\nbl    || ");                        
    mf_brd_debug("%-7s:%3d | ", "pin",       mf_brd.cfg.bl.pin);
	mf_brd_debug("%-7s:%3d | ", "pol",       mf_brd.cfg.bl.pol);
    mf_brd_debug("\r\naudio || ");
	mf_brd_debug("%-7s:%3d | ","type",	   mf_brd.cfg.audio.type);
	mf_brd_debug("%-7s:%3d | ","pa",         mf_brd.cfg.audio.pa);
	mf_brd_debug("%-7s:%3d | ","pol",        mf_brd.cfg.audio.pol);
	mf_brd_debug("%-7s:%3d | ","bck",        mf_brd.cfg.audio.bck);
	mf_brd_debug("\r\n      || ");
	mf_brd_debug("%-7s:%3d | ","lrck",       mf_brd.cfg.audio.lrck);
	mf_brd_debug("%-7s:%3d | ","din",        mf_brd.cfg.audio.din);
	mf_brd_debug("%-7s:%3d | ","dout",       mf_brd.cfg.audio.dout);
	mf_brd_debug("%-7s:%3d | ","mclk",       mf_brd.cfg.audio.mclk);
    mf_brd_debug("\r\ni2c   || ");
	mf_brd_debug("%-7s:%3d | ","scl",        mf_brd.cfg.i2c.scl);
	mf_brd_debug("%-7s:%3d | ","sda",        mf_brd.cfg.i2c.sda);
	mf_brd_debug("\r\nnet   || ");
	mf_brd_debug("%-7s:%3d | ","type",       mf_brd.cfg.net.type);
	mf_brd_debug("%-7s:%3d | ","sck",        mf_brd.cfg.net.sck);
	mf_brd_debug("%-7s:%3d | ","mosi",       mf_brd.cfg.net.mosi);
	mf_brd_debug("%-7s:%3d | ","miso",       mf_brd.cfg.net.miso);
	mf_brd_debug("\r\n      || ");
	mf_brd_debug("%-7s:%3d | ","cs",         mf_brd.cfg.net.cs);
	mf_brd_debug("%-7s:%3d | ","rst",        mf_brd.cfg.net.rst);
	mf_brd_debug("\r\npic_uart|| ");
	mf_brd_debug("%-7s:%3d | ","baud",       mf_brd.cfg.pic_stream.baud);
	mf_brd_debug("%-7s:%3d | ","tx_pin",        mf_brd.cfg.pic_stream.tx_pin);
	mf_brd_debug("\r\n-----------------------------------------------------------------");
    mf_brd_debug("\r\nmodel || ");                     
    mf_brd_debug("%-12s:%3d | ", "ftr_len",   mf_brd.cfg.model.ftr_len);
	mf_brd_debug("%-12s:%3d | ", "face_minw", mf_brd.cfg.model.face_minw);
    mf_brd_debug("%-12s:%3d | ", "face_minh", mf_brd.cfg.model.face_minh);
	mf_brd_debug("\r\n      || ");
	mf_brd_debug("%-12s:%3d | ", "checkpose", mf_brd.cfg.model.checkpose);
	mf_brd_debug("\r\n      || ");
	mf_brd_debug("%-12s: %02d | ", "fd_gate", (int)(mf_brd.cfg.model.fd_gate));
    mf_brd_debug("%-12s: %02d | ", "fe_gate", (int)mf_brd.cfg.model.fe_gate);
	mf_brd_debug("%-12s: %02d | ", "fe_gate_ir", (int)mf_brd.cfg.model.fe_gate_ir);
	mf_brd_debug("%-12s: %02d | ","live_gate",(int)mf_brd.cfg.model.live_gate);
    mf_brd_debug("\r\nflow  || "); 
    mf_brd_debug("%-12s:%3d | ", "close_lcd", mf_brd.cfg.flow.close_lcd);
	mf_brd_debug("%-12s:%3d | ", "night_gate",mf_brd.cfg.flow.night_gate);
    mf_brd_debug("%-12s:%3d | ", "open_lcd_n",mf_brd.cfg.flow.open_lcd_n);
	mf_brd_debug("\r\n      || ");
	mf_brd_debug("%-12s:%3d | ", "open_led_n",mf_brd.cfg.flow.open_led_n);
    mf_brd_debug("%-12s:%3d | ", "open_ir_n", mf_brd.cfg.flow.open_ir_n);
	mf_brd_debug("%-12s:%3d | ", "darkface_n",mf_brd.cfg.flow.darkface_n);
	mf_brd_debug("\r\n      || ");
    mf_brd_debug("%-12s:%3d | ", "ir_period", mf_brd.cfg.flow.ir_period);
	mf_brd_debug("%-12s:%3d | ", "ir_oni",    mf_brd.cfg.flow.ir_oni);
    mf_brd_debug("\r\nuartp || "); 
    mf_brd_debug("%-12s:%3d | ", "out_fea",       mf_brd.cfg.uartp.out_fea);
	mf_brd_debug("%-12s:%3d | ", "auto_out_fea",  mf_brd.cfg.uartp.auto_out_fea);
    mf_brd_debug("%-12s:%3d | ", "pkt_fix",       mf_brd.cfg.uartp.pkt_fix);
	mf_brd_debug("\r\n      || ");
	mf_brd_debug("%-12s:%3d | ","out_int_ms",mf_brd.cfg.uartp.out_interval_ms);
    mf_brd_debug("%-12s:%3d | ", "port_baud",     mf_brd.cfg.uartp.port_baud);
	mf_brd_debug("\r\nwifi  || "); 
	mf_brd_debug("ssid='%s', passwd='%s';  ", \
				mf_brd.cfg.wifi.ssid, mf_brd.cfg.wifi.passwd);
	mf_brd_debug("factory_flag=%d", mf_brd.cfg.factory_flag);
	mf_brd_debug("\r\n-----------------------------------------------------------------");
	mf_brd_debug("\r\nHEX config dump (%ld Byte):\r\n", sizeof(board_cfg_t));
	uint8_t* ptr = (uint8_t*)&mf_brd.cfg;
	for(int i = 0; i < sizeof(board_cfg_t)-512; i++)
	{
		mf_brd_debug("%02x", ptr[i]);
		if(i%32==31) mf_brd_debug("\r\n");
	}
	mf_brd_debug("\r\n-----------------------------------------------------------------");
	mf_brd_debug("\r\n");
    return;
}
#endif

//按键初始化/////////////////////////////////////////////////////

#pragma GCC push_options
#pragma GCC optimize ("O1")
static void _mf_brd_update_key(void)
{
    return;
}
#pragma GCC pop_options

//继电器驱动函数/////////////////////////////////////////////////////
static void _mf_brd_set_relay(int state, uint8_t time)
{
	return;
}


static void _mf_brd_relay_loop(void)
{
	if(0xFF == mf_brd.cfg.relay.pin) {
		return;
	}

	if(mf_brd.relay_flag) {
		uint32_t dt = (uint32_t)(sysctl_get_time_us()/1000/100 - mf_brd.relay_start_t);
		if( dt >= mf_brd.relay_time) {
			_mf_brd_set_relay(0, 0);
		}
	}
	return ;
}


//LED驱动函数/////////////////////////////////////////////////////
static void _mf_brd_set_IR_LED(int state)
{
	if (1 == state) {
		es_hal_ir_open();
	} else {
		es_hal_ir_close();
	}
    return;
}

static void _mf_brd_turn_W_LED(float duty)
{
    return;
}

static void _mf_brd_set_W_LED(int state)
{
	// mf_brd_debug("_mf_brd_set_W_LED, state:%d\r\n", state);
    state = state ? 1: 0;
	if (state == 1) {
		es_hal_led_open(ES_TRUE);
		es_facecb_update_detect_time();
	} else {
		es_hal_led_close();
	}
    return;
}


//////////////////////////////////////////////////////////////////////////
static mf_err_t _mf_cfg_load(void)
{
	mf_err_t err = MF_ERR_NONE;
    uint8_t sha256[32];
	
	board_cfg_t* cfg = &mf_brd.cfg;

#ifdef FLASH_LAYOUT_V2
	#undef BOARD_CFG_ADDR
	#define BOARD_CFG_ADDR							(508 * 1024)
#endif

    err = mf_flash.read(BOARD_CFG_ADDR, (uint8_t *)cfg, sizeof(board_cfg_t));
    if((cfg->header == CFG_HEADER) && (err == MF_ERR_NONE) && (cfg->version == (float)CFG_VERSION)) {
        sha256_hard_calculate((uint8_t *)((uint8_t*)cfg + 32), sizeof(board_cfg_t) - 32, sha256);
        if(memcmp(cfg->cfg_sha256, sha256, 32) == 0) {
            cfg->cfg_right_flag = 1;
            err = MF_ERR_NONE;
        } else {
			mf_brd_debug("sha256 check fail");
            err = MF_ERR_CFG_INIT1;
        }
    } else {
		mf_brd_debug("read flash fail, err:%d", err);
        memset(cfg, 0, sizeof(board_cfg_t));
        err = MF_ERR_CFG_INIT2;
    }
	
    return err;
}

////////////////////////////////////////////////////////////////////////


static int get_boot_app_section(void)
{
    uint8_t boot_app = 0xff, *deadbeef = (uint8_t *)(uintptr_t)(0x80000000 + 0x02);

    if ((deadbeef[2] == 0x55) && (deadbeef[3] == 0xaa) &&
        (deadbeef[0] == (uint8_t)(~deadbeef[1] & 0xff)))
    {
        if (deadbeef[0] == 0x00)
            boot_app = 0;
        else if (deadbeef[0] == 0x01)
            boot_app = 1;
        else
            boot_app = 0xff;
    }

    mf_brd_debug("boot_app:%x %02x %02x %02x %02x\r\n", boot_app, deadbeef[0], deadbeef[1], deadbeef[2], deadbeef[3]);

    return boot_app;
}


static void _mf_brd_check_wdt_reboot(void)
{
    sysctl_reset_enum_status_t v_reset_status = sysctl_get_reset_status();
    if (v_reset_status == SYSCTL_RESET_STATUS_WDT0 || v_reset_status == SYSCTL_RESET_STATUS_WDT1)
    {
        mf_brd_debug("wdt reboot!\n");
    }
	return;
}

/*****************************************************************************/
// Private Func 局部函数
/*****************************************************************************/
static mf_err_t _mf_brd_cfg_save(void)
{
	board_cfg_t* cfg = &mf_brd.cfg;
    mf_err_t err;

    cfg->cfg_right_flag = 0;

    sha256_hard_calculate((uint8_t *)((uint8_t*)cfg + 32), sizeof(board_cfg_t) - 32, cfg->cfg_sha256);

#if MF_BRD_DEBUG
    mf_brd_debug("boardcfg checksum:");
    for(uint8_t i = 0; i < 32; i++)
    {
        mf_brd_debug("%02X", cfg->cfg_sha256[i]);
    }
    mf_brd_debug("\r\n");
	mf_brd_debug("cfg->factory_flag:%d", cfg->factory_flag);
#endif

    err = mf_flash.write(BOARD_CFG_ADDR, (uint8_t *)cfg, sizeof(board_cfg_t));

    return (err == MF_ERR_NONE) ? MF_ERR_NONE : MF_ERR_CFG_SAVE;
}

/* Reset to default_cfg */
static mf_err_t _mf_brd_reset(const board_cfg_t* default_cfg)
{
	mf_err_t err = MF_ERR_NONE;

	board_cfg_t* cfg = &mf_brd.cfg;

	if(default_cfg == NULL) {
		memcpy((uint8_t*)cfg, (uint8_t*)mf_brd.default_cfg, sizeof(board_cfg_t));
	} else {
		memcpy((uint8_t*)cfg, (uint8_t*)default_cfg, sizeof(board_cfg_t));
	}

    cfg->header         = CFG_HEADER;
    cfg->cfg_right_flag = 0;
    cfg->version        = CFG_VERSION;
    cfg->factory_flag = 1;
	cfg->relay.pol = ES_RELAY_OPEN_IO_VAL;
	cfg->relay.opent = (ES_RELAY_CONTINUE_MS/1000);
	cfg->car_expire = 0;
	cfg->lock = 0;
	cfg->emergency = 0;
	cfg->safetybelt_detect = 1;
	cfg->limit_speed = 0;
	cfg->working_face_detect = 1;
	cfg->posture_detect = 1;

    // memset(cfg->wifi.ssid, 0, sizeof(cfg->wifi.ssid));
    // memset(cfg->wifi.passwd, 0, sizeof(cfg->wifi.passwd));
    memset(cfg->user_custom_cfg, 0, sizeof(cfg->user_custom_cfg));

	err = _mf_brd_cfg_save();
	if(err != MF_ERR_NONE)
	{
		mf_brd_debug("save mf_brd.cfg failed!\r\n");
	}

	return err;
}

//BOARD_CFG_ADDR
static mf_err_t _mf_brd_init(mf_brd_type_t board_type, const board_cfg_t* cfg)
{
	mf_err_t err = MF_ERR_NONE;

	mf_brd.default_cfg = &board_default_conf;
	if (cfg != NULL) {
		mf_brd.default_cfg = cfg;
	}
	

	mf_brd.boot_section = get_boot_app_section();
	

	/*load cfg from flash*/
	if(BOARD_CFG_LEN <= sizeof(board_cfg_t)) {
		while (1)
		{
			mf_brd_debug("too large\r\n");
			sleep(1);
		}
	}

#if CONFIG_PROJ_MODEL_TEST
	memcpy((uint8_t*)&mf_brd.cfg, (uint8_t*)cfg, sizeof(board_cfg_t));
#else
	err = _mf_cfg_load();
    if(err == MF_ERR_NONE) {
		mf_brd.init_flag = 1;
        mf_brd_debug("load cfg %s;  ", mf_brd.cfg.cfg_right_flag ? "success" : "error");
    } else {
        mf_brd_debug("load cfg failed,save default config, ret=%d\r\n", err);
		err = _mf_brd_reset(cfg);
		if(err != MF_ERR_NONE) {
			return err;
		}
    }
#endif

	mf_brd.type = board_type;

	//打印板卡配置信息
#if MF_BRD_DEBUG
	if(err == MF_ERR_NONE) {
		_mf_cfg_print();
	}
#endif
	//检查复位情况
	_mf_brd_check_wdt_reboot();

	return err;
}

/*****************************************************************************/
// Public Var 全局变量
/*****************************************************************************/



mf_brd_t mf_brd = {
	.init_flag      = 1,
	.default_cfg    = &board_default_conf,
	.key_press      = 0,
	.key_long_press = 0,
	.relay_flag     = 0,
	.init           = _mf_brd_init,
	.cfg_save		= _mf_brd_cfg_save,
	.reset_default  = _mf_brd_reset,
	.update_key     = _mf_brd_update_key,
	.set_IR_LED     = _mf_brd_set_IR_LED,
	.set_W_LED		= _mf_brd_set_W_LED,
	.turn_W_LED		= _mf_brd_turn_W_LED,
	.set_relay      = _mf_brd_set_relay,
	.relay_loop     = _mf_brd_relay_loop,
#if MF_BRD_DEBUG
	.print			= _mf_cfg_print,
#else
    .print          = NULL,
#endif
};


