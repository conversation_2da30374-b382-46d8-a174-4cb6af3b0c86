#include "facelib_inc.h"

#pragma GCC diagnostic ignored "-Wunused-variable"
#pragma GCC diagnostic ignored "-Wunused-function"

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
#define CAMERA_DIRECTION (0x01)

extern void gc0328_dual_init_sccb(int num); // 1 650, 0 850

static uint8_t sensor_mono_regs[][2];
static uint8_t sensor_color_regs[][2];

///////////////////////////////////////////////////////////////////////////////
static int myi2c_send_data(i2c_device_number_t i2c_num, const uint8_t *send_buf, size_t send_buf_len)
{
    configASSERT(i2c_num < I2C_MAX_NUM);
    volatile i2c_t *i2c_adapter = i2c[i2c_num];
    size_t fifo_len, index;
    i2c_adapter->clr_tx_abrt = i2c_adapter->clr_tx_abrt;
    while(send_buf_len)
    {
        fifo_len = 8 - i2c_adapter->txflr;
        fifo_len = send_buf_len < fifo_len ? send_buf_len : fifo_len;
        for(index = 0; index < fifo_len; index++)
            i2c_adapter->data_cmd = I2C_DATA_CMD_DATA(*send_buf++);
        if(i2c_adapter->tx_abrt_source != 0)
            return 1;
        send_buf_len -= fifo_len;
    }
    while((i2c_adapter->status & I2C_STATUS_ACTIVITY) || !(i2c_adapter->status & I2C_STATUS_TFE))
        ;

    if(i2c_adapter->tx_abrt_source != 0)
        return 1;

    return 0;
}

///////////////////////////////////////////////////////////////////////////////
uint8_t gc0328_dual_rd_reg(uint8_t num, uint8_t reg)
{
    uint8_t reg_buf[1];
    uint8_t data_buf;

    reg_buf[0] = reg & 0xff;
    i2c_recv_data(num, reg_buf, 1, &data_buf, 1);
    return data_buf;
}

void gc0328_dual_wr_reg(uint8_t num, uint8_t reg, uint8_t data)
{
    uint8_t buf[2];

    buf[0] = reg & 0xff;
    buf[1] = data;
    myi2c_send_data(num, buf, 2);
}

///////////////////////////////////////////////////////////////////////////////
void open_gc0328_850()
{
    usleep(1 * 100);
    gc0328_dual_init_sccb(1);
    usleep(1 * 100);
    gc0328_dual_wr_reg(1, 0xFE, 0x00);
    gc0328_dual_wr_reg(1, 0xF1, 0x00);
    gc0328_dual_wr_reg(1, 0xF2, 0x00);

    usleep(1 * 100);
    gc0328_dual_init_sccb(0);
    usleep(1 * 100);
    gc0328_dual_wr_reg(0, 0xFE, 0x00);
    gc0328_dual_wr_reg(0, 0xF1, 0x07);
    gc0328_dual_wr_reg(0, 0xF2, 0x01);
}

void open_gc0328_650()
{
    usleep(1 * 100);
    gc0328_dual_init_sccb(0);
    usleep(1 * 100);
    gc0328_dual_wr_reg(0, 0xFE, 0x00);
    gc0328_dual_wr_reg(0, 0xF1, 0x00);
    gc0328_dual_wr_reg(0, 0xF2, 0x00);

    usleep(1 * 100);
    gc0328_dual_init_sccb(1);
    usleep(1 * 100);
    gc0328_dual_wr_reg(1, 0xFE, 0x00);
    gc0328_dual_wr_reg(1, 0xF1, 0x07);
    gc0328_dual_wr_reg(1, 0xF2, 0x01);
}

///////////////////////////////////////////////////////////////////////////////
int gc0328_dual_modify_reg(uint8_t reg_data[][2])
{
    for(uint16_t i = 0; reg_data[i][0]; i++)
    {
        for(uint16_t j = 0; sensor_color_regs[j][0]; j++)
        {
            if(sensor_color_regs[j][0] == reg_data[i][0])
            {
                // printk("%02X:%02X\r\n", reg_data[i][0], reg_data[i][1]);
                sensor_color_regs[j][1] = reg_data[i][1];
            }
        }
    }
    return 0;
}

int gc0328_dual_set_hmirror(uint8_t val)
{
    uint8_t tmp = 0;

    /* 修改850摄像头初始化寄存器 */
    for(uint16_t i = 0; sensor_mono_regs[i][0]; i++)
    {
        if(sensor_mono_regs[i][0] == 0x17)
        {
            tmp = sensor_mono_regs[i][1];
            if(val)
            {
                tmp |= (1 << 0);
            } else
            {
                tmp &= ~(1 << 0);
            }
            sensor_mono_regs[i][1] = tmp;
        }
    }

    /* 修改650摄像头初始化寄存器 */
    for(uint16_t i = 0; sensor_color_regs[i][0]; i++)
    {
        if(sensor_color_regs[i][0] == 0x17)
        {
            tmp = sensor_color_regs[i][1];
            if(val)
            {
                tmp |= (1 << 0);
            } else
            {
                tmp &= ~(1 << 0);
            }
            sensor_color_regs[i][1] = tmp;
        }
    }

    return 0;
}

int gc0328_dual_set_vflip(uint8_t val)
{
    uint8_t tmp = 0;
    // 修改850摄像头初始化寄存器 
    for(uint16_t i = 0; sensor_mono_regs[i][0]; i++)
    {
        if(sensor_mono_regs[i][0] == 0x17)
        {
            tmp = sensor_mono_regs[i][1];
            if(val)
            {
                tmp |= (1 << 1);
            } else
            {
                tmp &= ~(1 << 1);
            }
            sensor_mono_regs[i][1] = tmp;
			break;
        }
    }

    // 修改650摄像头初始化寄存器 
    for(uint16_t i = 0; sensor_color_regs[i][0]; i++)
    {
        if(sensor_color_regs[i][0] == 0x17)
        {
            tmp = sensor_color_regs[i][1];
            if(val)
            {
                tmp |= (1 << 1);
            } else
            {
                tmp &= ~(1 << 1);
            }
            sensor_color_regs[i][1] = tmp;
			break;
        }
    }

    return 0;
}

int gc0328_dual_read_id(uint16_t *manuf_id, uint16_t *device_id)
{
    uint8_t num = 0;

    open_gc0328_850();
    *manuf_id = (uint16_t)gc0328_dual_rd_reg(num, 0xf0);

    num = 1;
    open_gc0328_650();
    *device_id = (uint16_t)gc0328_dual_rd_reg(num, 0xf0);

    return 0;
}


int gc0328_dual_config(void)
{
    uint8_t data, num = 0;

    open_gc0328_850();

    for(uint16_t i = 0; sensor_mono_regs[i][0]; i++)
    {
        gc0328_dual_wr_reg(num, sensor_mono_regs[i][0], sensor_mono_regs[i][1]);
    }

    usleep(1000);

    num = 1;
    open_gc0328_650();
    for(uint16_t i = 0; sensor_color_regs[i][0]; i++)
    {
        gc0328_dual_wr_reg(num, sensor_color_regs[i][0], sensor_color_regs[i][1]);
    }

    /* 默认打开850摄像头 */
    dvp_set_output_enable(0, 1); //enable to ai
    dvp_set_output_enable(1, 0); //disable to lcd
    open_gc0328_850();

    return 0;
}
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////

/** The default register settings**/
static uint8_t sensor_mono_regs[][2] = {
    {0xFE, 0x80}, //软复位
    {0xFC, 0x16}, //使能时钟

    {0xfe, 0x00}, //下面两个值减小可以加快帧率
    {0x05, 0x01}, //hblank high  但是太小会花屏
    {0x06, 0x80}, // HB low
    {0x07, 0x00}, //vblank high
    {0x08, 0x40}, // VB  low

    {0xFE, 0x00}, //选择page0

    {0x09, 0x00}, // Row起始地址 H
    {0x0A, 0x00}, // Row起始地址 L
    {0x0B, 0x00}, //行起始H
    {0x0C, 0x00}, // L
    {0x0D, 0x01}, //窗高H
    {0x0E, 0xE8}, // L  488
    {0x0F, 0x02}, //窗宽H
    {0x10, 0x88}, // L 648
    /*
    {0x09, 0x00}, //Row起始地址 H
    {0x0A, 0x78}, //Row起始地址 L
    {0x0B, 0x00}, //行起始H
    {0x0C, 0xa0}, //L
    {0x0D, 0x00}, //窗高H
    {0x0E, 0xf8}, //L  240
    {0x0F, 0x01}, //窗宽H
    {0x10, 0x48}, //L 320*/

    {0x16, 0x00},                    //关闭模拟增益
    {0x17, 0x14 | CAMERA_DIRECTION}, //翻转 抓取值15
    {0x19, 0x06},                    // AD pipe number
    {0x1F, 0xC8},
    {0x20, 0x01},
    {0x21, 0x78},
    {0x22, 0xB0},
    {0x23, 0x06},
    {0x24, 0x16}, //电流驱动能力 //1B
    {0x26, 0x00},

    {0xFE, 0x00}, // BLK
    {0x27, 0xB7}, // f7
    {0x28, 0x7F}, //
    {0x29, 0x40}, // 20
    {0x33, 0x20}, //
    {0x34, 0x20}, //
    {0x35, 0x20}, //
    {0x36, 0x20}, //
    {0x32, 0x08}, //
    {0x3B, 0x00}, //手工调整R oft
    {0x3C, 0x00}, // G1
    {0x3D, 0x00}, // G2
    {0x3E, 0x00}, // B
    {0x47, 0x00}, //全局黑色偏移高2位
    {0x48, 0x00}, //低8位
    {0x2E, 0x01}, // G1黑电流
    {0x2F, 0x01}, // R黑电流
    {0x30, 0x01}, // B黑电流
    {0x31, 0x01}, // G2黑电流
    // ISP
    {0x40, 0x7F}, //
    {0x41, 0x26}, //
    {0x42, 0x00}, // disable AWB
    {0x43, 0x00}, //
    {0x44, 0x26}, // RGB565 0x26  YUYV 0x22
    {0x45, 0x00}, //
    {0x46, 0x03}, //!!!注意修改
    {0x4F, 0x01}, //
    {0x4B, 0x8a}, //
    {0x50, 0x01}, //   使能crop模式
                  //{0x54, 0x02}, //
                  //{0x5A, 0x0E}, //
    {0x51, 0x00},
    {0x52, 0x00},
    {0x53, 0x00},
    {0x54, 0x00},
    {0x55, 0x00},
    {0x56, 0xf0},
    {0x57, 0x01},
    {0x58, 0x40},
    //{0x59, 0x22},	//
    //{0x55, 0x00}, //窗高H
    //{0x56, 0xf0}, //L  488
    //{0x57, 0x01}, //窗宽H
    //{0x58, 0x40}, //L 648
    {0x59, 0x22},

    {0x5a, 0x03},
    {0x5b, 0x00},
    {0x5c, 0x00},
    {0x5d, 0x00},
    {0x5e, 0x00},
    {0x5f, 0x00},
    {0x60, 0x00},
    {0x61, 0x00},
    {0x62, 0x00},

    // PREGAIN
    {0x70, 0x50}, //
    {0x71, 0x50}, //
    {0x72, 0x60}, //
    // DNDD
    {0x7E, 0x0A}, //
    {0x7F, 0x03}, //
    {0x81, 0x15}, //
    {0x82, 0x90}, //
    {0x83, 0x02}, //
    {0x84, 0xE5}, //
    // INTPEE  插值和边缘增强
    {0x90, 0x2C}, //
    {0x92, 0x02}, //
    {0x94, 0x02}, //
    {0x95, 0x35}, //
    // YCP  饱和度
    //{0xD0, 0xf0}, //
    {0xD1, 0x20}, //
    {0xD2, 0x20}, //
    {0xD3, 0x38}, //
    {0xD4, 0x80}, //
    {0xDD, 0xD3}, //
    {0xDE, 0x38}, //
    {0xE4, 0x88}, //
    {0xE5, 0x40}, //
    {0xD7, 0x0E}, //
    // AEC
    {0xFE, 0x01}, //
    //{0x06, 0x08},
    //{0x07, 0x06},
    //{0x08, 0x70},
    //{0x09, 0xa0},

    {0x10, 0x08}, //
    {0x11, 0x11}, //
    {0x12, 0x11}, //
    {0x13, 0x20}, //目标亮度
    {0x15, 0xFC}, //
    {0x18, 0x02}, //
    {0x21, 0xF0}, //
    {0x22, 0x60}, //
    {0x23, 0x30}, //
    {0x24, 0x16}, // //1B
    {0x25, 0x00}, //

    // {0x29, 0x00},
    // {0x2a, 0x4e}, // step
    // {0x2b, 0x02},
    // {0x2c, 0x00},
    // {0x2d, 0x03},
    // {0x2e, 0x00},
    // {0x2f, 0x04},
    // {0x30, 0x00},
    // {0x31, 0x05},
    // {0x32, 0x00},

    {0x29, 0x00},
    {0x2a, 0x4e}, // step
    {0x2b, 0x02},
    {0x2c, 0x00},
    {0x2d, 0x02},
    {0x2e, 0x00},
    {0x2f, 0x02},
    {0x30, 0x00},
    {0x31, 0x02},
    {0x32, 0x00},
    {0x3D, 0x60}, //
    {0x3E, 0x40}, //

    //关输出       //
    {0xFE, 0x00}, //
    {0xF1, 0x00}, //
    {0xF2, 0x00}, //
    {0x00, 0x00},
};

// important reg
/*
{0xfe , 0x80},  //reset
{0x17 , 0x15},  //vflip & mirror set 17 (bit0,1)
{0x24 , 0x11},  //output pin ability of driver config
{0x42 , 0xff},//enable AWB
{0x44, 0x26}, //RGB565
{0x46, 0x03}, //SYNC 极性
{0x50, 0x01}, //crop windows
{0x51, 0x00},	//crop y h
{0x52, 0x00},	//l
{0x53, 0x00},	//crop x h
{0x54, 0x00},	//l
{0x55, 0x00},	//crop winh h
{0x56, 0xf0},	//l	0x0f0=240
{0x57, 0x01},	//crop winw	h
{0x58, 0x40},	//l	0x140=320
{0x59, 0x22},	//subsample ratio for row,col
{0x5a, 0x03},	//neughbor average mode

{0x70, 0x85},  //global gain for range

P1:
{0x13, 0x20}, //期待曝光的流明值(Y)
*/

/*
When exp_time <= win_height + VB, Bt=VB – St - Et. Frame rate is controlled by
window_height + VB.
When exp_time > win_height + VB, Bt=exp_time - win_height – St - Et. Frame rate
is controlled by exp_time.

*/

static uint8_t sensor_color_regs[][2] = {
#if 0
    {0xfe, 0x80},
    {0xfe, 0x80},
    {0xfc, 0x16},
    {0xfc, 0x16},
    {0xfc, 0x16},
    {0xfc, 0x16},

    {0xfe, 0x00},
    {0x4f, 0x00},
    {0x42, 0x00},
    {0x03, 0x00},
    {0x04, 0xc0},
    {0x77, 0x62},
    {0x78, 0x40},
    {0x79, 0x4d},

    {0xfe, 0x01},
    {0x4f, 0x00},
    {0x4c, 0x01},
    {0xfe, 0x00},
    //////////////////////////////
    ///////////AWB///////////
    ////////////////////////////////
    {0xfe, 0x01},
    {0x51, 0x80},
    {0x52, 0x12},
    {0x53, 0x80},
    {0x54, 0x60},
    {0x55, 0x01},
    {0x56, 0x06},
    {0x5b, 0x02},
    {0xb1, 0xdc},
    {0xb2, 0xdc},
    {0x7c, 0x71},
    {0x7d, 0x00},
    {0x76, 0x00},
    {0x79, 0x20},
    {0x7b, 0x00},
    {0x70, 0xFF},
    {0x71, 0x00},
    {0x72, 0x10},
    {0x73, 0x40},
    {0x74, 0x40},
    ////AWB//
    {0x50, 0x00},
    {0xfe, 0x01},
    {0x4f, 0x00},
    {0x4c, 0x01},
    {0x4f, 0x00},
    {0x4f, 0x00},
    {0x4f, 0x00},
    {0x4d, 0x36},
    {0x4e, 0x02},
    {0x4d, 0x46},
    {0x4e, 0x02},
    {0x4e, 0x02},
    {0x4d, 0x53},
    {0x4e, 0x08},
    {0x4e, 0x04},
    {0x4e, 0x04},
    {0x4d, 0x63},
    {0x4e, 0x08},
    {0x4e, 0x08},
    {0x4d, 0x82},
    {0x4e, 0x20},
    {0x4e, 0x20},
    {0x4d, 0x92},
    {0x4e, 0x40},
    {0x4d, 0xa2},
    {0x4e, 0x40},
    {0x4f, 0x01},

    {0x50, 0x88},
    {0xfe, 0x00},
    ////////////////////////////////////////////////
    //////////// BLK //////////////////////
    ////////////////////////////////////////////////
    {0x27, 0x00},
    {0x2a, 0x40},
    {0x2b, 0x40},
    {0x2c, 0x40},
    {0x2d, 0x40},
    //////////////////////////////////////////////
    ////////// page 0 ////////////////////////
    //////////////////////////////////////////////
    {0xfe, 0x00},
    {0x05, 0x00},
    {0x06, 0xde},
    {0x07, 0x00},
    {0x08, 0xa7},

    {0x0d, 0x01},
    {0x0e, 0xe8},
    {0x0f, 0x02},
    {0x10, 0x88},
    {0x09, 0x00},
    {0x0a, 0x00},
    {0x0b, 0x00},
    {0x0c, 0x00},
    {0x16, 0x00},
    {0x17, 0x14 | CAMERA_DIRECTION},
    {0x18, 0x0e},
    {0x19, 0x06},

    {0x1b, 0x48},
    {0x1f, 0xC8},
    {0x20, 0x01},
    {0x21, 0x78},
    {0x22, 0xb0},
    {0x23, 0x06},
    {0x24, 0x11},
    {0x26, 0x00},

    {0x50, 0x01}, // crop mode
    // global gain for range
    {0x70, 0x85},
    ////////////////////////////////////////////////
    //////////// block enable /////////////
    ////////////////////////////////////////////////
    {0x40, 0x7f},
    {0x41, 0x24},
    {0x42, 0xff},
    {0x45, 0x00},
    {0x44, 0x06},
    {0x46, 0x03}, //[0] VSYNC polarity 0x02

    {0x4b, 0x01},
    {0x50, 0x01},
    // DN & EEINTP
    {0x7e, 0x0a},
    {0x7f, 0x03},
    {0x81, 0x15},
    {0x82, 0x85},
    {0x83, 0x02},
    {0x84, 0xe5},
    {0x90, 0xac},
    {0x92, 0x02},
    {0x94, 0x02},
    {0x95, 0x54},
    ///////YCP
    {0xd1, 0x32},
    {0xd2, 0x32},
    {0xdd, 0x58},
    {0xde, 0x36},
    {0xe4, 0x88},
    {0xe5, 0x40},
    {0xd7, 0x0e},
    /////////////////////////////
    //////////////// GAMMA //////
    /////////////////////////////
    // rgb gamma
#if 0   /* RGBGAMMA_test */
    {0xfe, 0x00},
    {0xbf, 0x08},
    {0xc0, 0x10},
    {0xc1, 0x22},
    {0xc2, 0x32},
    {0xc3, 0x43},
    {0xc4, 0x50},
    {0xc5, 0x5e},
    {0xc6, 0x78},
    {0xc7, 0x90},
    {0xc8, 0xa6},
    {0xc9, 0xb9},
    {0xca, 0xc9},
    {0xcb, 0xd6},
    {0xcc, 0xe0},
    {0xcd, 0xee},
    {0xce, 0xf8},
    {0xcf, 0xff},
#elif 1 /* RGBGAMMA_m6 */
    {0xBF, 0x14},
    {0xc0, 0x28},
    {0xc1, 0x44},
    {0xc2, 0x5D},
    {0xc3, 0x72},
    {0xc4, 0x86},
    {0xc5, 0x95},
    {0xc6, 0xB1},
    {0xc7, 0xC6},
    {0xc8, 0xD5},
    {0xc9, 0xE1},
    {0xcA, 0xEA},
    {0xcB, 0xF1},
    {0xcC, 0xF5},
    {0xcD, 0xFB},
    {0xcE, 0xFE},
    {0xcF, 0xFF},
#endif

    /// Y gamma
    {0xfe, 0x00},
    {0x63, 0x00},
    {0x64, 0x05},
    {0x65, 0x0b},
    {0x66, 0x19},
    {0x67, 0x2e},
    {0x68, 0x40},
    {0x69, 0x54},
    {0x6a, 0x66},
    {0x6b, 0x86},
    {0x6c, 0xa7},
    {0x6d, 0xc6},
    {0x6e, 0xe4},
    {0x6f, 0xFF},
    //////ASDE
    {0xfe, 0x01},
    {0x18, 0x02},
    {0xfe, 0x00},
    {0x98, 0x00},
    {0x9b, 0x20},
    {0x9c, 0x80},
    {0xa4, 0x10},
    {0xa8, 0xB0},
    {0xaa, 0x40},
    {0xa2, 0x23},
    {0xad, 0x01},
    //////////////////////////////////////////////
    ////////// AEC ////////////////////////
    //////////////////////////////////////////////
    {0xfe, 0x01},
    {0x9c, 0x02},
    {0x08, 0xa0},
    {0x09, 0xe8},

    {0x10, 0x00},
    {0x11, 0x11},
    {0x12, 0x10},
    // {0x13, 0x80},
    {0x13, 0xB0}, //提高显示的亮度.
    {0x15, 0xfc},
    {0x18, 0x03},
    {0x21, 0xc0},
    {0x22, 0x60},
    {0x23, 0x30},
    {0x25, 0x00},
    {0x24, 0x14},
    //////////////////////////////////////
    ////////////LSC//////////////////////
    //////////////////////////////////////
    // gc0328 Alight lsc reg setting list
    ////Record date: 2013-04-01 15:59:05
    {0xfe, 0x01},
    {0xc0, 0x0d},
    {0xc1, 0x05},
    {0xc2, 0x00},
    {0xc6, 0x07},
    {0xc7, 0x03},
    {0xc8, 0x01},
    {0xba, 0x19},
    {0xbb, 0x10},
    {0xbc, 0x0a},
    {0xb4, 0x19},
    {0xb5, 0x0d},
    {0xb6, 0x09},
    {0xc3, 0x00},
    {0xc4, 0x00},
    {0xc5, 0x0e},
    {0xc9, 0x00},
    {0xca, 0x00},
    {0xcb, 0x00},
    {0xbd, 0x07},
    {0xbe, 0x00},
    {0xbf, 0x0e},
    {0xb7, 0x09},
    {0xb8, 0x00},
    {0xb9, 0x0d},
    {0xa8, 0x01},
    {0xa9, 0x00},
    {0xaa, 0x03},
    {0xab, 0x02},
    {0xac, 0x05},
    {0xad, 0x0c},
    {0xae, 0x03},
    {0xaf, 0x00},
    {0xb0, 0x04},
    {0xb1, 0x04},
    {0xb2, 0x03},
    {0xb3, 0x08},
    {0xa4, 0x00},
    {0xa5, 0x00},
    {0xa6, 0x00},
    {0xa7, 0x00},
    {0xa1, 0x3c},
    {0xa2, 0x50},
    {0xfe, 0x00},
    /// cct
    {0xB1, 0x02},
    {0xB2, 0x02},
    {0xB3, 0x07},
    {0xB4, 0xf0},
    {0xB5, 0x05},
    {0xB6, 0xf0},

    {0xfe, 0x00},
    {0x27, 0xf7},
    {0x28, 0x7F},
    {0x29, 0x20},
    {0x33, 0x20},
    {0x34, 0x20},
    {0x35, 0x20},
    {0x36, 0x20},
    {0x32, 0x08},

    {0x47, 0x00},
    {0x48, 0x00},

    {0xfe, 0x01},
    {0x79, 0x00},
    {0x7d, 0x00},
    {0x50, 0x88},
    {0x5b, 0x04},
    {0x76, 0x8f},
    {0x80, 0x70},
    {0x81, 0x70},
    {0x82, 0xb0},
    {0x70, 0xff},
    {0x71, 0x00},
    {0x72, 0x10},
    {0x73, 0x40},
    {0x74, 0x40},

    {0xfe, 0x00},
    {0x70, 0x45},
    {0x4f, 0x01},
    {0xf1, 0x07},

    {0xf2, 0x01},
    //////////// Set Frame Rate /////////////
    {0xfe, 0x00},
    {0x05, 0x02},
    {0x06, 0x2c}, // HB
    {0x07, 0x00},
    {0x08, 0x88}, // VB
    {0xfe, 0x01},
    {0x29, 0x00},
    {0x2a, 0x4e}, // step
    {0x2b, 0x02}, //high 4bit
    {0x2c, 0x70}, //low 8bit
    {0x2d, 0x03},
    {0x2e, 0x0c},
    {0x2f, 0x05},
    {0x30, 0x00},
    {0x31, 0x0f},	//夜间主循环7帧 //高位是4bit，即最大设为0x0f
    {0x32, 0x00},   //low 8bit
    {0x33, 0x30},	//4档曝光

    {0xfe, 0x00},
    //////////// Set Window /////////////
    {0xfe, 0x00},
    {0x59, 0x22}, // subsampleratio=2
    {0x5a, 0x03},
    {0x5b, 0x00},
    {0x5c, 0x00},
    {0x5d, 0x00},
    {0x5e, 0x00},
    {0x5f, 0x00},
    {0x60, 0x00},
    {0x61, 0x00},
    {0x62, 0x00},
    {0x50, 0x01}, // crop 320x240 //
    {0x51, 0x00},
    {0x52, 0x00},
    {0x53, 0x00},
    {0x54, 0x00},
    {0x55, 0x00},
    {0x56, 0xf0}, // 240
    {0x57, 0x01},
    {0x58, 0x40}, // 320

    {0xFE, 0x00},
    {0xF1, 0x00},
    {0xF2, 0x00},
#else // 帧率更快的配置，裸跑大概30帧
    {0xFE, 0x80},   // [7] soft reset; [1:0] page select 00:REGF 01:REGF1
    {0xFE, 0x80},
    {0xFC, 0x16},   // [4] digital clock enable; [2] da25_en; [1] da18_en
    {0xFC, 0x16},
    {0xFC, 0x16},
    {0xFC, 0x16},

    {0xFE, 0x00},   // [7] soft reset; [1:0] page select 00:REGF 01:REGF1
    {0x4F, 0x00},   // [0] AEC enable
    {0x42, 0x00},   // [7] auto saturation; [6] auto EE; [5] auto DN; [4] auto DD; [3] auto LSC; [2] ABS enable; [1] AWB enable; [0] auto Y offset
    {0x03, 0x00},   // Exposure time high: [3:0] exposure [11:8], use line processing time as the unit. controlled by AEC if AEC is in function
    {0x04, 0xC0},   // Exposure time low: Exposure[7:0], controlled by AEC if AEC is in function
    {0x77, 0x62},   // AWB_R_gain: 2.6bits, AWB red gain, controlled by AWB
    {0x78, 0x40},   // AWB_G_gain: 2.6bits, AWB green gain, controlled by AWB
    {0x79, 0x4D},   // AWB_B_gain: 2.6bits, AWB blue gain, controlled by AWB

    {0x05, 0x00},   // HB high: [3:0] HBLANK high bit[11:8]
    {0x06, 0xDE},   // HB low: HBLANK low bit[7:0]
    {0x07, 0x00},   // VB high: [3:0] VBLANK high bit[11:8]
    {0x08, 0x24},   // VB low: VBLANK low bit[7:0]

    {0xFE , 0x01},   // page1: [7] soft reset; [1:0] page select 00:REGF 01:REGF1
    {0x29 , 0x00},   // AEC_anti_flicker_step[11:8]: Anti-flicker step[11:8]
    {0x2a , 0x83},   // AEC_anti_flicker_step[7:0]: Anti-flicker step[7:0]

#if 0  // 固定曝光时间
    {0x2b , 0x02},   // AEC_exp_level_0
    {0x2c , 0x0C},            
    {0x2D , 0x02},   // AEC_exp_level_1
    {0x2E , 0x0C},   
    {0x2F , 0x02},   // AEC_exp_level_2
    {0x30 , 0x0C},   
    {0x31 , 0x02},   // AEC_exp_level_3 
    {0x32 , 0x0C},   
#else
    {0x2b, 0x02}, //high 4bit
    {0x2c, 0x0c}, //low 8bit
    {0x2d, 0x03},
    {0x2e, 0x0c},
    {0x2f, 0x04},
    {0x30, 0x0c},
    {0x31, 0x05},	//夜间主循环7帧 //高位是4bit，即最大设为0x0f
    {0x32, 0xFF},   //low 8bit
#endif

    {0xFE , 0x01},   // page 1
    {0x51 , 0x80},   // AWB_PRE_THD_min: Dominate luma THD
    {0x52 , 0x12},   
    {0x53 , 0x80},   // AWB_PRE_THD_min_MIX: mix luma number THD
    {0x54 , 0x60},   
    {0x55 , 0x01},   
    {0x56 , 0x06},   
    {0x5B , 0x02},   // mix base gain and adaptive gain limit
    {0x61 , 0xDC}, 
    {0x62 , 0xDC},  
    {0x7C , 0x71},   // adust_speed adjust_margin: [6:4] AWB gain adjust speed, the bigger the quicker; [3:0] if averages of R/G/B's difference is smaller than margin, it means AWB is OK, and AWB will stop
    {0x7D , 0x00},  
    {0x76 , 0x00},  
    {0x79 , 0x20},  
    {0x7B , 0x00},  // show_and_mode: [5] skin_mode; [1] dark_mode
    {0x70 , 0xFF},  
    {0x71 , 0x00},  
    {0x72 , 0x10},  
    {0x73 , 0x40},  
    {0x74 , 0x40},  

    {0x50 , 0x00},  
    {0xFE , 0x01},  // page 1
    {0x4F , 0x00},   
    {0x4C , 0x01},  
    {0x4F , 0x00},  
    {0x4F , 0x00},  
    {0x4F , 0x00},  
    {0x4D , 0x36},  
    {0x4E , 0x02}, 
    {0x4E , 0x02},  
    {0x4D , 0x44}, 
    {0x4E , 0x02},
    {0x4E , 0x02},
    {0x4E , 0x02},
    {0x4E , 0x02},
    {0x4D , 0x53},  
    {0x4E , 0x08},  
    {0x4E , 0x08},  
    {0x4E , 0x02}, 
    {0x4D , 0x63},
    {0x4E , 0x08},
    {0x4E , 0x08},
    {0xFE , 0x00},  // page 0
    {0x4D , 0x73},  // auto_middle_gamma_en: [0] auto middle gamma enable
    {0x4E , 0x20},  
    {0x4D , 0x83},
    {0x4E , 0x20},
    {0x4F , 0x01},  // AEC enable: [0] AEC enable

    {0x50 , 0x88},  // Crop_win_mode: [0] crop window mode enable
    {0xFE , 0x00},  // page 0

    {0x27 , 0x00},  
    {0x2A , 0x40},
    {0x2B , 0x40},
    {0x2C , 0x40},
    {0x2D , 0x40},

    {0xFE , 0x00},  // page 0
    {0x0D,  0x01},  // window height high: [0] Window height high[8]   -- height 488
    {0x0E,  0xE8},  // Window height low: Window height low[7:0]
    {0x0F,  0x02},  // window width high: [1:0] Window width high[9:8]   -- width 648
    {0x10,  0x88},  // window width low: Window width low[7:0]
    {0x09 , 0x00},  // Row start high: [0] row start high bit[8]
    {0x0A , 0x00},  // Row start low:row start low bit[7:0]
    {0x0B , 0x00},  // Col start high: [1:0] col start high bit[9:8]
    {0x0C , 0x00},  // Col start low: col start low bit[7:0]
    {0x16 , 0x00},  // Analog gain: [7] Analog gain enable
    {0x17,  0x14 | CAMERA_DIRECTION}, // 设置摄像头方向
    {0x18 , 0x0E},  // CISCTL_mode2: [7:6] output mode-VGA mode; [5] column binning; [4] double reset mode; [3:2] sdark mode -- sdark 4 rows in each frame; [1] new exposure/normal bad frame; [0] badframe enable
    {0x19 , 0x06},  // CISCTL_mode3: [6] for double restg; [5] restg on/off; [4] capture AD data edge; [3:0] AD pipe number

    {0x1B , 0x48},  // Rsh width: [7:4] restg_width, X2; [3:0] sh_width, X2
    {0x1F , 0xC8},  
    {0x20 , 0x01},
    {0x21 , 0x78},
    {0x22 , 0xB0},
    {0x23 , 0x04},
    {0x24 , 0x16}, //1B
    {0x26 , 0x00},

    {0x50 , 0x01},  // Crop_win_mode: [0] crop window mode enable

    {0x59 , 0x22},  // subsample: [7:4] subsample row ratio; [3:0] subsample col ratio
    {0x51 , 0x00},  // Crop_win_y1
    {0x52 , 0x00},
    {0x53 , 0x00},  // Crop_win_x1
    {0x54 , 0x00},
    {0x55 , 0x00},  // Crop_win_height
    {0x56 , 0xF0},  
    {0x57 , 0x01},  // Crop_win_width
    {0x58 , 0x40},

    {0x70 , 0x85},  // Global gain

    {0x40 , 0x7F},  // Block_enable_1: [7] middle gamma enable; [6] gamma enable; [5] CC enable; [4] Edge enhancement enable, [3] Interpolation enable; [2] Noise removal enable; [1] Defect removal enable; [0] Lens-shading correction enable
    {0x41 , 0x26},  // Block_enable_2: [6] low light Y enable; [5] skin enable; [4] skin Y enable; [3] new skin mode; [2] autogray enable; [1] Y gamma enable; [0] block skin
    {0x42 , 0xFF},  // [7] auto saturation; [6] auto EE; [5] auto DN; [4] auto DD; [3] auto LSC; [2] ABS enable; [1] AWB enable; [0] auto Y offset
    {0x45 , 0x00},  // Auto middle gamma mode: [1] auto gamma mode outdoor; [0] auto gamma mode lowlight
    {0x44 , 0x06},  // Output_format: RGB565
    {0x46 , 0x03},  // SYNC_mode: [1] HSYNC polarity; [0] VSYNC polarity
                    // Xel chaneg it, original 0x02, it must be 0x03!!!

    {0x4B , 0x01},  // Debug mode 1: [0] update gain mode
    {0x50 , 0x01},  // Crop_win_mode: [0] crop window mode enable

    {0x7E , 0x0A},   
    {0x7F , 0x03},
    {0x80 , 0x27},
    {0x81 , 0x15},
    {0x82 , 0x90},
    {0x83 , 0x02},
    {0x84 , 0x23},
    {0x90 , 0x2C},
    {0x92 , 0x02},
    {0x94 , 0x02},
    {0x95 , 0x35},

    {0xD1 , 0x32},  // Cb saturation
    {0xD2 , 0x32},  // Cr saturation
    {0xDD , 0x18},  
    {0xDE , 0x32},
    {0xE4 , 0x88},
    {0xE5 , 0x40},
    {0xD7 , 0x0E},

    {0xFE , 0x00},  // page 0

#if 1 // LSC配置
    {0xBF , 0x10},
    {0xC0 , 0x1C},
    {0xC1 , 0x33},
    {0xC2 , 0x48},
    {0xC3 , 0x5A},
    {0xC4 , 0x6B},
    {0xC5 , 0x7B},
    {0xC6 , 0x95},
    {0xC7 , 0xAB},
    {0xC8 , 0xBF},
    {0xC9 , 0xCD},
    {0xCA , 0xD9},
    {0xCB , 0xE3},
    {0xCC , 0xEB},
    {0xCD , 0xF7},
    {0xCE , 0xFD},
    {0xCF , 0xFF},
#else
    {0xBF, 0x14},
    {0xc0, 0x28},
    {0xc1, 0x44},
    {0xc2, 0x5D},
    {0xc3, 0x72},
    {0xc4, 0x86},
    {0xc5, 0x95},
    {0xc6, 0xB1},
    {0xc7, 0xC6},
    {0xc8, 0xD5},
    {0xc9, 0xE1},
    {0xcA, 0xEA},
    {0xcB, 0xF1},
    {0xcC, 0xF5},
    {0xcD, 0xFB},
    {0xcE, 0xFE},
    {0xcF, 0xFF},
#endif

    {0xFE , 0x00},  // page 0
    {0x63 , 0x00},
    {0x64 , 0x05},
    {0x65 , 0x0C},
    {0x66 , 0x1A},
    {0x67 , 0x29},
    {0x68 , 0x39},
    {0x69 , 0x4B},
    {0x6A , 0x5E},
    {0x6B , 0x82},
    {0x6C , 0xA4},
    {0x6D , 0xC5},
    {0x6E , 0xE5},
    {0x6F , 0xFF},

    {0xFE , 0x01},  // page 1
    {0x18 , 0x02},
    {0xFE , 0x00},  // page 0
    {0x98 , 0x00},
    {0x9B , 0x20},
    {0x9C , 0x80},
    {0xA4 , 0x10},
    {0xA8 , 0xB0},
    {0xAA , 0x40},
    {0xA2 , 0x23},
    {0xAD , 0x01},

    {0xFE , 0x01},  // page 1
    {0x9C , 0x02},
    {0x08 , 0xA0},
    {0x09 , 0xE8},

    {0x10 , 0x00},
    {0x11 , 0x11},
    {0x12 , 0x10},
    {0x13 , 0xc0},//Mike :expected luminance value, original 0x80
    {0x15 , 0xFC},
    {0x18 , 0x03},
    {0x21 , 0xC0},
    {0x22 , 0xa0},//Mike:the max pre-gain AEC can output,original 0x60
    {0x23 , 0x30},
    {0x25 , 0x00},
    {0x24 , 0x16}, //1B

    {0xFE , 0x01},  // page 1
    {0xC0 , 0x10},
    {0xC1 , 0x0C},
    {0xC2 , 0x0A},
    {0xC6 , 0x0E},
    {0xC7 , 0x0B},
    {0xC8 , 0x0A},
    {0xBA , 0x26},
    {0xBB , 0x1C},
    {0xBC , 0x1D},
    {0xB4 , 0x23},
    {0xB5 , 0x1C},
    {0xB6 , 0x1A},
    {0xC3 , 0x00},
    {0xC4 , 0x00},
    {0xC5 , 0x00},
    {0xC9 , 0x00},
    {0xCA , 0x00},
    {0xCB , 0x00},
    {0xBD , 0x00},
    {0xBE , 0x00},
    {0xBF , 0x00},
    {0xB7 , 0x07},
    {0xB8 , 0x05},
    {0xB9 , 0x05},
    {0xA8 , 0x07},
    {0xA9 , 0x06},
    {0xAA , 0x00},
    {0xAB , 0x04},
    {0xAC , 0x00},
    {0xAD , 0x02},
    {0xAE , 0x0D},
    {0xAF , 0x05},
    {0xB0 , 0x00},
    {0xB1 , 0x07},
    {0xB2 , 0x03},
    {0xB3 , 0x00},
    {0xA4 , 0x00},
    {0xA5 , 0x00},
    {0xA6 , 0x00},
    {0xA7 , 0x00},
    {0xA1 , 0x3C},
    {0xA2 , 0x50},
    {0xFE , 0x00},  // page 0

    {0xB1 , 0x04},  
    {0xB2 , 0xFD},
    {0xB3 , 0xFC},
    {0xB4 , 0xF0},
    {0xB5 , 0x05},
    {0xB6 , 0xF0},

    {0xFE , 0x00},  // page 0
    {0x27 , 0xF7},
    {0x28 , 0x7F},
    {0x29 , 0x20},
    {0x33 , 0x20},
    {0x34 , 0x20},
    {0x35 , 0x20},
    {0x36 , 0x20},
    {0x32 , 0x08},

    {0x47 , 0x00},
    {0x48 , 0x00},

    {0xFE , 0x01},  // page 1
    {0x79 , 0x00},  
    {0x7D , 0x00},
    {0x50 , 0x88},  // AWB_PRE_mode: [7] PRE_enable; [3] AWB_PRE_adjust_speed enable
    {0x5B , 0x0C},  
    {0x76 , 0x8F},
    {0x80 , 0x70},
    {0x81 , 0x70},
    {0x82 , 0xB0},
    {0x70 , 0xFF},
    {0x71 , 0x00},
    {0x72 , 0x28},
    {0x73 , 0x0B},
    {0x74 , 0x0B},

    {0xFE , 0x00},  // page 0
    {0x70 , 0x50},  // global gain.Mike
    {0x4F , 0x01},  // AEC enable
    {0xF1 , 0x00},  // Pad_setting1: [2] pclk output enable; [1] HSYNC output enable; [0] VSYNC output enable
    {0xF2 , 0x00},  // Pad_setting2: [0] data output enable
#endif
    {0x00, 0x00},
};

///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
