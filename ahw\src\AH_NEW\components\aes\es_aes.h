/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_aes.h
** bef: define the interface for aes
** auth: lines<<EMAIL>>
** create on 2021.05.13 
*/

#ifndef _ES_AES_H_
#define _ES_AES_H_

#ifdef __cplusplus 
extern "C" { 
#endif

#include "es_types.h"

ES_S32 es_aes_cbc128_encrypt(ES_BYTE *out, ES_U32 *out_len, ES_BYTE *in, ES_U32 in_len);

ES_S32 es_aes_cbc128_decrypt(ES_BYTE *out, ES_U32 *out_len, ES_BYTE *in, ES_U32 in_len);


#ifdef __cplusplus
}
#endif
#endif