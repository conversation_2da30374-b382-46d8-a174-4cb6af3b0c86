#ifndef _ES_HAL_RTC_H_
#define _ES_HAL_RTC_H_

#ifdef __cplusplus 
extern "C" { 
#endif

#include "es_types.h"

typedef struct {
    ES_U16 year;
    ES_U8 mon;
    ES_U8 mday;

    ES_U8 hour;
    ES_U8 min;
    ES_U8 sec;
    ES_U8 wday;
} es_hal_rtc_time_t;

ES_S32 es_hal_rtc_init(ES_VOID);

ES_S32 es_hal_rtc_read_time(es_hal_rtc_time_t *time);

ES_S32 es_hal_rtc_write_time(const es_hal_rtc_time_t *time);

#ifdef __cplusplus 
}
#endif
#endif