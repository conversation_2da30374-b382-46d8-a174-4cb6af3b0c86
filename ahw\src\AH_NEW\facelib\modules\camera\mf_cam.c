#include "facelib_inc.h"

/*****************************************************************************/
// Function definitions
/*****************************************************************************/

/*****************************************************************************/
// Private Var 局部变量
/*****************************************************************************/


/*****************************************************************************/
// Public Var 全局变量
/*****************************************************************************/
mf_cam_t mf_cam;
extern mf_cam_t mf_dualcam_gc0328;

/*****************************************************************************/
// Driver 底层驱动
/*****************************************************************************/
static void mf_dvp_reset(void)
{
    /* First power down */
    dvp->cmos_cfg |= DVP_CMOS_POWER_DOWN;
    msleep(5);
    dvp->cmos_cfg &= ~DVP_CMOS_POWER_DOWN;
    msleep(5);

    /* Second reset */
    dvp->cmos_cfg &= ~DVP_CMOS_RESET;
    msleep(5);
    dvp->cmos_cfg |= DVP_CMOS_RESET;
    msleep(5);
}

void mf_dvp_init(uint8_t reg_len)
{
    sysctl_clock_enable(SYSCTL_CLOCK_DVP);
    sysctl_reset(SYSCTL_RESET_DVP);
    dvp->cmos_cfg &= (~DVP_CMOS_CLK_DIV_MASK);
    dvp->cmos_cfg |= DVP_CMOS_CLK_DIV(3) | DVP_CMOS_CLK_ENABLE;
    mf_dvp_reset();
}

uint32_t mf_dvp_set_xclk_rate(uint32_t xclk_rate)
{
    uint32_t v_apb1_clk = sysctl_clock_get_freq(SYSCTL_CLOCK_APB1);
    uint32_t v_period;
    if(v_apb1_clk > xclk_rate * 2)
        v_period = round(v_apb1_clk / (xclk_rate * 2.0)) - 1;
    else
        v_period = 0;
    if(v_period > 255)
        v_period = 255;
    dvp->cmos_cfg &= (~DVP_CMOS_CLK_DIV_MASK);
    dvp->cmos_cfg |= DVP_CMOS_CLK_DIV(v_period) | DVP_CMOS_CLK_ENABLE;
    mf_dvp_reset();
    return v_apb1_clk / ((v_period + 1) * 2);
}


/*****************************************************************************/
// Public 全局函数
/*****************************************************************************/
mf_err_t mf_camera_choose(mf_cam_type_t type)
{
	mf_err_t err = MF_ERR_NONE;
	switch(type)
	{
	case MF_CAM_GC0328_DUAL:
		memcpy(&mf_cam, &mf_dualcam_gc0328, sizeof(mf_cam_t));
		break;
	// case MF_CAM_GC0328_SINGLE:
	// 	memcpy(&mf_cam, &mf_singlecam_gc0328, sizeof(mf_cam_t));
	// 	break;
	case MF_CAM_OV2640_SINGLE:
		err = MF_ERR_CAM_TYPE;
		break;
	default:
		err = MF_ERR_CAM_TYPE;
		break;
	}
	return err;
}
