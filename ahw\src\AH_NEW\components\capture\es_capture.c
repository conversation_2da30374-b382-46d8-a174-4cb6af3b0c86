/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_capture.c
** bef: define the interface for image capture.
** auth: lines<<EMAIL>>
** create on 2023.01.11 
*/

#include "es_inc.h"
#if ES_CAPTURE_ENABLE
#include "facelib_inc.h"
#if ES_FACE_UPLOAD_ENABLE
#include "jpeg_encode.h"
#endif

#define ES_CAPTURE_DEBUG
#ifdef ES_CAPTURE_DEBUG
#define es_capture_debug es_log_info
#define es_capture_error es_log_error
#else
#define es_capture_debug(...)
#define es_capture_error(...)
#endif

#define ES_FACECD_SAME_UID_TIMEOUT_SEC          (5)
#if ES_FACE_UPLOAD_ENABLE
#define ES_FACE_PIC_JPEG_ENCODE_BUF_LEN         (20 * 1024)
#define ES_FACE_PIC_JPEG_ENCODE_QUALITY         (50) /* 1-34, 35-59, 60+ */
#endif

#if ES_CAM_DIR 
// vertical
#define JPEG_SRC_WIDTH      (240)
#define JPEG_SRC_HEIGHT     (320)
#else
#define JPEG_SRC_WIDTH      (320)
#define JPEG_SRC_HEIGHT     (240)
#endif

#if ES_FACE_UPLOAD_ENABLE
#if CONFIG_APP_WITH_OTA
    /* max_size JPEG_ENCODE_BUF_LEN */
    static uint8_t *face_pic_jpeg_encode_buf = (uint8_t*)(uintptr_t)0x80000000;
#else
    static uint8_t face_pic_jpeg_encode_buf[ES_FACE_PIC_JPEG_ENCODE_BUF_LEN];
#endif /* CONFIG_APP_WITH_OTA */
#endif

static ES_BOOL capture_open = ES_FALSE;
static ES_U32 capture_interval_sec = 0;
static ES_U32 capture_time = 0;

ES_S32 es_capture_upload_pic(ES_U32 timestamp, ES_U8 type)
{
    jpeg_encode_t jpeg_src, jpeg_out;
    uint8_t *cam_buf = ES_NULL;

    cam_buf = mf_cam.rgb_image[mf_cam.rgb_buf_index];
    jpeg_src.w = JPEG_SRC_WIDTH;
    jpeg_src.h = JPEG_SRC_HEIGHT;
    jpeg_src.bpp = 2;
    jpeg_src.data = cam_buf;

    jpeg_out.w = JPEG_SRC_WIDTH;
    jpeg_out.h = JPEG_SRC_HEIGHT;
    jpeg_out.bpp = ES_FACE_PIC_JPEG_ENCODE_BUF_LEN;
    jpeg_out.data = face_pic_jpeg_encode_buf;

    convert_rgb565_order((uint16_t*)cam_buf, JPEG_SRC_WIDTH, JPEG_SRC_HEIGHT);
    if (jpeg_compress(&jpeg_src, &jpeg_out, ES_FACE_PIC_JPEG_ENCODE_QUALITY, 0) != 0) {
        convert_rgb565_order((uint16_t*)cam_buf, JPEG_SRC_WIDTH, JPEG_SRC_HEIGHT);
        es_ui_cam_update(ES_TRUE);
        es_capture_debug("jpeg compress error");
        capture_time = es_time_get_timestamp();
        return ES_RET_FAILURE;
    }
    convert_rgb565_order((uint16_t*)cam_buf, JPEG_SRC_WIDTH, JPEG_SRC_HEIGHT);
    es_ui_cam_update(ES_TRUE);

    es_capture_debug("jpeg length:%d", jpeg_out.bpp);
    es_ntework_upload_pic((const ES_BYTE *)jpeg_out.data, jpeg_out.bpp, timestamp, type);
    capture_time = es_time_get_timestamp();

    return ES_RET_SUCCESS;
}

ES_S32 es_capture_init(ES_VOID)
{
    return ES_RET_SUCCESS;
}

ES_S32 es_capture_run(ES_VOID)
{
    ES_U32 now_time;

    if (!capture_open || 0 == capture_time) {
        return ES_RET_SUCCESS;
    }

    // if (capture_interval_sec < 10) {
    //     return ES_RET_SUCCESS;
    // }

    now_time = es_time_get_timestamp();
    if ((now_time - capture_time) < capture_interval_sec) {
        return ES_RET_SUCCESS;
    }

    es_capture_upload_pic(now_time, ES_FACECB_CAPTURE);

    return ES_RET_SUCCESS;
}

ES_S32 es_captrue_open(ES_U32 interval_sec)
{
    if (interval_sec < ES_CAPTURE_MIN_INTERVAL_SEC || ES_CAPTURE_MAX_INTERVAL_SEC < interval_sec) {
        return ES_RET_FAILURE;
    }

    capture_interval_sec = interval_sec;
    capture_open = ES_TRUE;
    capture_time = es_time_get_timestamp();

    return ES_RET_SUCCESS;
}

ES_S32 es_captrue_close(ES_VOID)
{
    capture_open = ES_FALSE;
    capture_interval_sec = 0;
    capture_time = 0;

    return ES_RET_SUCCESS;
}

#endif