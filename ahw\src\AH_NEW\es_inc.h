#ifndef _ES_INC_H_
#define _ES_INC_H_

#include <math.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stddef.h>
#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>
#include <float.h>
#include <math.h>

// sdk
#include "entry.h"
#include "plic.h"
#include "printf.h"
#include "sha256.h"
#include "sysctl.h"
#include "sleep.h"
#include "wdt.h"
#include "uart.h"
#include "fpioa.h"
#include "gpiohs.h"
#include "gpio.h"
#include "gpio_common.h"
#include "pwm.h"
#include "rtc.h"
#include "atomic.h"
#include "i2c.h"
#include "i2s.h"
#include "timer.h"

// 3th party
#include "pt.h"
#include "pt-sem.h"
#include "lvgl.h"
#include "lv_hal_disp.h"
#include "lv_hal_indev.h"
#include "lv_fs_if.h"
#include "lv_sjpg.h"
#include "lv_qrcode.h"
#include "ff.h"
#include "cJSON.h"
#include "cJSON_Utils.h"
#include "base64.h"
#include "md5.h"

// face lib
// #include "facelib_inc.h"

// es
#include "es_constants.h"
#include "es_config.h"
#include "es_types.h"
#include "es_init.h"
#include "es_bsp.h"
#include "es_hal_i2c.h"
#include "es_hal_rtc.h"
#include "es_hal_lcd.h"
#include "es_hal_wdg.h"
#include "es_hal_ir.h"
#include "es_hal_led.h"
#include "es_hal_ble.h"
#include "es_hal_lte.h"
#include "es_hal_wifi.h"
#include "es_ble.h"
#include "es_ble_proto.h"
#include "es_hal_uart.h"
#include "es_hal_sys.h"
#include "es_cache.h"
#include "es_circle_buf.h"
#include "es_log.h"
#include "es_ui.h"
#include "es_ui_cam.h"
#include "es_ui_face.h"
#include "es_ui_font.h"
#include "es_ui_infobar.h"
#include "es_ui_saver.h"
#include "es_ui_factory.h"
#include "es_facecb.h"
#include "es_utils.h"
#include "es_time.h"
#include "facedb_wrapper.h"
#include "es_string.h"
#include "es_task_queue.h"
#include "es_fs.h"
#include "es_tuya_port.h"
#include "es_tuya_dp.h"
#include "es_network.h"
#include "es_network_ble.h"
#include "es_network_lte.h"
#include "es_network_wifi.h"
#include "es_network_tuya.h"
#include "es_network_httpc.h"
#include "es_network_mqtt.h"
#include "es_gps_minmea.h"
#include "es_key.h"
#include "es_passlog.h"
#include "es_relay.h"
#include "es_door.h"
#include "es_audio.h"
#include "es_voice.h"
#include "es_serv_proto.h"
#include "es_ota.h"
#include "es_ota_dl.h"
#include "es_crc.h"
#include "es_mem.h"
#include "es_flash.h"
#include "es_model.h"
#include "es_aes.h"
#include "es_dev_cfg.h"
#include "es_resource.h"
#include "es_capture.h"
#include "es_safetybelt.h"
#include "es_spec_car.h"

#endif