/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_network_ble.c
** bef: define the interface for ble network.
** auth: lines<<EMAIL>>
** create on 2021.12.21 
*/

#include "es_inc.h"

// #define ES_NETWORK_DEBUG
#ifdef ES_NETWORK_DEBUG
#define es_network_debug es_log_info
#define es_network_error es_log_error
#else
#define es_network_debug(...)
#define es_network_error(...)
#endif

#define ES_UPLOAD_PIC_USE_BASE64            (0)

#if (0 == ES_TUYA_MODULE_ENABLE)
static ES_S32 network_get_jpg_name(ES_CHAR *filename, ES_U32 timestamp)
{
    ES_CHAR mqtt_id[ES_MAC_COLON_STR_LEN] = {0};
    if (ES_RET_SUCCESS != es_hal_lte_get_mqtt_id(mqtt_id)) {
        return ES_RET_FAILURE;
    }

    snprintf(filename, ES_HTTP_HEADER_VALUE_LEN, "%s-%d", mqtt_id, timestamp);
    return ES_RET_SUCCESS;
}

static ES_S32 network_comm_upload_pic(const ES_BYTE *jpeg, ES_U32 jpeg_len, ES_U32 timestamp, ES_U8 type)
{
    ES_S32 ret = ES_RET_SUCCESS;

#if ES_UPLOAD_PIC_USE_BASE64
    ES_CHAR *jpg_base64 = ES_NULL;
    ES_U32 jpg_base64_len = 0;
    ES_BYTE *data = ES_NULL;
    ES_U32 data_len = 0;
    ES_CHAR filename[ES_HTTP_HEADER_VALUE_LEN] = {0};

    jpg_base64 = (ES_CHAR *)base64_encode(jpeg, jpeg_len, &jpg_base64_len);
    if (ES_NULL == jpg_base64 || 0 == jpg_base64_len) {
        ret = ES_RET_FAILURE;
        goto END;
    }

    network_get_jpg_name(filename, timestamp);
    if (0 == filename[0]) {
        ret = ES_RET_FAILURE;
        goto END;
    }

    data_len = (((jpg_base64_len+256)/1024)+1)*1024;
    data = (ES_BYTE *)es_malloc(data_len);
    if (ES_NULL == data) {
        ret = ES_RET_FAILURE;
        goto END;
    }

    data_len = sprintf((ES_CHAR *)data, "{\"FileName\":\"%s\",\"FileType\":%d,\"PicStr\":\"%s\"}",
        (const ES_CHAR*)filename, type, jpg_base64);
    ret = es_network_httpc_upload_pic(ES_HTTP_UPLOAD_FACE_URL, data, data_len, ES_NULL, 0);

END:
    if (ES_NULL != jpg_base64) {
        es_free(jpg_base64);
        jpg_base64 = ES_NULL;
    }

    if (ES_NULL != data) {
        es_free(data);
        data = ES_NULL;
    }
#else
#define HTTP_UPLOAD_FACE_PIC_URL_LEN        (128)
    ES_CHAR face_pic_url[HTTP_UPLOAD_FACE_PIC_URL_LEN]={0};
    ES_CHAR filename[ES_HTTP_HEADER_VALUE_LEN] = {0};

    network_get_jpg_name(filename, timestamp);
    if (0 == filename[0]) {
        return ES_RET_FAILURE;
    }

    sprintf(face_pic_url, ES_HTTP_UPLOAD_FACE_URL, type, filename);
    ret = es_network_httpc_upload_pic(face_pic_url, jpeg, jpeg_len, ES_NULL, 0);
#endif
    return ret;
}
#endif

ES_S32 es_network_init(ES_VOID)
{
#if ES_BLE_MODULE_ENABLE
    if (ES_RET_SUCCESS != es_network_ble_init()) {
        es_network_debug("ble init fail");
        return ES_RET_FAILURE;
    }
#endif

#if ES_4GLTE_ENABLE
    if (ES_RET_SUCCESS != es_network_lte_init()) {
        es_network_debug("lte init fail");
        return ES_RET_FAILURE;
    }
#endif

#if ES_TUYA_MODULE_ENABLE
    if (ES_RET_SUCCESS != es_network_tuya_init()) {
        es_network_debug("tuya init fail");
        return ES_RET_FAILURE;
    }
#endif

#if ES_MQTT_ENABLE
    if (ES_RET_SUCCESS != es_network_mqtt_init()) {
        es_network_debug("mqtt init fail");
        return ES_RET_FAILURE;
    }
#endif

#if ES_HTTP_ENABLE
    if (ES_RET_SUCCESS != es_network_httpc_init()) {
        es_network_debug("httpc init fail");
        return ES_RET_FAILURE;
    }
#endif

    return ES_RET_SUCCESS;
}

ES_VOID es_network_task(ES_VOID)
{
#if ES_BLE_MODULE_ENABLE
    es_network_ble_task();
#endif

#if ES_4GLTE_ENABLE
    es_network_lte_task();
#endif

#if ES_TUYA_MODULE_ENABLE
    es_network_tuya_task();
#endif

#if ES_MQTT_ENABLE
    es_network_mqtt_task();
#endif
}

ES_S32 es_network_get_status(ES_VOID)
{
#if ES_4GLTE_ENABLE
    if (ES_NETWORK_CONNECTED == es_network_lte_get_status()) {
        return ES_NETWORK_CONNECTED;
    }
#endif

#if ES_TUYA_MODULE_ENABLE
    if (ES_NETWORK_CONNECTED == es_network_tuya_get_status()) {
        return ES_NETWORK_CONNECTED;
    }
#endif

#if ES_BLE_MODULE_ENABLE
    if (ES_NETWORK_CONNECTED == es_network_ble_get_status()) {
        return ES_NETWORK_CONNECTED;
    }
#endif

    return ES_NETWORK_CONNECTING;
}

ES_U32 es_network_get_mac(ES_BYTE *mac)
{
#if ES_4GLTE_ENABLE
    return es_hal_lte_get_mac(mac);
#endif

#if ES_BLE_MODULE_ENABLE
    return es_ble_read_mac(mac);
#endif

    return ES_RET_FAILURE;
}

ES_U8 es_network_get_rssi(ES_VOID)
{
#if ES_4GLTE_ENABLE
    return es_hal_lte_get_rssi();
#endif
    return 0;
}

ES_S32 es_network_upload_pass_log(const ES_BYTE *uid, ES_U32 timestamp, es_network_pass_type_e type, ES_U32 expire, ES_U32 status)
{
#if ES_TUYA_MODULE_ENABLE
    return es_dp_upload_face_data((unsigned char *)uid, timestamp, ES_DP_FACE_DATA_PASS);
#endif

#if ES_MQTT_ENABLE
    return es_network_mqtt_upload_pass_log(uid, timestamp, ES_DP_FACE_DATA_PASS, expire, status);
#endif

    return ES_RET_FAILURE;
}

// type is es_facecb_type_e.
ES_S32 es_ntework_upload_pic(const ES_BYTE *pic_data, ES_U32 pic_data_len, ES_U32 timestamp, ES_U8 type)
{
#if ES_TUYA_MODULE_ENABLE
    return es_network_tuya_upload_pic(pic_data, pic_data_len, type);
#else
    return network_comm_upload_pic(pic_data, pic_data_len, timestamp, type);
#endif
}

