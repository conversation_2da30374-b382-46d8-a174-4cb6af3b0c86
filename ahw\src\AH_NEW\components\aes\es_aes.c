/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_aes.c
** bef: implement the interface for aes
** auth: lines<<EMAIL>>
** create on 2021.05.13 
*/

#include "es_inc.h"
#include "aes.h"

// V5ictZgTItHMOjqh
// 'V', '5', 'i', 'c', 't', 'Z', 'g', 'T', 'I', 't', 'H', 'M', 'O', 'j', 'q', 'h',
// AIHARDWARE-2021-
// 'A', 'I', 'H', 'A', 'R', 'D', 'W', 'A', 'R', 'E', '-', '2', '0', '2', '1', '-'
// RA5QIUE3nol8PMPW
// 'R', 'A', '5', 'Q', 'I', 'U', 'E', '3', 'n', 'o', 'l', '8', 'P', 'M', 'P', 'W'
// uint8_t aes_key[] = {'A', 'I', 'H', 'A', 'R', 'D', 'W', 'A', 'R', 'E', '-', '2', '0', '2', '1', '-',
//                      'R', 'A', '5', 'Q', 'I', 'U', 'E', '3', 'n', 'o', 'l', '8', 'P', 'M', 'P', 'W'};
static uint8_t aes_key[] = {'V', '5', 'i', 'c', 't', 'Z', 'g', 'T', 'I', 't', 'H', 'M', 'O', 'j', 'q', 'h',
                            'A', 'I', 'H', 'A', 'R', 'D', 'W', 'A', 'R', 'E', '-', '2', '0', '2', '1', '-'};
static uint8_t aes_iv[]  = {'R', 'A', '5', 'Q', 'I', 'U', 'E', '3', 'n', 'o', 'l', '8', 'P', 'M', 'P', 'W',
                            'A', 'I', 'H', 'A', 'R', 'D', 'W', 'A', 'R', 'E', '-', '2', '0', '2', '1', '-'};
size_t key_len = AES_256;

ES_S32 es_aes_cbc128_encrypt(ES_BYTE *out, ES_U32 *out_len, ES_BYTE *in, ES_U32 in_len)
{
    cbc_context_t cbc_context;
    cbc_context.input_key = aes_key;
    cbc_context.iv = aes_iv;

    aes_cbc128_hard_encrypt(&cbc_context, in, in_len, out);

    *out_len = (in_len + 15) / 16 * 16;
    return ES_RET_SUCCESS;
}

ES_S32 es_aes_cbc128_decrypt(ES_BYTE *out, ES_U32 *out_len, ES_BYTE *in, ES_U32 in_len)
{
    cbc_context_t cbc_context;
    cbc_context.input_key = aes_key;
    cbc_context.iv = aes_iv;

    aes_cbc128_hard_decrypt(&cbc_context, in, in_len, out);

    *out_len = (in_len + 15) / 16 * 16;
    return ES_RET_SUCCESS;
}

