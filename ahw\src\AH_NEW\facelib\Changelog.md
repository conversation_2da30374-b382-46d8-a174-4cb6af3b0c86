# Changelog

## 2020-01-02

- 新的活体模型

```
新版活体模型移植：
模型输入为simility的128x128灰度图的三个crop，取R通道即可
三个crop的(x,y,w,h)为：
eye:  (44,40,40,24)
nose: (44,74,40,24)
mouth:  (44,86,40,24)
初始化模型（以eye为例）：
if (kpu_load_kmodel(&as_eye_task, as_eye_model_data) != 0)
{
 printf("\nas eye model init error\n");
 while (1)
  ;
}
使用模型
image_crop(&similarity_image, &eye_img, 44, 40);
g_ai_done_flag = 0;
kpu_run_kmodel(&as_eye_task, eye_img.addr, 5, ai_done, NULL);
while (!g_ai_done_flag)
 ;
kpu_get_output(&as_eye_task, 0, (uint8_t *)&result, &result_size);
as_result[0] = result[1];
同理将nose和mouth的result[1]存到as_result[1]，as_result[2]

float fc_weights[3]={0.41,1.807,1.685};
float fc_res = fc_weights[0]*as_result[0]+fc_weights[1]*as_result[1]+fc_weights[2]*as_result[2];
if (fc_res < 3.2){假}
else {真}
```

