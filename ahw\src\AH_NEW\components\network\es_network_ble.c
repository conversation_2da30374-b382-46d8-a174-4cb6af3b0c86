/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_network_ble.c
** bef: define the interface for ble network.
** auth: lines<<EMAIL>>
** create on 2021.12.21 
*/

#include "es_inc.h"

#if ES_BLE_MODULE_ENABLE


ES_S32 es_network_ble_init(ES_VOID)
{
    if (ES_RET_SUCCESS != es_ble_init()) {
        return ES_RET_FAILURE;
    }

    return ES_RET_SUCCESS;
}

ES_VOID es_network_ble_task(ES_VOID)
{
    es_ble_run();
}

ES_S32 es_network_ble_get_status(ES_VOID)
{
    if (es_ble_is_connected()) {
        return ES_NETWORK_CONNECTED;
    }
    return ES_NETWORK_CONNECTING;
}

#endif
