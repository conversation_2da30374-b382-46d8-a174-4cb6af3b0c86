/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_circle_buf.c
** bef: implement the interface for circle bufrer. 
** auth: lines<<EMAIL>>
** create on 2019.11.16 
*/

#include "es_inc.h"


#define CIRCLE_USE_COUNT        (0)

typedef struct {
    ES_U16 size;
    ES_U16 start;
    ES_U16 end;
#if CIRCLE_USE_COUNT
    ES_U16 count;
#endif
    ES_BYTE *data;
}es_circle_buf_t;

static ES_BOOL es_circle_buf_is_full(const ES_VOID *b) {
    es_circle_buf_t *buf = (es_circle_buf_t *)b;

#if CIRCLE_USE_COUNT
     if (buf->count == buf->size) {
#else
    if (((buf->end+1) % buf->size) == buf->start) {
#endif
        return ES_TRUE;
    }

    return ES_FALSE;
}

static ES_BOOL es_circle_buf_is_empty(const ES_VOID *b) {
    es_circle_buf_t *buf = (es_circle_buf_t *)b;

#if CIRCLE_USE_COUNT
    if (0 == buf->count) {
#else
    if (buf->end == buf->start) {
#endif
        return ES_TRUE;
    }

    return ES_FALSE;
}

ES_S32 es_circle_buf_free(ES_VOID *b)
{
    es_circle_buf_t *buf = ES_NULL;

    buf = (es_circle_buf_t *)b;
    if (!buf) {
        return ES_RET_PARAM_ERR;
    }

    if (buf->data) {
        es_free(buf->data);
    }

    es_free(buf);
    buf = ES_NULL;

    return ES_RET_SUCCESS;
}

ES_VOID *es_circle_buf_new(ES_U16 size)
{
    es_circle_buf_t *b = ES_NULL;

    b = (es_circle_buf_t *)es_malloc(sizeof(es_circle_buf_t));
    if (ES_NULL == b) {
        return NULL;
    }

    es_memset(b, 0x00, sizeof(es_circle_buf_t));
    b->size = size;
    b->data = (ES_BYTE *)es_malloc(size);
    if (ES_NULL == b->data) {
        es_circle_buf_free(b);
        return ES_NULL;
    }

    return (ES_VOID *)b;
}


ES_S32 es_circel_write_byte(ES_VOID *b, ES_BYTE c)
{
    es_circle_buf_t *buf = (es_circle_buf_t *)b;

    if (!b) {
        return ES_RET_FAILURE;
    }

    if (es_circle_buf_is_full(b)) {
        return ES_RET_FAILURE;
    }

    buf->data[buf->end] = c;
    buf->end = (buf->end+1) % buf->size;
#if CIRCLE_USE_COUNT
    buf->count++;
#endif

    return (ES_RET_SUCCESS);
}

ES_S32 es_circel_write(ES_VOID *b, const ES_BYTE *data, ES_U16 len)
{
    ES_S32 i = 0;

    for (i = 0; i < len; i++) {
        if (ES_RET_SUCCESS != es_circel_write_byte(b, (ES_BYTE)data[i])) {
            break;
        }
    }

    return i;
}


ES_S32 es_circel_read_byte(ES_VOID *b, ES_BYTE *c)
{
    es_circle_buf_t *buf = (es_circle_buf_t *)b;
    
    if (!b) {
        return ES_RET_FAILURE;
    }

    if (es_circle_buf_is_empty(b)) {
        return ES_RET_FAILURE;
    }

    *c = buf->data[buf->start];
    buf->start = (buf->start+1) % buf->size;
#if CIRCLE_USE_COUNT
    buf->count--;
#endif

    return (ES_RET_SUCCESS);
}

ES_S32 es_circel_read(ES_VOID *b, const ES_BYTE *data, ES_U16 len)
{
    ES_S32 i = 0;

    for (i = 0; i < len; i++) {
        if (ES_RET_SUCCESS != es_circel_read_byte(b, (ES_BYTE *)&data[i])) {
            break;
        }
    }

    return i;
}

ES_S32 es_circle_buf_reset(ES_VOID *b)
{
    es_circle_buf_t *buf = (es_circle_buf_t *)b;
    
    if (!b) {
        return ES_RET_FAILURE;
    }

    buf->start = 0;
    buf->end = 0;
#if CIRCLE_USE_COUNT
    buf->count = 0;
#endif
    return ES_RET_SUCCESS;
}

ES_U32 es_circel_get_data_size(ES_VOID *b)
{
    es_circle_buf_t *buf = (es_circle_buf_t *)b;
#if (0 == CIRCLE_USE_COUNT)
    ES_U16 start = 0;
    ES_U16 end = 0;
#endif
    if (!b) {
        return ES_RET_FAILURE;
    }
#if CIRCLE_USE_COUNT
    return (ES_S32) buf->count;
#else
    start = buf->start;
    end = buf->end;

    if (start == end) {
        return 0;
    }

    if (start > end) {
        return ((buf->size+end) - start);
    }
    
    return end - start;
#endif
}




