/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_task_queue.c
** bef: define the interface for task queue.
** auth: lines<<EMAIL>>
** create on 2021.11.26 
*/

#include "es_inc.h"
#include "facelib_inc.h"

// #define TASK_QUEUE_DEBUG
#ifdef TASK_QUEUE_DEBUG
#define task_queue_debug es_log_info
#define task_queue_error es_log_error
#else
#define task_queue_debug(...)
#define task_queue_error(...)
#endif

#define ES_TASK_QUEUE_COUNT             (2)

static spinlock_t core0_lock;
static spinlock_t core1_lock;

static es_task_param_t core0_task_queue[ES_TASK_QUEUE_COUNT] = {0};
static ES_U8 core0_task_queue_count = 0;
static es_task_param_t core1_task_queue[ES_TASK_QUEUE_COUNT] = {0};
static ES_U8 core1_task_queue_count = 0;

static ES_S32 es_task_queue_add(const es_task_param_t *p, spinlock_t *lock, es_task_param_t *queue)
{
    ES_U8 i = 0;

    spinlock_lock(lock);

    for (i = 0; i < ES_TASK_QUEUE_COUNT; i++) {
        if (ES_TASK_QUEUE_NONE == queue[i].type) {
            queue[i].type = p->type;
            queue[i].param = p->param;
            queue[i].timeout = p->timeout;
            break;
        }
    }
    spinlock_unlock(lock);

    if (i >= ES_TASK_QUEUE_COUNT) {
        return ES_RET_FAILURE;
    }

    return ES_RET_SUCCESS;
}

static ES_S32 es_task_queue_core0_exec(ES_U8 id)
{
    if (ES_TASK_PARSE_FACE_FILE == core0_task_queue[id].type) {
        return es_facedb_file_parse((const ES_BYTE *)core0_task_queue[id].param);
    } else if (ES_TASK_PARSE_MQTT_FACE_FEA == core0_task_queue[id].type) {
        return es_facedb_parse_mqtt_face_fea(core0_task_queue[id].param);
#if ES_TUYA_MODULE_ENABLE
    } else if (ES_TASK_PARSE_FACE_TUYA_ADD == core0_task_queue[id].type) {
        return es_facedb_tuya_add((const ES_VOID *)core0_task_queue[id].param);
    } else if (ES_TASK_PARSE_FACE_TUYA_DEL == core0_task_queue[id].type) {
        return es_facedb_tuya_del((const ES_VOID *)core0_task_queue[id].param);
#endif
    } else if (ES_TASK_PARSE_PERIP_BLE_CFG == core0_task_queue[id].type) {
        return es_dev_cfg_update_ble_mac_list((const cJSON *)core0_task_queue[id].param);
    } else if (ES_TASK_FACE_FEATRUE_GET_SHA256 == core0_task_queue[id].type) {
        mf_facedb.get_feature_sha256((uint8_t *)core0_task_queue[id].param);
        return ES_RET_SUCCESS;
    } else if (ES_TASK_ACTIVE_DEVICE == core0_task_queue[id].type) {
        mf_brd.cfg.factory_flag = 0;
        mf_brd.cfg_save();
        return ES_RET_SUCCESS;
    } else if (ES_TASK_DEL_FACEDB_ALL == core0_task_queue[id].type) {
        mf_facedb.del_all();
    } else if (ES_TASK_SAVE_BRD_CFG == core0_task_queue[id].type) {
        mf_brd.cfg_save();
        return ES_RET_SUCCESS;
    } else if (ES_TASK_RESET_DEVICE == core0_task_queue[id].type) {
        mf_brd.reset_default(ES_NULL);
#if ES_TUYA_MODULE_ENABLE
        es_tuya_port_reset_wifi();
#endif
        es_hal_sys_reset();
        return ES_RET_SUCCESS;
    } else if (ES_TASK_PASSLOG_READ_JSON_STR == core0_task_queue[id].type) {
#if ES_PASSLOG_ENABLE
        es_passlog_read_json_str((ES_CHAR *)core0_task_queue[id].param, ES_PASSLOG_JSON_STR_LEN);
#endif
        return ES_RET_SUCCESS;
    } else if (ES_TASK_PASSLOG_UPLOAD_RESULT == core0_task_queue[id].type) {
#if ES_PASSLOG_ENABLE
        es_passlog_set_upload_result((ES_BOOL)core0_task_queue[id].param);
        return ES_RET_SUCCESS;
#endif

#if ES_MODEL_ACTIVE_ENABLE
    } else if (ES_TASK_MODEL_ACTIVE == core0_task_queue[id].type) {
        es_serv_proto_model_active_decode((const ES_BYTE *)core0_task_queue[id].param, es_strlen((const ES_CHAR *)core0_task_queue[id].param));
        return ES_RET_SUCCESS;
#endif

#if ES_AUDIO_ENABLE
    } else if (ES_TASK_VOICE_PLAY == core0_task_queue[id].type) {
        return es_voice_play((es_voice_type_t)core0_task_queue[id].param);
#endif
    }

    return ES_RET_FAILURE;
}

ES_S32 es_task_queue_init(ES_VOID)
{
    es_memset(core0_task_queue, 0x00, sizeof(core0_task_queue));
    es_memset(core1_task_queue, 0x00, sizeof(core1_task_queue));

    return ES_RET_SUCCESS;
}



// only push, no wait finish
ES_S32 es_task_queue_push(const es_task_param_t *p)
{
    spinlock_t *lock = ES_NULL;
    es_task_param_t *queue = ES_NULL;

    // add to core0
    if (ES_TASK1_QUEUE_START & p->type) {
        lock = (spinlock_t *)&core1_lock;
        queue = core1_task_queue;
        core1_task_queue_count++;
    } else {
        lock = (spinlock_t *)&core0_lock;
        queue = core0_task_queue;
        core0_task_queue_count++;
    }

    return es_task_queue_add(p, lock, queue);
}

// push to queue, wait for finish or timeout
ES_S32 es_task_queue_push_wait(const es_task_param_t *p)
{
    ES_U32 timeout_count = 0;

    if (ES_RET_SUCCESS != es_task_queue_push(p)) {
        task_queue_debug("task push fail");
        return ES_RET_FAILURE;
    }

    while (core0_task_queue_count) {
        es_os_msleep(1);
        timeout_count++;
        if (p->timeout > 0 && timeout_count > p->timeout) {
            task_queue_debug("task wait timeout, fail");
            return ES_RET_FAILURE;
        }
    }

    return ES_RET_SUCCESS;
}

ES_VOID es_task_queue_core0_run(ES_VOID)
{
    ES_U8 i = 0;

    spinlock_lock(&core0_lock);

    for (i = 0; i < ES_TASK_QUEUE_COUNT; i++) {
        if (ES_TASK_QUEUE_NONE != core0_task_queue[i].type) {
            es_task_queue_core0_exec(i);
            core0_task_queue[i].type = ES_TASK_QUEUE_NONE;
        }
    }
    core0_task_queue_count = 0;
    spinlock_unlock(&core0_lock);
}

ES_VOID es_task_queue_core1_run(ES_VOID)
{

}


ES_S32 es_task_queue_play_voice(ES_U8 type)
{
    es_task_param_t task_param;

    task_param.type = ES_TASK_VOICE_PLAY;
    task_param.param = (ES_VOID *)type;
    task_param.timeout = 0;
    return es_task_queue_push_wait(&task_param);
}