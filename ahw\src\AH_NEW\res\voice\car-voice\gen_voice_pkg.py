import os, sys, re
import random
import string
import time,zipfile

def get_zip(files,zip_name):
    zp=zipfile.ZipFile(zip_name,'w', zipfile.ZIP_DEFLATED)
    for file in files:
        zp.write(file)
    zp.close()
    time.sleep(1)
    print('压缩完成')

voice_list = [
    'VOICE_01_FACE_PASS',
    'VOICE_02_CAR_BOOT',
    'VOICE_03_CAR_YEAR_EXPIRE_SOON',
    'VOICE_04_LOOK_AND_CHECK',
    'VOICE_05_NOT_AUTH_AND_LEAVE',
    'VOICE_06_AUTH_PASS',
    'VOICE_07_UPDATE_DRIVER_INFO_OK',
    'VOICE_08_SET_INFO_OK',
    'VOICE_09_RESET_INFO_OK',
    'VOICE_10_UNLOCK_CAR_OK',
    'VOICE_11_LOCK_CAR_OK',
    'VOICE_12_DRIVER_LICENSE_EXPIRE',
    'VOICE_13_CAR_EXPIRE_AFTER_MONTH',
	'VOICE_14_EXPIRE_AND_LOCKED',
	'VOICE_15_EXCEPTION_AND_CONTACT',
    'VOICE_16_CAR_LOCKED',
    'VOICE_17_DO_NOT_USE_PHONE',
    'VOICE_18_NO_SMOKING',
    'VOICE_19_FATIGUE_DRIVING',
    'VOICE_20_SAFETY_BELT',
    'VOICE_21_OVER_SPEED',
    'VOICE_22_POSTURE_DETECTION',
]

if len(sys.argv) < 2:
    addr = int(0x200000)
else:
    addr = int(sys.argv[1], 16)

bin_all = open('voice_all.bin', 'wb')

f_json = open('flash-list.json', 'w')
f_json.write("{\r\n  \"version\": \"0.1.0\",\r\n  \"files\": [");
json_fmtstr = "\r\n    {\r\n      \"address\": %d,\r\n      \"bin\": \"%s\",\r\n      \"sha256Prefix\": false\r\n    }"
f_json.write(json_fmtstr%(addr, "voice_all.bin"));
f_json.write("\r\n  ]\r\n}\r\n")
f_json.close()

for wav in voice_list:
    if os.path.isfile(wav+'.wav') == False:
        print("%s.wav not exist! exit"%wav)
        exit()

    os.system("python3 convert_le.py %s.wav %s.bin > /dev/null"%(wav, wav))
    # os.system("cp %s.wav %s.bin"%(wav, wav))

    with open("%s.bin"%wav, 'rb') as f:
        tmp = f.read()
        bin_all.write(tmp)

    wav_size = os.path.getsize("%s.bin"%wav)
    
    # print("%s.bin"%wav)
    print("#define %s_ADDR\t(0x%x)\r\n#define %s_SIZE\t(%d)\r\n"%(wav.upper(), addr, wav.upper(), wav_size))

    addr += wav_size

bin_all.close()

for wav in voice_list:
    print("{%s_ADDR, %s_SIZE, ES_%s},"%(wav.upper(), wav.upper(), wav.upper()))

files = ['voice_all.bin', 'flash-list.json']
get_zip(files, 'voice.kfpkg')

os.system('rm *.bin flash-list.json')
