#ifndef _MF_FACEDB_H
#define _MF_FACEDB_H

#include <stdint.h>
#include "mf_constants.h"

/*****************************************************************************/
// Enums & Macro
/*****************************************************************************/

typedef enum 
{
	MF_DBINFO_FULL=0,
	MF_DBINFO_LITE,
	MF_DBINFO_NONE, //not support
}mf_dbinfo_type_t;


typedef enum 
{
	MF_DBOPS_ADD=0,
	MF_DBOPS_DEL,
	MF_DBOPS_UPDATE,
	MF_DBOPS_GET
}mf_dbops_type_t;
//
/*****************************************************************************/
// Types
/*****************************************************************************/

typedef struct
{
	//Private
	//Public
	uint16_t init_flag;
	uint32_t hdr_addr;
	uint32_t data_addr;
	uint16_t item_size;		//存储单元(含单元头部)的大小
	uint8_t* item_buf;		//供外部使用的单个item缓存
	
	//Const Public
	//flash数据库，db_addr为flash地址；tf数据库，为文件句柄
	mf_err_t (*init)(uint32_t db_addr);
	void     (*deinit)(void);
	uint16_t (*num)(void);
	mf_err_t (*del_all)(void);
	int32_t  (*uid2oft)(uint8_t* uid);
	int32_t  (*vid2oft)(uint32_t vid);
	int32_t  (*uid2vid)(uint8_t* uid);
	
	mf_err_t (*add)(uint8_t *uid, void* item);	   //增
	mf_err_t (*add_fast)(uint8_t *uid, uint8_t stat, uint8_t* ftr, uint8_t* name, uint8_t* note);	   //增
	//edit via oft 第idx个单元偏移处
	mf_err_t (*del_oft)(uint32_t oft);               //删
	mf_err_t (*get_oft)(uint32_t oft, void* item);   //查
	mf_err_t (*update_oft)(uint32_t oft, void* item);//改
	//edit via vid (virtual id) 第idx个有效数据
	mf_err_t (*del_vid)(uint32_t vid);               //删
	mf_err_t (*get_vid)(uint32_t vid, void* item);   //查
	mf_err_t (*update_vid)(uint32_t vid, void* item);//改
	//edit via uid
	mf_err_t (*del_uid)(uint8_t* uid);               //删
	mf_err_t (*get_uid)(uint8_t* uid, void* item);   //查
	mf_err_t (*update_uid)(uint8_t* uid, void* item);//改
	//callback, for stat or app usage
	void     (*ops_cb)(mf_dbops_type_t type, uint32_t id, void* item);
	void     (*user_ops_cb)(mf_dbops_type_t type, uint32_t id, void* item);
	//iterate
	void     (*iterate_init)(void);
	uint32_t (*iterate)(void* item, uint32_t* vid, uint32_t* oft); //return 0 end
	//utils  
	mf_err_t (*get_uid_meta)(void* item, uint8_t** uid, void** meta);
	mf_err_t (*get_feature_sha256)(uint8_t *sha256);
	mf_err_t (*update_feature_sha256)(void);
	
} mf_facedb_t;



/*****************************************************************************/
// Functions
/*****************************************************************************/
mf_err_t mf_facedb_init(void);

/*****************************************************************************/
// Vars
/*****************************************************************************/
extern mf_facedb_t mf_facedb;

#endif