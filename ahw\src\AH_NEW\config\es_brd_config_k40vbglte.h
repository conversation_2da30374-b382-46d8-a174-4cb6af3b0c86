/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_utils.h
** bef: define the interface for configure 
** auth: lines<<EMAIL>>
** create on 2020.06.27 
*/

#ifndef _ES_BRD_CONFIG_K35VWTY_H_
#define _ES_BRD_CONFIG_K35VWTY_H_

#ifdef __cplusplus 
extern "C" { 
#endif

// Col is 96
/////////////////////////////////////// system info ///////////////////////////////////////////
#define ES_SYSTEM_INFO
#define ES_SYS_SVER_VAL                     (10012)
#define ES_SYS_SVER_STR                     ("1.0.12")
#define ES_SYS_HVER_VAL                     (10000)
#define ES_SYS_HVER_STR                     ("1.0.0")


/////////////////////////////////////// 4G LTE ////////////////////////////////////////////////
#define ES_4GLTE_ENABLE                     (1)
#define ES_4GLTE_UART_ID                    (ES_UART_ID_0)
#define ES_4GLTE_UART_BAUD                  (ES_UART_BAUD_115200)
#define ES_4GLTE_MODULE_TYPE                (ES_4GLTE_MODULE_EC800X)
#define ES_4GLTE_POWER_PIN                  (19)
#define ES_4GLTE_POWER_HS_NUM               (28)
#define ES_IO20_PIN                         (20)
#define ES_IO20_HS_NUM                      (29)

/////////////////////////////////////// BLE Module ////////////////////////////////////////////
#define ES_BLE_MODULE_ENABLE                (0)
#define ES_BLE_HAL_TYPE                     (ES_BLE_HAL_571)
#define ES_BLE_UART_ID                      (ES_UART_ID_1)
#define ES_BLE_PAYLOAD_GPIO_PIN             (26)
#define ES_BLE_PAYLOAD_GPIO_HS_NUM          (29)
#define ES_BLE_PAYLOAD_DATA_LEN             (32)


/////////////////////////////////////// LCD ///////////////////////////////////////////////////
#define ES_LCD_DRIVER_TYPE                  (ES_LCD_DRIVER_NT35510)
#define ES_LCD_DCX_HS_NUM                   (5)
#define ES_LCD_RST_HS_NUM                   (6)
#define ES_LCD_BL_HS_NUM                    (16)
#define ES_LCD_SPI_CS_HS_NUM                (17)
#define ES_LCD_CS_PIN                       (36)
#define ES_LCD_RST_PIN                      (37)
#define ES_LCD_DCX_PIN                      (38)
#define ES_LCD_SCK_PIN                      (39)
#define ES_LCD_BL_PIN                       (17)
#define ES_LCD_PWM_DEV_BL                   (PWM_DEVICE_0)
#define ES_LCD_PWDM_CHN_BL                  (PWM_CHANNEL_1)
#define ES_LCD_DIR                          (1) // 0:horizontal, 1:vertical
#define ES_LCD_HMIRROR                      (0)
#define ES_LCD_VFLIP                        (0)
#define ES_LCD_XY_SWAP                      (0)
#define ES_LCD_DIR_PARAM                    ((ES_LCD_HMIRROR<<7) | (ES_LCD_VFLIP<<6) | (ES_LCD_XY_SWAP<<5))
#define ES_LCD_WIDTH                        (480)
#define ES_LCD_HEIGHT                       (800)


/////////////////////////////////////// UI ////////////////////////////////////////////////////
#define ES_UI_WIDTH                         (240)
#define ES_UI_HEIGHT                        (320)
#define ES_UI_CAM_WIDTH                     (240)
#define ES_UI_CAM_HEIGHT                    (320)
#define ES_UI_FACE_EDGE_LINE_WIDTH          (2) // pixel
#define ES_UI_FACE_EDGE_LINE_LENGTH         (10) // pixel
#define ES_UI_TYPE                          (ES_UI_TYPE_K40V_480_800_GPS)
#define ES_UI_DRAW_BUF_LINE                 (32)
#define ES_UI_BAR_WIDTH                     (480)
#define ES_UI_BAR_HEIGHT                    (160)


/////////////////////////////////////// Camera ////////////////////////////////////////////////
#define ES_CAM_WIDTH                        (240)
#define ES_CAM_HEIGHT                       (320)
#define ES_CAM_DIR                          (1) // direction, 0:horizontal, 1:vertical
#define ES_CAM_HMIRROR                      (1) // hmirror
#define ES_CAM_VFLIP                        (0)// vflip
#define ES_CAM_EXP_TIME                     (128)



/////////////////////////////////////// UART //////////////////////////////////////////////////
#define ES_UART_MODULE_ENABLE               (1)
#define ES_UART0_RX_PIN                     (7)
#define ES_UART0_TX_PIN                     (6)
#define ES_UART1_RX_PIN                     (27)
#define ES_UART1_TX_PIN                     (26)
#define ES_UART2_RX_PIN                     (4)
#define ES_UART2_TX_PIN                     (5)


/////////////////////////////////////// KEY ///////////////////////////////////////////////////
#define ES_KEY_MODULE_ENABLE                (0)
#define ES_KEY1_GPIO_PIN                    (8)
#define ES_KEY1_GPIO_HS_NUM                 (0)
#define ES_KEY1_PRESS_VAL                   (0)


/////////////////////////////////////// file system ///////////////////////////////////////////
#define ES_FS_ENABLE                        (0)


/////////////////////////////////////// FACE //////////////////////////////////////////////////
#define ES_FACE_UPLOAD_ENABLE               (1)


/////////////////////////////////////// PASS LOG //////////////////////////////////////////////
#define ES_PASSLOG_ENABLE                   (0)
#define ES_PASSLOG_HDR_MAGIC                (0x12345678)
#define ES_PASSLOG_CACHE_COUNT              (8)
#define ES_PASSLOG_JSON_STR_LEN             (96)
#define ES_PASSLOG_UID_LEN                  (32)


/////////////////////////////////////// DOOR //////////////////////////////////////////////////
#define ES_DOOR_ENABLE                      (0)


/////////////////////////////////////// MODEL /////////////////////////////////////////////////
#define ES_MODEL_ACTIVE_ENABLE              (1)
#define ES_MODEL_ACTIVE_PROTO_DATA_LEN      (256)
#define ES_MODEL_ACTIVE_KEY_LEN             (32)


/////////////////////////////////////// OTA ///////////////////////////////////////////////////
#define ES_OTA_DL_ENABLE                    (1)


/////////////////////////////////////// GPS ///////////////////////////////////////////////////
#define ES_GPS_ENABLE                       (1)


/////////////////////////////////////// CAPTURE ///////////////////////////////////////////////
#define ES_CAPTURE_ENABLE                   (1)
#define ES_CAPTURE_MIN_INTERVAL_SEC         (30)
#define ES_CAPTURE_MAX_INTERVAL_SEC         (60*60)

/////////////////////////////////////// MQTT //////////////////////////////////////////////////
#define ES_MQTT_ENABLE                      (1)
#define ES_MQTT_CFG_IP                      ("************")
#define ES_MQTT_CFG_PORT                    (1883)
#define ES_MQTT_CFG_USERNAME                ("admin")
#define ES_MQTT_CFG_PASSWD                  ("Faceos2016")
// #define ES_MQTT_CFG_DEV_MAC_FMT             ("%02X:%02X:%02X:%02X:%02X:%02X")
#define ES_MQTT_CFG_DEV_MAC_FMT             ("%02X%02X%02X%02X%02X%02X")
#define ES_MQTT_CFG_DEV_SUB_TOPIC_FMT       ("/one2one/device/%s")
// #define ES_MQTT_CFG_DEV_PUB_TOPIC           ("/one2one/server/DC3104D4C4E8692EDB2D575637AC93392022")
#define ES_MQTT_CFG_DEV_PUB_TOPIC           ("/one2one/server/DC3104D4C4E8692EDB2D575637AC9339A")
#define ES_MQTT_HEART_TIME_SEC              (60)


#ifdef __cplusplus 
}
#endif
#endif