/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_passlog.h
** bef: define the interface for pass log. 
** auth: lines<<EMAIL>>
** create on 2022.01.02
*/

#ifndef _ES_PASSLOG_H_
#define _ES_PASSLOG_H_

#ifdef __cplusplus 
extern "C" { 
#endif

#include "es_types.h"

#define ES_PASSLOG_TEMP_LEN     (8)

typedef struct {
    ES_U32 id;
    ES_U32 timestamp;
    ES_U8 uid[ES_PASSLOG_UID_LEN];
    ES_CHAR temp_val[ES_PASSLOG_TEMP_LEN];    // temperature value, "36.8" or "36.80"
} es_passlog_t;


ES_S32 es_passlog_init(ES_VOID);

ES_U32 es_passlog_get_count(ES_VOID);

ES_S32 es_passlog_write(const es_passlog_t *log);

ES_S32 es_passlog_read_from_flash(es_passlog_t *list, ES_U32 count);

ES_S32 es_passlog_read_from_cache(es_passlog_t *list, ES_U32 count);

ES_U32 es_passlog_read_json_str(ES_CHAR *str, ES_U32 str_len);

ES_S32 es_passlog_set_upload_result(ES_BOOL success);

ES_S32 es_passlog_delete(ES_U32 id);

ES_S32 es_passlog_delete_all(ES_VOID);

ES_VOID es_passlog_try_upload(ES_VOID);

#ifdef __cplusplus 
}
#endif

#endif