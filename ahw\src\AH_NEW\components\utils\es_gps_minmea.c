/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_gps.c
** bef: implement the interface for gps. 
** auth: lines<<EMAIL>>
** create on 2022.12.03
*/

#include "es_inc.h"

#if ES_GPS_ENABLE

#define GPS_MINMEA_TEST_DATA        (0)

// #define GPS_MINMEA_DEBUG
#ifdef GPS_MINMEA_DEBUG
#define gps_minmea_debug es_log_info
#define gps_minmea_error es_log_error
#else
#define gps_minmea_debug(...)
#define gps_minmea_error(...)
#endif

#define GPS_MINMEA_ENBLE            (0)

#define GPS_MINMEA_FIELD_MAX_LEN    (16)
#define GPS_MINMEA_TYPE_STR_LEN     (7)
#define GPS_MINMEA_TYPE_RMC         ("$GNRMC,")
#define GPS_MINMEA_TYPE_GSA         ("$GNGSA,")
#define GPS_MINMEA_TYPE_VTG         ("$GNVTG,")
#define GPS_MINMEA_TYPE_GGA         ("$GNGGA,")
#define GPS_MINMEA_TYPE_LOC         ("PSLOC: ")

static ES_BOOL minmea_isfield(ES_CHAR c)
{
    return es_isprint((unsigned ES_CHAR) c) && c != ',' && c != '*';
}

static const ES_CHAR *minmea_get_field(const ES_CHAR *data, ES_U32 field_idx)
{
    const ES_CHAR *p = ES_NULL;
    ES_U32 i = 0;
    if (0 == field_idx) {
        return data;
    }

    p = data;
    while(*p) {
        if (!minmea_isfield(*p)) {
            i++;
        }
        if (i == field_idx) {
            p++;
            if (!minmea_isfield(*p)) {
                return ES_NULL;
            }
            return p;
        }
        p++;
    }

    return ES_NULL;
}

static ES_S32 minmea_get_field_value(const ES_CHAR *data, ES_U32 field_idx, ES_CHAR *value)
{
    const ES_CHAR *p = ES_NULL;
    ES_U32 i = 0;

    p = minmea_get_field(data, field_idx);
    if (ES_NULL == p) {
        return ES_RET_FAILURE;
    }

    while(minmea_isfield(*p) && *p != '\0' && (i < GPS_MINMEA_FIELD_MAX_LEN)) {
        value[i] = *p;
        p++;
        i++;
    }
    value[i] = 0;

    if (0 == i) {
        return ES_RET_FAILURE;
    }

    return ES_RET_SUCCESS;
}

#if GPS_MINMEA_ENBLE
/*
$GNRMC,063233.00,A,2506.09919,N,10239.30019,E,0.297,,041222,,,A,V*13
$GNRMC,063243.00,A,2506.09830,N,10239.30008,E,0.149,,041222,,,A,V*1E
$GNRMC,063253.00,A,2506.09883,N,10239.30064,E,0.062,,041222,,,A,V*15
$GNRMC,063313.00,A,2506.09860,N,10239.30052,E,0.370,,041222,,,A,V*18
*/
static ES_S32 es_gps_minmea_parse_rmc(const ES_CHAR *data, es_gps_info_t *info)
{
    ES_CHAR field_value[GPS_MINMEA_FIELD_MAX_LEN] = {0};

    gps_minmea_debug("RMC:%s", data);
    if (ES_RET_SUCCESS != minmea_get_field_value(data, 1, field_value)) {
        return ES_RET_FAILURE;
    }

    gps_minmea_debug("RMC status:%s", field_value);
    if ('A' != field_value[0]) {
        return ES_RET_FAILURE;
    }

    if (ES_RET_SUCCESS != minmea_get_field_value(data, 6, field_value)) {
        return ES_RET_FAILURE;
    }

    info->speed_km = atof(field_value) * 1.85f;
    gps_minmea_debug("RMC speed:%f km/h", info->speed_km);
    
    return ES_RET_SUCCESS;
}

/*
$GNVTG,,T,,M,0.062,N,0.116,K,A*3F
$GNVTG,,T,,M,0.272,N,0.503,K,A*3C
$GNVTG,,T,,M,0.370,N,0.685,K,A*32
*/
static ES_S32 es_gps_minmea_parse_vtg(const ES_CHAR *data, es_gps_info_t *info)
{
    ES_CHAR field_value[GPS_MINMEA_FIELD_MAX_LEN] = {0};

    gps_minmea_debug("VTG:%s", data);
    if (ES_RET_SUCCESS != minmea_get_field_value(data, 8, field_value)) {
        return ES_RET_FAILURE;
    }

    gps_minmea_debug("VTG VAILD:%s", field_value);
    if ('N' == field_value[0]) {
        return ES_RET_FAILURE;
    }

    if (ES_RET_SUCCESS != minmea_get_field_value(data, 6, field_value)) {
        return ES_RET_FAILURE;
    }

    gps_minmea_debug("VTG speed:%s", field_value);

    return ES_RET_SUCCESS;
}

/*
$GNGGA,063303.00,2506.09872,N,10239.30071,E,1,14,1.17,1874.1,M,,M,,*69
$GNGGA,063353.00,2506.09868,N,10239.30174,E,1,17,0.91,1874.4,M,,M,,*6A
$GNGGA,063343.00,2506.09867,N,10239.30152,E,1,16,0.94,1873.5,M,,M,,*62
$GNGGA,063323.00,2506.09847,N,10239.30031,E,1,15,1.19,1871.6,M,,M,,*64
*/
static ES_S32 es_gps_minmea_parse_gga(const ES_CHAR *data, es_gps_info_t *info)
{
    ES_CHAR field_value[GPS_MINMEA_FIELD_MAX_LEN] = {0};

    gps_minmea_debug("GGA:%s", data);
    if (ES_RET_SUCCESS != minmea_get_field_value(data, 5, field_value)) {
        return ES_RET_FAILURE;
    }
    gps_minmea_debug("GGA VAILD:%s", field_value);
    if ('1' != field_value[0]) {
        return ES_RET_FAILURE;
    }

    if (ES_RET_SUCCESS != minmea_get_field_value(data, 1, field_value)) {
        return ES_RET_FAILURE;
    }
    gps_minmea_debug("GGA LAT:%s", field_value);

    if (ES_RET_SUCCESS != minmea_get_field_value(data, 3, field_value)) {
        return ES_RET_FAILURE;
    }
    gps_minmea_debug("GGA LON:%s", field_value);

    if (ES_RET_SUCCESS != minmea_get_field_value(data, 8, field_value)) {
        return ES_RET_FAILURE;
    }
    gps_minmea_debug("GGA AL:%s", field_value);

    return ES_RET_SUCCESS;
}

#endif

/*
PSLOC: 023034.00,22.64271,114.03298,1.33,64.0,3,0.000,0.407,0.220,101222,03
PSLOC: 023339.00,25.10108,102.65551,99.99,2036.8,3,0.000,1.808,0.976,101222,00
PSLOC: 023357.00,25.10096,102.65532,7.30,2036.9,3,0.000,0.310,0.167,101222,16
PSLOC: 023345.00,25.10105,102.65546,7.29,2037.0,3,0.000,0.610,0.329,101222,00
*/
static ES_S32 es_gps_minmea_parse_loc(const ES_CHAR *data, es_gps_info_t *info)
{
    ES_CHAR field_value[GPS_MINMEA_FIELD_MAX_LEN] = {0};

    gps_minmea_debug("LOC:%s", data);
    if (ES_RET_SUCCESS != minmea_get_field_value(data, 1, field_value)) {
        return ES_RET_FAILURE;
    }
    es_strncpy(info->latitude, field_value, sizeof(info->latitude));
    gps_minmea_debug("LOC latitude:%s", info->latitude);


    if (ES_RET_SUCCESS != minmea_get_field_value(data, 2, field_value)) {
        return ES_RET_FAILURE;
    }
    es_strncpy(info->longitude, field_value, sizeof(info->longitude));
    gps_minmea_debug("LOC longitude:%s", info->longitude);


    if (ES_RET_SUCCESS != minmea_get_field_value(data, 3, field_value)) {
        return ES_RET_FAILURE;
    }
    es_strncpy(info->hdop, field_value, sizeof(info->hdop));
    gps_minmea_debug("LOC hdop:%s", info->hdop);


    if (ES_RET_SUCCESS != minmea_get_field_value(data, 4, field_value)) {
        return ES_RET_FAILURE;
    }
    es_strncpy(info->altitude, field_value, sizeof(info->altitude));
    gps_minmea_debug("LOC altitude:%s", info->altitude);


    if (ES_RET_SUCCESS != minmea_get_field_value(data, 5, field_value)) {
        return ES_RET_FAILURE;
    }
    es_strncpy(info->fix, field_value, sizeof(info->fix));
    gps_minmea_debug("LOC fix:%s", info->fix);


    if (ES_RET_SUCCESS != minmea_get_field_value(data, 6, field_value)) {
        return ES_RET_FAILURE;
    }
    es_strncpy(info->cog, field_value, sizeof(info->cog));
    gps_minmea_debug("LOC cog:%s", info->cog);

    if (ES_RET_SUCCESS != minmea_get_field_value(data, 7, field_value)) {
        return ES_RET_FAILURE;
    }
    es_strncpy(info->spkm, field_value, sizeof(info->spkm));
    gps_minmea_debug("LOC spkm:%s", info->spkm);


    if (ES_RET_SUCCESS != minmea_get_field_value(data, 8, field_value)) {
        return ES_RET_FAILURE;
    }
    es_strncpy(info->spkn, field_value, sizeof(info->spkn));
    gps_minmea_debug("LOC spkn:%s", info->spkn);


    if (ES_RET_SUCCESS != minmea_get_field_value(data, 10, field_value)) {
        return ES_RET_FAILURE;
    }
    es_strncpy(info->nsta, field_value, sizeof(info->nsta));
    gps_minmea_debug("LOC nsta:%s", info->nsta);

    return ES_RET_SUCCESS;
}

ES_S32 es_gps_minmea_parse(const ES_CHAR *minmea, es_gps_info_t *info)
{
    gps_minmea_debug("minmea:%s", minmea);

#if GPS_MINMEA_ENBLE
    if (0 == es_strncmp(minmea, GPS_MINMEA_TYPE_RMC, GPS_MINMEA_TYPE_STR_LEN)) {
#if GPS_MINMEA_TEST_DATA
        return es_gps_minmea_parse_rmc((const ES_CHAR *)("063233.00,A,2506.09919,N,10239.30019,E,0.297,,041222,,,A,V*13"), info);
#else
        return es_gps_minmea_parse_rmc((const ES_CHAR *)(minmea+GPS_MINMEA_TYPE_STR_LEN), info);
#endif
    } else if (0 == es_strncmp(minmea, GPS_MINMEA_TYPE_VTG, GPS_MINMEA_TYPE_STR_LEN)) {
#if GPS_MINMEA_TEST_DATA
        return es_gps_minmea_parse_vtg((const ES_CHAR *)(",T,,M,0.062,N,0.116,K,A*3F"), info);
#else
        return es_gps_minmea_parse_vtg((const ES_CHAR *)(minmea+GPS_MINMEA_TYPE_STR_LEN), info);
#endif
    } else if (0 == es_strncmp(minmea, GPS_MINMEA_TYPE_GGA, GPS_MINMEA_TYPE_STR_LEN)) {
#if GPS_MINMEA_TEST_DATA
        return es_gps_minmea_parse_gga((const ES_CHAR *)("063323.00,2506.09847,N,10239.30031,E,1,15,1.19,1871.6,M,,M,,*64"), info);
#else
        return es_gps_minmea_parse_gga((const ES_CHAR *)(minmea+GPS_MINMEA_TYPE_STR_LEN), info);
#endif
    }
#else
    if (0 == es_strncmp(minmea, GPS_MINMEA_TYPE_LOC, GPS_MINMEA_TYPE_STR_LEN)) {
        return es_gps_minmea_parse_loc((const ES_CHAR *)(minmea+GPS_MINMEA_TYPE_STR_LEN), info);
    }
#endif
    return ES_RET_FAILURE;
}

#endif
