/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_config.h
** bef: define the interface for configure 
** auth: lines<<EMAIL>>
** create on 2020.06.27 
*/

#ifndef _ES_CONFIG_H_
#define _ES_CONFIG_H_

#ifdef __cplusplus 
extern "C" { 
#endif

/////////////////////////////////////// Board /////////////////////////////////////////////////
#define ES_BRD_TYPE_K28VB       (1)
#define ES_BRD_TYPE_K28VWTY     (2)
#define ES_BRD_TYPE_K54G        (3)
#define ES_BRD_TYPE_K28VLAN     (4)
#define ES_BRD_TYPE_K28VLTE     (5)
#define ES_BRD_TYPE_K28VHC      (6)
#define ES_BRD_TYPE_K35VWTY     (7)
#define ES_BRD_TYPE_K40VWTY     (8)
#define ES_BRD_TYPE_K40VBLTE    (9)
#define ES_BRD_TYPE_K40VBGLTE   (10)
#define ES_BRD_TYPE_K40VBGLTE_SCAR    (11)
#define ES_BRD_TYPE_K35VBGLTE_SCAR    (12)

#define ES_BRD_TYPE             (ES_BRD_TYPE_K35VBGLTE_SCAR)


#if (ES_BRD_TYPE == ES_BRD_TYPE_K28VWTY)
#include "es_brd_config_k28vwty.h"
#define ES_BRD_MODE             ("K28VWTY")
#elif (ES_BRD_TYPE == ES_BRD_TYPE_K28VB)
#include "es_brd_config_k28vb.h"
#define ES_BRD_MODE             ("K28VB")
#elif (ES_BRD_TYPE == ES_BRD_TYPE_K54G)
#include "es_brd_config_k54g.h"
#define ES_BRD_MODE             ("K54G")
#elif (ES_BRD_TYPE == ES_BRD_TYPE_K28VLAN)
#include "es_brd_config_k28vlan.h"
#define ES_BRD_MODE             ("K28VLAN")
#elif (ES_BRD_TYPE == ES_BRD_TYPE_K28VLTE)
#include "es_brd_config_k28vlte.h"
#define ES_BRD_MODE             ("K28VLTE")
#elif (ES_BRD_TYPE == ES_BRD_TYPE_K28VHC)
#include "es_brd_config_k28vhc.h"
#define ES_BRD_MODE             ("K28VHC")
#elif (ES_BRD_TYPE == ES_BRD_TYPE_K35VWTY)
#include "es_brd_config_k35vwty.h"
#define ES_BRD_MODE             ("K35VWTY")
#elif (ES_BRD_TYPE == ES_BRD_TYPE_K40VWTY)
#include "es_brd_config_k40vwty.h"
#define ES_BRD_MODE             ("K40VWTY")
#elif (ES_BRD_TYPE == ES_BRD_TYPE_K40VBLTE)
#include "es_brd_config_k40vblte.h"
#define ES_BRD_MODE             ("K40VBLTE")
#elif (ES_BRD_TYPE == ES_BRD_TYPE_K40VBGLTE)
#include "es_brd_config_k40vbglte.h"
#define ES_BRD_MODE             ("K40VBGLTE")
#elif (ES_BRD_TYPE == ES_BRD_TYPE_K40VBGLTE_SCAR)
#include "es_brd_config_k40vbglte_scar.h"
#define ES_BRD_MODE             ("K40VBGLTE_SCAR")
#elif (ES_BRD_TYPE == ES_BRD_TYPE_K35VBGLTE_SCAR)
#include "es_brd_config_k35vbglte_scar.h"
#define ES_BRD_MODE             ("K35VBGLTE_SCAR")
#else
#error "please config borad type!!!!"
#define ES_BRD_MODE             ("error")
#endif

#include "es_brd_config_default.h"

#ifdef __cplusplus 
}
#endif
#endif