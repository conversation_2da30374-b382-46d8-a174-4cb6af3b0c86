/*
 * Base64 encoding/decoding (RFC1341)
 * Copyright (c) 2005, <PERSON><PERSON> <<EMAIL>>
 *
 * This software may be distributed under the terms of the BSD license.
 * See README for more details.
 */

#ifndef BASE64_H
#define BASE64_H

#include <stddef.h>
#include <stdlib.h>
#include <string.h>

unsigned char *base64_encode(const unsigned char *src, unsigned int len, unsigned int *out_len);
unsigned char *base64_decode(const unsigned char *src, unsigned int len, unsigned int *out_len);

#endif /* BASE64_H */
