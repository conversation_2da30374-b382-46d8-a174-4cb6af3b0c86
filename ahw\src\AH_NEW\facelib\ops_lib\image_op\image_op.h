#ifndef __IMAGE_OP_H
#define __IMAGE_OP_H

#include <stdint.h>

typedef enum 
{
	IMGOP_MCU=0,
	IMGOP_MCU_H2V,
	IMGOP_SIPEED,
	IMGOP_SIPEED_H2V,
}mf_imgop_type_t;


void image_op_init(mf_imgop_type_t type);
mf_imgop_type_t get_image_op(void);

void image_rgb888_roate_right90(uint8_t *out, uint8_t *src, uint16_t w,
                                uint16_t h);
void image_rgb888_roate_left90(uint8_t *out, uint8_t *src, uint16_t w,
                               uint16_t h);

void convert_rgb565_order(uint16_t *image, uint16_t w, uint16_t h);

void image_rgb565_roate_right90(uint16_t *out, uint16_t *src, uint16_t w,
                                uint16_t h);
void image_rgb565_roate_left90(uint16_t *out, uint16_t *src, uint16_t w,
                               uint16_t h);
void image_rgb565_roate_right90_nobuf(uint16_t *buf, uint16_t w, uint16_t h);


void image_r8g8b8_roate_right90(uint8_t *out, uint8_t *src, uint16_t w,
                                uint16_t h);
void image_r8g8b8_roate_left90(uint8_t *out, uint8_t *src, uint16_t w,
                               uint16_t h);

void image_rgb5652rgb888(uint16_t *rgb565, uint8_t *rgb888, uint16_t img_w,
                         uint16_t img_h);

void image_rgb565_draw_edge(uint8_t *gram,
                            uint16_t x1, uint16_t y1,
                            uint16_t x2, uint16_t y2,
                            uint16_t color, uint16_t img_w, uint16_t img_h);

// void image_rgb565_draw_string(uint16_t *ptr, char *str, uint8_t size,
//                               uint16_t x, uint16_t y,
//                               uint16_t color, uint16_t *bg_color,
//                               uint16_t img_w, uint16_t img_h);

// typedef uint8_t (*get_zhCN_dat)(uint8_t *zhCN_char, uint8_t *zhCN_dat, uint8_t size);

// void image_rgb565_draw_zhCN_string(uint16_t *ptr, uint8_t *zhCN_string, uint8_t size,
//                                    uint16_t x, uint16_t y,
//                                    uint16_t color, uint16_t *bg_color,
//                                    uint16_t img_w, uint16_t img_h,
//                                    get_zhCN_dat get_font_data);
								   
void image_rgb8882rgb565(uint16_t *rgb565, uint8_t *rgb888,
                         uint16_t img_w, uint16_t img_h);

typedef struct
{
    uint16_t *img_addr;
    uint16_t x;
    uint16_t y;
    uint16_t w;
    uint16_t h;
} mix_image_t;

typedef struct
{
    uint8_t *addr;
    uint16_t width;
    uint16_t height;
    uint16_t pixel;
} image_t;

void image_rgb565_mix_pic_with_alpha(mix_image_t *img_src, mix_image_t *img_dst,
                                     uint32_t alpha, uint8_t del_white);

void image_rgb565_paste_img(uint16_t *canvas, uint16_t canvas_w, uint16_t canvas_h,
                            uint16_t *img, uint16_t img_w, uint16_t img_h,
                            int16_t x_oft, int16_t y_oft);

void image_r8g8b8_to_rgb565(uint16_t *rgb565, uint8_t *r8g8b8, uint16_t img_w, uint16_t img_h);
void image_rgb565_to_r8g8b8(uint16_t *rgb565, uint8_t *r8g8b8, uint16_t img_w, uint16_t img_h);

void image_rgb565_mix_flashpic_with_alpha(mix_image_t *img_src, mix_image_t *img_dst, uint32_t alpha, uint8_t del_white);
void image_rgb565_draw_edge_normal(uint32_t *gram,
                            uint16_t x1, uint16_t y1,
                            uint16_t x2, uint16_t y2,
                            uint16_t color,
                            uint16_t img_w, uint16_t img_h);
void image_rgb565_draw_edge_h2v(uint16_t *gram,
                            uint16_t x1, uint16_t y1,
                            uint16_t x2, uint16_t y2,
                            uint16_t color,
                            uint16_t img_w, uint16_t img_h);
void image_resize_565(image_t *image_src, image_t *image_dst);
#endif
