/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_facedb_wrapper.c
** bef: define the interface for facedb wrapper.
** auth: lines<<EMAIL>>
** create on 2021.11.24 
*/

#include "es_inc.h"
#include "facelib_inc.h"

// #define ES_FACEDB_WRAPPER_DEBUG
#ifdef ES_FACEDB_WRAPPER_DEBUG
#define es_facedb_wrapper_debug es_log_info
#define es_facedb_wrapper_error es_log_error
#else
#define es_facedb_wrapper_debug(...)
#define es_facedb_wrapper_error(...)
#endif

#define FACECB_TUYA_ENABLE_RULEIDLIST           (0) //ruleIdList
#define FACEDB_FILE_SEPARATOR_CHAR              (',')

#define FACEDB_GET_JSON_MSG_UID() do { \
        tmp = cJSON_GetObjectItem_Type(msg, "uid", cJSON_String); \
        if (ES_NULL == tmp) { \
            return ES_RET_FAILURE; \
        } \
        es_string_str2hex((const ES_CHAR *)tmp->valuestring, uid_hex, FACEDB_UID_LEN>>1); \
    } while(0)

typedef struct {
    ES_BYTE *uid;
    ES_BYTE *featrue;
    ES_BYTE *name;
    ES_BYTE *wigend;
    ES_U32 start_time;
    ES_U32 end_time;
} es_facedb_add_param_t;

typedef struct {
    ES_BYTE *uid;
    ES_U32 start_time;
    ES_U32 end_time;
} es_facedb_update_param_t;

static ES_U32 facedb_run_time = 0;


static ES_S32 es_facedb_add(const es_facedb_add_param_t *p)
{
    dbf_item_t* item = ES_NULL;
    dbmeta_t* meta = ES_NULL;
    ES_S32 vid = 0;


    item = (dbf_item_t*)es_malloc(mf_facedb.item_size);
    if (item == ES_NULL) {
        return ES_RET_FAILURE;
    }
    es_memset(item, 0x00, mf_facedb.item_size);
    meta = (dbmeta_t*)item->meta;

    vid = mf_facedb.uid2vid(p->uid);
    if (vid != -1) {
        if (MF_ERR_NONE != mf_facedb.get_vid(vid, (void *)item)) {
            es_memset(item, 0x00, mf_facedb.item_size);
            es_memset(meta, 0x00, sizeof(dbmeta_t));
        }
    }

    meta->stat = 2;
    memcpy(meta->ftr_rgb, p->featrue, FACEDB_FTR_DIMENSION);
#if FRETRUE_IR_ENABLE
    memcpy(meta->ftr_ir, p->featrue, FACEDB_FTR_DIMENSION);
#endif
    meta->valid = 1;

    if (p->name) {
        strncpy(meta->name, (char*)p->name, FACEDB_NAME_LEN);
    } else {
        strncpy(meta->name, "User", FACEDB_NAME_LEN);
    }

    if (p->wigend) {
        memcpy(meta->name + 10, p->wigend, 6);
    }
                        
    strcpy(meta->note, "none");
    if (0 != p->start_time) {
        meta->start_time = p->start_time;
    }

    if (0 != p->end_time) {
        meta->end_time = p->end_time;
    }

    if (-1 == vid) {
        if (mf_facedb.add(p->uid, item) != MF_ERR_NONE) {
            es_free(item);
            es_facedb_wrapper_error("add fail");
            return ES_RET_FAILURE;
        }
    } else {
        if (MF_ERR_NONE != mf_facedb.update_vid(vid, (void *)item)) {
            es_free(item);
            es_facedb_wrapper_error("update_vid fail");
            return ES_RET_FAILURE;
        }
    }

    es_free(item);
    return ES_RET_SUCCESS;
}

#if FACECB_TUYA_ENABLE_RULEIDLIST
static ES_S32 es_facedb_update(const es_facedb_update_param_t *p)
{
    dbf_item_t* item = ES_NULL;
    dbmeta_t* meta = ES_NULL;
    ES_S32 vid = 0;

    vid = mf_facedb.uid2vid(p->uid);
     if (vid < 0) {
        es_facedb_wrapper_debug("vid:%d not exist", vid);
        return ES_RET_FAILURE;
    }

    item = (dbf_item_t*)es_malloc(mf_facedb.item_size);
    if (item == ES_NULL) {
        es_facedb_wrapper_error("no memory");
        return ES_RET_FAILURE;
    }
    es_memset(item, 0x00, sizeof(dbf_item_t));
    meta = (dbmeta_t*)item->meta;
    es_memset(meta, 0x00, sizeof(dbmeta_t));

    if (MF_ERR_NONE != mf_facedb.get_vid(vid, (void *)item)) {
        es_free(item);
        es_facedb_wrapper_error("get_vid fail");
        return ES_RET_FAILURE;
    }

    meta->start_time = p->start_time;
    meta->end_time = p->end_time;
    if (MF_ERR_NONE != mf_facedb.update_vid(vid, (void *)item)) {
        es_free(item);
        es_facedb_wrapper_error("update_vid fail");
        return ES_RET_FAILURE;
    }

    return ES_RET_SUCCESS;
}
#endif

static ES_S32 es_facedb_del(const ES_BYTE *uid)
{
    ES_S32 vid;

    vid = mf_facedb.uid2vid((uint8_t*)uid);
    if (vid < 0) {
        es_facedb_wrapper_debug("vid:%d not exist", vid);
        return ES_RET_SUCCESS;
    }

    if (mf_facedb.del_vid(vid) != MF_ERR_NONE) {
        es_facedb_wrapper_error("try del vid:%d fail", vid);
        return ES_RET_FAILURE;
    }

    // es_facedb_wrapper_debug("del vid:%d success", vid);
    return ES_RET_SUCCESS;
}

static ES_S32 es_facedb_add_update(const es_facedb_add_param_t *p)
{
    // delete
    es_facedb_del(p->uid);

    return es_facedb_add(p);
}

static ES_U32 es_facedb_file_get_face_num(const ES_BYTE *p)
{
    ES_U32 face_num = 0;

    face_num = atoi((char *)p);
    if (face_num <= 0) {
        return 0;
    }

    return face_num;
}


static ES_U32 es_facedb_file_get_uid(const ES_BYTE *p, ES_BYTE *uid)
{
    return es_string_str2hex((const ES_CHAR *)p, uid, FACEDB_UID_LEN);
}

static const ES_BYTE *es_facedb_file_get_next_field(const ES_BYTE *p)
{
    const ES_BYTE *pos = p;

    pos = (const ES_BYTE *)es_string_find_char((const ES_CHAR *)pos, FACEDB_FILE_SEPARATOR_CHAR);
    if (ES_NULL == pos) {
        return ES_NULL;
    }

    pos = (const ES_BYTE *) (pos + 1);
    if (*pos == 0) {
        return ES_NULL;
    }

    return pos;
}

static ES_U32 es_facedb_file_get_name_hex(const ES_BYTE *p, ES_BYTE *name)
{
    ES_U32 i = 0;
    const ES_BYTE *pos = p;
    ES_BYTE name_str[FACEDB_NAME_LEN*2+1] = {0};
    ES_U32 name_hex_len = 0;
    ES_BYTE *name_hex = ES_NULL;

    for (i = 0; i < FACEDB_NAME_LEN*2; i++) {
        if (*pos == 0 || *pos == ',' || *pos == ' ') {
            break;
        }
        name_str[i] = *pos;
        pos = (const ES_BYTE *) (pos + 1);
    }
    // es_facedb_wrapper_debug("i:%d", i);
    // es_facedb_wrapper_debug("name_str:%s", name_str);
    name_hex = base64_decode((const ES_BYTE *)name_str, i, &name_hex_len);
    if (ES_NULL == name_hex) {
        return 0;
    }

    // es_facedb_wrapper_debug("name_hex_len:%d", name_hex_len);
    if (name_hex_len > FACEDB_NAME_LEN) {
        es_free(name_hex);
        name_hex = ES_NULL;
        return 0;
    }

    es_memcpy(name, name_hex, name_hex_len);
    es_free(name_hex);
    name_hex = ES_NULL;

    return name_hex_len;
}

static ES_BYTE *es_facedb_file_get_feature_hex(const ES_BYTE *p)
{
    ES_U32 feature_hex_len = 0;
    ES_BYTE *feature_hex = ES_NULL;
    
    feature_hex = base64_decode(p, MAX_FTR_BASE64_STR_LEN, &feature_hex_len);
    if (ES_NULL == feature_hex) {
        return ES_NULL;
    }

    // es_facedb_wrapper_debug("feature_hex_len:%d", feature_hex_len);
    if (FACEDB_FTR_DIMENSION != feature_hex_len) {
        es_free(feature_hex);
        feature_hex = ES_NULL;
        return ES_NULL;
    }

    return feature_hex;
}

static ES_U32 es_facedb_file_get_timeout(const ES_BYTE *p)
{
    ES_U32 timeout = 0;

    if (4 != es_string_str2hex((const ES_CHAR *)p, (ES_BYTE *)&timeout, 4)) {
        timeout = 1; 
    } else {
        // swap
        timeout = ((timeout << 8) &0xFF00FF00) | ((timeout >> 8) &0x00FF00FF);
        timeout = (timeout >> 16) | (timeout << 16);
    }

    return timeout;
}

/*
Format:
1,uid(hex),name(base64),feature(base64),wigend(hex),feature timestamp
1,0A907D2BA05CB12B1D6051782DDAD166,zfVTSVI=                ,5f4uLuEU4+P/Lx7+JydV1AYLJ8UrPd4mXvkYshsk7ujxtAIqBREB5urPAi02wdIo8Bng8BD61eP5Sfn7GgAAFeYIMhL60g7V+RrdJv81AD9IyfzhG/zkJLIELi3yIuwC2QvS2Qfm69ku9Op1D+0N7EcG8yj7qP3b7zriAxkGyM63I9YCBeH2GAUE9d/rAuEQHtzC59nf5w4F8hPODA7BGuYb+wDf3PAe2NAA3AyWBPX+/uz3A9E0/tvyw+EXANgIQ/YAAA==,000000,00000000
*/
static ES_S32 es_facedb_file_add_face(const ES_BYTE *p)
{
    const ES_BYTE *pos = p;
    ES_S32 ret = ES_RET_SUCCESS;
    // const ES_BYTE *tmp_pos = ES_NULL;
    ES_U32 filed_length = 0;
    ES_BYTE uid[FACEDB_UID_LEN] = {0};
    ES_BYTE name_hex[FACEDB_NAME_LEN] = {0};
    ES_BYTE *feature_hex = ES_NULL;
    ES_BYTE wiegand_hex[FACEDB_WIEGAND_LEN] = {0};
    ES_U32 end_time = 0;
    es_facedb_add_param_t facedb_add_param;

    // skip "1,"
    pos = (const ES_BYTE *) (pos + 2);

    // get uid
    filed_length = es_facedb_file_get_uid(pos, uid);
    if (0 == filed_length) {
        ret = ES_RET_FAILURE;
        goto func_end;
    }
    // es_facedb_wrapper_debug("uid length:%d", filed_length);
    // es_log_dump_hex(uid, FACEDB_UID_LEN);

    // get name
    pos = es_facedb_file_get_next_field(pos);
    if (ES_NULL == pos) {
        ret = ES_RET_FAILURE;
        goto func_end;
    }

    filed_length = es_facedb_file_get_name_hex(pos, name_hex);
    if (0 == filed_length) {
        ret = ES_RET_FAILURE;
        goto func_end;
    }
    // es_facedb_wrapper_debug("name hex:");
    // es_log_dump_hex(name_hex, FACEDB_NAME_LEN);

    // get featrue
    pos = es_facedb_file_get_next_field(pos);
    if (ES_NULL == pos) {
        ret = ES_RET_FAILURE;
        goto func_end;
    }

    feature_hex = es_facedb_file_get_feature_hex(pos);
    if (ES_NULL == feature_hex) {
        ret = ES_RET_FAILURE;
        goto func_end;
    }
    // es_facedb_wrapper_debug("feature hex:");
    // es_log_dump_hex(feature_hex, FACEDB_FTR_DIMENSION);

    // get wigend
    pos = es_facedb_file_get_next_field(pos);
    if (ES_NULL == pos) {
        ret = ES_RET_FAILURE;
        goto func_end;
    }
    // es_string_str2hex((const ES_CHAR *)p, wiegand_hex, FACEDB_WIEGAND_LEN);
    // es_facedb_wrapper_debug("wiegand_hex:");
    // es_log_dump_hex(wiegand_hex, FACEDB_WIEGAND_LEN);

    // get end_time
    pos = es_facedb_file_get_next_field(pos);
    if (ES_NULL == pos) {
        ret = ES_RET_FAILURE;
        goto func_end;
    }
    end_time = es_facedb_file_get_timeout(pos);
    // es_facedb_wrapper_debug("end_time:%d", end_time);


    memset(&facedb_add_param, 0x00, sizeof(facedb_add_param));
    facedb_add_param.uid = uid;
    facedb_add_param.name = name_hex;
    facedb_add_param.featrue = feature_hex;
    facedb_add_param.wigend = wiegand_hex;
    facedb_add_param.start_time = 0;
    facedb_add_param.end_time = end_time;
    ret = es_facedb_add(&facedb_add_param);

func_end:

    if (feature_hex) {
        es_free(feature_hex);
        feature_hex = ES_NULL;
    }

    return ret;
}

/*
Format:
2,uid(hex)
2,0A907D2BA05CB12B1D6051782DDAD166
*/
static ES_U32 es_facedb_file_del_face(const ES_BYTE *p)
{
    ES_BYTE uid[FACEDB_UID_LEN] = {0};
    const ES_BYTE *pos = p;

    // skip "2,"
    pos = (const ES_BYTE *) (pos + 2);

    // get uid
    if (0 == es_facedb_file_get_uid(pos, uid)) {
        es_facedb_wrapper_error("get uid fail");
        return ES_RET_FAILURE;
    }

    // es_facedb_wrapper_debug("delete uid:");
    // es_log_dump_hex(uid, FACEDB_UID_LEN);

    return es_facedb_del(uid);
}



ES_S32 es_facedb_file_parse(const ES_BYTE *file_data)
{
    ES_U32 face_num = 0;
    ES_U32 i = 0;
    const ES_BYTE *cur_pos = ES_NULL;

    facedb_run_time = es_time_get_timestamp();

    // first line is the number of face.
    cur_pos = file_data;
    face_num = es_facedb_file_get_face_num(cur_pos);
    if (0 == face_num || face_num > 100) {
        return ES_RET_FAILURE;
    }
    es_facedb_wrapper_debug("face_num:%d", face_num);
    // printk("%s", file_data);

    // skip first line
    cur_pos = (const ES_BYTE *)es_string_get_next_line((const ES_CHAR *)cur_pos);
    if (ES_NULL == cur_pos) {
        return ES_RET_FAILURE;
    }
 
    for (i = 0; i < face_num; i++) {
        if ('1' == *cur_pos) { // add face
            es_facedb_file_add_face(cur_pos);
        } else if ('2' == *cur_pos) { // del face
            es_facedb_file_del_face(cur_pos);
        } else {
            es_facedb_wrapper_error("unknown cmd, %c", *cur_pos);
            return ES_RET_FAILURE;
        }

        // goto next line
        cur_pos = (const ES_BYTE *)es_string_get_next_line((const ES_CHAR *)cur_pos);
        if (ES_NULL == cur_pos) {
            return ES_RET_FAILURE;
        }
    }

    return ES_RET_SUCCESS;
}

ES_S32 es_facedb_parse_mqtt_face_fea(const ES_VOID *msg_json_obj)
{
    cJSON *tmp = ES_NULL;
    const cJSON *msg = (const cJSON *)msg_json_obj;
    ES_BYTE uid_hex[FACEDB_UID_LEN] = {0};
    es_facedb_add_param_t add_param;
    ES_BYTE *fea_hex = ES_NULL;
    ES_U32 hlen;

    tmp = cJSON_GetObjectItem_Type(msg, "fea", cJSON_String);
    if (ES_NULL == tmp) {
        tmp = cJSON_GetObjectItem_Type(msg, "del_all", cJSON_Number);
        if (ES_NULL != tmp) {
            if (1 == tmp->valueint) {
                mf_facedb.del_all();
                return ES_RET_SUCCESS;
            }
            FACEDB_GET_JSON_MSG_UID();
            return es_facedb_del((const ES_BYTE *)uid_hex);
        }
    }

    es_memset(&add_param, 0x00, sizeof(add_param));
    // uid
    FACEDB_GET_JSON_MSG_UID();
    add_param.uid = uid_hex;

    // status
    tmp = cJSON_GetObjectItem_Type(msg, "status", cJSON_Number);
    if (ES_NULL != tmp) {
        add_param.start_time = (ES_U32)tmp->valueint;
    }

    // expire time
    tmp = cJSON_GetObjectItem_Type(msg, "expire", cJSON_Number);
    if (ES_NULL != tmp) {
        add_param.end_time = (ES_U32)tmp->valueint;
    }

    // fea data
    tmp = cJSON_GetObjectItem_Type(msg, "fea", cJSON_String);
    if (ES_NULL == tmp) {
        return ES_RET_FAILURE;
    }

    if (strlen(tmp->valuestring) != 264 /* 1048 */) {
        return ES_RET_FAILURE;
    }
    //compress feature
    // es_facedb_wrapper_debug("fea:%s", json_tmp->valuestring);
    fea_hex = base64_decode((const ES_BYTE *)tmp->valuestring, 264, &hlen);
    if(fea_hex == NULL || hlen != (mf_model.ftr_len)) {
        return ES_RET_FAILURE;
    }
    add_param.featrue = fea_hex;
    es_facedb_add_update(&add_param);
    es_free(fea_hex);

    return ES_RET_SUCCESS;
}

#if ES_TUYA_MODULE_ENABLE
static ES_U8 *es_facedb_tuya_get_name_hex(const cJSON *data, ES_U32 *name_len)
{
    cJSON *json_tmp = ES_NULL;
    ES_U8 *name_hex = ES_NULL;

    json_tmp = cJSON_GetObjectItem(data, "nameBytes");
    if (json_tmp == ES_NULL) {
        es_facedb_wrapper_debug("get nameBytes field fail");
        goto FUNC_END;
    }

    if (cJSON_String != json_tmp->type) {
        es_facedb_wrapper_debug("nameBytes must been string");
        goto FUNC_END;
    }

    if (FACEDB_NAME_LEN < (strlen(json_tmp->valuestring)/4)*3) {
        es_facedb_wrapper_debug("nameBytes is too long");
        goto FUNC_END;
    }

    name_hex = base64_decode((const ES_BYTE *)json_tmp->valuestring, strlen(json_tmp->valuestring), name_len);
    if (ES_NULL == name_hex) {
        es_facedb_wrapper_debug("nameBytes base64 decode fail");
        goto FUNC_END;
    }

     /* 限制小于4个汉字 */
    if (*name_len > 8) {
        // es_facedb_wrapper_debug("name length error");
        goto FUNC_END;
    }

    return name_hex;

FUNC_END:
    if (name_hex) {
        es_free(name_hex);
        name_hex = ES_NULL;
    }
    return ES_NULL;
}

// static void es_dp_compress_feature(float* feature, int8_t* compress_feature)
// {
//     float temp = 0.0;
//     for(uint16_t i = 0; i < mf_model.ftr_len; i++)
//     {
//         if(feature[i] > 0.25)
//         {
//             temp = 0.25;
//         } else if(feature[i] < -0.25)
//         {
//             temp = -0.25;
//         } else
//         {
//             temp = feature[i];
//         }
//         compress_feature[i] = (int8_t)(temp / 0.5 * 256);
//     }
//     return;
// }


// 	"data": {
// 		"faceId": "1450280916004896784",
// 		"featureCont": "/fIOA+cIDywt+Oo7+t8aDRAcDf7h1STeF+T5/gQxDk70IOPe6Soh/Q6MJAgmDc0ZCvcI+A3/jAn6Ah0o5bQC+/b9AgEn5xfe5dQW+xO7H9j9LfzK0tcp3y/xHwg59dnQGgwFERQ56DQxyuLXLAHJ+K/jANUQ2jsFBdcJzRcB7fW8rQ4zEhTSFgTXzeX41Cg4kP7rOs5fIO81D/UzMw9H+Swf6BAMAen3+gTs0+4O+xTJ7eLyMBAu5ggG+entAwC4Grrt+A==",
// 		"uid": "ubay1634126568561KVt3",
// 		"url": "https://tuya-biz-data-1254153901.cos.ap-shanghai.myqcloud.com/microapp/1634270460d348e9c7f7b.jpg?sign=q-sign-algorithm%3Dsha1%26q-ak%3DAKIDopcCYgw0qRoyV5qfKjvg2pPkqESnb5zI%26q-sign-time%3D1634608889%3B1634612489%26q-key-time%3D1634608889%3B1634612489%26q-header-list%3Dhost%26q-url-param-list%3D%26q-signature%3De6061d432ebc795aa7f9796ac53a5f0671980b07&imageMogr2/format/jpeg/quality/50/thumbnail/1024x1024/thumbnail/5000000@/auto-orient"
// 	},
static ES_S32 es_facedb_tuya_set_face_feature(const ES_VOID *json_data)
{
    const cJSON *root = (const cJSON *)json_data;
    cJSON *json_tmp = ES_NULL;
    ES_S32 ret = 0;
    ES_U32 hlen = 0;
    ES_BYTE *fea_hex = ES_NULL;
    ES_BYTE uid_hex[FACEDB_UID_LEN] = {0};
    ES_BYTE int8_fea[MAX_FTR_LEN] = {0};
    ES_U8 *name_hex = ES_NULL;
    ES_U32  name_len = 0;
    es_facedb_add_param_t facedb_add_param;

    json_tmp = cJSON_GetObjectItem(root, "uid");
    if (json_tmp == ES_NULL) {
        ret = -1;
        es_facedb_wrapper_debug("get uid field fail");
        goto FUNC_END;
    }

    if (cJSON_String != json_tmp->type) {
        ret = -1;
        es_facedb_wrapper_debug("featureCont must been string");
        goto FUNC_END;
    }

    if (0 == strlen(json_tmp->valuestring)) {
        ret = -1;
        es_facedb_wrapper_debug("uid length error");
        goto FUNC_END;
    }
    strncpy((ES_CHAR *)uid_hex, json_tmp->valuestring, FACEDB_UID_LEN);


    json_tmp = cJSON_GetObjectItem(root, "featureCont");
    if (json_tmp == ES_NULL) {
        ret = -1;
        es_facedb_wrapper_debug("get featureCont field fail");
        goto FUNC_END;
    }

    if (cJSON_String != json_tmp->type) {
        ret = -1;
        es_facedb_wrapper_debug("featureCont must been string");
        goto FUNC_END;
    }

    if(strlen(json_tmp->valuestring) == 264 /* 1048 */) {
        //compress feature
        // es_facedb_wrapper_debug("fea:%s", json_tmp->valuestring);
        fea_hex = base64_decode((const ES_BYTE *)json_tmp->valuestring, 264, &hlen);
        if(fea_hex == NULL || hlen != (mf_model.ftr_len)) {
            ret = -1;
            es_facedb_wrapper_debug("feature base64 decode error");
            goto FUNC_END;
        }
        es_facedb_wrapper_debug("base64, hlen:%ld", hlen);
        memcpy(int8_fea, fea_hex, mf_model.ftr_len);
    // } else if(strlen(json_tmp->valuestring) == /* 264 */ 1048) {
    //     //uncompress feature
    //     fea_hex = base64_decode((ES_U8*)json_tmp->valuestring, 1048, &hlen);
    //     if(fea_hex == NULL || hlen != (mf_model.ftr_len * 4)) {
    //         ret = -1;
    //         es_facedb_wrapper_debug("feature base64 decode error");
    //         goto FUNC_END;
    //     }
    //     es_dp_compress_feature((float*)fea_hex, int8_fea);
    } else {
        ret = -1;
        es_facedb_wrapper_debug("feature length error");
        goto FUNC_END;
    }

    name_hex = es_facedb_tuya_get_name_hex(root, &name_len);

    memset(&facedb_add_param, 0x00, sizeof(facedb_add_param));
    facedb_add_param.uid = uid_hex;
    facedb_add_param.name = name_hex;
    facedb_add_param.featrue = fea_hex;
    facedb_add_param.wigend = ES_NULL;
    facedb_add_param.start_time = 0;
    facedb_add_param.end_time = 0;
    ret = es_facedb_add(&facedb_add_param);


FUNC_END:
    if (fea_hex) {
        es_free(fea_hex);
        fea_hex = ES_NULL;
    }

    if (name_hex) {
        es_free(name_hex);
        name_hex = ES_NULL;
    }

    return ret;
}

static ES_S32 es_facedb_tuya_set_user_info(const ES_VOID *json_data)
{
#if FACECB_TUYA_ENABLE_RULEIDLIST
    const cJSON *root = (const cJSON *)json_data;
    cJSON *json_tmp = ES_NULL;
    ES_S32 ret = ES_RET_SUCCESS;
    ES_BYTE uid_hex[FACEDB_UID_LEN] = {0};
    es_facedb_update_param_t facedb_update_param;
    ES_U32 start_time = 0;
    ES_U32 end_time = 0;

    json_tmp = cJSON_GetObjectItem(root, "uid");
    if (json_tmp == ES_NULL) {
        ret = -1;
        es_facedb_wrapper_debug("get uid field fail");
        goto FUNC_END;
    }

    if (cJSON_String != json_tmp->type) {
        ret = -1;
        es_facedb_wrapper_debug("featureCont must been string");
        goto FUNC_END;
    }

    if (0 == strlen(json_tmp->valuestring)) {
        ret = -1;
        es_facedb_wrapper_debug("uid length error");
        goto FUNC_END;
    }
    strncpy((ES_CHAR *)uid_hex, json_tmp->valuestring, FACEDB_UID_LEN);
    es_facedb_wrapper_debug("uid:%s", json_tmp->valuestring);

    json_tmp = cJSON_GetObjectItem(root, "beginTime");
    if (json_tmp == ES_NULL) {
        ret = -1;
        es_facedb_wrapper_debug("get endTime field fail");
        goto FUNC_END;
    }

    if (cJSON_Number != json_tmp->type) {
        ret = -1;
        es_facedb_wrapper_debug("beginTime must been number");
        goto FUNC_END;
    }
    start_time = (ES_U32)(((int64_t)json_tmp->valueint)/1000);
    es_facedb_wrapper_debug("start_time:%d", start_time);

    json_tmp = cJSON_GetObjectItem(root, "endTime");
    if (json_tmp == ES_NULL) {
        ret = -1;
        es_facedb_wrapper_debug("get endTime field fail");
        goto FUNC_END;
    }

    if (cJSON_Number != json_tmp->type) {
        ret = -1;
        es_facedb_wrapper_debug("endTime must been number");
        goto FUNC_END;
    }
    end_time = (ES_U32)(((int64_t)json_tmp->valueint)/1000);
    es_facedb_wrapper_debug("end_time:%d", end_time);

    memset(&facedb_update_param, 0x00, sizeof(facedb_update_param));
    facedb_update_param.uid = uid_hex;
    facedb_update_param.start_time = start_time;
    facedb_update_param.end_time = end_time;
    ret = es_facedb_update(&facedb_update_param);

FUNC_END:
    return ret;
#else
    return ES_RET_SUCCESS;
#endif
}

ES_S32 es_facedb_tuya_add(const ES_VOID *json_data)
{
    cJSON *json_tmp = ES_NULL;

    facedb_run_time = es_time_get_timestamp();

    json_tmp = cJSON_GetObjectItem((const cJSON *)json_data, "featureCont");
    if (ES_NULL != json_tmp) {
        return es_facedb_tuya_set_face_feature(json_data);
    }

    json_tmp = cJSON_GetObjectItem((const cJSON *)json_data, "ruleIdList");
    if (ES_NULL != json_tmp) {
        return es_facedb_tuya_set_user_info(json_data);
    }

    return ES_RET_FAILURE;
}


ES_S32 es_facedb_tuya_del(const ES_VOID *json_data)
{
    cJSON *json_tmp = ES_NULL;
    ES_BYTE uid_hex[FACEDB_UID_LEN] = {0};

    facedb_run_time = es_time_get_timestamp();

    json_tmp = cJSON_GetObjectItem((const cJSON *)json_data, "uid");
    if (json_tmp == ES_NULL) {
        es_facedb_wrapper_debug("get uid field fail");
        return ES_RET_FAILURE;
    }

    if (cJSON_String != json_tmp->type) {
        es_facedb_wrapper_debug("featureCont must been string");
        return ES_RET_FAILURE;
    }

    if (0 == strlen(json_tmp->valuestring)) {
        es_facedb_wrapper_debug("uid length error");
        return ES_RET_FAILURE;
    }

    strncpy((char *)uid_hex, json_tmp->valuestring, FACEDB_UID_LEN);
    return es_facedb_del(uid_hex);
}
#endif



ES_BOOL es_facedb_runing(ES_VOID)
{
    ES_U32 now_time = 0;

    if (0 == facedb_run_time) {
        return ES_FALSE;
    }

    now_time = es_time_get_timestamp();
    if ((now_time - facedb_run_time) > 5) {
        facedb_run_time = 0;
        return ES_FALSE;
    }
    
    return ES_TRUE;
}