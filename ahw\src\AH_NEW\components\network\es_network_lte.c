/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_network_lte.c
** bef: define the interface for 4G LTE.
** auth: lines<<EMAIL>>
** create on 2021.12.21 
*/

#include "es_inc.h"

#if ES_4GLTE_ENABLE

ES_S32 es_network_lte_init(ES_VOID)
{
    if (ES_RET_SUCCESS != es_hal_lte_init()) {
        return ES_RET_FAILURE;
    }

    return ES_RET_SUCCESS;
}

ES_VOID es_network_lte_task(ES_VOID)
{
    es_hal_lte_task();
}

ES_S32 es_network_lte_get_status(ES_VOID)
{
    if (es_hal_lte_is_ready()) {
        return ES_NETWORK_CONNECTED;
    }
    return ES_NETWORK_CONNECTING;
}

#endif
