/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_cache.h
** bef: define the interface for cache.
** auth: lines<<EMAIL>>
** create on 2020.08.05
*/

#ifndef _ES_CACHE_H_
#define _ES_CACHE_H_

#ifdef __cplusplus 
extern "C" { 
#endif

#include "es_types.h"


ES_VOID *es_cache_create(ES_U32 size);

ES_S32 es_cache_read(const ES_VOID *cache, ES_U32 offset, ES_BYTE *data, ES_U32 len);

const ES_BYTE *es_cache_get_buf_addr(const ES_VOID *cache);

ES_S32 es_cache_write(const ES_VOID *cache, ES_U32 offset, const ES_BYTE *data, ES_U32 len);

ES_S32 es_cache_destroy(const ES_VOID *cache);

#ifdef __cplusplus 
}
#endif

#endif