/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_dev_cfg.h
** bef: define the interface for device config.
** auth: lines<<EMAIL>>
** create on 2019.11.16 
*/

#ifndef _ES_DEV_CONFIG_H_
#define _ES_DEV_CONFIG_H_

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#include "es_types.h"


ES_S32 es_dev_cfg_init(ES_VOID);

ES_S32 es_dev_cfg_try_pull_cfg(ES_VOID);

ES_S32 es_dev_cfg_check_nfc_id(const ES_BYTE *nfc_id);

ES_S32 es_dev_cfg_update_nfc_list(const cJSON *array, ES_U32 pkg_id, ES_U32 pkg_num);

ES_S32 es_dev_cfg_update_ble_mac_list(const cJSON *array);

ES_S32 es_dev_cfg_update_qrcode(const ES_CHAR *qrcode_str);

ES_S32 es_dev_cfg_get_qrcode(ES_CHAR *qrcode_str);

#ifdef __cplusplus 
}
#endif
#endif
