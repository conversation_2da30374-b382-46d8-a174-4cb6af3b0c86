/*******************************************************************************
 * Size: 28 px
 * Bpp: 4
 * Opts: --no-compress --no-prefilter --bpp 4 --size 28 --font <PERSON>-Medium.ttf -r 0x20-0x7F,0xB0,0x2022 --font <PERSON><PERSON>ome5-Solid+Brands+Regular.woff -r 61441,61448,61451,61452,61452,61453,61457,61459,61461,61465,61468,61473,61478,61479,61480,61502,61507,61512,61515,61516,61517,61521,61522,61523,61524,61543,61544,61550,61552,61553,61556,61559,61560,61561,61563,61587,61589,61636,61637,61639,61641,61664,61671,61674,61683,61724,61732,61787,61931,62016,62017,62018,62019,62020,62087,62099,62212,62189,62810,63426,63650 --format lvgl -o lv_font_montserrat_28.c --force-fast-kern-format
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "../../lvgl.h"
#endif

#ifndef LV_FONT_MONTSERRAT_28
#define LV_FONT_MONTSERRAT_28 1
#endif

#if LV_FONT_MONTSERRAT_28

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0xc, 0xff, 0x50, 0xcf, 0xf4, 0xb, 0xff, 0x30,
    0xaf, 0xf3, 0xa, 0xff, 0x20, 0x9f, 0xf2, 0x9,
    0xff, 0x10, 0x8f, 0xf0, 0x7, 0xff, 0x0, 0x7f,
    0xf0, 0x6, 0xfe, 0x0, 0x6f, 0xe0, 0x5, 0xfd,
    0x0, 0x27, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x86, 0x0, 0xdf, 0xf6, 0xf, 0xff, 0x80,
    0x6f, 0xc1,

    /* U+0022 "\"" */
    0x3f, 0xf1, 0x2, 0xff, 0x23, 0xff, 0x10, 0x2f,
    0xf2, 0x2f, 0xf0, 0x1, 0xff, 0x12, 0xff, 0x0,
    0x1f, 0xf1, 0x2f, 0xf0, 0x1, 0xff, 0x11, 0xff,
    0x0, 0xf, 0xf0, 0x1f, 0xf0, 0x0, 0xff, 0x0,
    0x98, 0x0, 0x9, 0x90,

    /* U+0023 "#" */
    0x0, 0x0, 0x0, 0xaf, 0x50, 0x0, 0x6, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xf3, 0x0, 0x0,
    0x7f, 0x80, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x10,
    0x0, 0x9, 0xf6, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xf0, 0x0, 0x0, 0xbf, 0x40, 0x0, 0x0, 0x0,
    0x1, 0xfd, 0x0, 0x0, 0xd, 0xf2, 0x0, 0x0,
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe0, 0x0, 0x0, 0x8f, 0x80, 0x0,
    0x4, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x9, 0xf6,
    0x0, 0x0, 0x5f, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0x40, 0x0, 0x7, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xf2, 0x0, 0x0, 0x9f, 0x60, 0x0,
    0x0, 0x0, 0x0, 0xff, 0x0, 0x0, 0xa, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xe0, 0x0, 0x0,
    0xcf, 0x30, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x4f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x7, 0xf8, 0x0, 0x0, 0x2f, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0x60, 0x0, 0x4, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xf4, 0x0, 0x0, 0x6f,
    0x90, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x20, 0x0,
    0x8, 0xf7, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0,
    0x0, 0x0, 0xaf, 0x50, 0x0, 0x0,

    /* U+0024 "$" */
    0x0, 0x0, 0x0, 0xf, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x8c, 0xef, 0xff, 0xd9, 0x40, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x20,
    0x5, 0xff, 0xfc, 0x8f, 0xd7, 0xae, 0xff, 0x10,
    0xe, 0xff, 0x50, 0xf, 0xc0, 0x0, 0x59, 0x0,
    0x4f, 0xf9, 0x0, 0xf, 0xc0, 0x0, 0x0, 0x0,
    0x5f, 0xf6, 0x0, 0xf, 0xc0, 0x0, 0x0, 0x0,
    0x4f, 0xfa, 0x0, 0xf, 0xc0, 0x0, 0x0, 0x0,
    0xe, 0xff, 0x80, 0xf, 0xc0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xff, 0xbf, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x5e, 0xff, 0xff, 0xfe, 0x94, 0x0, 0x0,
    0x0, 0x1, 0x6b, 0xff, 0xff, 0xff, 0xc3, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xed, 0xff, 0xff, 0x30,
    0x0, 0x0, 0x0, 0xf, 0xc0, 0x3c, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0xf, 0xc0, 0x0, 0xdf, 0xf1,
    0x0, 0x0, 0x0, 0xf, 0xc0, 0x0, 0x8f, 0xf3,
    0x4, 0x0, 0x0, 0xf, 0xc0, 0x0, 0xbf, 0xf1,
    0x5f, 0xa2, 0x0, 0xf, 0xc0, 0x5, 0xff, 0xc0,
    0xaf, 0xff, 0xc8, 0x7f, 0xd8, 0xcf, 0xff, 0x30,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe4, 0x0,
    0x0, 0x17, 0xbe, 0xff, 0xff, 0xc7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xc0, 0x0, 0x0, 0x0,

    /* U+0025 "%" */
    0x0, 0x7d, 0xfe, 0x80, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0x30, 0x0, 0xa, 0xfd, 0x9c, 0xfc, 0x0,
    0x0, 0x0, 0x9, 0xf9, 0x0, 0x0, 0x4f, 0xb0,
    0x0, 0x8f, 0x70, 0x0, 0x0, 0x4f, 0xd0, 0x0,
    0x0, 0xbf, 0x30, 0x0, 0xf, 0xd0, 0x0, 0x0,
    0xdf, 0x30, 0x0, 0x0, 0xef, 0x0, 0x0, 0xc,
    0xf1, 0x0, 0x9, 0xf9, 0x0, 0x0, 0x0, 0xfe,
    0x0, 0x0, 0xa, 0xf1, 0x0, 0x4f, 0xd0, 0x0,
    0x0, 0x0, 0xef, 0x0, 0x0, 0xc, 0xf1, 0x0,
    0xef, 0x30, 0x0, 0x0, 0x0, 0xbf, 0x30, 0x0,
    0xf, 0xd0, 0x9, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xb0, 0x0, 0x8f, 0x70, 0x4f, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xfd, 0x9c, 0xfc, 0x0,
    0xef, 0x30, 0x5, 0x88, 0x50, 0x0, 0x0, 0x7d,
    0xfe, 0x80, 0x9, 0xf8, 0x1, 0xcf, 0xff, 0xfc,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xd0, 0xb,
    0xf9, 0x11, 0x8f, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0x30, 0x2f, 0xc0, 0x0, 0xc, 0xf3, 0x0,
    0x0, 0x0, 0x9, 0xf8, 0x0, 0x6f, 0x60, 0x0,
    0x6, 0xf7, 0x0, 0x0, 0x0, 0x4f, 0xd0, 0x0,
    0x8f, 0x40, 0x0, 0x4, 0xf8, 0x0, 0x0, 0x1,
    0xef, 0x30, 0x0, 0x7f, 0x50, 0x0, 0x5, 0xf8,
    0x0, 0x0, 0xa, 0xf8, 0x0, 0x0, 0x4f, 0x80,
    0x0, 0x8, 0xf5, 0x0, 0x0, 0x4f, 0xd0, 0x0,
    0x0, 0xe, 0xe1, 0x0, 0x1e, 0xe0, 0x0, 0x1,
    0xef, 0x30, 0x0, 0x0, 0x4, 0xfe, 0x87, 0xef,
    0x40, 0x0, 0xa, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x3b, 0xff, 0xb3, 0x0,

    /* U+0026 "&" */
    0x0, 0x0, 0x19, 0xdf, 0xfd, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xfe, 0x40, 0x5, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x3f, 0xf5, 0x0, 0x0,
    0x9f, 0xc0, 0x0, 0x0, 0x0, 0x4f, 0xf3, 0x0,
    0x0, 0x8f, 0xc0, 0x0, 0x0, 0x0, 0x2f, 0xf7,
    0x0, 0x1, 0xef, 0x80, 0x0, 0x0, 0x0, 0xc,
    0xff, 0x20, 0x2d, 0xfe, 0x10, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xd8, 0xff, 0xe3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xfb, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xcf, 0xff, 0xe1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xfe, 0x8f, 0xfd, 0x10,
    0x0, 0x24, 0x0, 0x6, 0xff, 0xa1, 0x5, 0xff,
    0xd1, 0x0, 0x9f, 0xb0, 0x2f, 0xfb, 0x0, 0x0,
    0x5f, 0xfd, 0x10, 0xdf, 0x70, 0x8f, 0xf2, 0x0,
    0x0, 0x5, 0xff, 0xd6, 0xff, 0x30, 0xbf, 0xe0,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0xfb, 0x0, 0xaf,
    0xf1, 0x0, 0x0, 0x0, 0x5, 0xff, 0xf5, 0x0,
    0x6f, 0xfa, 0x0, 0x0, 0x0, 0x9, 0xff, 0xfd,
    0x10, 0xd, 0xff, 0xe7, 0x54, 0x59, 0xef, 0xfb,
    0xff, 0xd1, 0x1, 0xbf, 0xff, 0xff, 0xff, 0xfe,
    0x60, 0x5f, 0xf8, 0x0, 0x4, 0x9d, 0xff, 0xeb,
    0x60, 0x0, 0x5, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0027 "'" */
    0x3f, 0xf1, 0x3f, 0xf1, 0x2f, 0xf0, 0x2f, 0xf0,
    0x2f, 0xf0, 0x1f, 0xf0, 0x1f, 0xf0, 0x9, 0x80,

    /* U+0028 "(" */
    0x0, 0x4, 0xff, 0x40, 0x0, 0xcf, 0xc0, 0x0,
    0x4f, 0xf5, 0x0, 0xb, 0xfe, 0x0, 0x0, 0xff,
    0x90, 0x0, 0x5f, 0xf4, 0x0, 0x9, 0xff, 0x0,
    0x0, 0xcf, 0xd0, 0x0, 0xf, 0xfa, 0x0, 0x2,
    0xff, 0x70, 0x0, 0x3f, 0xf6, 0x0, 0x4, 0xff,
    0x60, 0x0, 0x5f, 0xf5, 0x0, 0x5, 0xff, 0x50,
    0x0, 0x4f, 0xf6, 0x0, 0x3, 0xff, 0x60, 0x0,
    0x2f, 0xf7, 0x0, 0x0, 0xff, 0xa0, 0x0, 0xc,
    0xfd, 0x0, 0x0, 0x9f, 0xf0, 0x0, 0x5, 0xff,
    0x40, 0x0, 0xf, 0xf9, 0x0, 0x0, 0xaf, 0xe0,
    0x0, 0x4, 0xff, 0x50, 0x0, 0xc, 0xfc, 0x0,
    0x0, 0x4f, 0xf4,

    /* U+0029 ")" */
    0xd, 0xfb, 0x0, 0x0, 0x6f, 0xf3, 0x0, 0x0,
    0xdf, 0xb0, 0x0, 0x7, 0xff, 0x20, 0x0, 0x2f,
    0xf7, 0x0, 0x0, 0xdf, 0xc0, 0x0, 0x8, 0xff,
    0x0, 0x0, 0x6f, 0xf3, 0x0, 0x3, 0xff, 0x60,
    0x0, 0xf, 0xf9, 0x0, 0x0, 0xff, 0xa0, 0x0,
    0xe, 0xfb, 0x0, 0x0, 0xdf, 0xc0, 0x0, 0xd,
    0xfc, 0x0, 0x0, 0xef, 0xb0, 0x0, 0xf, 0xfa,
    0x0, 0x0, 0xff, 0x90, 0x0, 0x3f, 0xf6, 0x0,
    0x6, 0xff, 0x30, 0x0, 0x8f, 0xf0, 0x0, 0xd,
    0xfc, 0x0, 0x2, 0xff, 0x70, 0x0, 0x7f, 0xf2,
    0x0, 0xd, 0xfb, 0x0, 0x6, 0xff, 0x30, 0x0,
    0xdf, 0xb0, 0x0,

    /* U+002A "*" */
    0x0, 0x0, 0x3f, 0x60, 0x0, 0x0, 0x0, 0x3,
    0xf6, 0x0, 0x0, 0x1e, 0x80, 0x3f, 0x60, 0x7e,
    0x33, 0xdf, 0xe9, 0xfa, 0xdf, 0xe5, 0x0, 0x7e,
    0xff, 0xff, 0x91, 0x0, 0x1, 0xaf, 0xff, 0xc2,
    0x0, 0x7, 0xef, 0xef, 0xef, 0xf9, 0x13, 0xfe,
    0x63, 0xf6, 0x4d, 0xf6, 0x6, 0x10, 0x3f, 0x60,
    0x6, 0x0, 0x0, 0x3, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x17, 0x30, 0x0, 0x0,

    /* U+002B "+" */
    0x0, 0x0, 0x0, 0x44, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x50, 0x0, 0x0, 0x3, 0x33, 0x33, 0xff, 0x73,
    0x33, 0x31, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x50, 0x0, 0x0,

    /* U+002C "," */
    0x1a, 0xb4, 0x8, 0xff, 0xe0, 0x9f, 0xff, 0x2,
    0xdf, 0xc0, 0xa, 0xf6, 0x0, 0xef, 0x10, 0x2f,
    0xc0, 0x6, 0xf6, 0x0,

    /* U+002D "-" */
    0x25, 0x55, 0x55, 0x55, 0x6, 0xff, 0xff, 0xff,
    0xf2, 0x6f, 0xff, 0xff, 0xff, 0x20,

    /* U+002E "." */
    0x1a, 0xc4, 0x9, 0xff, 0xe0, 0xaf, 0xff, 0x2,
    0xde, 0x60,

    /* U+002F "/" */
    0x0, 0x0, 0x0, 0x0, 0x5, 0x86, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x70, 0x0, 0x0,
    0x0, 0x4, 0xff, 0x20, 0x0, 0x0, 0x0, 0xa,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x6, 0xff, 0x10, 0x0,
    0x0, 0x0, 0xb, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xa0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0x50, 0x0, 0x0, 0x0, 0x7,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0x90, 0x0, 0x0, 0x0, 0x3, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x9, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xd0, 0x0, 0x0,
    0x0, 0x0,

    /* U+0030 "0" */
    0x0, 0x0, 0x7, 0xcf, 0xfe, 0xb5, 0x0, 0x0,
    0x0, 0x0, 0x4e, 0xff, 0xff, 0xff, 0xfb, 0x10,
    0x0, 0x0, 0x4f, 0xff, 0xd9, 0x8a, 0xff, 0xfd,
    0x10, 0x0, 0x1e, 0xff, 0x70, 0x0, 0x1, 0xbf,
    0xfa, 0x0, 0x8, 0xff, 0x80, 0x0, 0x0, 0x0,
    0xdf, 0xf3, 0x0, 0xef, 0xe0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0x90, 0x3f, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xfe, 0x7, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xf1, 0x9f, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0x3a, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xf4, 0xaf, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0x49, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xf3, 0x7f, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0x13, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0xef, 0xe0, 0xe,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf9, 0x0,
    0x8f, 0xf8, 0x0, 0x0, 0x0, 0xd, 0xff, 0x30,
    0x1, 0xef, 0xf7, 0x0, 0x0, 0x1b, 0xff, 0xa0,
    0x0, 0x4, 0xff, 0xfd, 0x98, 0xaf, 0xff, 0xd1,
    0x0, 0x0, 0x4, 0xef, 0xff, 0xff, 0xff, 0xc1,
    0x0, 0x0, 0x0, 0x0, 0x7c, 0xff, 0xeb, 0x50,
    0x0, 0x0,

    /* U+0031 "1" */
    0xcf, 0xff, 0xff, 0xf6, 0xcf, 0xff, 0xff, 0xf6,
    0x57, 0x77, 0xaf, 0xf6, 0x0, 0x0, 0x5f, 0xf6,
    0x0, 0x0, 0x5f, 0xf6, 0x0, 0x0, 0x5f, 0xf6,
    0x0, 0x0, 0x5f, 0xf6, 0x0, 0x0, 0x5f, 0xf6,
    0x0, 0x0, 0x5f, 0xf6, 0x0, 0x0, 0x5f, 0xf6,
    0x0, 0x0, 0x5f, 0xf6, 0x0, 0x0, 0x5f, 0xf6,
    0x0, 0x0, 0x5f, 0xf6, 0x0, 0x0, 0x5f, 0xf6,
    0x0, 0x0, 0x5f, 0xf6, 0x0, 0x0, 0x5f, 0xf6,
    0x0, 0x0, 0x5f, 0xf6, 0x0, 0x0, 0x5f, 0xf6,
    0x0, 0x0, 0x5f, 0xf6, 0x0, 0x0, 0x5f, 0xf6,

    /* U+0032 "2" */
    0x0, 0x3, 0x8c, 0xef, 0xfc, 0x82, 0x0, 0x0,
    0x2, 0xbf, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0,
    0x2e, 0xff, 0xfb, 0x98, 0xae, 0xff, 0xf5, 0x0,
    0x2d, 0xf9, 0x10, 0x0, 0x0, 0x8f, 0xfe, 0x0,
    0x1, 0x60, 0x0, 0x0, 0x0, 0xc, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xe1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xfa, 0x77, 0x77, 0x77, 0x77, 0x70,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,

    /* U+0033 "3" */
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x6,
    0x77, 0x77, 0x77, 0x77, 0xdf, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1e, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xf9, 0x61, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x3, 0x88, 0xad,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xa0, 0x50, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf8,
    0x3f, 0xc4, 0x0, 0x0, 0x0, 0x5f, 0xff, 0x2a,
    0xff, 0xfe, 0xa8, 0x89, 0xdf, 0xff, 0x70, 0x9,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x1,
    0x6a, 0xdf, 0xfe, 0xc7, 0x20, 0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xfe, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xa0, 0x0, 0xa, 0xb7, 0x0,
    0x0, 0x0, 0x3f, 0xfd, 0x0, 0x0, 0xf, 0xfa,
    0x0, 0x0, 0x1, 0xef, 0xf2, 0x0, 0x0, 0xf,
    0xfa, 0x0, 0x0, 0xb, 0xff, 0x50, 0x0, 0x0,
    0xf, 0xfa, 0x0, 0x0, 0x8f, 0xfd, 0x66, 0x66,
    0x66, 0x6f, 0xfc, 0x66, 0x62, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xfa, 0x0, 0x0,

    /* U+0035 "5" */
    0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x4f, 0xf9, 0x77, 0x77, 0x77, 0x77, 0x0,
    0x0, 0x6f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xff, 0xff, 0xec, 0x94, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc2, 0x0,
    0x0, 0x77, 0x77, 0x77, 0x9c, 0xff, 0xfe, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2c, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf0,
    0x3, 0x10, 0x0, 0x0, 0x0, 0x1, 0xff, 0xd0,
    0xd, 0xe6, 0x0, 0x0, 0x0, 0x2c, 0xff, 0x70,
    0x5f, 0xff, 0xfb, 0x98, 0x9b, 0xff, 0xfd, 0x0,
    0x5, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xb1, 0x0,
    0x0, 0x4, 0x9c, 0xef, 0xed, 0x94, 0x0, 0x0,

    /* U+0036 "6" */
    0x0, 0x0, 0x2, 0x8c, 0xef, 0xfd, 0xa4, 0x0,
    0x0, 0x1, 0xaf, 0xff, 0xff, 0xff, 0xff, 0x40,
    0x0, 0x1d, 0xff, 0xfb, 0x86, 0x89, 0xed, 0x0,
    0x0, 0xcf, 0xfb, 0x20, 0x0, 0x0, 0x2, 0x0,
    0x6, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xf3, 0x5, 0xae, 0xfe, 0xd8, 0x20, 0x0,
    0xaf, 0xf3, 0xbf, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0xaf, 0xfd, 0xff, 0x95, 0x46, 0xaf, 0xff, 0x60,
    0x9f, 0xff, 0xd1, 0x0, 0x0, 0x4, 0xff, 0xe0,
    0x7f, 0xff, 0x30, 0x0, 0x0, 0x0, 0x8f, 0xf5,
    0x4f, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf7,
    0x1f, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf6,
    0xa, 0xff, 0x30, 0x0, 0x0, 0x0, 0x8f, 0xf4,
    0x2, 0xff, 0xd1, 0x0, 0x0, 0x4, 0xff, 0xd0,
    0x0, 0x7f, 0xff, 0x95, 0x45, 0xaf, 0xff, 0x40,
    0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xe4, 0x0,
    0x0, 0x0, 0x17, 0xce, 0xfe, 0xc7, 0x10, 0x0,

    /* U+0037 "7" */
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9,
    0x2f, 0xfb, 0x77, 0x77, 0x77, 0x77, 0xcf, 0xf5,
    0x2f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xff, 0xe0,
    0x2f, 0xf8, 0x0, 0x0, 0x0, 0x6, 0xff, 0x70,
    0x2f, 0xf8, 0x0, 0x0, 0x0, 0xd, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xfe, 0x0, 0x0, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x0, 0x5a, 0xdf, 0xfd, 0xa6, 0x0, 0x0,
    0x0, 0x3d, 0xff, 0xff, 0xff, 0xff, 0xd3, 0x0,
    0x2, 0xef, 0xfd, 0x75, 0x57, 0xdf, 0xff, 0x20,
    0xa, 0xff, 0x80, 0x0, 0x0, 0x8, 0xff, 0xa0,
    0xe, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf0,
    0xf, 0xfb, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf0,
    0xf, 0xfd, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf0,
    0xa, 0xff, 0x50, 0x0, 0x0, 0x5, 0xff, 0xa0,
    0x1, 0xef, 0xfa, 0x42, 0x25, 0xaf, 0xfe, 0x10,
    0x0, 0x2c, 0xff, 0xff, 0xff, 0xff, 0xc2, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0xb, 0xff, 0xc5, 0x20, 0x2, 0x6d, 0xff, 0xb0,
    0x5f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf5,
    0xaf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xfa,
    0xbf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfc,
    0x9f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xfa,
    0x4f, 0xfd, 0x20, 0x0, 0x0, 0x2, 0xdf, 0xf5,
    0xa, 0xff, 0xfa, 0x64, 0x56, 0xaf, 0xff, 0xb0,
    0x0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0x0, 0x2, 0x7c, 0xef, 0xfe, 0xc7, 0x20, 0x0,

    /* U+0039 "9" */
    0x0, 0x0, 0x5b, 0xef, 0xed, 0x93, 0x0, 0x0,
    0x0, 0x2c, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0,
    0x1, 0xdf, 0xfc, 0x64, 0x58, 0xdf, 0xfa, 0x0,
    0x9, 0xff, 0x80, 0x0, 0x0, 0xa, 0xff, 0x60,
    0xf, 0xfd, 0x0, 0x0, 0x0, 0x0, 0xef, 0xe0,
    0x2f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xf5,
    0x2f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xf9,
    0xf, 0xfd, 0x0, 0x0, 0x0, 0x0, 0xef, 0xfc,
    0xa, 0xff, 0x80, 0x0, 0x0, 0xa, 0xff, 0xfd,
    0x2, 0xff, 0xfd, 0x75, 0x58, 0xdf, 0xee, 0xfe,
    0x0, 0x3e, 0xff, 0xff, 0xff, 0xfe, 0x3e, 0xfe,
    0x0, 0x0, 0x7c, 0xef, 0xeb, 0x60, 0xf, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xa0,
    0x0, 0x11, 0x0, 0x0, 0x0, 0x8f, 0xfe, 0x10,
    0x0, 0x8f, 0xa8, 0x67, 0xae, 0xff, 0xf3, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x20, 0x0,
    0x0, 0x39, 0xcf, 0xff, 0xd9, 0x40, 0x0, 0x0,

    /* U+003A ":" */
    0x2d, 0xe6, 0xa, 0xff, 0xf0, 0x9f, 0xfe, 0x1,
    0xac, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xac, 0x40, 0x9f, 0xfe,
    0xa, 0xff, 0xf0, 0x2d, 0xe6, 0x0,

    /* U+003B ";" */
    0x2d, 0xe6, 0xa, 0xff, 0xf0, 0x9f, 0xfe, 0x1,
    0xac, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xab, 0x40, 0x8f, 0xfe,
    0x9, 0xff, 0xf0, 0x2d, 0xfc, 0x0, 0xaf, 0x60,
    0xe, 0xf1, 0x2, 0xfc, 0x0, 0x6f, 0x60, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x96, 0x0,
    0x0, 0x0, 0x0, 0x16, 0xdf, 0xf6, 0x0, 0x0,
    0x0, 0x3a, 0xff, 0xff, 0xb2, 0x0, 0x1, 0x7d,
    0xff, 0xfd, 0x71, 0x0, 0x4, 0xaf, 0xff, 0xfa,
    0x30, 0x0, 0x0, 0x2f, 0xff, 0xc6, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xfe, 0x61, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xa4, 0x0, 0x0, 0x0,
    0x0, 0x16, 0xdf, 0xff, 0xd7, 0x10, 0x0, 0x0,
    0x0, 0x3, 0x9f, 0xff, 0xfb, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x6c, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x39, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1,

    /* U+003D "=" */
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x2f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x3, 0x33,
    0x33, 0x33, 0x33, 0x33, 0x31, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0x33, 0x33, 0x33, 0x33, 0x33,
    0x31, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,

    /* U+003E ">" */
    0x1b, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xfe, 0x82, 0x0, 0x0, 0x0, 0x0, 0x9, 0xef,
    0xff, 0xc5, 0x0, 0x0, 0x0, 0x0, 0x5, 0xbf,
    0xff, 0xe9, 0x20, 0x0, 0x0, 0x0, 0x2, 0x8e,
    0xff, 0xfc, 0x60, 0x0, 0x0, 0x0, 0x0, 0x4a,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x4, 0xbf,
    0xf6, 0x0, 0x0, 0x0, 0x28, 0xef, 0xff, 0xc3,
    0x0, 0x0, 0x5c, 0xff, 0xfe, 0x82, 0x0, 0x3,
    0x9f, 0xff, 0xfb, 0x50, 0x0, 0x0, 0x1f, 0xff,
    0xe8, 0x20, 0x0, 0x0, 0x0, 0x2f, 0xb5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+003F "?" */
    0x0, 0x4, 0x9d, 0xef, 0xfc, 0x92, 0x0, 0x0,
    0x2b, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x3e,
    0xff, 0xe9, 0x76, 0x8d, 0xff, 0xf6, 0x4, 0xef,
    0x70, 0x0, 0x0, 0x7, 0xff, 0xe0, 0x1, 0x50,
    0x0, 0x0, 0x0, 0xd, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xaa, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x18, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xed, 0x10, 0x0, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x0, 0x0, 0x27, 0xbd, 0xff, 0xfe,
    0xb8, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xcf, 0xff, 0xfe, 0xde, 0xff, 0xff, 0xc5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xd7,
    0x20, 0x0, 0x0, 0x26, 0xcf, 0xfb, 0x10, 0x0,
    0x0, 0x0, 0x1d, 0xfe, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4d, 0xfd, 0x10, 0x0, 0x0, 0x1d,
    0xfc, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xfd, 0x10, 0x0, 0xa, 0xfc, 0x0, 0x0,
    0x18, 0xdf, 0xfd, 0x81, 0xf, 0xf8, 0xb, 0xfa,
    0x0, 0x3, 0xff, 0x20, 0x0, 0x4f, 0xff, 0xff,
    0xff, 0xe4, 0xff, 0x80, 0x1e, 0xf3, 0x0, 0xbf,
    0x80, 0x0, 0x4f, 0xff, 0x83, 0x35, 0xbf, 0xff,
    0xf8, 0x0, 0x6f, 0xb0, 0x1f, 0xf1, 0x0, 0xe,
    0xfe, 0x20, 0x0, 0x0, 0x6f, 0xff, 0x80, 0x0,
    0xef, 0x15, 0xfc, 0x0, 0x5, 0xff, 0x40, 0x0,
    0x0, 0x0, 0xaf, 0xf8, 0x0, 0xa, 0xf4, 0x8f,
    0x80, 0x0, 0xaf, 0xd0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0x80, 0x0, 0x7f, 0x79, 0xf7, 0x0, 0xd,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf8, 0x0,
    0x5, 0xf8, 0xaf, 0x60, 0x0, 0xef, 0x90, 0x0,
    0x0, 0x0, 0x0, 0xef, 0x80, 0x0, 0x4f, 0x99,
    0xf7, 0x0, 0xd, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf8, 0x0, 0x5, 0xf8, 0x7f, 0x90, 0x0,
    0xaf, 0xd0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x80,
    0x0, 0x7f, 0x75, 0xfc, 0x0, 0x5, 0xff, 0x40,
    0x0, 0x0, 0x0, 0xaf, 0xf8, 0x0, 0xa, 0xf4,
    0x1f, 0xf1, 0x0, 0xe, 0xfe, 0x20, 0x0, 0x0,
    0x6f, 0xff, 0x90, 0x1, 0xff, 0x0, 0xbf, 0x80,
    0x0, 0x4f, 0xff, 0x83, 0x24, 0xaf, 0xfc, 0xff,
    0x53, 0xcf, 0x80, 0x3, 0xff, 0x20, 0x0, 0x4f,
    0xff, 0xff, 0xff, 0xe4, 0x4f, 0xff, 0xff, 0xc0,
    0x0, 0xa, 0xfc, 0x0, 0x0, 0x18, 0xcf, 0xfd,
    0x81, 0x0, 0x5d, 0xfe, 0x80, 0x0, 0x0, 0xd,
    0xfc, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1d, 0xfe, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1a, 0xff, 0xd7, 0x30, 0x0,
    0x0, 0x37, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xcf, 0xff, 0xfe, 0xee, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x37, 0xbe, 0xff, 0xfd, 0xa6, 0x10, 0x0, 0x0,
    0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xe9, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0x81, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xff, 0x10, 0xaf, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfa, 0x0,
    0x3f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf3, 0x0, 0xc, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xd0, 0x0, 0x6, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x60,
    0x0, 0x0, 0xef, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xfe, 0x0, 0x0, 0x0, 0x8f, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xf8, 0x0, 0x0, 0x0,
    0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf5,
    0x33, 0x33, 0x33, 0x3c, 0xff, 0x30, 0x0, 0x0,
    0x2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0x9, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x1f, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf8, 0x0,
    0x0, 0x7f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xfe, 0x0, 0x0, 0xef, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0x60, 0x5, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xd0, 0xc, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xf4,

    /* U+0042 "B" */
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xa5, 0x0,
    0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xd2, 0x0, 0x1f, 0xfc, 0x44, 0x44, 0x44, 0x69,
    0xff, 0xfd, 0x0, 0x1f, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x1c, 0xff, 0x60, 0x1f, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xa0, 0x1f, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xb0, 0x1f, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x90, 0x1f,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0x40,
    0x1f, 0xfb, 0x0, 0x0, 0x0, 0x14, 0xbf, 0xfb,
    0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xa0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe7, 0x0, 0x1f, 0xfc, 0x44, 0x44, 0x44,
    0x45, 0x8e, 0xff, 0xa0, 0x1f, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xf4, 0x1f, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xf9, 0x1f, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfc, 0x1f,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xfb,
    0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xf7, 0x1f, 0xfc, 0x44, 0x44, 0x44, 0x45, 0x8e,
    0xff, 0xe1, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x20, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xda, 0x50, 0x0,

    /* U+0043 "C" */
    0x0, 0x0, 0x0, 0x38, 0xce, 0xfe, 0xda, 0x50,
    0x0, 0x0, 0x0, 0x2, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xe5, 0x0, 0x0, 0x5, 0xff, 0xff, 0xda,
    0x89, 0xbf, 0xff, 0xf8, 0x0, 0x5, 0xff, 0xfb,
    0x30, 0x0, 0x0, 0x6, 0xef, 0xb0, 0x1, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x2, 0x80, 0x0,
    0xaf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x29, 0x0,
    0x0, 0x5f, 0xff, 0xb3, 0x0, 0x0, 0x0, 0x6e,
    0xfb, 0x0, 0x0, 0x5f, 0xff, 0xfd, 0xa8, 0x8a,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x2c, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x50, 0x0, 0x0, 0x0, 0x3,
    0x8c, 0xef, 0xed, 0xa5, 0x0, 0x0,

    /* U+0044 "D" */
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xec, 0x94, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd4, 0x0, 0x0, 0x1f, 0xfd, 0x77, 0x77,
    0x77, 0x9c, 0xff, 0xff, 0x80, 0x0, 0x1f, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x29, 0xff, 0xf8, 0x0,
    0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0x40, 0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xc0, 0x1f, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xf3, 0x1f, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf7,
    0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xfa, 0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xfc, 0x1f, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xfc, 0x1f, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xfa,
    0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xf7, 0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xf3, 0x1f, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xc0, 0x1f, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0x40,
    0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x19, 0xff,
    0xf8, 0x0, 0x1f, 0xfd, 0x77, 0x77, 0x77, 0x9c,
    0xff, 0xff, 0x80, 0x0, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd4, 0x0, 0x0, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0xec, 0x94, 0x0, 0x0, 0x0,

    /* U+0045 "E" */
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0x1f, 0xfd, 0x77, 0x77, 0x77, 0x77, 0x77, 0x50,
    0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0x1f, 0xfd, 0x66, 0x66, 0x66, 0x66, 0x63, 0x0,
    0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xfd, 0x77, 0x77, 0x77, 0x77, 0x77, 0x71,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,

    /* U+0046 "F" */
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc1,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x1f,
    0xfd, 0x77, 0x77, 0x77, 0x77, 0x77, 0x51, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xd6, 0x66, 0x66,
    0x66, 0x66, 0x30, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0x1, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x90, 0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x0, 0x0, 0x38, 0xce, 0xff, 0xda, 0x61,
    0x0, 0x0, 0x0, 0x2, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x5, 0xff, 0xff, 0xda,
    0x88, 0xae, 0xff, 0xfb, 0x0, 0x4, 0xff, 0xfc,
    0x30, 0x0, 0x0, 0x4, 0xdf, 0xd1, 0x1, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x81, 0x0,
    0x9f, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x10, 0xaf,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0x38, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf3, 0x5f, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0x31, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xf3, 0x9, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x30, 0x1f,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf3,
    0x0, 0x4f, 0xff, 0xc3, 0x0, 0x0, 0x0, 0x3c,
    0xff, 0x30, 0x0, 0x5f, 0xff, 0xfd, 0xa8, 0x89,
    0xdf, 0xff, 0xe1, 0x0, 0x0, 0x2b, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x91, 0x0, 0x0, 0x0, 0x3,
    0x8c, 0xef, 0xfd, 0xa6, 0x10, 0x0,

    /* U+0048 "H" */
    0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xfc, 0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xfc, 0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xfc, 0x1f, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xfc, 0x1f, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xfc, 0x1f, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfc, 0x1f, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfc, 0x1f,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfc,
    0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xfc, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x1f, 0xfd, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x7f, 0xfc, 0x1f, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xfc, 0x1f, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfc, 0x1f, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfc, 0x1f,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfc,
    0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xfc, 0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xfc, 0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xfc, 0x1f, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xfc,

    /* U+0049 "I" */
    0x1f, 0xfb, 0x1f, 0xfb, 0x1f, 0xfb, 0x1f, 0xfb,
    0x1f, 0xfb, 0x1f, 0xfb, 0x1f, 0xfb, 0x1f, 0xfb,
    0x1f, 0xfb, 0x1f, 0xfb, 0x1f, 0xfb, 0x1f, 0xfb,
    0x1f, 0xfb, 0x1f, 0xfb, 0x1f, 0xfb, 0x1f, 0xfb,
    0x1f, 0xfb, 0x1f, 0xfb, 0x1f, 0xfb, 0x1f, 0xfb,

    /* U+004A "J" */
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0x90, 0x4,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x17, 0x77,
    0x77, 0x78, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0x80, 0x6, 0x0, 0x0, 0x0, 0x8f, 0xf6,
    0xa, 0xf8, 0x0, 0x0, 0x1f, 0xff, 0x11, 0xdf,
    0xfd, 0x86, 0x8e, 0xff, 0xa0, 0x2, 0xcf, 0xff,
    0xff, 0xff, 0xc0, 0x0, 0x0, 0x5b, 0xef, 0xeb,
    0x60, 0x0,

    /* U+004B "K" */
    0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef,
    0xe2, 0x1, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x2,
    0xef, 0xf3, 0x0, 0x1f, 0xfb, 0x0, 0x0, 0x0,
    0x1, 0xef, 0xf4, 0x0, 0x1, 0xff, 0xb0, 0x0,
    0x0, 0x1, 0xdf, 0xf5, 0x0, 0x0, 0x1f, 0xfb,
    0x0, 0x0, 0x1, 0xdf, 0xf6, 0x0, 0x0, 0x1,
    0xff, 0xb0, 0x0, 0x0, 0xcf, 0xf7, 0x0, 0x0,
    0x0, 0x1f, 0xfb, 0x0, 0x0, 0xcf, 0xf8, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xb0, 0x0, 0xbf, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xfb, 0x0, 0xbf,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xb0,
    0xaf, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xfb, 0x9f, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xff, 0xfe, 0x8f, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xfe, 0x20, 0x8f, 0xfe,
    0x10, 0x0, 0x0, 0x1, 0xff, 0xfe, 0x20, 0x0,
    0xbf, 0xfc, 0x0, 0x0, 0x0, 0x1f, 0xfe, 0x20,
    0x0, 0x0, 0xcf, 0xfa, 0x0, 0x0, 0x1, 0xff,
    0xb0, 0x0, 0x0, 0x1, 0xef, 0xf7, 0x0, 0x0,
    0x1f, 0xfb, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf4,
    0x0, 0x1, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xe2, 0x0, 0x1f, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xd0, 0x1, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xb0,

    /* U+004C "L" */
    0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xd7, 0x77, 0x77, 0x77, 0x77, 0x72, 0x1f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x51, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,

    /* U+004D "M" */
    0x1f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xfc, 0x1f, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xfc, 0x1f, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xfc, 0x1f, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xfc, 0x1f, 0xff, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xfc, 0x1f,
    0xfe, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xfc, 0x1f, 0xfa, 0xcf, 0xe1, 0x0, 0x0,
    0x0, 0x4, 0xff, 0x7e, 0xfc, 0x1f, 0xfa, 0x3f,
    0xf9, 0x0, 0x0, 0x0, 0xd, 0xfd, 0xe, 0xfc,
    0x1f, 0xfa, 0x9, 0xff, 0x20, 0x0, 0x0, 0x6f,
    0xf4, 0xe, 0xfc, 0x1f, 0xfa, 0x1, 0xff, 0xb0,
    0x0, 0x0, 0xef, 0xb0, 0xe, 0xfc, 0x1f, 0xfa,
    0x0, 0x7f, 0xf5, 0x0, 0x8, 0xff, 0x20, 0xe,
    0xfc, 0x1f, 0xfa, 0x0, 0xd, 0xfe, 0x0, 0x2f,
    0xf8, 0x0, 0xe, 0xfc, 0x1f, 0xfa, 0x0, 0x4,
    0xff, 0x70, 0xbf, 0xe0, 0x0, 0xe, 0xfc, 0x1f,
    0xfa, 0x0, 0x0, 0xaf, 0xf6, 0xff, 0x60, 0x0,
    0xe, 0xfc, 0x1f, 0xfa, 0x0, 0x0, 0x2f, 0xff,
    0xfc, 0x0, 0x0, 0xe, 0xfc, 0x1f, 0xfa, 0x0,
    0x0, 0x8, 0xff, 0xf3, 0x0, 0x0, 0xe, 0xfc,
    0x1f, 0xfa, 0x0, 0x0, 0x0, 0xef, 0xa0, 0x0,
    0x0, 0xe, 0xfc, 0x1f, 0xfa, 0x0, 0x0, 0x0,
    0x49, 0x10, 0x0, 0x0, 0xe, 0xfc, 0x1f, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xfc, 0x1f, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xfc,

    /* U+004E "N" */
    0x1f, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xfc, 0x1f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xfc, 0x1f, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xfc, 0x1f, 0xff, 0xfe, 0x10, 0x0,
    0x0, 0x0, 0xf, 0xfc, 0x1f, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0xf, 0xfc, 0x1f, 0xfc, 0xdf,
    0xf9, 0x0, 0x0, 0x0, 0xf, 0xfc, 0x1f, 0xfb,
    0x2f, 0xff, 0x50, 0x0, 0x0, 0xf, 0xfc, 0x1f,
    0xfb, 0x5, 0xff, 0xf2, 0x0, 0x0, 0xf, 0xfc,
    0x1f, 0xfb, 0x0, 0x9f, 0xfd, 0x10, 0x0, 0xf,
    0xfc, 0x1f, 0xfb, 0x0, 0xc, 0xff, 0xb0, 0x0,
    0xf, 0xfc, 0x1f, 0xfb, 0x0, 0x1, 0xef, 0xf8,
    0x0, 0xf, 0xfc, 0x1f, 0xfb, 0x0, 0x0, 0x3f,
    0xff, 0x40, 0xf, 0xfc, 0x1f, 0xfb, 0x0, 0x0,
    0x6, 0xff, 0xf2, 0xf, 0xfc, 0x1f, 0xfb, 0x0,
    0x0, 0x0, 0xaf, 0xfd, 0xf, 0xfc, 0x1f, 0xfb,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xaf, 0xfc, 0x1f,
    0xfb, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff, 0xfc,
    0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xfc, 0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xfc, 0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xfc, 0x1f, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xfc,

    /* U+004F "O" */
    0x0, 0x0, 0x0, 0x38, 0xce, 0xff, 0xda, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2b, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x70, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xda, 0x89, 0xbf, 0xff, 0xfb, 0x10,
    0x0, 0x0, 0x4f, 0xff, 0xb3, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xc0, 0x0, 0x1, 0xef, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xdf, 0xf9, 0x0, 0x9,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0x20, 0x1f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0x90, 0x5f, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xd0,
    0x8f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xf0, 0xaf, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xf2, 0xaf, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xf2, 0x8f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xf0, 0x5f, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xd0, 0x1f,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0x90, 0x9, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0x20, 0x1, 0xef, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xdf, 0xf9, 0x0,
    0x0, 0x4f, 0xff, 0xb3, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xc0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xda,
    0x89, 0xbf, 0xff, 0xfc, 0x10, 0x0, 0x0, 0x0,
    0x2b, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x38, 0xce, 0xff, 0xda,
    0x60, 0x0, 0x0, 0x0,

    /* U+0050 "P" */
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xda, 0x50, 0x0,
    0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd3,
    0x0, 0x1f, 0xfd, 0x77, 0x77, 0x78, 0xaf, 0xff,
    0xf3, 0x1, 0xff, 0xb0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xd0, 0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0x51, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xf9, 0x1f, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xb1, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xfb, 0x1f, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0x91, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xf5, 0x1f, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xfd, 0x1, 0xff, 0xd7,
    0x77, 0x77, 0x7a, 0xff, 0xff, 0x30, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x30, 0x1, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0xa5, 0x0, 0x0, 0x1f,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x0, 0x0, 0x28, 0xce, 0xff, 0xda, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xe7, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xff, 0xda, 0x89, 0xbf, 0xff, 0xfb,
    0x10, 0x0, 0x0, 0x4, 0xff, 0xfc, 0x30, 0x0,
    0x0, 0x7, 0xff, 0xfc, 0x0, 0x0, 0x1, 0xef,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x2, 0xdf, 0xf8,
    0x0, 0x0, 0x9f, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xf2, 0x0, 0xf, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0x80,
    0x5, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xfd, 0x0, 0x8f, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf0, 0x9,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0x20, 0xaf, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xf1, 0x8, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xff, 0x0, 0x5f, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xd0, 0x1, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf8,
    0x0, 0xa, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0x20, 0x0, 0x2f, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x1d, 0xff, 0x90, 0x0,
    0x0, 0x5f, 0xff, 0xa2, 0x0, 0x0, 0x0, 0x5e,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xfc,
    0x87, 0x79, 0xef, 0xff, 0xc1, 0x0, 0x0, 0x0,
    0x0, 0x3d, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xad, 0xff,
    0xff, 0xd6, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0x40, 0x0, 0x0,
    0x93, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xa5, 0x46, 0xdf, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4e, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xce, 0xfd, 0x81, 0x0,

    /* U+0052 "R" */
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xda, 0x50, 0x0,
    0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x30, 0x0, 0x1f, 0xfd, 0x77, 0x77, 0x78, 0xaf,
    0xff, 0xf3, 0x0, 0x1f, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xfd, 0x0, 0x1f, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0x50, 0x1f, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0x90, 0x1f, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xb0, 0x1f,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xb0,
    0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0x90, 0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0x50, 0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xfe, 0x0, 0x1f, 0xfd, 0x66, 0x66, 0x67,
    0x9e, 0xff, 0xf3, 0x0, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x30, 0x0, 0x1f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x1f, 0xfb,
    0x0, 0x0, 0x0, 0xcf, 0xf3, 0x0, 0x0, 0x1f,
    0xfb, 0x0, 0x0, 0x0, 0x2f, 0xfe, 0x0, 0x0,
    0x1f, 0xfb, 0x0, 0x0, 0x0, 0x6, 0xff, 0xa0,
    0x0, 0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xf5, 0x0, 0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x1e, 0xfe, 0x10, 0x1f, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xb0,

    /* U+0053 "S" */
    0x0, 0x0, 0x6b, 0xdf, 0xfe, 0xc8, 0x30, 0x0,
    0x0, 0x5e, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x20,
    0x4, 0xff, 0xfc, 0x86, 0x67, 0xbf, 0xff, 0x10,
    0xd, 0xff, 0x50, 0x0, 0x0, 0x0, 0x69, 0x0,
    0x3f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xff, 0xa6, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x5e, 0xff, 0xff, 0xfd, 0x94, 0x0, 0x0,
    0x0, 0x0, 0x6b, 0xff, 0xff, 0xff, 0xd3, 0x0,
    0x0, 0x0, 0x0, 0x4, 0x8c, 0xff, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2b, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf3,
    0x5, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf1,
    0x5f, 0xb4, 0x0, 0x0, 0x0, 0x6, 0xff, 0xb0,
    0xaf, 0xff, 0xe9, 0x76, 0x68, 0xdf, 0xff, 0x30,
    0x7, 0xef, 0xff, 0xff, 0xff, 0xff, 0xd3, 0x0,
    0x0, 0x5, 0x9d, 0xef, 0xed, 0xa5, 0x0, 0x0,

    /* U+0054 "T" */
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x5e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x67, 0x77, 0x77, 0x8f, 0xfc, 0x77, 0x77,
    0x77, 0x20, 0x0, 0x0, 0x2, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x90, 0x0,
    0x0, 0x0,

    /* U+0055 "U" */
    0x3f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xf5, 0x3f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xf5, 0x3f, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xf5, 0x3f, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xf5, 0x3f, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xf5, 0x3f, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xf5, 0x3f, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf5, 0x3f,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf5,
    0x3f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xf5, 0x3f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xf5, 0x3f, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xf5, 0x3f, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xf5, 0x2f, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xf4, 0xf, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xf2, 0xc, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0xef, 0xe0, 0x7,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x90,
    0x0, 0xef, 0xf9, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0x20, 0x0, 0x4f, 0xff, 0xea, 0x88, 0xae, 0xff,
    0xf5, 0x0, 0x0, 0x3, 0xef, 0xff, 0xff, 0xff,
    0xfe, 0x40, 0x0, 0x0, 0x0, 0x6, 0xae, 0xff,
    0xeb, 0x60, 0x0, 0x0,

    /* U+0056 "V" */
    0xd, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xc0, 0x6f, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xf5, 0x0, 0xef, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xfe, 0x0,
    0x8, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0x70, 0x0, 0x1f, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xf1, 0x0, 0x0, 0xaf, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xf9, 0x0, 0x0,
    0x3, 0xff, 0xc0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0x20, 0x0, 0x0, 0xc, 0xff, 0x30, 0x0, 0x0,
    0x1, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x5f, 0xfa,
    0x0, 0x0, 0x0, 0x8f, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xf1, 0x0, 0x0, 0xe, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0x80, 0x0, 0x6,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfe,
    0x0, 0x0, 0xdf, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xf5, 0x0, 0x4f, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xc0, 0xa, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0x32, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xfa, 0x8f, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xf9, 0x0, 0x0,
    0x0, 0x0,

    /* U+0057 "W" */
    0xe, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x70,
    0x9f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf2, 0x4,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xfc, 0x0, 0xe,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x3, 0xff, 0x70, 0x0, 0xaf,
    0xf3, 0x0, 0x0, 0x0, 0xe, 0xfb, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x8f, 0xf2, 0x0, 0x4, 0xff,
    0x90, 0x0, 0x0, 0x4, 0xff, 0x4b, 0xff, 0x0,
    0x0, 0x0, 0xd, 0xfd, 0x0, 0x0, 0xf, 0xfe,
    0x0, 0x0, 0x0, 0x9f, 0xf0, 0x6f, 0xf5, 0x0,
    0x0, 0x2, 0xff, 0x70, 0x0, 0x0, 0xaf, 0xf3,
    0x0, 0x0, 0xe, 0xfa, 0x1, 0xff, 0xa0, 0x0,
    0x0, 0x8f, 0xf2, 0x0, 0x0, 0x5, 0xff, 0x80,
    0x0, 0x4, 0xff, 0x40, 0xb, 0xff, 0x0, 0x0,
    0xd, 0xfd, 0x0, 0x0, 0x0, 0xf, 0xfd, 0x0,
    0x0, 0xaf, 0xe0, 0x0, 0x6f, 0xf4, 0x0, 0x2,
    0xff, 0x80, 0x0, 0x0, 0x0, 0xaf, 0xf3, 0x0,
    0xf, 0xfa, 0x0, 0x1, 0xff, 0xa0, 0x0, 0x8f,
    0xf3, 0x0, 0x0, 0x0, 0x5, 0xff, 0x80, 0x4,
    0xff, 0x40, 0x0, 0xb, 0xff, 0x0, 0xd, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfd, 0x0, 0xaf,
    0xe0, 0x0, 0x0, 0x5f, 0xf4, 0x2, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xf2, 0xf, 0xf9,
    0x0, 0x0, 0x0, 0xff, 0x90, 0x7f, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0x85, 0xff, 0x40,
    0x0, 0x0, 0xb, 0xfe, 0xd, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xfd, 0xaf, 0xe0, 0x0,
    0x0, 0x0, 0x5f, 0xf7, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xf3, 0x0, 0x0, 0x0,

    /* U+0058 "X" */
    0xc, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xf9, 0x0, 0x2f, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x2f, 0xfd, 0x0, 0x0, 0x6f, 0xfc, 0x0, 0x0,
    0x0, 0xd, 0xff, 0x30, 0x0, 0x0, 0xbf, 0xf7,
    0x0, 0x0, 0x8, 0xff, 0x70, 0x0, 0x0, 0x1,
    0xef, 0xf3, 0x0, 0x4, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xd0, 0x1, 0xef, 0xe1, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xa0, 0xaf, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xaf,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xd2, 0xff,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x1, 0xef, 0xf2,
    0x6, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xf7, 0x0, 0xa, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x6f, 0xfb, 0x0, 0x0, 0x1e, 0xff, 0x40, 0x0,
    0x0, 0x2f, 0xfe, 0x10, 0x0, 0x0, 0x3f, 0xfe,
    0x10, 0x0, 0xd, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x8f, 0xfa, 0x0, 0x9, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xf6, 0x4, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xf2,

    /* U+0059 "Y" */
    0xc, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xe0, 0x3, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0x50, 0x0, 0x9f, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xfb, 0x0, 0x0, 0x1e,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf2, 0x0,
    0x0, 0x6, 0xff, 0x90, 0x0, 0x0, 0x3, 0xff,
    0x80, 0x0, 0x0, 0x0, 0xdf, 0xf2, 0x0, 0x0,
    0xd, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xfb,
    0x0, 0x0, 0x6f, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0x50, 0x1, 0xef, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xe0, 0x9, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf8, 0x3f,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0xdf, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xf7, 0x0, 0x0, 0x0, 0x0,

    /* U+005A "Z" */
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x39, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf3, 0x47, 0x77, 0x77, 0x77, 0x77, 0x77, 0xdf,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xfe, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xef, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1e, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xf8, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x74, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x9c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9,

    /* U+005B "[" */
    0x1f, 0xff, 0xff, 0xc1, 0xff, 0xff, 0xfc, 0x1f,
    0xfb, 0x33, 0x21, 0xff, 0xa0, 0x0, 0x1f, 0xfa,
    0x0, 0x1, 0xff, 0xa0, 0x0, 0x1f, 0xfa, 0x0,
    0x1, 0xff, 0xa0, 0x0, 0x1f, 0xfa, 0x0, 0x1,
    0xff, 0xa0, 0x0, 0x1f, 0xfa, 0x0, 0x1, 0xff,
    0xa0, 0x0, 0x1f, 0xfa, 0x0, 0x1, 0xff, 0xa0,
    0x0, 0x1f, 0xfa, 0x0, 0x1, 0xff, 0xa0, 0x0,
    0x1f, 0xfa, 0x0, 0x1, 0xff, 0xa0, 0x0, 0x1f,
    0xfa, 0x0, 0x1, 0xff, 0xa0, 0x0, 0x1f, 0xfa,
    0x0, 0x1, 0xff, 0xa0, 0x0, 0x1f, 0xfa, 0x0,
    0x1, 0xff, 0xb3, 0x32, 0x1f, 0xff, 0xff, 0xc1,
    0xff, 0xff, 0xfc,

    /* U+005C "\\" */
    0x7, 0x83, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x60,

    /* U+005D "]" */
    0x7f, 0xff, 0xff, 0x67, 0xff, 0xff, 0xf6, 0x13,
    0x37, 0xff, 0x60, 0x0, 0x4f, 0xf6, 0x0, 0x4,
    0xff, 0x60, 0x0, 0x4f, 0xf6, 0x0, 0x4, 0xff,
    0x60, 0x0, 0x4f, 0xf6, 0x0, 0x4, 0xff, 0x60,
    0x0, 0x4f, 0xf6, 0x0, 0x4, 0xff, 0x60, 0x0,
    0x4f, 0xf6, 0x0, 0x4, 0xff, 0x60, 0x0, 0x4f,
    0xf6, 0x0, 0x4, 0xff, 0x60, 0x0, 0x4f, 0xf6,
    0x0, 0x4, 0xff, 0x60, 0x0, 0x4f, 0xf6, 0x0,
    0x4, 0xff, 0x60, 0x0, 0x4f, 0xf6, 0x0, 0x4,
    0xff, 0x60, 0x0, 0x4f, 0xf6, 0x0, 0x4, 0xff,
    0x61, 0x33, 0x7f, 0xf6, 0x7f, 0xff, 0xff, 0x67,
    0xff, 0xff, 0xf6,

    /* U+005E "^" */
    0x0, 0x0, 0x7, 0x83, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x3f, 0xd7, 0xf9,
    0x0, 0x0, 0x0, 0xa, 0xf6, 0x1f, 0xf0, 0x0,
    0x0, 0x1, 0xff, 0x0, 0xaf, 0x60, 0x0, 0x0,
    0x8f, 0x90, 0x3, 0xfd, 0x0, 0x0, 0xe, 0xf2,
    0x0, 0xd, 0xf4, 0x0, 0x6, 0xfb, 0x0, 0x0,
    0x6f, 0xb0, 0x0, 0xdf, 0x40, 0x0, 0x0, 0xff,
    0x20, 0x4f, 0xd0, 0x0, 0x0, 0x9, 0xf9, 0xb,
    0xf7, 0x0, 0x0, 0x0, 0x2f, 0xf0,

    /* U+005F "_" */
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff,

    /* U+0060 "`" */
    0x38, 0x87, 0x0, 0x0, 0x6, 0xff, 0xb0, 0x0,
    0x0, 0x2d, 0xfc, 0x10, 0x0, 0x0, 0xaf, 0xd1,

    /* U+0061 "a" */
    0x0, 0x17, 0xbe, 0xff, 0xda, 0x40, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0xd, 0xfe,
    0xa6, 0x56, 0xaf, 0xff, 0x60, 0x3, 0x70, 0x0,
    0x0, 0x2, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xf4, 0x0, 0x28, 0xce, 0xef, 0xff, 0xff,
    0xf4, 0x6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0x2f, 0xfe, 0x51, 0x0, 0x0, 0x6f, 0xf5, 0x7f,
    0xf4, 0x0, 0x0, 0x0, 0x6f, 0xf5, 0x9f, 0xf1,
    0x0, 0x0, 0x0, 0x8f, 0xf5, 0x7f, 0xf5, 0x0,
    0x0, 0x2, 0xff, 0xf5, 0x2f, 0xfe, 0x50, 0x1,
    0x6e, 0xff, 0xf5, 0x5, 0xff, 0xff, 0xff, 0xff,
    0x9f, 0xf5, 0x0, 0x29, 0xef, 0xfd, 0x92, 0x3f,
    0xf5,

    /* U+0062 "b" */
    0x7f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf3, 0x5, 0xbe, 0xfd, 0xb5, 0x0, 0x0,
    0x7f, 0xf5, 0xcf, 0xff, 0xff, 0xff, 0xd2, 0x0,
    0x7f, 0xfe, 0xff, 0xa7, 0x68, 0xef, 0xff, 0x20,
    0x7f, 0xff, 0xd2, 0x0, 0x0, 0x9, 0xff, 0xd0,
    0x7f, 0xff, 0x20, 0x0, 0x0, 0x0, 0xaf, 0xf5,
    0x7f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xfa,
    0x7f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0xe, 0xfc,
    0x7f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfe,
    0x7f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0xe, 0xfc,
    0x7f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xfa,
    0x7f, 0xff, 0x20, 0x0, 0x0, 0x0, 0xbf, 0xf5,
    0x7f, 0xff, 0xd2, 0x0, 0x0, 0x9, 0xff, 0xd0,
    0x7f, 0xfd, 0xff, 0xa7, 0x68, 0xef, 0xff, 0x20,
    0x7f, 0xf3, 0xcf, 0xff, 0xff, 0xff, 0xd2, 0x0,
    0x7f, 0xf2, 0x5, 0xbe, 0xfe, 0xb5, 0x0, 0x0,

    /* U+0063 "c" */
    0x0, 0x0, 0x39, 0xdf, 0xfd, 0x93, 0x0, 0x0,
    0x1, 0xbf, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x1,
    0xdf, 0xfe, 0x96, 0x69, 0xff, 0xf8, 0x0, 0xbf,
    0xfa, 0x0, 0x0, 0x1, 0xcf, 0x80, 0x3f, 0xfc,
    0x0, 0x0, 0x0, 0x1, 0x20, 0x8, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xfc, 0x0, 0x0, 0x0,
    0x1, 0x20, 0x0, 0xbf, 0xfa, 0x0, 0x0, 0x1,
    0xcf, 0x80, 0x1, 0xdf, 0xfe, 0x96, 0x69, 0xff,
    0xf8, 0x0, 0x1, 0xbf, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x39, 0xdf, 0xfd, 0x93, 0x0,
    0x0,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf8,
    0x0, 0x0, 0x5a, 0xdf, 0xec, 0x60, 0x2f, 0xf8,
    0x0, 0x2c, 0xff, 0xff, 0xff, 0xfd, 0x4f, 0xf8,
    0x1, 0xef, 0xfe, 0x96, 0x6a, 0xff, 0xef, 0xf8,
    0xc, 0xff, 0xa0, 0x0, 0x0, 0x1c, 0xff, 0xf8,
    0x3f, 0xfc, 0x0, 0x0, 0x0, 0x1, 0xef, 0xf8,
    0x9f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf8,
    0xbf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf8,
    0xcf, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf8,
    0xbf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf8,
    0x9f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf8,
    0x3f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf8,
    0xc, 0xff, 0x80, 0x0, 0x0, 0xb, 0xff, 0xf8,
    0x1, 0xef, 0xfc, 0x63, 0x47, 0xef, 0xef, 0xf8,
    0x0, 0x2c, 0xff, 0xff, 0xff, 0xfe, 0x3f, 0xf8,
    0x0, 0x0, 0x5b, 0xdf, 0xec, 0x70, 0xf, 0xf8,

    /* U+0065 "e" */
    0x0, 0x0, 0x4b, 0xdf, 0xec, 0x61, 0x0, 0x0,
    0x1, 0xcf, 0xff, 0xff, 0xff, 0xe4, 0x0, 0x1,
    0xef, 0xfb, 0x64, 0x59, 0xff, 0xf4, 0x0, 0xbf,
    0xf4, 0x0, 0x0, 0x2, 0xdf, 0xe1, 0x3f, 0xf6,
    0x0, 0x0, 0x0, 0x2, 0xff, 0x69, 0xff, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xfb, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xdc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0xbf, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x10, 0x0, 0xbf, 0xfb, 0x10, 0x0, 0x0,
    0x5e, 0x30, 0x1, 0xdf, 0xff, 0x96, 0x68, 0xcf,
    0xfc, 0x0, 0x1, 0xbf, 0xff, 0xff, 0xff, 0xfd,
    0x20, 0x0, 0x0, 0x39, 0xde, 0xfe, 0xa5, 0x0,
    0x0,

    /* U+0066 "f" */
    0x0, 0x0, 0x18, 0xdf, 0xeb, 0x40, 0x0, 0x1d,
    0xff, 0xff, 0xf9, 0x0, 0x9, 0xff, 0xa4, 0x49,
    0x30, 0x0, 0xef, 0xd0, 0x0, 0x0, 0x0, 0xf,
    0xf9, 0x0, 0x0, 0x0, 0x1, 0xff, 0x90, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xff, 0xfe, 0x9, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x23, 0x4f, 0xfb, 0x33,
    0x33, 0x0, 0x1, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x1f, 0xfa, 0x0, 0x0, 0x0, 0x1, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x1f, 0xfa, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x1f, 0xfa,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x1f, 0xfa, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x1f, 0xfa, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x1f,
    0xfa, 0x0, 0x0, 0x0,

    /* U+0067 "g" */
    0x0, 0x0, 0x5b, 0xdf, 0xec, 0x71, 0xc, 0xfc,
    0x0, 0x3d, 0xff, 0xff, 0xff, 0xfe, 0x4c, 0xfc,
    0x3, 0xff, 0xfd, 0x86, 0x68, 0xef, 0xfe, 0xfc,
    0xd, 0xff, 0x80, 0x0, 0x0, 0x8, 0xff, 0xfc,
    0x5f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xfc,
    0xaf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xfc,
    0xcf, 0xe0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xfc,
    0xcf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfc,
    0xaf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xfc,
    0x5f, 0xfa, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xfc,
    0xd, 0xff, 0x90, 0x0, 0x0, 0x9, 0xff, 0xfc,
    0x2, 0xff, 0xfe, 0x96, 0x69, 0xef, 0xff, 0xfc,
    0x0, 0x3d, 0xff, 0xff, 0xff, 0xfe, 0x4f, 0xfb,
    0x0, 0x0, 0x5b, 0xdf, 0xec, 0x71, 0xf, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf5,
    0x3, 0xe6, 0x0, 0x0, 0x0, 0x5, 0xff, 0xe0,
    0xc, 0xff, 0xea, 0x76, 0x68, 0xcf, 0xff, 0x50,
    0x2, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x0, 0x3, 0x8b, 0xef, 0xfe, 0xb7, 0x10, 0x0,

    /* U+0068 "h" */
    0x7f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf3, 0x6,
    0xbe, 0xfe, 0xb5, 0x0, 0x7, 0xff, 0x5d, 0xff,
    0xff, 0xff, 0xfb, 0x0, 0x7f, 0xff, 0xfe, 0x97,
    0x7a, 0xff, 0xf9, 0x7, 0xff, 0xfb, 0x0, 0x0,
    0x3, 0xff, 0xf2, 0x7f, 0xfe, 0x0, 0x0, 0x0,
    0x7, 0xff, 0x67, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x3f, 0xf8, 0x7f, 0xf4, 0x0, 0x0, 0x0, 0x1,
    0xff, 0x97, 0xff, 0x30, 0x0, 0x0, 0x0, 0x1f,
    0xfa, 0x7f, 0xf3, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xa7, 0xff, 0x30, 0x0, 0x0, 0x0, 0x1f, 0xfa,
    0x7f, 0xf3, 0x0, 0x0, 0x0, 0x1, 0xff, 0xa7,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x1f, 0xfa, 0x7f,
    0xf3, 0x0, 0x0, 0x0, 0x1, 0xff, 0xa7, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x1f, 0xfa, 0x7f, 0xf3,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xa0,

    /* U+0069 "i" */
    0x5e, 0xd3, 0xdf, 0xfa, 0xbf, 0xf7, 0x6, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xf3, 0x7f, 0xf3,
    0x7f, 0xf3, 0x7f, 0xf3, 0x7f, 0xf3, 0x7f, 0xf3,
    0x7f, 0xf3, 0x7f, 0xf3, 0x7f, 0xf3, 0x7f, 0xf3,
    0x7f, 0xf3, 0x7f, 0xf3, 0x7f, 0xf3, 0x7f, 0xf3,
    0x7f, 0xf3,

    /* U+006A "j" */
    0x0, 0x0, 0x3, 0xee, 0x40, 0x0, 0x0, 0xbf,
    0xfc, 0x0, 0x0, 0x9, 0xff, 0x90, 0x0, 0x0,
    0x6, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x60,
    0x0, 0x0, 0x5f, 0xf6, 0x0, 0x0, 0x5, 0xff,
    0x60, 0x0, 0x0, 0x5f, 0xf6, 0x0, 0x0, 0x5,
    0xff, 0x60, 0x0, 0x0, 0x5f, 0xf6, 0x0, 0x0,
    0x5, 0xff, 0x60, 0x0, 0x0, 0x5f, 0xf6, 0x0,
    0x0, 0x5, 0xff, 0x60, 0x0, 0x0, 0x5f, 0xf6,
    0x0, 0x0, 0x5, 0xff, 0x60, 0x0, 0x0, 0x5f,
    0xf6, 0x0, 0x0, 0x5, 0xff, 0x60, 0x0, 0x0,
    0x5f, 0xf6, 0x0, 0x0, 0x5, 0xff, 0x60, 0x0,
    0x0, 0x5f, 0xf5, 0x0, 0x0, 0x9, 0xff, 0x30,
    0xb6, 0x58, 0xff, 0xe0, 0x5f, 0xff, 0xff, 0xf4,
    0x2, 0xad, 0xfe, 0xb3, 0x0,

    /* U+006B "k" */
    0x7f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf3, 0x0, 0x0, 0x0, 0x4f, 0xfe, 0x20,
    0x7f, 0xf3, 0x0, 0x0, 0x5, 0xff, 0xe2, 0x0,
    0x7f, 0xf3, 0x0, 0x0, 0x6f, 0xfe, 0x20, 0x0,
    0x7f, 0xf3, 0x0, 0x7, 0xff, 0xe2, 0x0, 0x0,
    0x7f, 0xf3, 0x0, 0x8f, 0xfe, 0x20, 0x0, 0x0,
    0x7f, 0xf3, 0x9, 0xff, 0xe2, 0x0, 0x0, 0x0,
    0x7f, 0xf4, 0xaf, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x7f, 0xfe, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xfc, 0x5f, 0xff, 0x20, 0x0, 0x0,
    0x7f, 0xff, 0xb0, 0x8, 0xff, 0xd0, 0x0, 0x0,
    0x7f, 0xfa, 0x0, 0x0, 0xbf, 0xfa, 0x0, 0x0,
    0x7f, 0xf3, 0x0, 0x0, 0xd, 0xff, 0x70, 0x0,
    0x7f, 0xf3, 0x0, 0x0, 0x2, 0xff, 0xf4, 0x0,
    0x7f, 0xf3, 0x0, 0x0, 0x0, 0x5f, 0xfe, 0x10,
    0x7f, 0xf3, 0x0, 0x0, 0x0, 0x8, 0xff, 0xc0,

    /* U+006C "l" */
    0x7f, 0xf3, 0x7f, 0xf3, 0x7f, 0xf3, 0x7f, 0xf3,
    0x7f, 0xf3, 0x7f, 0xf3, 0x7f, 0xf3, 0x7f, 0xf3,
    0x7f, 0xf3, 0x7f, 0xf3, 0x7f, 0xf3, 0x7f, 0xf3,
    0x7f, 0xf3, 0x7f, 0xf3, 0x7f, 0xf3, 0x7f, 0xf3,
    0x7f, 0xf3, 0x7f, 0xf3, 0x7f, 0xf3, 0x7f, 0xf3,
    0x7f, 0xf3,

    /* U+006D "m" */
    0x7f, 0xf2, 0x18, 0xce, 0xfd, 0x93, 0x0, 0x2,
    0x8d, 0xff, 0xd9, 0x20, 0x0, 0x7f, 0xf5, 0xef,
    0xff, 0xff, 0xff, 0x70, 0x7f, 0xff, 0xff, 0xff,
    0xf6, 0x0, 0x7f, 0xff, 0xfc, 0x64, 0x5a, 0xff,
    0xfa, 0xff, 0xc6, 0x45, 0xaf, 0xff, 0x30, 0x7f,
    0xff, 0x80, 0x0, 0x0, 0x6f, 0xff, 0xf8, 0x0,
    0x0, 0x7, 0xff, 0xb0, 0x7f, 0xfd, 0x0, 0x0,
    0x0, 0xd, 0xff, 0xc0, 0x0, 0x0, 0x0, 0xdf,
    0xf0, 0x7f, 0xf7, 0x0, 0x0, 0x0, 0x9, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x9f, 0xf1, 0x7f, 0xf4,
    0x0, 0x0, 0x0, 0x7, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x8f, 0xf2, 0x7f, 0xf3, 0x0, 0x0, 0x0,
    0x7, 0xff, 0x30, 0x0, 0x0, 0x0, 0x8f, 0xf3,
    0x7f, 0xf3, 0x0, 0x0, 0x0, 0x7, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x8f, 0xf3, 0x7f, 0xf3, 0x0,
    0x0, 0x0, 0x7, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x8f, 0xf3, 0x7f, 0xf3, 0x0, 0x0, 0x0, 0x7,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x8f, 0xf3, 0x7f,
    0xf3, 0x0, 0x0, 0x0, 0x7, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x8f, 0xf3, 0x7f, 0xf3, 0x0, 0x0,
    0x0, 0x7, 0xff, 0x30, 0x0, 0x0, 0x0, 0x8f,
    0xf3, 0x7f, 0xf3, 0x0, 0x0, 0x0, 0x7, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x8f, 0xf3, 0x7f, 0xf3,
    0x0, 0x0, 0x0, 0x7, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x8f, 0xf3,

    /* U+006E "n" */
    0x7f, 0xf2, 0x7, 0xce, 0xfe, 0xb5, 0x0, 0x7,
    0xff, 0x5e, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x7f,
    0xff, 0xfd, 0x75, 0x58, 0xef, 0xf9, 0x7, 0xff,
    0xf9, 0x0, 0x0, 0x2, 0xef, 0xf2, 0x7f, 0xfd,
    0x0, 0x0, 0x0, 0x7, 0xff, 0x67, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x2f, 0xf8, 0x7f, 0xf4, 0x0,
    0x0, 0x0, 0x1, 0xff, 0x97, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x1f, 0xfa, 0x7f, 0xf3, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xa7, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x1f, 0xfa, 0x7f, 0xf3, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xa7, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x1f, 0xfa, 0x7f, 0xf3, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xa7, 0xff, 0x30, 0x0, 0x0, 0x0, 0x1f,
    0xfa, 0x7f, 0xf3, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xa0,

    /* U+006F "o" */
    0x0, 0x0, 0x4a, 0xdf, 0xed, 0x82, 0x0, 0x0,
    0x0, 0x1b, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x1, 0xef, 0xfe, 0x96, 0x6a, 0xff, 0xfb, 0x0,
    0xb, 0xff, 0xa0, 0x0, 0x0, 0x1c, 0xff, 0x70,
    0x3f, 0xfc, 0x0, 0x0, 0x0, 0x1, 0xef, 0xe0,
    0x8f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf5,
    0xbf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf7,
    0xcf, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf9,
    0xbf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf7,
    0x8f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf5,
    0x3f, 0xfc, 0x0, 0x0, 0x0, 0x1, 0xef, 0xe0,
    0xb, 0xff, 0xa0, 0x0, 0x0, 0x1c, 0xff, 0x70,
    0x1, 0xdf, 0xfe, 0x96, 0x6a, 0xff, 0xfb, 0x0,
    0x0, 0x1b, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x4a, 0xdf, 0xed, 0x92, 0x0, 0x0,

    /* U+0070 "p" */
    0x7f, 0xf2, 0x6, 0xbe, 0xfd, 0xb5, 0x0, 0x0,
    0x7f, 0xf4, 0xdf, 0xff, 0xff, 0xff, 0xd2, 0x0,
    0x7f, 0xfe, 0xfe, 0x84, 0x46, 0xcf, 0xff, 0x20,
    0x7f, 0xff, 0xc1, 0x0, 0x0, 0x7, 0xff, 0xd0,
    0x7f, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x9f, 0xf5,
    0x7f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xfa,
    0x7f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xe, 0xfc,
    0x7f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfe,
    0x7f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0xe, 0xfc,
    0x7f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xfa,
    0x7f, 0xff, 0x20, 0x0, 0x0, 0x0, 0xbf, 0xf5,
    0x7f, 0xff, 0xd2, 0x0, 0x0, 0x9, 0xff, 0xd0,
    0x7f, 0xfe, 0xff, 0xa7, 0x68, 0xef, 0xff, 0x20,
    0x7f, 0xf4, 0xcf, 0xff, 0xff, 0xff, 0xd2, 0x0,
    0x7f, 0xf3, 0x5, 0xbe, 0xfe, 0xb5, 0x0, 0x0,
    0x7f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x0, 0x5a, 0xdf, 0xec, 0x60, 0xf, 0xf8,
    0x0, 0x2c, 0xff, 0xff, 0xff, 0xfd, 0x2f, 0xf8,
    0x1, 0xef, 0xfe, 0x96, 0x6a, 0xff, 0xef, 0xf8,
    0xc, 0xff, 0xa0, 0x0, 0x0, 0x2c, 0xff, 0xf8,
    0x3f, 0xfc, 0x0, 0x0, 0x0, 0x1, 0xef, 0xf8,
    0x9f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf8,
    0xbf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf8,
    0xcf, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf8,
    0xbf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf8,
    0x9f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf8,
    0x3f, 0xfc, 0x0, 0x0, 0x0, 0x1, 0xef, 0xf8,
    0xc, 0xff, 0xa0, 0x0, 0x0, 0x1c, 0xff, 0xf8,
    0x1, 0xef, 0xfe, 0x96, 0x6a, 0xff, 0xef, 0xf8,
    0x0, 0x2c, 0xff, 0xff, 0xff, 0xfc, 0x3f, 0xf8,
    0x0, 0x0, 0x5b, 0xdf, 0xec, 0x60, 0x2f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf8,

    /* U+0072 "r" */
    0x7f, 0xf2, 0x6, 0xce, 0x87, 0xff, 0x3d, 0xff,
    0xf8, 0x7f, 0xfd, 0xff, 0xc9, 0x57, 0xff, 0xfd,
    0x20, 0x0, 0x7f, 0xff, 0x10, 0x0, 0x7, 0xff,
    0x80, 0x0, 0x0, 0x7f, 0xf5, 0x0, 0x0, 0x7,
    0xff, 0x30, 0x0, 0x0, 0x7f, 0xf3, 0x0, 0x0,
    0x7, 0xff, 0x30, 0x0, 0x0, 0x7f, 0xf3, 0x0,
    0x0, 0x7, 0xff, 0x30, 0x0, 0x0, 0x7f, 0xf3,
    0x0, 0x0, 0x7, 0xff, 0x30, 0x0, 0x0, 0x7f,
    0xf3, 0x0, 0x0, 0x0,

    /* U+0073 "s" */
    0x0, 0x2, 0x9d, 0xff, 0xec, 0x83, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0x80, 0x5, 0xff,
    0xe8, 0x65, 0x69, 0xef, 0x20, 0xb, 0xff, 0x20,
    0x0, 0x0, 0x3, 0x0, 0xd, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xfe, 0xb8, 0x51, 0x0,
    0x0, 0x0, 0x4d, 0xff, 0xff, 0xff, 0xd5, 0x0,
    0x0, 0x0, 0x36, 0x9c, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xef, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xf2, 0x5, 0x70, 0x0,
    0x0, 0x0, 0xbf, 0xf1, 0xe, 0xff, 0xa7, 0x55,
    0x7c, 0xff, 0xb0, 0x1c, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x10, 0x0, 0x38, 0xce, 0xff, 0xda, 0x50,
    0x0,

    /* U+0074 "t" */
    0x0, 0x8, 0x85, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x1f, 0xfa, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xa0, 0x0, 0x0, 0x9f, 0xff,
    0xff, 0xff, 0xfe, 0x9, 0xff, 0xff, 0xff, 0xff,
    0xe0, 0x23, 0x4f, 0xfb, 0x33, 0x33, 0x0, 0x1,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x1f, 0xfa, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x1f, 0xfa, 0x0, 0x0, 0x0, 0x1, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x1f, 0xfa, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xa0, 0x0, 0x0, 0x0, 0xf, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xb5, 0x5a, 0x40, 0x0, 0x2e,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0x19, 0xef, 0xea,
    0x30,

    /* U+0075 "u" */
    0x9f, 0xf1, 0x0, 0x0, 0x0, 0x5, 0xff, 0x69,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x5f, 0xf6, 0x9f,
    0xf1, 0x0, 0x0, 0x0, 0x5, 0xff, 0x69, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x5f, 0xf6, 0x9f, 0xf1,
    0x0, 0x0, 0x0, 0x5, 0xff, 0x69, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x5f, 0xf6, 0x9f, 0xf1, 0x0,
    0x0, 0x0, 0x5, 0xff, 0x69, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x5f, 0xf6, 0x9f, 0xf1, 0x0, 0x0,
    0x0, 0x5, 0xff, 0x68, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x8f, 0xf6, 0x5f, 0xf7, 0x0, 0x0, 0x0,
    0xe, 0xff, 0x61, 0xff, 0xe2, 0x0, 0x0, 0xa,
    0xff, 0xf6, 0x8, 0xff, 0xe8, 0x44, 0x6d, 0xff,
    0xff, 0x60, 0xa, 0xff, 0xff, 0xff, 0xfe, 0x5f,
    0xf6, 0x0, 0x4, 0xae, 0xfe, 0xc7, 0x2, 0xff,
    0x60,

    /* U+0076 "v" */
    0xd, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0x70, 0x6f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xf1, 0x0, 0xef, 0xc0, 0x0, 0x0, 0x0, 0x1f,
    0xf9, 0x0, 0x8, 0xff, 0x30, 0x0, 0x0, 0x7,
    0xff, 0x20, 0x0, 0x2f, 0xfa, 0x0, 0x0, 0x0,
    0xef, 0xb0, 0x0, 0x0, 0xaf, 0xf1, 0x0, 0x0,
    0x5f, 0xf4, 0x0, 0x0, 0x3, 0xff, 0x80, 0x0,
    0xc, 0xfd, 0x0, 0x0, 0x0, 0xd, 0xfe, 0x0,
    0x3, 0xff, 0x60, 0x0, 0x0, 0x0, 0x6f, 0xf5,
    0x0, 0xaf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xc0, 0x1f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0x37, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xfa, 0xef, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0x60, 0x0, 0x0, 0x0,

    /* U+0077 "w" */
    0xaf, 0xe0, 0x0, 0x0, 0x0, 0xa, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xd4, 0xff, 0x40, 0x0,
    0x0, 0x1, 0xff, 0xf5, 0x0, 0x0, 0x0, 0xe,
    0xf7, 0xe, 0xf9, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0xb0, 0x0, 0x0, 0x5, 0xff, 0x10, 0x8f, 0xf0,
    0x0, 0x0, 0xc, 0xff, 0xff, 0x10, 0x0, 0x0,
    0xbf, 0xb0, 0x2, 0xff, 0x50, 0x0, 0x2, 0xff,
    0x5f, 0xf6, 0x0, 0x0, 0x1f, 0xf5, 0x0, 0xc,
    0xfb, 0x0, 0x0, 0x8f, 0xd0, 0xbf, 0xc0, 0x0,
    0x6, 0xfe, 0x0, 0x0, 0x7f, 0xf1, 0x0, 0xe,
    0xf7, 0x5, 0xff, 0x20, 0x0, 0xcf, 0x90, 0x0,
    0x1, 0xff, 0x60, 0x4, 0xff, 0x10, 0xe, 0xf8,
    0x0, 0x2f, 0xf3, 0x0, 0x0, 0xb, 0xfc, 0x0,
    0xaf, 0xb0, 0x0, 0x9f, 0xe0, 0x8, 0xfd, 0x0,
    0x0, 0x0, 0x5f, 0xf2, 0x1f, 0xf5, 0x0, 0x3,
    0xff, 0x40, 0xef, 0x70, 0x0, 0x0, 0x0, 0xef,
    0x86, 0xff, 0x0, 0x0, 0xc, 0xf9, 0x4f, 0xf1,
    0x0, 0x0, 0x0, 0x9, 0xfd, 0xcf, 0x90, 0x0,
    0x0, 0x6f, 0xfa, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xf3, 0x0, 0x0, 0x1, 0xff, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xfd, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0x70, 0x0, 0x0, 0x0, 0x4f,
    0xfa, 0x0, 0x0, 0x0,

    /* U+0078 "x" */
    0x1e, 0xfe, 0x10, 0x0, 0x0, 0x9, 0xff, 0x50,
    0x3, 0xff, 0xc0, 0x0, 0x0, 0x5f, 0xf9, 0x0,
    0x0, 0x6f, 0xf8, 0x0, 0x2, 0xef, 0xc0, 0x0,
    0x0, 0xa, 0xff, 0x50, 0xc, 0xff, 0x20, 0x0,
    0x0, 0x0, 0xdf, 0xe2, 0x9f, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xfe, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xfe, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xfa, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xc0, 0x5f, 0xf9, 0x0, 0x0,
    0x0, 0x1d, 0xfe, 0x10, 0x9, 0xff, 0x50, 0x0,
    0x0, 0xbf, 0xf4, 0x0, 0x0, 0xdf, 0xf2, 0x0,
    0x7, 0xff, 0x80, 0x0, 0x0, 0x2f, 0xfd, 0x0,
    0x3f, 0xfc, 0x0, 0x0, 0x0, 0x5, 0xff, 0xa0,

    /* U+0079 "y" */
    0xd, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0x70, 0x6f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xf0, 0x0, 0xef, 0xd0, 0x0, 0x0, 0x0, 0x1f,
    0xf8, 0x0, 0x7, 0xff, 0x50, 0x0, 0x0, 0x8,
    0xff, 0x10, 0x0, 0x1f, 0xfc, 0x0, 0x0, 0x0,
    0xef, 0xa0, 0x0, 0x0, 0x9f, 0xf3, 0x0, 0x0,
    0x6f, 0xf3, 0x0, 0x0, 0x2, 0xff, 0xa0, 0x0,
    0xd, 0xfc, 0x0, 0x0, 0x0, 0xa, 0xff, 0x10,
    0x4, 0xff, 0x40, 0x0, 0x0, 0x0, 0x3f, 0xf8,
    0x0, 0xbf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xf0, 0x2f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0x79, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xfe, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x1, 0x0, 0x0, 0x9f, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0xeb, 0x65, 0xaf, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xfd, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x6c, 0xff, 0xd7, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+007A "z" */
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5b, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf4, 0x23, 0x33, 0x33,
    0x33, 0x8f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xfd, 0x10, 0x0, 0x0, 0x0, 0x1d, 0xff, 0x30,
    0x0, 0x0, 0x0, 0xb, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x8, 0xff, 0x80, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xe1,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xfe,
    0x33, 0x33, 0x33, 0x33, 0x1d, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80,

    /* U+007B "{" */
    0x0, 0x0, 0x6c, 0xff, 0x40, 0x0, 0x7f, 0xff,
    0xf4, 0x0, 0xe, 0xff, 0x73, 0x10, 0x1, 0xff,
    0xb0, 0x0, 0x0, 0x2f, 0xf8, 0x0, 0x0, 0x2,
    0xff, 0x80, 0x0, 0x0, 0x2f, 0xf8, 0x0, 0x0,
    0x2, 0xff, 0x80, 0x0, 0x0, 0x2f, 0xf8, 0x0,
    0x0, 0x2, 0xff, 0x80, 0x0, 0x0, 0x2f, 0xf8,
    0x0, 0x1, 0x3a, 0xff, 0x50, 0x0, 0x6f, 0xff,
    0x90, 0x0, 0x6, 0xff, 0xfd, 0x10, 0x0, 0x0,
    0x7f, 0xf6, 0x0, 0x0, 0x2, 0xff, 0x80, 0x0,
    0x0, 0x2f, 0xf8, 0x0, 0x0, 0x2, 0xff, 0x80,
    0x0, 0x0, 0x2f, 0xf8, 0x0, 0x0, 0x2, 0xff,
    0x80, 0x0, 0x0, 0x2f, 0xf8, 0x0, 0x0, 0x2,
    0xff, 0x80, 0x0, 0x0, 0x1f, 0xfb, 0x0, 0x0,
    0x0, 0xef, 0xf8, 0x31, 0x0, 0x6, 0xff, 0xff,
    0x40, 0x0, 0x5, 0xcf, 0xf4,

    /* U+007C "|" */
    0x1f, 0xf7, 0x1f, 0xf7, 0x1f, 0xf7, 0x1f, 0xf7,
    0x1f, 0xf7, 0x1f, 0xf7, 0x1f, 0xf7, 0x1f, 0xf7,
    0x1f, 0xf7, 0x1f, 0xf7, 0x1f, 0xf7, 0x1f, 0xf7,
    0x1f, 0xf7, 0x1f, 0xf7, 0x1f, 0xf7, 0x1f, 0xf7,
    0x1f, 0xf7, 0x1f, 0xf7, 0x1f, 0xf7, 0x1f, 0xf7,
    0x1f, 0xf7, 0x1f, 0xf7, 0x1f, 0xf7, 0x1f, 0xf7,
    0x1f, 0xf7, 0x1f, 0xf7,

    /* U+007D "}" */
    0x7f, 0xfb, 0x40, 0x0, 0x7, 0xff, 0xff, 0x40,
    0x0, 0x14, 0x9f, 0xfc, 0x0, 0x0, 0x0, 0xdf,
    0xf0, 0x0, 0x0, 0xa, 0xff, 0x0, 0x0, 0x0,
    0xaf, 0xf0, 0x0, 0x0, 0xa, 0xff, 0x0, 0x0,
    0x0, 0xaf, 0xf0, 0x0, 0x0, 0xa, 0xff, 0x0,
    0x0, 0x0, 0xaf, 0xf0, 0x0, 0x0, 0xa, 0xff,
    0x0, 0x0, 0x0, 0x8f, 0xf8, 0x30, 0x0, 0x1,
    0xbf, 0xff, 0x30, 0x0, 0x2e, 0xff, 0xf3, 0x0,
    0x9, 0xff, 0x50, 0x0, 0x0, 0xaf, 0xf0, 0x0,
    0x0, 0xa, 0xff, 0x0, 0x0, 0x0, 0xaf, 0xf0,
    0x0, 0x0, 0xa, 0xff, 0x0, 0x0, 0x0, 0xaf,
    0xf0, 0x0, 0x0, 0xa, 0xff, 0x0, 0x0, 0x0,
    0xaf, 0xf0, 0x0, 0x0, 0xd, 0xff, 0x0, 0x1,
    0x49, 0xff, 0xc0, 0x0, 0x7f, 0xff, 0xf4, 0x0,
    0x7, 0xff, 0xb4, 0x0, 0x0,

    /* U+007E "~" */
    0x0, 0x7e, 0xfc, 0x30, 0x0, 0x2, 0xfa, 0x7,
    0xff, 0xff, 0xf6, 0x0, 0x5, 0xf8, 0xe, 0xf6,
    0x27, 0xff, 0x80, 0x1d, 0xf4, 0x3f, 0xb0, 0x0,
    0x4e, 0xff, 0xff, 0xb0, 0x5f, 0x70, 0x0, 0x1,
    0xaf, 0xfa, 0x10,

    /* U+00B0 "°" */
    0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x7e, 0xff,
    0xc4, 0x0, 0xa, 0xfa, 0x67, 0xdf, 0x50, 0x4f,
    0x80, 0x0, 0xc, 0xf0, 0x9f, 0x0, 0x0, 0x4,
    0xf5, 0xbd, 0x0, 0x0, 0x2, 0xf6, 0x9f, 0x10,
    0x0, 0x5, 0xf4, 0x3f, 0xa0, 0x0, 0x1e, 0xe0,
    0x8, 0xfd, 0x9a, 0xff, 0x40, 0x0, 0x5c, 0xfe,
    0xa2, 0x0,

    /* U+2022 "•" */
    0x0, 0x1, 0x0, 0x4, 0xef, 0xc1, 0xe, 0xff,
    0xfb, 0x1f, 0xff, 0xfd, 0xe, 0xff, 0xfa, 0x3,
    0xcf, 0xb1,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x38, 0xdf, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x15, 0xae, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x7c, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x49, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x16, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe9, 0xbf, 0xff, 0x0, 0x0,
    0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x73, 0x0, 0x8f, 0xff, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xfe, 0xa5, 0x10, 0x0, 0x0,
    0x8f, 0xff, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xd8, 0x30, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xa2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0x0, 0x0,
    0x0, 0xf, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0x0, 0x0, 0x0, 0xf,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0x0, 0x0, 0x0, 0xf, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0x0, 0x0, 0x0, 0xf, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0x0, 0x0,
    0x0, 0xf, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0x0, 0x0, 0x0, 0xf,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x1, 0x7a, 0xcb,
    0xcf, 0xff, 0x0, 0x0, 0x0, 0xf, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x0, 0xf, 0xff, 0x80, 0x0, 0x0,
    0x2, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x1,
    0x33, 0x2f, 0xff, 0x80, 0x0, 0x0, 0x7, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x3, 0xcf, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x5, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x5f, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xf4,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x7, 0xef, 0xff, 0xfb, 0x20, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x32, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0xe4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x27, 0xaa, 0x95, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F008 "" */
    0x9b, 0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb0, 0x0, 0xb9, 0xfe, 0x44,
    0x4e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe4, 0x44, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x88, 0x8f, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xf8, 0x88, 0xff,
    0xfc, 0x0, 0xc, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xb0, 0x0, 0xcf, 0xfc, 0x0,
    0xc, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xb0, 0x0, 0xcf, 0xfd, 0x0, 0xd, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xd0,
    0x0, 0xdf, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xcc, 0xcf, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xfc, 0xcc, 0xff, 0xfc, 0x0,
    0xc, 0xff, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xff, 0xc0, 0x0, 0xcf, 0xfc, 0x0, 0xc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0,
    0x0, 0xcf, 0xfc, 0x0, 0xc, 0xff, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xff, 0xc0, 0x0, 0xcf,
    0xff, 0xcc, 0xcf, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xfc, 0xcc, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xff, 0xff, 0xff, 0xfd, 0x0, 0xd, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xd0,
    0x0, 0xdf, 0xfc, 0x0, 0xc, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xb0, 0x0, 0xcf,
    0xfc, 0x0, 0xc, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xb0, 0x0, 0xcf, 0xff, 0x88,
    0x8f, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xf8, 0x88, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x44, 0x4e, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe4, 0x44, 0xef,
    0xab, 0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb0, 0x0, 0xb9,

    /* U+F00B "" */
    0x9f, 0xff, 0xff, 0xfb, 0x0, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xff, 0xff,
    0xff, 0xff, 0x21, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x21, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x21, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x21, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x21, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xaf, 0xff, 0xff, 0xfc,
    0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xff, 0xfb, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf9, 0xff, 0xff, 0xff, 0xff,
    0x21, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x21, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x21, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x21, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x21, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xaf, 0xff, 0xff, 0xfc, 0x0, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xfc,
    0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0xff, 0xff, 0xff, 0xff, 0x21, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x21, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x21, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x21, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x21, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x9f, 0xff, 0xff, 0xfb, 0x0, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf9,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xe9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x8e, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0x80, 0x0,
    0xa, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x9f, 0xff,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x0, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0x60, 0x0,
    0x6f, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xf6, 0x6, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0xff, 0xff, 0xaf, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xe7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F00D "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x10, 0x0, 0x6, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x2d, 0xfa, 0x0, 0x6f, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0x2, 0xef, 0xff, 0xa0, 0xef, 0xff,
    0xff, 0x50, 0x0, 0x0, 0x2e, 0xff, 0xff, 0xf2,
    0xcf, 0xff, 0xff, 0xf5, 0x0, 0x2, 0xef, 0xff,
    0xff, 0xf1, 0x2e, 0xff, 0xff, 0xff, 0x50, 0x2e,
    0xff, 0xff, 0xff, 0x50, 0x2, 0xef, 0xff, 0xff,
    0xf7, 0xef, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x2e,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x2, 0xef, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xff, 0xff,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2e, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x2, 0xef, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x2e, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x50, 0x0, 0x2, 0xef, 0xff, 0xff,
    0xf6, 0xef, 0xff, 0xff, 0xf5, 0x0, 0x2e, 0xff,
    0xff, 0xff, 0x40, 0x2e, 0xff, 0xff, 0xff, 0x50,
    0xcf, 0xff, 0xff, 0xf4, 0x0, 0x2, 0xef, 0xff,
    0xff, 0xf1, 0xef, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x2e, 0xff, 0xff, 0xf2, 0x6f, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x2, 0xef, 0xff, 0xa0, 0x6, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x10, 0x0,

    /* U+F011 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x69, 0x96,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xa8, 0x0, 0x3,
    0xff, 0xff, 0x30, 0x0, 0x8a, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x3e, 0xff, 0x60, 0x3, 0xff, 0xff,
    0x30, 0x6, 0xff, 0xe3, 0x0, 0x0, 0x0, 0x2,
    0xef, 0xff, 0xe0, 0x3, 0xff, 0xff, 0x30, 0xe,
    0xff, 0xff, 0x30, 0x0, 0x0, 0xd, 0xff, 0xff,
    0xe0, 0x3, 0xff, 0xff, 0x30, 0xe, 0xff, 0xff,
    0xd1, 0x0, 0x0, 0x9f, 0xff, 0xfe, 0x30, 0x3,
    0xff, 0xff, 0x30, 0x3, 0xef, 0xff, 0xf9, 0x0,
    0x2, 0xff, 0xff, 0xe2, 0x0, 0x3, 0xff, 0xff,
    0x30, 0x0, 0x2e, 0xff, 0xff, 0x20, 0x9, 0xff,
    0xff, 0x50, 0x0, 0x3, 0xff, 0xff, 0x30, 0x0,
    0x5, 0xff, 0xff, 0x90, 0xe, 0xff, 0xfc, 0x0,
    0x0, 0x3, 0xff, 0xff, 0x30, 0x0, 0x0, 0xcf,
    0xff, 0xe0, 0x3f, 0xff, 0xf5, 0x0, 0x0, 0x3,
    0xff, 0xff, 0x30, 0x0, 0x0, 0x6f, 0xff, 0xf3,
    0x6f, 0xff, 0xf0, 0x0, 0x0, 0x3, 0xff, 0xff,
    0x30, 0x0, 0x0, 0xf, 0xff, 0xf6, 0x7f, 0xff,
    0xe0, 0x0, 0x0, 0x3, 0xff, 0xff, 0x30, 0x0,
    0x0, 0xe, 0xff, 0xf8, 0x8f, 0xff, 0xd0, 0x0,
    0x0, 0x2, 0xff, 0xff, 0x20, 0x0, 0x0, 0xd,
    0xff, 0xf8, 0x8f, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0xef, 0xfe, 0x0, 0x0, 0x0, 0xe, 0xff, 0xf7,
    0x6f, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x2, 0x20,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xf6, 0x3f, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0xf3, 0xe, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xe0, 0x9, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0x90,
    0x2, 0xff, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2e, 0xff, 0xff, 0x20, 0x0, 0x9f,
    0xff, 0xfe, 0x30, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xef, 0xff, 0xf9, 0x0, 0x0, 0x1e, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xd1, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xfa,
    0x75, 0x57, 0xaf, 0xff, 0xff, 0xfe, 0x20, 0x0,
    0x0, 0x0, 0x3e, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe3, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x9d,
    0xff, 0xfe, 0xd9, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2c, 0xff, 0xff, 0xd6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xc1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xc8, 0x0, 0x5d,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x6d, 0x40,
    0x0, 0x0, 0xcf, 0xfe, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0xcf, 0xfe, 0x20, 0x0, 0x7f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x0, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xe7, 0x32, 0x5d, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x13, 0xcf, 0xff, 0xff,
    0xff, 0xe1, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff,
    0xfe, 0x60, 0x0, 0x7f, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0xe, 0xff, 0xff, 0xfb, 0x10, 0x0,
    0x4, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xff, 0x90, 0x0, 0x0, 0x5f, 0xff,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xfa, 0x0, 0x0, 0x4, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0xe, 0xff, 0xff, 0xfb, 0x10, 0x3, 0xcf, 0xff,
    0xff, 0xff, 0xe1, 0x0, 0x0, 0xb, 0xff, 0xff,
    0xff, 0xfe, 0x60, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xe7, 0x32, 0x5d, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x17, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x1e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0x0, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x0, 0x0, 0xcf, 0xfe, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0xcf, 0xfe, 0x10, 0x0, 0x1,
    0xc8, 0x0, 0x5d, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x5d, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2c, 0xff, 0xff, 0xd6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x37,
    0x60, 0x0, 0x0, 0x48, 0x88, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xfd, 0x10, 0x0, 0xaf, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xe3, 0x0, 0xbf, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xff, 0xff,
    0xff, 0xff, 0x60, 0xbf, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xef, 0xff, 0xf8,
    0xcf, 0xff, 0xf9, 0xbf, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xfe, 0x30,
    0xa, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xd2, 0x3,
    0x10, 0x7f, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xbf, 0xff, 0xfa, 0x0, 0x8f,
    0xd2, 0x4, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x2d, 0xff, 0xff, 0x80, 0xb, 0xff,
    0xff, 0x40, 0x2d, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xf5, 0x2, 0xdf, 0xff,
    0xff, 0xf7, 0x1, 0xbf, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xfe, 0x30, 0x3e, 0xff, 0xff,
    0xff, 0xff, 0xa0, 0x9, 0xff, 0xff, 0xc1, 0x0,
    0xa, 0xff, 0xff, 0xc1, 0x6, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x10, 0x6f, 0xff, 0xfe, 0x30,
    0xbf, 0xff, 0xfa, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe3, 0x4, 0xef, 0xff, 0xf4,
    0xbf, 0xff, 0x70, 0x1b, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x50, 0x2d, 0xff, 0xf3,
    0xd, 0xf4, 0x2, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0xbf, 0x60,
    0x1, 0x20, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0x3, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0xcf, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xf2, 0x0,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xf2, 0x0,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xf2, 0x0,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xf2, 0x0,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xf2, 0x0,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xd0, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xfe, 0x10, 0x0, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x55, 0x55,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1c, 0xdd, 0xdd, 0xef,
    0xff, 0xff, 0xfe, 0xdd, 0xdd, 0xc1, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x11, 0x11, 0x11, 0x10, 0x8,
    0xff, 0xff, 0x80, 0x1, 0x11, 0x11, 0x11, 0x10,
    0xbf, 0xff, 0xff, 0xff, 0xfd, 0x10, 0x8f, 0xf8,
    0x1, 0xdf, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe2, 0x6, 0x60, 0x2d, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x30, 0x3, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0x1e, 0xb0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x2e, 0xc1,
    0x8f, 0xff, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x37, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x73,

    /* U+F01C "" */
    0x0, 0x0, 0x0, 0x2b, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xd8, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x9f, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x1, 0xef, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xef, 0xff, 0x30, 0x0,
    0x0, 0x5f, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xd0, 0x0,
    0x1, 0xef, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xf8, 0x0,
    0xa, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xef, 0xff, 0x20,
    0x5f, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xd0,
    0xdf, 0xff, 0xb8, 0x88, 0x88, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x48, 0x88, 0x88, 0x8e, 0xff, 0xf5,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0x1b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x50,

    /* U+F021 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x58, 0x86, 0x0, 0x0,
    0x0, 0x0, 0x4, 0x9d, 0xef, 0xfe, 0xb7, 0x20,
    0x0, 0x0, 0xdf, 0xff, 0x0, 0x0, 0x0, 0x6,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x30, 0x0,
    0xdf, 0xff, 0x0, 0x0, 0x1, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0x10, 0xcf, 0xff,
    0x0, 0x0, 0x2e, 0xff, 0xff, 0xff, 0xfe, 0xef,
    0xff, 0xff, 0xff, 0xe3, 0xbf, 0xff, 0x0, 0x2,
    0xef, 0xff, 0xff, 0x94, 0x0, 0x0, 0x4b, 0xff,
    0xff, 0xfe, 0xdf, 0xff, 0x0, 0xd, 0xff, 0xff,
    0xc2, 0x0, 0x0, 0x0, 0x0, 0x3c, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x9f, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0x2, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x11, 0x0, 0x8, 0xff, 0xff, 0xff, 0x9, 0xff,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff,
    0xee, 0xff, 0xff, 0xff, 0xe, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x3f, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x2b, 0xcc, 0x60, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xbc, 0xcc, 0xcc, 0xcc, 0xcc, 0xca, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xac, 0xcc, 0xcc, 0xcc, 0xcc, 0xcb, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xbc, 0xb2, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xe0, 0xff, 0xff, 0xff, 0xee, 0xff, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0x90,
    0xff, 0xff, 0xff, 0x80, 0x0, 0x11, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0x20, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xf9, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xb2, 0x0, 0x0, 0x0, 0x0, 0x2c, 0xff, 0xff,
    0xd0, 0x0, 0xff, 0xfd, 0xef, 0xff, 0xff, 0xa4,
    0x0, 0x0, 0x4a, 0xff, 0xff, 0xfe, 0x20, 0x0,
    0xff, 0xfb, 0x3e, 0xff, 0xff, 0xff, 0xfe, 0xef,
    0xff, 0xff, 0xff, 0xe2, 0x0, 0x0, 0xff, 0xfc,
    0x1, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x10, 0x0, 0x0, 0xff, 0xfd, 0x0, 0x3,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x60, 0x0,
    0x0, 0x0, 0xff, 0xfd, 0x0, 0x0, 0x2, 0x7c,
    0xff, 0xff, 0xd9, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x68, 0x85, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xb6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xff, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xff, 0xff, 0x38, 0x88, 0x88, 0x8f, 0xff, 0xff,
    0xff, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x10,

    /* U+F027 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3b, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3e, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x38,
    0x88, 0x88, 0x8f, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x0, 0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x1, 0xb9, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x6f, 0xfc, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x1, 0xcf,
    0xf7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0xef, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0xa, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0xdf,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0xbf, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x6f, 0xfd, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x2, 0xdb, 0x10,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0,
    0x0, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x18, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xc1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xb6, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xdf, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x0,
    0x0, 0x0, 0x21, 0x0, 0x1c, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0x0,
    0x0, 0x4, 0xfe, 0x50, 0x1, 0xef, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0x0,
    0x0, 0x5, 0xff, 0xf6, 0x0, 0x4f, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0x40, 0xa, 0xff, 0x40,
    0x38, 0x88, 0x88, 0x8f, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xe0, 0x2, 0xff, 0xa0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x1c, 0xa1, 0x0, 0x8f, 0xf6, 0x0, 0xcf, 0xf0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x6f, 0xfd, 0x0, 0x1f, 0xfc, 0x0, 0x8f, 0xf2,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x1c, 0xff, 0x80, 0xa, 0xff, 0x0, 0x4f, 0xf5,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x0, 0xdf, 0xd0, 0x7, 0xff, 0x20, 0x3f, 0xf7,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x0, 0xaf, 0xf0, 0x6, 0xff, 0x30, 0x2f, 0xf7,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x0, 0xdf, 0xd0, 0x7, 0xff, 0x20, 0x3f, 0xf7,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x1c, 0xff, 0x80, 0xa, 0xff, 0x0, 0x4f, 0xf5,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x6f, 0xfd, 0x0, 0x1f, 0xfc, 0x0, 0x8f, 0xf2,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x1c, 0xa1, 0x0, 0x9f, 0xf6, 0x0, 0xdf, 0xe0,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xe0, 0x3, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0x40, 0xa, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0x0,
    0x0, 0x5, 0xff, 0xf6, 0x0, 0x4f, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0x0,
    0x0, 0x4, 0xfe, 0x50, 0x1, 0xef, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0x0,
    0x0, 0x0, 0x21, 0x0, 0x1d, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xdf, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xc1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x18, 0x50, 0x0, 0x0, 0x0,

    /* U+F03E "" */
    0x1b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb1, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x93, 0x27, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0x0, 0x0, 0xe, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0, 0x0,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xd8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0xff, 0xfc, 0x10, 0x6f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x50, 0x3, 0xdf, 0xff, 0xff, 0xff,
    0xc1, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0xef, 0xff, 0xff, 0xff, 0xfc, 0x10, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0xff, 0xc1, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc1, 0x3e, 0xff,
    0xfc, 0x10, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xff, 0xff, 0xfc, 0x10, 0x3, 0xef, 0xc1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xc1, 0x0, 0x0, 0x3b, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0xff, 0xfd, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xfc,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x1b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb1,

    /* U+F043 "" */
    0x0, 0x0, 0x0, 0x0, 0x39, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf3, 0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x0, 0xe, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe0, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0xff, 0xfa, 0xd, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xef, 0xf8,
    0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0xdf, 0xfc, 0x6, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x9f, 0xff, 0x20, 0xcf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0x4f, 0xff, 0xb0, 0x9,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0xc, 0xff,
    0xfa, 0x0, 0x2, 0xcf, 0xff, 0xff, 0xfe, 0x10,
    0x2, 0xff, 0xff, 0xd6, 0x10, 0xbf, 0xff, 0xff,
    0xf5, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x4, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x18, 0xff, 0xff, 0xff, 0xfa, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0x67, 0x64, 0x0, 0x0,
    0x0, 0x0,

    /* U+F048 "" */
    0x48, 0x88, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0x70, 0xbf, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xf9, 0xbf, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x1c, 0xff, 0xfc, 0xbf, 0xff, 0x30, 0x0, 0x0,
    0x1, 0xdf, 0xff, 0xfc, 0xbf, 0xff, 0x30, 0x0,
    0x0, 0x2e, 0xff, 0xff, 0xfc, 0xbf, 0xff, 0x30,
    0x0, 0x3, 0xef, 0xff, 0xff, 0xfc, 0xbf, 0xff,
    0x30, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xfc, 0xbf,
    0xff, 0x30, 0x5, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xbf, 0xff, 0x30, 0x6f, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0xbf, 0xff, 0x37, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0xbf, 0xff, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xbf,
    0xff, 0x5d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xbf, 0xff, 0x31, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0xbf, 0xff, 0x30, 0xb, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0xbf, 0xff, 0x30, 0x0, 0xaf, 0xff,
    0xff, 0xff, 0xfc, 0xbf, 0xff, 0x30, 0x0, 0x9,
    0xff, 0xff, 0xff, 0xfc, 0xbf, 0xff, 0x30, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xfc, 0xbf, 0xff, 0x30,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xfc, 0xbf, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xfc, 0xbf,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x4, 0xff, 0xfb,
    0x9f, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x3d,
    0xd3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F04B "" */
    0x3, 0x75, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xfc, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xff, 0xff, 0x91, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xe6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xa1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x40,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb2, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x50,
    0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb2, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb2,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x50, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb2, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x40, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xe6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0xff,
    0x91, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xfc, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x74,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F04C "" */
    0x1a, 0xef, 0xff, 0xff, 0xd5, 0x0, 0x0, 0x1a,
    0xef, 0xff, 0xff, 0xd5, 0xa, 0xff, 0xff, 0xff,
    0xff, 0xf2, 0x0, 0xa, 0xff, 0xff, 0xff, 0xff,
    0xf2, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0xef, 0xff, 0xff, 0xff, 0xff,
    0x60, 0x0, 0xef, 0xff, 0xff, 0xff, 0xff, 0x66,
    0xff, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x6, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0x4, 0x89, 0x99, 0x98,
    0x71, 0x0, 0x0, 0x4, 0x89, 0x99, 0x98, 0x71,
    0x0,

    /* U+F04D "" */
    0x3, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x60, 0x6, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xd0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x6f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7a,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0x1a, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd5,
    0x0,

    /* U+F051 "" */
    0x5, 0x81, 0x0, 0x0, 0x0, 0x0, 0x0, 0x68,
    0x87, 0x5f, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xff, 0x7f, 0xff, 0xe3, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0x8f, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x8f, 0xff, 0xff,
    0xff, 0x60, 0x0, 0x0, 0xff, 0xff, 0x8f, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0xff, 0xff, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0xff, 0xff,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0xff,
    0xff, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0,
    0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe3, 0xff, 0xff,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x20, 0xff,
    0xff, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xd2, 0x0,
    0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xfd, 0x10,
    0x0, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xc1,
    0x0, 0x0, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x8f, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0xff, 0xff, 0x8f, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0x7f,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0x1c, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F052 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x9a, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1e, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xdf,
    0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe1, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x10, 0x0, 0x0, 0x5, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xc0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x3,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xa0, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0xa, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf3, 0x1, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd1, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0xe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x5, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb0,

    /* U+F053 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x93, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xd1, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xff, 0xfd, 0x10, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0xff, 0xd1, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xfd, 0x10, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xff, 0xd1, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xff, 0xfd, 0x10, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0x0,

    /* U+F054 "" */
    0x0, 0x78, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x1e, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x1, 0xcf, 0xff, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x1c, 0xff, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0x1, 0xcf, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x1c, 0xff, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x1, 0xcf, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x1c, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0x1, 0xdf, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x1d, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x11, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5a, 0xa8, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x20, 0x23, 0x33, 0x33, 0x33,
    0x8f, 0xff, 0xfe, 0x33, 0x33, 0x33, 0x33, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8d, 0xdc, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F068 "" */
    0x1, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x10, 0x8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x77, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe1, 0x1, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x10, 0x0,

    /* U+F06E "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x26, 0xad, 0xef,
    0xfe, 0xc9, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xa2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3c, 0xff, 0xff, 0xff, 0xba,
    0xbd, 0xff, 0xff, 0xff, 0x91, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xfd, 0x50, 0x0,
    0x0, 0x18, 0xff, 0xff, 0xfe, 0x40, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x3e, 0xff, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x39,
    0x95, 0x0, 0x2, 0xef, 0xff, 0xff, 0x70, 0x0,
    0x0, 0xaf, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x3f,
    0xff, 0xd2, 0x0, 0x5f, 0xff, 0xff, 0xf5, 0x0,
    0x7, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0x3f,
    0xff, 0xfe, 0x10, 0xd, 0xff, 0xff, 0xff, 0x20,
    0x2f, 0xff, 0xff, 0xff, 0x20, 0x0, 0x0, 0xaf,
    0xff, 0xff, 0x80, 0x8, 0xff, 0xff, 0xff, 0xc0,
    0xbf, 0xff, 0xff, 0xff, 0x0, 0x48, 0x6b, 0xff,
    0xff, 0xff, 0xd0, 0x5, 0xff, 0xff, 0xff, 0xf5,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x4, 0xff, 0xff, 0xff, 0xf9,
    0xbf, 0xff, 0xff, 0xff, 0x0, 0x6f, 0xff, 0xff,
    0xff, 0xff, 0xe0, 0x5, 0xff, 0xff, 0xff, 0xf5,
    0x1f, 0xff, 0xff, 0xff, 0x20, 0x1f, 0xff, 0xff,
    0xff, 0xff, 0x90, 0x8, 0xff, 0xff, 0xff, 0xc0,
    0x6, 0xff, 0xff, 0xff, 0x70, 0x8, 0xff, 0xff,
    0xff, 0xff, 0x20, 0xd, 0xff, 0xff, 0xff, 0x20,
    0x0, 0xaf, 0xff, 0xff, 0xe0, 0x0, 0xaf, 0xff,
    0xff, 0xf4, 0x0, 0x5f, 0xff, 0xff, 0xf5, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xfb, 0x0, 0x4, 0xad,
    0xc8, 0x10, 0x2, 0xef, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x3e, 0xff, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xfd, 0x50, 0x0,
    0x0, 0x18, 0xff, 0xff, 0xfe, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3c, 0xff, 0xff, 0xff, 0xba,
    0xac, 0xff, 0xff, 0xff, 0x91, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xa2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x26, 0xad, 0xff,
    0xfe, 0xc9, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F070 "" */
    0x4, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xff, 0xc1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xe3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x26, 0xad, 0xff, 0xed,
    0xb7, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xdf, 0xff, 0xfa, 0x16, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xba, 0xbe, 0xff, 0xff, 0xfe, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff,
    0xff, 0xc5, 0x0, 0x0, 0x3, 0xbf, 0xff, 0xff,
    0xb1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3e,
    0xff, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1b, 0xff, 0xff, 0xc1, 0x6, 0xba, 0x50,
    0x0, 0x7f, 0xff, 0xff, 0xe2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xe4, 0x7f,
    0xff, 0xc1, 0x0, 0xbf, 0xff, 0xff, 0xd1, 0x0,
    0x0, 0x0, 0xad, 0x20, 0x0, 0x4, 0xff, 0xff,
    0xfc, 0xff, 0xff, 0xc0, 0x3, 0xff, 0xff, 0xff,
    0xb0, 0x0, 0x0, 0x5f, 0xff, 0x50, 0x0, 0x2,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0x50, 0xe, 0xff,
    0xff, 0xff, 0x50, 0x0, 0xe, 0xff, 0xff, 0x90,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0xbf, 0xff, 0xff, 0xfe, 0x0, 0x2, 0xff, 0xff,
    0xff, 0xc2, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff,
    0xb0, 0xa, 0xff, 0xff, 0xff, 0xf2, 0x0, 0xe,
    0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x3e, 0xff,
    0xff, 0xfa, 0x0, 0xcf, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x1b, 0xff, 0xff, 0xd2, 0xe, 0xff, 0xff, 0xff,
    0x50, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0x90, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x4, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x2, 0xef,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x1, 0xcf,
    0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xdf, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xe2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xbf, 0xff, 0xff, 0xc4, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6e, 0xff, 0xff,
    0xfe, 0xba, 0xb5, 0x0, 0x0, 0x2d, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xdf, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x37, 0xbd, 0xef, 0xfd, 0xb4, 0x0,
    0x0, 0x7, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xef, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xcf, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x44, 0x0,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0x87, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xef, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xff, 0xff, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xff, 0x80, 0x0, 0x1e, 0xff,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xff, 0xf6, 0x0, 0x0, 0xef,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0x70, 0x0, 0xf,
    0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xff, 0xff, 0xff, 0x80, 0x0,
    0xf, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0x1, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0x0, 0x2f, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xfb,
    0x0, 0x3, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0xff, 0xff, 0xff,
    0xd1, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x30, 0x0, 0x0, 0x5, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x1, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x40, 0x0, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe0, 0x0, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0x1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x9, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x13, 0xcf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x10, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf7, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x48,
    0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x99, 0x99, 0x99, 0x98, 0x71, 0x0,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xa2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xfe, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xe3, 0x0, 0x24, 0x44, 0x44, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x44, 0x4f, 0xff, 0xfe, 0x30,
    0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xe3, 0xff, 0xff,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0x3, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0x0, 0x0, 0x2e, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x1, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd1,
    0x12, 0x22, 0x26, 0xff, 0xff, 0xa0, 0x1d, 0xff,
    0xff, 0xd2, 0x2f, 0xff, 0xfd, 0x10, 0x0, 0x0,
    0x0, 0x7f, 0xfb, 0x1, 0xdf, 0xff, 0xfd, 0x10,
    0xf, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xc0, 0xc, 0xff, 0xff, 0xe2, 0x0, 0xe, 0xfd,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xfe, 0x20, 0x0, 0x4, 0x81, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x4, 0x81, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xf5, 0x5, 0xe2, 0x0, 0xe, 0xfe,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff,
    0x60, 0x4f, 0xfd, 0x10, 0xf, 0xff, 0xe2, 0x0,
    0x12, 0x22, 0x26, 0xff, 0xff, 0xf7, 0x3, 0xff,
    0xff, 0xd2, 0x2f, 0xff, 0xfe, 0x20, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x1, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe2, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0x0, 0x0, 0x2e, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xe2, 0x24, 0x44,
    0x44, 0x30, 0x0, 0x0, 0x0, 0x0, 0x2, 0x44,
    0x4f, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xe3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfe, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xa2, 0x0, 0x0,

    /* U+F077 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xb2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xe2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xe2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xff, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xfa, 0x3f, 0xff, 0xff, 0xe2, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xfa, 0x0, 0x3f, 0xff,
    0xff, 0xe2, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xfa, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xe2, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xff, 0xe2, 0x0, 0x8, 0xff, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0xe2, 0x6, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0xe0, 0x9f, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0x11, 0xdf, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x60, 0x1,
    0xa8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3b, 0x50, 0x0,

    /* U+F078 "" */
    0x1, 0xa8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3b, 0x60, 0x1, 0xdf, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0x60, 0x9f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0x16, 0xff, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0xd0, 0x8, 0xff, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0xe2, 0x0, 0x8,
    0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0xe2, 0x0, 0x0, 0x8, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0xe2, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xfa, 0x0, 0x3f, 0xff,
    0xff, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xfa, 0x3f, 0xff, 0xff, 0xe2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xe2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xff, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xe2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xa2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F079 "" */
    0x0, 0x0, 0x0, 0x42, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xf4,
    0x0, 0x0, 0x57, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x60, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xff, 0xf4, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0xff, 0xf4, 0x2, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x4, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xff, 0xef, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf9,
    0x0, 0x0, 0xf, 0xff, 0xf4, 0xef, 0xf9, 0x9f,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0x90, 0x0, 0x0, 0x7f, 0xf5, 0xe, 0xff,
    0x90, 0xaf, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xf9, 0x0, 0x0, 0x0, 0x32, 0x0,
    0xef, 0xf9, 0x0, 0x41, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x19, 0x80,
    0xe, 0xff, 0x90, 0x2a, 0x70, 0x0, 0x0, 0xe,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xff, 0xa0, 0xef, 0xf9, 0x2e, 0xff, 0x60, 0x0,
    0x0, 0xef, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xff, 0x9e, 0xff, 0xad, 0xff, 0xf9,
    0x0, 0x0, 0xe, 0xff, 0xc7, 0x77, 0x77, 0x77,
    0x77, 0x73, 0x4, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x10, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x4, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0x10, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x4, 0xff,
    0xff, 0xff, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0x3, 0xff, 0xff, 0xfc, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xfc, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xdb, 0x0, 0x0,
    0x0,

    /* U+F07B "" */
    0x1b, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd8, 0x88, 0x88, 0x88, 0x88, 0x87, 0x30,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x1b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb1,

    /* U+F093 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xe2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xff,
    0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xe2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x22, 0x22, 0xcf,
    0xff, 0xff, 0xf6, 0x22, 0x22, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x11, 0x11, 0x11, 0x0, 0xbf,
    0xff, 0xff, 0xf4, 0x0, 0x11, 0x11, 0x11, 0x10,
    0xbf, 0xff, 0xff, 0xff, 0xc0, 0xaf, 0xff, 0xff,
    0xf3, 0xc, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xff,
    0xff, 0xff, 0xf1, 0x18, 0x99, 0x99, 0x60, 0x1f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x10, 0x0, 0x0, 0x1, 0xcf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xcc, 0xcc, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0x1e, 0xb0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x2e, 0xc1,
    0x8f, 0xff, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x37, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x73,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0x62, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xfe, 0xb7, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0xff, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x26, 0x10, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xbf, 0xfc, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x6d, 0xff, 0xff,
    0xfa, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xfe, 0x10,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x5, 0xef, 0xff, 0xff, 0xfe, 0x20, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xff,
    0xff, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0xc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x20, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x91, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xe9,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xd9, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x17,
    0x76, 0x53, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0C4 "" */
    0x0, 0x5, 0x99, 0x72, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x1, 0x7b, 0xb8,
    0x10, 0x1e, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x2, 0xef, 0xff, 0xfe, 0x38, 0xff, 0xff,
    0xef, 0xff, 0xf1, 0x0, 0x0, 0x2, 0xef, 0xff,
    0xff, 0xf4, 0xdf, 0xfe, 0x10, 0x7f, 0xff, 0x50,
    0x0, 0x2, 0xef, 0xff, 0xff, 0xf5, 0xf, 0xff,
    0x80, 0x0, 0xff, 0xf7, 0x0, 0x2, 0xef, 0xff,
    0xff, 0xf5, 0x0, 0xef, 0xfc, 0x0, 0x4f, 0xff,
    0x60, 0x2, 0xef, 0xff, 0xff, 0xf5, 0x0, 0xa,
    0xff, 0xfd, 0xbf, 0xff, 0xf4, 0x3, 0xef, 0xff,
    0xff, 0xf5, 0x0, 0x0, 0x2f, 0xff, 0xff, 0xff,
    0xff, 0xe5, 0xef, 0xff, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x18, 0xcd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xef, 0xff,
    0xff, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0x99, 0xef, 0xff, 0xff, 0xff, 0xff, 0xe2,
    0x0, 0x0, 0x0, 0x0, 0x2d, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe2, 0x0, 0x0, 0x0,
    0x1e, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xff, 0xff,
    0xff, 0xe2, 0x0, 0x0, 0x8, 0xff, 0xff, 0xef,
    0xff, 0xf6, 0x5, 0xff, 0xff, 0xff, 0xe2, 0x0,
    0x0, 0xdf, 0xfe, 0x10, 0x7f, 0xff, 0x50, 0x5,
    0xff, 0xff, 0xff, 0xe2, 0x0, 0xf, 0xff, 0x80,
    0x0, 0xff, 0xf7, 0x0, 0x5, 0xff, 0xff, 0xff,
    0xe2, 0x0, 0xef, 0xfc, 0x0, 0x4f, 0xff, 0x60,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xe2, 0xa, 0xff,
    0xfd, 0xbf, 0xff, 0xf2, 0x0, 0x0, 0x5, 0xff,
    0xff, 0xff, 0xe2, 0x2f, 0xff, 0xff, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0x40,
    0x4f, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xbe, 0xfb, 0x30, 0x0, 0x18, 0xcd, 0xb5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F0C5 "" */
    0x0, 0x0, 0x0, 0x1, 0x44, 0x44, 0x44, 0x44,
    0x42, 0x3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0x80, 0xeb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xe, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0xef,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0xe, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0xef, 0xff, 0xf4, 0xaf, 0xff, 0xf4, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xff, 0x40, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0xbb, 0xbb, 0xb3, 0xff, 0xff, 0xf4, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x5f, 0xff, 0xff, 0x40, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf5, 0xff, 0xff, 0xf4,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x5f, 0xff, 0xff, 0x40, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xff, 0xff,
    0xf4, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x5f, 0xff, 0xff, 0x40, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xff,
    0xff, 0xf4, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x5f, 0xff, 0xff, 0x40, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0xff, 0xff, 0xf4, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x5f, 0xff, 0xff, 0x40,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0xff, 0xff, 0xf4, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x5f, 0xff, 0xff,
    0x40, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0xff, 0xff, 0xf4, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5f, 0xff,
    0xff, 0x40, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0xff, 0xff, 0xf5, 0xa, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x1f,
    0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xa2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x37, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x60, 0x0,
    0x0, 0x0, 0x0,

    /* U+F0C7 "" */
    0x4, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x87, 0x20, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0,
    0x0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x40, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x40, 0x0, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xff, 0xff, 0x40, 0xf, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xff, 0x30, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xfe, 0xf,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xf3, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff,
    0x4f, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0xf4, 0xff, 0xfd, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x7e, 0xff, 0xff,
    0xff, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x82, 0x15, 0xef, 0xff,
    0xff, 0xff, 0xff, 0x4f, 0xff, 0xff, 0xff, 0xff,
    0x70, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0xb,
    0xff, 0xff, 0xff, 0xff, 0x4f, 0xff, 0xff, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xf4, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xff, 0x4f, 0xff, 0xff,
    0xff, 0xff, 0x30, 0x0, 0x0, 0xef, 0xff, 0xff,
    0xff, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x10,
    0x0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0x4f, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0xa9, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x3a,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x1a, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc3,
    0x0,

    /* U+F0C9 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xae, 0xee, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xee, 0xee, 0xee, 0xee, 0xed, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xa2, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x61, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xae, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xee, 0xee, 0xee, 0xed, 0x4f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8d, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0E0 "" */
    0x1b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb1, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x7, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x70, 0x0, 0x3e, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe3, 0x0, 0xe5, 0x1, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x10, 0x5e,
    0xff, 0x90, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x70, 0x9, 0xff, 0xff, 0xfd,
    0x20, 0x3d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xd3, 0x2, 0xdf, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa1, 0x6, 0xff,
    0xff, 0xff, 0xff, 0x60, 0xa, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x30, 0x2d, 0xff, 0xff,
    0xd2, 0x3, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf6, 0x0, 0x7e, 0xe7, 0x0, 0x7f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xb1, 0x0, 0x0, 0x1b, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x95, 0x59, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x1b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb1,

    /* U+F0E7 "" */
    0x0, 0x8, 0xbb, 0xbb, 0xbb, 0xbb, 0x80, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd2, 0x7,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x70, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0x0, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xff, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F0EA "" */
    0x0, 0x0, 0x0, 0x1, 0x43, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xfe, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x49, 0x99, 0x99, 0xff, 0xef, 0xfe, 0x99,
    0x99, 0x81, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xa0, 0x1e, 0xff, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xfa, 0x1, 0xef,
    0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xfc, 0x64, 0x44, 0x44, 0x44,
    0x30, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xff, 0x20, 0xaf, 0xff, 0xff,
    0xff, 0xa0, 0xd6, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xf0, 0x1f, 0xff, 0xff, 0xff, 0xfa, 0xe, 0xf6,
    0x0, 0xf, 0xff, 0xff, 0xff, 0x1, 0xff, 0xff,
    0xff, 0xff, 0xa0, 0xef, 0xf6, 0x0, 0xff, 0xff,
    0xff, 0xf0, 0x1f, 0xff, 0xff, 0xff, 0xfa, 0xe,
    0xff, 0xf6, 0xf, 0xff, 0xff, 0xff, 0x1, 0xff,
    0xff, 0xff, 0xff, 0xa0, 0xef, 0xff, 0xf3, 0xff,
    0xff, 0xff, 0xf0, 0x1f, 0xff, 0xff, 0xff, 0xfa,
    0x4, 0x44, 0x44, 0x1f, 0xff, 0xff, 0xff, 0x1,
    0xff, 0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xf0, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x5f, 0xff, 0xff, 0xff,
    0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0xff, 0xff, 0xff, 0xf0, 0x1f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x5f, 0xff, 0xff,
    0xff, 0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0xff, 0xff, 0xff, 0xf0, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5f, 0xff,
    0xff, 0xff, 0x1, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0xaf, 0xff, 0xff, 0xf0, 0x1f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x42, 0x0,

    /* U+F0F3 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xa2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x8d, 0xff, 0xfa, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xd4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x10, 0x0, 0x0,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x0, 0x3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xa0, 0x1, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x3e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0x6f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3e, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x15, 0x73, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F11C "" */
    0x1b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x50,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0xff, 0xfd, 0x88, 0x9f, 0xf8, 0x88, 0xff, 0x98,
    0x8e, 0xfa, 0x88, 0xbf, 0xd8, 0x89, 0xff, 0xf8,
    0xff, 0xf8, 0x0, 0xf, 0xc0, 0x0, 0xcf, 0x0,
    0xa, 0xf1, 0x0, 0x4f, 0x70, 0x0, 0xff, 0xf8,
    0xff, 0xf8, 0x0, 0xf, 0xc0, 0x0, 0xcf, 0x0,
    0xa, 0xf1, 0x0, 0x4f, 0x70, 0x0, 0xff, 0xf8,
    0xff, 0xf9, 0x0, 0x1f, 0xd0, 0x0, 0xdf, 0x10,
    0xc, 0xf3, 0x0, 0x6f, 0x90, 0x1, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xdc, 0xce, 0xfe, 0xcc, 0xdf,
    0xfc, 0xcd, 0xff, 0xcc, 0xcf, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0x20, 0x6, 0xf6, 0x0, 0x3f,
    0x80, 0x0, 0xfd, 0x0, 0xa, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0x20, 0x6, 0xf6, 0x0, 0x2f,
    0x80, 0x0, 0xed, 0x0, 0xa, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0x20, 0x6, 0xf6, 0x0, 0x3f,
    0x80, 0x0, 0xfd, 0x0, 0xa, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xdc, 0xce, 0xfe, 0xcc, 0xdf,
    0xfc, 0xcd, 0xff, 0xcc, 0xcf, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xf9, 0x0, 0x1f, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0x90, 0x1, 0xff, 0xf8,
    0xff, 0xf8, 0x0, 0xf, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0x70, 0x0, 0xff, 0xf8,
    0xff, 0xf8, 0x0, 0xf, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0x70, 0x0, 0xff, 0xf8,
    0xff, 0xfd, 0x88, 0x9f, 0xf8, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0xbf, 0xd8, 0x89, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0x1b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x50,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0x72, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xef, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x18, 0xef, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x29, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3a, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4c, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x7e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x2, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0, 0x4,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x0, 0x0, 0x1, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70,
    0x0, 0x0, 0xd, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x10, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x3, 0x44, 0x44, 0x44, 0x44, 0x4d, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xff,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x17, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F15B "" */
    0x14, 0x44, 0x44, 0x44, 0x44, 0x44, 0x10, 0x30,
    0x0, 0x0, 0xd, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0xf, 0xa0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x40, 0xff, 0xa0, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xf, 0xff,
    0xa0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x40, 0xff, 0xff, 0xa0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0xf, 0xff, 0xff, 0xa0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0xff, 0xff,
    0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xb, 0xbb, 0xbb, 0xbb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x14, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x10,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0x69, 0xab, 0xcb, 0xa9, 0x63, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x27,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc7,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x20, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xba,
    0xaa, 0xbd, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x1, 0xdf, 0xff, 0xff, 0xff, 0xc7, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x37, 0xcf, 0xff, 0xff,
    0xff, 0xd1, 0x3, 0xef, 0xff, 0xff, 0xf9, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x29,
    0xff, 0xff, 0xff, 0xe3, 0xef, 0xff, 0xff, 0xb2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xbf, 0xff, 0xff, 0xe7, 0xff, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xf7, 0x7,
    0xfe, 0x20, 0x0, 0x0, 0x0, 0x48, 0xbe, 0xff,
    0xfd, 0xb8, 0x40, 0x0, 0x0, 0x0, 0x2d, 0xf7,
    0x0, 0x4, 0x10, 0x0, 0x0, 0x18, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe8, 0x10, 0x0, 0x0,
    0x14, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xb1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xff, 0xc8, 0x76, 0x78, 0xcf,
    0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xe7, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xef, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x47, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3b, 0xdb, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F240 "" */
    0x2, 0x67, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x74,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0x9f,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0xff, 0xf8, 0xc, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x4,
    0xff, 0xff, 0xff, 0xff, 0x80, 0xcf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x28, 0xcf, 0xff, 0xff, 0xf8, 0xc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x8, 0xff, 0xff, 0xff, 0x80,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0x8f, 0xff, 0xff,
    0xf8, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x8, 0xff,
    0xff, 0xff, 0x80, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x3f,
    0xff, 0xff, 0xff, 0xf8, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x4, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0xfe, 0xff, 0xfb, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x79, 0xff, 0xfa, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe0, 0x1, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F241 "" */
    0x2, 0x67, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x74,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0x9f,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0xff, 0xf8, 0xe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xff, 0xff, 0x80, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x28, 0xcf, 0xff, 0xff, 0xf8, 0xe, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0x80,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xf8, 0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xff, 0x80, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0xff, 0xf8, 0xe, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x4, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0xfe, 0xff, 0xfb, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x79, 0xff, 0xfa, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe0, 0x1, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F242 "" */
    0x2, 0x67, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x74,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0x9f,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0xff, 0xf8, 0xe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xff, 0xff, 0x80, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x28, 0xcf, 0xff, 0xff, 0xf8, 0xe, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0x80,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xf8, 0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xff, 0x80, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0xff, 0xf8, 0xe, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0xfe, 0xff, 0xfb, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x79, 0xff, 0xfa, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe0, 0x1, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F243 "" */
    0x2, 0x67, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x74,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0x9f,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0xff, 0xf8, 0xe, 0xff, 0xff, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xff, 0xff, 0x80, 0xef, 0xff, 0xff,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x28, 0xcf, 0xff, 0xff, 0xf8, 0xe, 0xff,
    0xff, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0x80,
    0xef, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xf8, 0xe, 0xff, 0xff, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xff, 0x80, 0xef, 0xff, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0xff, 0xf8, 0xe, 0xff, 0xff, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0xfe, 0xff, 0xfb, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x79, 0xff, 0xfa, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe0, 0x1, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F244 "" */
    0x2, 0x67, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x74,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0x9f,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x28, 0xcf, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0xfe, 0xff, 0xfb, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x79, 0xff, 0xfa, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe0, 0x1, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5e, 0xfe, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x23, 0xff, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xbf, 0xff, 0xff,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xcd, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xe1, 0x0, 0xcf, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0x50, 0x0, 0x7, 0xa7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x10, 0x0, 0x0, 0x4, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xef, 0xfc, 0x20, 0x0, 0xc, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0x40,
    0x0, 0x0, 0x4f, 0xff, 0xff, 0xe1, 0x0, 0x4f,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xfb, 0x20, 0x0, 0xcf, 0xff, 0xff, 0xfa,
    0x35, 0xef, 0x94, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x4d, 0xff, 0xf7, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0xef, 0xff, 0xff, 0xfe, 0xbb, 0xbb, 0xbc, 0xff,
    0xcb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbf, 0xff,
    0xfe, 0x50, 0x8f, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x9f, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0x80, 0x0, 0xb, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0xe, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xb2, 0x0, 0x0, 0x0, 0x58,
    0x73, 0x0, 0x0, 0x0, 0x0, 0x7, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0x20, 0x6, 0xdd, 0xdd, 0xc2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xc0, 0x9, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xde, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xaf,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x19, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x34, 0x32, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3a, 0xef,
    0xff, 0xff, 0xfb, 0x50, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x10,
    0x0, 0x0, 0x1, 0xcf, 0xff, 0xff, 0xc7, 0xff,
    0xff, 0xff, 0xd1, 0x0, 0x0, 0xb, 0xff, 0xff,
    0xff, 0xc0, 0x8f, 0xff, 0xff, 0xfc, 0x0, 0x0,
    0x5f, 0xff, 0xff, 0xff, 0xc0, 0x9, 0xff, 0xff,
    0xff, 0x50, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0xaf, 0xff, 0xff, 0xb0, 0x2, 0xff, 0xff,
    0xff, 0xff, 0xc0, 0x0, 0xb, 0xff, 0xff, 0xf1,
    0x6, 0xff, 0xff, 0xaf, 0xff, 0xc0, 0x39, 0x0,
    0xcf, 0xff, 0xf5, 0xa, 0xff, 0xf9, 0x3, 0xef,
    0xc0, 0x3f, 0x90, 0x1d, 0xff, 0xf8, 0xc, 0xff,
    0xfd, 0x10, 0x3e, 0xc0, 0x2f, 0xb0, 0xc, 0xff,
    0xfa, 0xe, 0xff, 0xff, 0xd1, 0x3, 0xb0, 0x2b,
    0x0, 0xaf, 0xff, 0xfc, 0xf, 0xff, 0xff, 0xfd,
    0x10, 0x10, 0x0, 0x9, 0xff, 0xff, 0xfd, 0xf,
    0xff, 0xff, 0xff, 0xd1, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xfe, 0x1f, 0xff, 0xff, 0xff, 0xfd, 0x10,
    0x5, 0xff, 0xff, 0xff, 0xfe, 0x1f, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0x3, 0xff, 0xff, 0xff, 0xfe,
    0xf, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0xfd, 0xf, 0xff, 0xff, 0xfb, 0x0,
    0x20, 0x11, 0x4, 0xff, 0xff, 0xfd, 0xe, 0xff,
    0xff, 0xb0, 0x5, 0xc0, 0x2d, 0x10, 0x5f, 0xff,
    0xfc, 0xc, 0xff, 0xfb, 0x0, 0x5f, 0xc0, 0x2f,
    0xd0, 0x7, 0xff, 0xfa, 0x9, 0xff, 0xfb, 0x5,
    0xff, 0xc0, 0x3f, 0x60, 0x1d, 0xff, 0xf7, 0x5,
    0xff, 0xff, 0xdf, 0xff, 0xc0, 0x36, 0x1, 0xdf,
    0xff, 0xf4, 0x1, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0x0, 0x1d, 0xff, 0xff, 0xf0, 0x0, 0xaf, 0xff,
    0xff, 0xff, 0xd0, 0x1, 0xdf, 0xff, 0xff, 0xa0,
    0x0, 0x2f, 0xff, 0xff, 0xff, 0xd0, 0x1d, 0xff,
    0xff, 0xff, 0x20, 0x0, 0x6, 0xff, 0xff, 0xff,
    0xd1, 0xcf, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xff, 0xec, 0xff, 0xff, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x2, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xe7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x58, 0xab, 0xba, 0x84, 0x0, 0x0, 0x0,

    /* U+F2ED "" */
    0x0, 0x0, 0x0, 0x0, 0x14, 0x44, 0x44, 0x43,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x58, 0x88, 0x88, 0x8c, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x88, 0x88, 0x88, 0x1f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8b, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x28, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x86, 0x0, 0x4, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x0, 0x4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xc0, 0x0, 0x4f, 0xff, 0xf5, 0x5f, 0xff, 0x92,
    0xff, 0xfc, 0x1c, 0xff, 0xfc, 0x0, 0x4, 0xff,
    0xff, 0x22, 0xff, 0xf6, 0xe, 0xff, 0xa0, 0xaf,
    0xff, 0xc0, 0x0, 0x4f, 0xff, 0xf2, 0x2f, 0xff,
    0x60, 0xef, 0xfa, 0xa, 0xff, 0xfc, 0x0, 0x4,
    0xff, 0xff, 0x22, 0xff, 0xf6, 0xe, 0xff, 0xa0,
    0xaf, 0xff, 0xc0, 0x0, 0x4f, 0xff, 0xf2, 0x2f,
    0xff, 0x60, 0xef, 0xfa, 0xa, 0xff, 0xfc, 0x0,
    0x4, 0xff, 0xff, 0x22, 0xff, 0xf6, 0xe, 0xff,
    0xa0, 0xaf, 0xff, 0xc0, 0x0, 0x4f, 0xff, 0xf2,
    0x2f, 0xff, 0x60, 0xef, 0xfa, 0xa, 0xff, 0xfc,
    0x0, 0x4, 0xff, 0xff, 0x22, 0xff, 0xf6, 0xe,
    0xff, 0xa0, 0xaf, 0xff, 0xc0, 0x0, 0x4f, 0xff,
    0xf2, 0x2f, 0xff, 0x60, 0xef, 0xfa, 0xa, 0xff,
    0xfc, 0x0, 0x4, 0xff, 0xff, 0x22, 0xff, 0xf6,
    0xe, 0xff, 0xa0, 0xaf, 0xff, 0xc0, 0x0, 0x4f,
    0xff, 0xf2, 0x2f, 0xff, 0x60, 0xef, 0xfa, 0xa,
    0xff, 0xfc, 0x0, 0x4, 0xff, 0xff, 0x22, 0xff,
    0xf6, 0xe, 0xff, 0xa0, 0xaf, 0xff, 0xc0, 0x0,
    0x4f, 0xff, 0xf2, 0x2f, 0xff, 0x60, 0xef, 0xfa,
    0xa, 0xff, 0xfc, 0x0, 0x4, 0xff, 0xff, 0x55,
    0xff, 0xf9, 0x2f, 0xff, 0xc1, 0xcf, 0xff, 0xc0,
    0x0, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x0, 0x2, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xa0, 0x0, 0x9, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0x0, 0x0, 0x4,
    0x78, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x61, 0x0, 0x0,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x66, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xdf, 0xfe, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef,
    0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff, 0xff,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xec,
    0x0, 0xbf, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xfc, 0x0,
    0xbf, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xef, 0xff, 0xfc, 0x0, 0xbf,
    0xff, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xef, 0xff, 0xff, 0xfc, 0x0, 0xbf, 0xfe,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef,
    0xff, 0xff, 0xff, 0xfc, 0x0, 0xbe, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x0, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xfe,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xff, 0xff, 0xff, 0xfe, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xff, 0xff, 0xec, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x76,
    0x42, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F55A "" */
    0x0, 0x0, 0x0, 0x0, 0x8, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xd5, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x0, 0xb,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0,
    0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0x99, 0xff,
    0xff, 0xff, 0x99, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x8, 0xff, 0xff, 0x80, 0x7, 0xff, 0xff, 0xff,
    0xff, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe0, 0x0, 0x8, 0xff, 0x80, 0x0, 0xe, 0xff,
    0xff, 0xff, 0xf0, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x8, 0x80, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xff, 0xb, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x40, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xb, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x0, 0x8, 0x80, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0x0, 0xb, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x8, 0xff,
    0x80, 0x0, 0xe, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x8,
    0xff, 0xff, 0x80, 0x8, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x99, 0xff, 0xff, 0xff, 0x99, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd5, 0x0,

    /* U+F7C2 "" */
    0x0, 0x0, 0x0, 0x17, 0x88, 0x88, 0x88, 0x88,
    0x87, 0x50, 0x0, 0x0, 0x0, 0x1d, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc1, 0x0, 0x0, 0x2d,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0x0, 0x2e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x2e, 0xff, 0x20, 0x1f, 0xa0,
    0xe, 0xd0, 0x8, 0xff, 0xf0, 0x2e, 0xff, 0xf2,
    0x1, 0xfa, 0x0, 0xed, 0x0, 0x8f, 0xff, 0x3e,
    0xff, 0xff, 0x20, 0x1f, 0xa0, 0xe, 0xd0, 0x8,
    0xff, 0xfe, 0xff, 0xff, 0xf2, 0x1, 0xfa, 0x0,
    0xed, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0x20,
    0x1f, 0xa0, 0xe, 0xd0, 0x8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0x24,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x20,
    0x0,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x5c, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff,
    0x10, 0x0, 0x0, 0x7f, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xf1, 0x0,
    0x0, 0x8f, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xff, 0x10, 0x0, 0x9f,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xf1, 0x0, 0xaf, 0xff, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xff, 0x10, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x1c,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x2e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0x1d, 0xff, 0xff,
    0xfe, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
    0x55, 0x55, 0x10, 0x0, 0x1c, 0xff, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 121, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 120, .box_w = 5, .box_h = 20, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 50, .adv_w = 175, .box_w = 9, .box_h = 8, .ofs_x = 1, .ofs_y = 12},
    {.bitmap_index = 86, .adv_w = 315, .box_w = 19, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 276, .adv_w = 278, .box_w = 16, .box_h = 26, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 484, .adv_w = 378, .box_w = 22, .box_h = 20, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 704, .adv_w = 307, .box_w = 18, .box_h = 21, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 893, .adv_w = 94, .box_w = 4, .box_h = 8, .ofs_x = 1, .ofs_y = 12},
    {.bitmap_index = 909, .adv_w = 151, .box_w = 7, .box_h = 26, .ofs_x = 2, .ofs_y = -5},
    {.bitmap_index = 1000, .adv_w = 151, .box_w = 7, .box_h = 26, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 1091, .adv_w = 179, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 10},
    {.bitmap_index = 1152, .adv_w = 261, .box_w = 14, .box_h = 13, .ofs_x = 1, .ofs_y = 4},
    {.bitmap_index = 1243, .adv_w = 102, .box_w = 5, .box_h = 8, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 1263, .adv_w = 172, .box_w = 9, .box_h = 3, .ofs_x = 1, .ofs_y = 7},
    {.bitmap_index = 1277, .adv_w = 102, .box_w = 5, .box_h = 4, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1287, .adv_w = 158, .box_w = 12, .box_h = 27, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 1449, .adv_w = 299, .box_w = 17, .box_h = 20, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1619, .adv_w = 166, .box_w = 8, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1699, .adv_w = 257, .box_w = 16, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1859, .adv_w = 256, .box_w = 15, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2009, .adv_w = 300, .box_w = 18, .box_h = 20, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2189, .adv_w = 257, .box_w = 16, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2349, .adv_w = 276, .box_w = 16, .box_h = 20, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2509, .adv_w = 268, .box_w = 16, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2669, .adv_w = 289, .box_w = 16, .box_h = 20, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2829, .adv_w = 276, .box_w = 16, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2989, .adv_w = 102, .box_w = 5, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3027, .adv_w = 102, .box_w = 5, .box_h = 19, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 3075, .adv_w = 261, .box_w = 14, .box_h = 13, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 3166, .adv_w = 261, .box_w = 14, .box_h = 9, .ofs_x = 1, .ofs_y = 5},
    {.bitmap_index = 3229, .adv_w = 261, .box_w = 14, .box_h = 13, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 3320, .adv_w = 257, .box_w = 15, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3470, .adv_w = 463, .box_w = 27, .box_h = 25, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 3808, .adv_w = 328, .box_w = 22, .box_h = 20, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 4028, .adv_w = 339, .box_w = 18, .box_h = 20, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4208, .adv_w = 324, .box_w = 19, .box_h = 20, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4398, .adv_w = 370, .box_w = 20, .box_h = 20, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4598, .adv_w = 300, .box_w = 16, .box_h = 20, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4758, .adv_w = 284, .box_w = 15, .box_h = 20, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4908, .adv_w = 346, .box_w = 19, .box_h = 20, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5098, .adv_w = 364, .box_w = 18, .box_h = 20, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 5278, .adv_w = 139, .box_w = 4, .box_h = 20, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 5318, .adv_w = 230, .box_w = 13, .box_h = 20, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 5448, .adv_w = 322, .box_w = 19, .box_h = 20, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 5638, .adv_w = 266, .box_w = 15, .box_h = 20, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 5788, .adv_w = 428, .box_w = 22, .box_h = 20, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 6008, .adv_w = 364, .box_w = 18, .box_h = 20, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 6188, .adv_w = 376, .box_w = 22, .box_h = 20, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6408, .adv_w = 323, .box_w = 17, .box_h = 20, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 6578, .adv_w = 376, .box_w = 23, .box_h = 24, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 6854, .adv_w = 326, .box_w = 18, .box_h = 20, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 7034, .adv_w = 278, .box_w = 16, .box_h = 20, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7194, .adv_w = 263, .box_w = 17, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7364, .adv_w = 354, .box_w = 18, .box_h = 20, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 7544, .adv_w = 319, .box_w = 21, .box_h = 20, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 7754, .adv_w = 504, .box_w = 31, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8064, .adv_w = 302, .box_w = 19, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8254, .adv_w = 290, .box_w = 20, .box_h = 20, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 8454, .adv_w = 294, .box_w = 17, .box_h = 20, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 8624, .adv_w = 149, .box_w = 7, .box_h = 26, .ofs_x = 2, .ofs_y = -5},
    {.bitmap_index = 8715, .adv_w = 158, .box_w = 13, .box_h = 27, .ofs_x = -2, .ofs_y = -3},
    {.bitmap_index = 8891, .adv_w = 149, .box_w = 7, .box_h = 26, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 8982, .adv_w = 261, .box_w = 13, .box_h = 12, .ofs_x = 2, .ofs_y = 4},
    {.bitmap_index = 9060, .adv_w = 224, .box_w = 14, .box_h = 2, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9074, .adv_w = 269, .box_w = 8, .box_h = 4, .ofs_x = 3, .ofs_y = 17},
    {.bitmap_index = 9090, .adv_w = 268, .box_w = 14, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 9195, .adv_w = 306, .box_w = 16, .box_h = 21, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 9363, .adv_w = 256, .box_w = 15, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 9476, .adv_w = 306, .box_w = 16, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 9644, .adv_w = 274, .box_w = 15, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 9757, .adv_w = 158, .box_w = 11, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9873, .adv_w = 309, .box_w = 16, .box_h = 20, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 10033, .adv_w = 305, .box_w = 15, .box_h = 21, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 10191, .adv_w = 125, .box_w = 4, .box_h = 21, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 10233, .adv_w = 127, .box_w = 9, .box_h = 26, .ofs_x = -3, .ofs_y = -5},
    {.bitmap_index = 10350, .adv_w = 276, .box_w = 16, .box_h = 21, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 10518, .adv_w = 125, .box_w = 4, .box_h = 21, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 10560, .adv_w = 474, .box_w = 26, .box_h = 15, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 10755, .adv_w = 305, .box_w = 15, .box_h = 15, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 10868, .adv_w = 284, .box_w = 16, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 10988, .adv_w = 306, .box_w = 16, .box_h = 20, .ofs_x = 2, .ofs_y = -5},
    {.bitmap_index = 11148, .adv_w = 306, .box_w = 16, .box_h = 20, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 11308, .adv_w = 184, .box_w = 9, .box_h = 15, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 11376, .adv_w = 224, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11481, .adv_w = 185, .box_w = 11, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11586, .adv_w = 303, .box_w = 15, .box_h = 15, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 11699, .adv_w = 250, .box_w = 17, .box_h = 15, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 11827, .adv_w = 403, .box_w = 25, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12015, .adv_w = 247, .box_w = 16, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12135, .adv_w = 250, .box_w = 17, .box_h = 20, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 12305, .adv_w = 233, .box_w = 13, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 12403, .adv_w = 157, .box_w = 9, .box_h = 26, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 12520, .adv_w = 134, .box_w = 4, .box_h = 26, .ofs_x = 2, .ofs_y = -5},
    {.bitmap_index = 12572, .adv_w = 157, .box_w = 9, .box_h = 26, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 12689, .adv_w = 261, .box_w = 14, .box_h = 5, .ofs_x = 1, .ofs_y = 8},
    {.bitmap_index = 12724, .adv_w = 188, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = 11},
    {.bitmap_index = 12774, .adv_w = 141, .box_w = 6, .box_h = 6, .ofs_x = 1, .ofs_y = 5},
    {.bitmap_index = 12792, .adv_w = 448, .box_w = 28, .box_h = 29, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 13198, .adv_w = 448, .box_w = 28, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 13492, .adv_w = 448, .box_w = 28, .box_h = 25, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 13842, .adv_w = 448, .box_w = 28, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 14136, .adv_w = 308, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 14346, .adv_w = 448, .box_w = 28, .box_h = 29, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 14752, .adv_w = 448, .box_w = 27, .box_h = 29, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 15144, .adv_w = 504, .box_w = 32, .box_h = 25, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 15544, .adv_w = 448, .box_w = 28, .box_h = 29, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 15950, .adv_w = 504, .box_w = 32, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 16286, .adv_w = 448, .box_w = 28, .box_h = 29, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 16692, .adv_w = 224, .box_w = 14, .box_h = 23, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 16853, .adv_w = 336, .box_w = 21, .box_h = 23, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 17095, .adv_w = 504, .box_w = 32, .box_h = 27, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 17527, .adv_w = 448, .box_w = 28, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 17821, .adv_w = 308, .box_w = 20, .box_h = 29, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 18111, .adv_w = 392, .box_w = 18, .box_h = 26, .ofs_x = 3, .ofs_y = -3},
    {.bitmap_index = 18345, .adv_w = 392, .box_w = 25, .box_h = 29, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 18708, .adv_w = 392, .box_w = 25, .box_h = 25, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 19021, .adv_w = 392, .box_w = 25, .box_h = 25, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 19334, .adv_w = 392, .box_w = 18, .box_h = 26, .ofs_x = 3, .ofs_y = -3},
    {.bitmap_index = 19568, .adv_w = 392, .box_w = 26, .box_h = 25, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 19893, .adv_w = 280, .box_w = 16, .box_h = 25, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 20093, .adv_w = 280, .box_w = 16, .box_h = 25, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 20293, .adv_w = 392, .box_w = 25, .box_h = 25, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 20606, .adv_w = 392, .box_w = 25, .box_h = 7, .ofs_x = 0, .ofs_y = 7},
    {.bitmap_index = 20694, .adv_w = 504, .box_w = 32, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 21030, .adv_w = 560, .box_w = 35, .box_h = 29, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 21538, .adv_w = 504, .box_w = 33, .box_h = 29, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 22017, .adv_w = 448, .box_w = 28, .box_h = 25, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 22367, .adv_w = 392, .box_w = 25, .box_h = 15, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 22555, .adv_w = 392, .box_w = 25, .box_h = 15, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 22743, .adv_w = 560, .box_w = 35, .box_h = 22, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 23128, .adv_w = 448, .box_w = 28, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 23422, .adv_w = 448, .box_w = 28, .box_h = 29, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 23828, .adv_w = 448, .box_w = 29, .box_h = 29, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 24249, .adv_w = 392, .box_w = 25, .box_h = 25, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 24562, .adv_w = 392, .box_w = 25, .box_h = 29, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 24925, .adv_w = 392, .box_w = 25, .box_h = 25, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 25238, .adv_w = 392, .box_w = 25, .box_h = 23, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 25526, .adv_w = 448, .box_w = 28, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 25820, .adv_w = 280, .box_w = 19, .box_h = 29, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 26096, .adv_w = 392, .box_w = 25, .box_h = 29, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 26459, .adv_w = 392, .box_w = 25, .box_h = 29, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 26822, .adv_w = 504, .box_w = 32, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 27158, .adv_w = 448, .box_w = 30, .box_h = 29, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 27593, .adv_w = 336, .box_w = 21, .box_h = 29, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 27898, .adv_w = 560, .box_w = 35, .box_h = 26, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 28353, .adv_w = 560, .box_w = 35, .box_h = 19, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 28686, .adv_w = 560, .box_w = 35, .box_h = 19, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 29019, .adv_w = 560, .box_w = 35, .box_h = 19, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 29352, .adv_w = 560, .box_w = 35, .box_h = 19, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 29685, .adv_w = 560, .box_w = 35, .box_h = 19, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 30018, .adv_w = 560, .box_w = 36, .box_h = 23, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 30432, .adv_w = 392, .box_w = 22, .box_h = 29, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 30751, .adv_w = 392, .box_w = 25, .box_h = 29, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 31114, .adv_w = 448, .box_w = 29, .box_h = 29, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 31535, .adv_w = 560, .box_w = 35, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 31903, .adv_w = 336, .box_w = 21, .box_h = 29, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 32208, .adv_w = 451, .box_w = 29, .box_h = 19, .ofs_x = 0, .ofs_y = 1}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x1f72, 0xef51, 0xef58, 0xef5b, 0xef5c, 0xef5d, 0xef61,
    0xef63, 0xef65, 0xef69, 0xef6c, 0xef71, 0xef76, 0xef77, 0xef78,
    0xef8e, 0xef93, 0xef98, 0xef9b, 0xef9c, 0xef9d, 0xefa1, 0xefa2,
    0xefa3, 0xefa4, 0xefb7, 0xefb8, 0xefbe, 0xefc0, 0xefc1, 0xefc4,
    0xefc7, 0xefc8, 0xefc9, 0xefcb, 0xefe3, 0xefe5, 0xf014, 0xf015,
    0xf017, 0xf019, 0xf030, 0xf037, 0xf03a, 0xf043, 0xf06c, 0xf074,
    0xf0ab, 0xf13b, 0xf190, 0xf191, 0xf192, 0xf193, 0xf194, 0xf1d7,
    0xf1e3, 0xf23d, 0xf254, 0xf4aa, 0xf712, 0xf7f2
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 176, .range_length = 63475, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 62, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 0, 13, 14, 15, 16, 17,
    18, 19, 12, 20, 20, 0, 0, 0,
    21, 22, 23, 24, 25, 22, 26, 27,
    28, 29, 29, 30, 31, 32, 29, 29,
    22, 33, 34, 35, 3, 36, 30, 37,
    37, 38, 39, 40, 41, 42, 43, 0,
    44, 0, 45, 46, 47, 48, 49, 50,
    51, 45, 52, 52, 53, 48, 45, 45,
    46, 46, 54, 55, 56, 57, 51, 58,
    58, 59, 58, 60, 41, 0, 0, 9,
    61, 9, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 13, 14, 15, 16, 17, 12,
    18, 19, 20, 21, 21, 0, 0, 0,
    22, 23, 24, 25, 23, 25, 25, 25,
    23, 25, 25, 26, 25, 25, 25, 25,
    23, 25, 23, 25, 3, 27, 28, 29,
    29, 30, 31, 32, 33, 34, 35, 0,
    36, 0, 37, 38, 39, 39, 39, 0,
    39, 38, 40, 41, 38, 38, 42, 42,
    39, 42, 39, 42, 43, 44, 45, 46,
    46, 47, 46, 48, 0, 0, 35, 9,
    49, 9, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 4, 0, 0, 0,
    0, 3, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 1, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 1, 20, 0, 12, -10, 0, 0,
    0, 0, -25, -27, 3, 21, 10, 8,
    -18, 3, 22, 1, 19, 4, 14, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 27, 4, -3, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 9, 0, -13, 0, 0, 0, 0,
    0, -9, 8, 9, 0, 0, -4, 0,
    -3, 4, 0, -4, 0, -4, -2, -9,
    0, 0, 0, 0, -4, 0, 0, -6,
    -7, 0, 0, -4, 0, -9, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -4,
    -4, 0, -7, 0, -12, 0, -54, 0,
    0, -9, 0, 9, 13, 0, 0, -9,
    4, 4, 15, 9, -8, 9, 0, 0,
    -26, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -17, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -12, -5, -22, 0, -18,
    -3, 0, 0, 0, 0, 1, 17, 0,
    -13, -4, -1, 1, 0, -8, 0, 0,
    -3, -33, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -36, -4, 17,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -18, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 15,
    0, 4, 0, 0, -9, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 17, 4,
    1, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -17, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 3,
    9, 4, 13, -4, 0, 0, 9, -4,
    -15, -61, 3, 12, 9, 1, -6, 0,
    16, 0, 14, 0, 14, 0, -42, 0,
    -5, 13, 0, 15, -4, 9, 4, 0,
    0, 1, -4, 0, 0, -8, 36, 0,
    36, 0, 13, 0, 19, 6, 8, 13,
    0, 0, 0, -17, 0, 0, 0, 0,
    1, -3, 0, 3, -8, -6, -9, 3,
    0, -4, 0, 0, 0, -18, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -29, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 1, -25, 0, -28, 0, 0, 0,
    0, -3, 0, 44, -5, -6, 4, 4,
    -4, 0, -6, 4, 0, 0, -24, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -43, 0, 4, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -28, 0, 27, 0, 0, -17, 0,
    15, 0, -30, -43, -30, -9, 13, 0,
    0, -30, 0, 5, -10, 0, -7, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 12, 13, -55, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 21, 0, 3, 0, 0, 0,
    0, 0, 3, 3, -5, -9, 0, -1,
    -1, -4, 0, 0, -3, 0, 0, 0,
    -9, 0, -4, 0, -10, -9, 0, -11,
    -15, -15, -9, 0, -9, 0, -9, 0,
    0, 0, 0, -4, 0, 0, 4, 0,
    3, -4, 0, 1, 0, 0, 0, 4,
    -3, 0, 0, 0, -3, 4, 4, -1,
    0, 0, 0, -9, 0, -1, 0, 0,
    0, 0, 0, 1, 0, 6, -3, 0,
    -5, 0, -8, 0, 0, -3, 0, 13,
    0, 0, -4, 0, 0, 0, 0, 0,
    -1, 1, -3, -3, 0, 0, -4, 0,
    -4, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -2, -2, 0, -4, -5, 0,
    0, 0, 0, 0, 1, 0, 0, -3,
    0, -4, -4, -4, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -3, 0, 0,
    0, 0, -3, -6, 0, -7, 0, -13,
    -3, -13, 9, 0, 0, -9, 4, 9,
    12, 0, -11, -1, -5, 0, -1, -21,
    4, -3, 3, -24, 4, 0, 0, 1,
    -23, 0, -24, -4, -39, -3, 0, -22,
    0, 9, 13, 0, 6, 0, 0, 0,
    0, 1, 0, -8, -6, 0, -13, 0,
    0, 0, -4, 0, 0, 0, -4, 0,
    0, 0, 0, 0, -2, -2, 0, -2,
    -6, 0, 0, 0, 0, 0, 0, 0,
    -4, -4, 0, -3, -5, -4, 0, 0,
    -4, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -4, -4, 0, -5,
    0, -3, 0, -9, 4, 0, 0, -5,
    2, 4, 4, 0, 0, 0, 0, 0,
    0, -3, 0, 0, 0, 0, 0, 3,
    0, 0, -4, 0, -4, -3, -5, 0,
    0, 0, 0, 0, 0, 0, 4, 0,
    -4, 0, 0, 0, 0, -5, -7, 0,
    -9, 0, 13, -3, 1, -14, 0, 0,
    12, -22, -23, -19, -9, 4, 0, -4,
    -29, -8, 0, -8, 0, -9, 7, -8,
    -29, 0, -12, 0, 0, 2, -1, 4,
    -3, 0, 4, 0, -13, -17, 0, -22,
    -11, -9, -11, -13, -5, -12, -1, -9,
    -12, 3, 0, 1, 0, -4, 0, 0,
    0, 3, 0, 4, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -4,
    0, -2, 0, -1, -4, 0, -8, -10,
    -10, -1, 0, -13, 0, 0, 0, 0,
    0, 0, -4, 0, 0, 0, 0, 2,
    -3, 0, 0, 0, 4, 0, 0, 0,
    0, 0, 0, 0, 0, 22, 0, 0,
    0, 0, 0, 0, 3, 0, 0, 0,
    -4, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -8, 0, 4, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, 0, 0,
    -9, 0, 0, 0, 0, -22, -13, 0,
    0, 0, -7, -22, 0, 0, -4, 4,
    0, -12, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -7, 0, 0, -9,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 4, 0, -8, 0,
    0, 0, 0, 5, 0, 3, -9, -9,
    0, -4, -4, -5, 0, 0, 0, 0,
    0, 0, -13, 0, -4, 0, -7, -4,
    0, -10, -11, -13, -4, 0, -9, 0,
    -13, 0, 0, 0, 0, 36, 0, 0,
    2, 0, 0, -6, 0, 4, 0, -19,
    0, 0, 0, 0, 0, -42, -8, 15,
    13, -4, -19, 0, 4, -7, 0, -22,
    -2, -6, 4, -31, -4, 6, 0, 7,
    -16, -7, -17, -15, -19, 0, 0, -27,
    0, 26, 0, 0, -2, 0, 0, 0,
    -2, -2, -4, -12, -15, -1, -42, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    1, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -4, 0, -2, -4, -7, 0, 0,
    -9, 0, -4, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -1, 0, -9, 0, 0, 9,
    -1, 6, 0, -10, 4, -3, -1, -12,
    -4, 0, -6, -4, -3, 0, -7, -8,
    0, 0, -4, -1, -3, -8, -5, 0,
    0, -4, 0, 4, -3, 0, -10, 0,
    0, 0, -9, 0, -8, 0, -8, -8,
    4, 0, 0, 0, 0, 0, 0, 0,
    0, -9, 4, 0, -6, 0, -3, -5,
    -14, -3, -3, -3, -1, -3, -5, -1,
    0, 0, 0, 0, 0, -4, -4, -4,
    0, 0, 0, 0, 5, -3, 0, -3,
    0, 0, 0, -3, -5, -3, -4, -5,
    -4, 0, 4, 18, -1, 0, -12, 0,
    -3, 9, 0, -4, -19, -6, 7, 0,
    0, -21, -8, 4, -8, 3, 0, -3,
    -4, -14, 0, -7, 2, 0, 0, -8,
    0, 0, 0, 4, 4, -9, -9, 0,
    -8, -4, -7, -4, -4, 0, -8, 2,
    -9, -8, 13, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 4, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -8, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -3, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -4, -4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -7, 0, 0, -6,
    0, 0, -4, -4, 0, 0, 0, 0,
    -4, 0, 0, 0, 0, -2, 0, 0,
    0, 0, 0, -3, 0, 0, 0, 0,
    -7, 0, -9, 0, 0, 0, -15, 0,
    3, -10, 9, 1, -3, -21, 0, 0,
    -10, -4, 0, -18, -11, -13, 0, 0,
    -19, -4, -18, -17, -22, 0, -12, 0,
    4, 30, -6, 0, -10, -4, -1, -4,
    -8, -12, -8, -17, -18, -10, -4, 0,
    0, -3, 0, 1, 0, 0, -31, -4,
    13, 10, -10, -17, 0, 1, -14, 0,
    -22, -3, -4, 9, -41, -6, 1, 0,
    0, -29, -5, -23, -4, -33, 0, 0,
    -31, 0, 26, 1, 0, -3, 0, 0,
    0, 0, -2, -3, -17, -3, 0, -29,
    0, 0, 0, 0, -14, 0, -4, 0,
    -1, -13, -21, 0, 0, -2, -7, -13,
    -4, 0, -3, 0, 0, 0, 0, -20,
    -4, -15, -14, -4, -8, -11, -4, -8,
    0, -9, -4, -15, -7, 0, -5, -9,
    -4, -9, 0, 2, 0, -3, -15, 0,
    9, 0, -8, 0, 0, 0, 0, 5,
    0, 3, -9, 18, 0, -4, -4, -5,
    0, 0, 0, 0, 0, 0, -13, 0,
    -4, 0, -7, -4, 0, -10, -11, -13,
    -4, 0, -9, 4, 18, 0, 0, 0,
    0, 36, 0, 0, 2, 0, 0, -6,
    0, 4, 0, 0, 0, 0, 0, 0,
    0, 0, -1, 0, 0, 0, 0, 0,
    -3, -9, 0, 0, 0, 0, 0, -2,
    0, 0, 0, -4, -4, 0, 0, -9,
    -4, 0, 0, -9, 0, 8, -2, 0,
    0, 0, 0, 0, 0, 2, 0, 0,
    0, 0, 7, 9, 4, -4, 0, -14,
    -7, 0, 13, -15, -14, -9, -9, 18,
    8, 4, -39, -3, 9, -4, 0, -4,
    5, -4, -16, 0, -4, 4, -6, -4,
    -13, -4, 0, 0, 13, 9, 0, -13,
    0, -25, -6, 13, -6, -17, 1, -6,
    -15, -15, -4, 18, 4, 0, -7, 0,
    -12, 0, 4, 15, -10, -17, -18, -11,
    13, 0, 1, -33, -4, 4, -8, -3,
    -10, 0, -10, -17, -7, -7, -4, 0,
    0, -10, -9, -4, 0, 13, 10, -4,
    -25, 0, -25, -6, 0, -16, -26, -1,
    -14, -8, -15, -13, 12, 0, 0, -6,
    0, -9, -4, 0, -4, -8, 0, 8,
    -15, 4, 0, 0, -24, 0, -4, -10,
    -8, -3, -13, -11, -15, -10, 0, -13,
    -4, -10, -9, -13, -4, 0, 0, 1,
    21, -8, 0, -13, -4, 0, -4, -9,
    -10, -12, -13, -17, -6, -9, 9, 0,
    -7, 0, -22, -5, 3, 9, -14, -17,
    -9, -15, 15, -4, 2, -42, -8, 9,
    -10, -8, -17, 0, -13, -19, -5, -4,
    -4, -4, -9, -13, -1, 0, 0, 13,
    13, -3, -29, 0, -27, -10, 11, -17,
    -30, -9, -16, -19, -22, -15, 9, 0,
    0, 0, 0, -5, 0, 0, 4, -5,
    9, 3, -9, 9, 0, 0, -14, -1,
    0, -1, 0, 1, 1, -4, 0, 0,
    0, 0, 0, 0, -4, 0, 0, 0,
    0, 4, 13, 1, 0, -5, 0, 0,
    0, 0, -3, -3, -5, 0, 0, 0,
    1, 4, 0, 0, 0, 0, 4, 0,
    -4, 0, 17, 0, 8, 1, 1, -6,
    0, 9, 0, 0, 0, 4, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 13, 0, 13, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -27, 0, -4, 8, 0, 13,
    0, 0, 44, 5, -9, -9, 4, 4,
    -3, 1, -22, 0, 0, 22, -27, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -30, 17, 63, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -27, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -7, 0, 0, -9,
    -4, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, -12, 0,
    0, 1, 0, 0, 4, 58, -9, -4,
    14, 12, -12, 4, 0, 0, 4, 4,
    -6, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -58, 13, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -13,
    0, 0, 0, -12, 0, 0, 0, 0,
    -10, -2, 0, 0, 0, -10, 0, -5,
    0, -21, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -30, 0, 0,
    0, 0, 1, 0, 0, 0, 0, 0,
    0, -4, 0, 0, -9, 0, -7, 0,
    -12, 0, 0, 0, -8, 4, -5, 0,
    0, -12, -4, -10, 0, 0, -12, 0,
    -4, 0, -21, 0, -5, 0, 0, -36,
    -9, -18, -5, -16, 0, 0, -30, 0,
    -12, -2, 0, 0, 0, 0, 0, 0,
    0, 0, -7, -8, -4, -8, 0, 0,
    0, 0, -10, 0, -10, 6, -5, 9,
    0, -3, -10, -3, -8, -9, 0, -5,
    -2, -3, 3, -12, -1, 0, 0, 0,
    -39, -4, -6, 0, -10, 0, -3, -21,
    -4, 0, 0, -3, -4, 0, 0, 0,
    0, 3, 0, -3, -8, -3, 8, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 6, 0, 0, 0, 0, 0,
    0, -10, 0, -3, 0, 0, 0, -9,
    4, 0, 0, 0, -12, -4, -9, 0,
    0, -13, 0, -4, 0, -21, 0, 0,
    0, 0, -43, 0, -9, -17, -22, 0,
    0, -30, 0, -3, -7, 0, 0, 0,
    0, 0, 0, 0, 0, -4, -7, -2,
    -7, 1, 0, 0, 8, -6, 0, 14,
    22, -4, -4, -13, 5, 22, 8, 10,
    -12, 5, 19, 5, 13, 10, 12, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 28, 21, -8, -4, 0, -4,
    36, 19, 36, 0, 0, 0, 4, 0,
    0, 17, 0, 0, -7, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -3, 0,
    0, 0, 0, 0, 0, 0, 0, 6,
    0, 0, 0, 0, -38, -5, -4, -18,
    -22, 0, 0, -30, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -7, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -3,
    0, 0, 0, 0, 0, 0, 0, 0,
    6, 0, 0, 0, 0, -38, -5, -4,
    -18, -22, 0, 0, -18, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -4, 0, 0, 0, -10, 4, 0, -4,
    4, 8, 4, -13, 0, -1, -4, 4,
    0, 4, 0, 0, 0, 0, -11, 0,
    -4, -3, -9, 0, -4, -18, 0, 28,
    -4, 0, -10, -3, 0, -3, -8, 0,
    -4, -13, -9, -5, 0, 0, 0, -7,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -3, 0, 0, 0, 0, 0, 0,
    0, 0, 6, 0, 0, 0, 0, -38,
    -5, -4, -18, -22, 0, 0, -30, 0,
    0, 0, 0, 0, 0, 22, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -7, 0, -14, -5, -4, 13, -4, -4,
    -18, 1, -3, 1, -3, -12, 1, 10,
    1, 4, 1, 4, -11, -18, -5, 0,
    -17, -9, -12, -19, -17, 0, -7, -9,
    -5, -6, -4, -3, -5, -3, 0, -3,
    -1, 7, 0, 7, -3, 0, 14, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -3, -4, -4, 0, 0,
    -12, 0, -2, 0, -8, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -27, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -4, -4, 0, -6,
    0, 0, 0, 0, -4, 0, 0, -8,
    -4, 4, 0, -8, -9, -3, 0, -13,
    -3, -10, -3, -5, 0, -8, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -30, 0, 14, 0, 0, -8, 0,
    0, 0, 0, -6, 0, -4, 0, 0,
    -2, 0, 0, -3, 0, -10, 0, 0,
    19, -6, -15, -14, 3, 5, 5, -1,
    -13, 3, 7, 3, 13, 3, 15, -3,
    -12, 0, 0, -18, 0, 0, -13, -12,
    0, 0, -9, 0, -6, -8, 0, -7,
    0, -7, 0, -3, 7, 0, -4, -13,
    -4, 17, 0, 0, -4, 0, -9, 0,
    0, 6, -10, 0, 4, -4, 4, 0,
    0, -15, 0, -3, -1, 0, -4, 5,
    -4, 0, 0, 0, -18, -5, -10, 0,
    -13, 0, 0, -21, 0, 17, -4, 0,
    -8, 0, 3, 0, -4, 0, -4, -13,
    0, -4, 4, 0, 0, 0, 0, -3,
    0, 0, 4, -6, 1, 0, 0, -5,
    -3, 0, -5, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -28, 0, 10, 0,
    0, -4, 0, 0, 0, 0, 1, 0,
    -4, -4, 0, 0, 0, 9, 0, 10,
    0, 0, 0, 0, 0, -28, -26, 1,
    19, 13, 8, -18, 3, 19, 0, 17,
    0, 9, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 24, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 61,
    .right_class_cnt     = 49,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LV_VERSION_CHECK(8, 0, 0)
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LV_VERSION_CHECK(8, 0, 0)
    .cache = &cache
#endif
};


/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LV_VERSION_CHECK(8, 0, 0)
const lv_font_t lv_font_montserrat_28 = {
#else
lv_font_t lv_font_montserrat_28 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 30,          /*The maximum line height required by the font*/
    .base_line = 5,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0)
    .underline_position = -2,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc           /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
};



#endif /*#if LV_FONT_MONTSERRAT_28*/

