#ifndef _MF_KPU_V1_H
#define _MF_KPU_V1_H

#include <stdint.h>
#include <plic.h>
#include "dmac.h"

#include "mf_kpu_v3.h"

typedef struct
{
    uint32_t version;
    uint32_t flags;
    uint32_t layers_length;
    uint32_t max_start_address;
    uint32_t layers_argument_start;
} mf_kpu_lib_model_header_t;

typedef struct _mf_kpu_lib_quantize_param
{
    float scale;
    float bias;
} mf_kpu_lib_quantize_param_t;

extern volatile mf_kpu_config_t *const mf_kpu;

int mf_kpu_lib_start(mf_kpu_task_t *task);
int mf_kpu_lib_model_load_from_buffer(mf_kpu_task_t *task, uint8_t *buffer, mf_kpu_model_layer_metadata_t **meta);
int mf_kpu_lib_task_init(mf_kpu_task_t *task);
int mf_kpu_lib_task_deinit(mf_kpu_task_t *task);



#endif