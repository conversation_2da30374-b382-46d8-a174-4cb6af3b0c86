/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_time.h
** bef: define the interface for time. 
** auth: lines<<EMAIL>>
** create on 2021.11.19
*/

#ifndef _ES_TIME_H_
#define _ES_TIME_H_

#ifdef __cplusplus 
extern "C" { 
#endif

#include "es_types.h"

typedef struct {
    ES_U16 year;
    ES_U8 mon;
    ES_U8 mday;

    ES_U8 hour;
    ES_U8 min;
    ES_U8 sec;
    ES_U8 wday;

    ES_U32 timestamp;
} es_time_t;

ES_S32 es_time_init(ES_VOID);

ES_S32 es_time_check_timeout_ms(ES_U32 *last, ES_U32 timeout_ms);

ES_U32 es_time_get_sytem_ms(ES_VOID);

ES_U64 es_time_get_sytem_us(ES_VOID);

ES_S32 es_time_sync(es_time_t *time);

ES_S32 es_time_get_now(es_time_t *now);

ES_U32 es_time_get_timestamp(ES_VOID);

#ifdef __cplusplus 
}
#endif

#endif