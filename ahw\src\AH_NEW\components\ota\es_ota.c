/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_ota.c
** bef: define the interface for ota.
** auth: lines<<EMAIL>>
** create on 2022.01.12 
*/

#include "es_inc.h"

#if ES_OTA_ENABLE
#include "facelib_inc.h"

// #define ES_OTA_DEBUG
#ifdef ES_OTA_DEBUG
#define es_ota_debug es_log_info
#define es_ota_error es_log_error
#else
#define es_ota_debug(...)
#define es_ota_error(...)
#endif

#define ES_OTA_RX_BUF_SIZE              (12*1024)
#define ES_OTA_FLASE_SECTOR_SIZE        (4096)

//header format
//header*3  + which*1   + BOOT0 Addr*4  + BOOT1 Addr*4  + CRC8*1
//'XEL'      + 0/1       + 0010 0000     + 00a0 0000     + CRC8
typedef struct {
    ES_U8 head[3];
    ES_U8 which;
    ES_U32 boot0_addr;
    ES_U32 boot1_addr;
    ES_U8 crc8;
    ES_U8 resv[3]; /* 4字节对齐 */
} es_ota_boot_env_t;

typedef struct {
    ES_VOID *rx_buf;
    ES_U32 file_size;
    ES_U32 finish_size;
    ES_U32 write_flash_addr;
    ES_U32 version;
    ES_U8 status;
    ES_U8 error;
} es_ota_status_info_t;

static es_ota_boot_env_t ota_boot_env;
static es_ota_status_info_t ota_info;

#ifdef ES_OTA_DEBUG
static ES_VOID es_ota_print_env(es_ota_boot_env_t *env)
{
    es_ota_debug("head: %02x, %02x, %02x", env->head[0], env->head[1], env->head[2]);
    es_ota_debug("which:%d", env->which);
    es_ota_debug("boot0_addr:%d", env->boot0_addr);
    es_ota_debug("boot1_addr:%d", env->boot1_addr);
    es_ota_debug("crc8:%02x", env->crc8&0xFF);
}

static ES_VOID es_ota_print_info(ES_VOID)
{
    static ES_U32 last_time = 0;
    if (ES_RET_SUCCESS != es_time_check_timeout_ms(&last_time, 1000)) {
        return;
    }
    es_ota_debug("ota info:");
    es_ota_debug("status:%d", ota_info.status);
    es_ota_debug("file_size:%d", ota_info.file_size);
    es_ota_debug("finish_size:%d", ota_info.finish_size);
    es_ota_debug("flash addr:%08x", ota_info.write_flash_addr);
}

#endif

static ES_VOID es_ota_u32_swap(ES_BYTE *data, ES_U32 len)
{
    ES_U32 i;
    uint8_t tmp = 0;

    for (i = 0; i < ((len - 4) / 4) + 1; i++) {
        tmp = *(data + i * 4 + 0);
        *(data + i * 4 + 0) = *(data + i * 4 + 3);
        *(data + i * 4 + 3) = tmp;

        tmp = *(data + i * 4 + 1);
        *(data + i * 4 + 1) = *(data + i * 4 + 2);
        *(data + i * 4 + 2) = tmp;
    }
}

static ES_S32 es_ota_save_boot_env(es_ota_boot_env_t *env)
{
    ES_U8 tmp[sizeof(es_ota_boot_env_t)] = {0}, *tenv = (ES_U8 *)env;
    ES_U8 i = 0;
    
    env->crc8 = es_crc8((const ES_BYTE *)env, 12);
#ifdef ES_OTA_DEBUG
    es_ota_print_env(env);
#endif

    for(i = 0; i < (sizeof(es_ota_boot_env_t) / 4); i++) {
        tmp[i * 4 + 0] = tenv[4 * i + 3];
        tmp[i * 4 + 1] = tenv[4 * i + 2];
        tmp[i * 4 + 2] = tenv[4 * i + 1];
        tmp[i * 4 + 3] = tenv[4 * i + 0];
    }

    if (0 == mf_flash.write(ES_OTA_BOOT_ENV_ADDR, tmp, sizeof(es_ota_boot_env_t))) {
        return ES_RET_SUCCESS;
    }

    es_ota_debug("ota save env fail");
    return ES_RET_FAILURE;
}

static ES_S32 es_ota_load_boot_env(ES_VOID)
{
    ES_U8 *t = (ES_U8*)&ota_boot_env;
    ES_U8 tmp[sizeof(es_ota_boot_env_t)] = {0};
    ES_BOOL need_reset = ES_FALSE;
    ES_U8 i = 0;

    es_memset(&ota_boot_env, 0x00, sizeof(ota_boot_env));
    if (0 != mf_flash.read(ES_OTA_BOOT_ENV_ADDR, (ES_U8 *)tmp, sizeof(es_ota_boot_env_t))) {
        need_reset = ES_TRUE;
    } else {
        for(i = 0; i < (sizeof(es_ota_boot_env_t) / 4); i++) {
            t[i * 4 + 0] = tmp[i * 4 + 3];
            t[i * 4 + 1] = tmp[i * 4 + 2];
            t[i * 4 + 2] = tmp[i * 4 + 1];
            t[i * 4 + 3] = tmp[i * 4 + 0];
        }

        if (ota_boot_env.crc8 != es_crc8((const ES_BYTE *)&ota_boot_env, 12)) {
            need_reset = ES_TRUE;
        }
    }

    ota_boot_env.which = mf_brd.boot_section;
    if (ES_FALSE == need_reset) {
        es_ota_debug("ota load from flash success");
#ifdef ES_OTA_DEBUG
        es_ota_print_env(&ota_boot_env);
#endif
        return ES_RET_SUCCESS;
    }

    // reset env
    es_ota_debug("reset ota env");
    ota_boot_env.head[0] = 'X';
    ota_boot_env.head[1] = 'E';
    ota_boot_env.head[2] = 'L';
    ota_boot_env.boot0_addr = ES_OTA_APP0_DEFAULT_ADDR;
    ota_boot_env.boot1_addr = ES_OTA_APP1_DEFAULT_ADDR;
#ifdef ES_OTA_DEBUG
    es_ota_print_env(&ota_boot_env);
#endif
    return ES_RET_SUCCESS;
}

static ES_S32 es_ota_write_flash(ES_U32 size)
{
    ES_BYTE data[ES_OTA_FLASE_SECTOR_SIZE] = {0};
    ES_U32 read_size = 0;
    ES_U32 finish_size = 0;

    do {
        read_size = size - finish_size;
        if (read_size > ES_OTA_FLASE_SECTOR_SIZE) {
            read_size = ES_OTA_FLASE_SECTOR_SIZE;
        }

        es_circel_read(ota_info.rx_buf, data, read_size);
        es_ota_debug("write addr:%08x, read_size:%d", ota_info.write_flash_addr, read_size);
        if (read_size != ES_OTA_FLASE_SECTOR_SIZE) {
            read_size = ((read_size/4) + 1) * 4;
        }
        es_ota_u32_swap(data, read_size);
        if (0 != mf_flash.write(ota_info.write_flash_addr, data, read_size)) {
            es_ota_error("write addr:%08x error", ota_info.write_flash_addr);
            return ES_RET_FAILURE;
        }
        finish_size += read_size;
        ota_info.write_flash_addr += read_size;
    } while (finish_size < size);

    return ES_RET_SUCCESS;
}

static ES_S32 es_ota_check_and_write_flash(ES_VOID)
{
    ES_U32 rx_size = 0;

    if (ES_OTA_STATUS_FAIL == ota_info.status) {
        return ES_RET_FAILURE;
    }

    rx_size = es_circel_get_data_size(ota_info.rx_buf);
    if (ota_info.finish_size < ota_info.file_size) { // not finish download
        if (rx_size < (ES_OTA_RX_BUF_SIZE/2)) {
            return ES_RET_SUCCESS;
        } else {
            rx_size = ES_OTA_FLASE_SECTOR_SIZE; // write to flash
        }
    }

    // do write flash
    if (ES_RET_SUCCESS != es_ota_write_flash(rx_size)) {
        ota_info.error = 1;
        es_ota_debug("write flash error");
        return ES_RET_FAILURE;
    }

    // download finish
    if (ota_info.finish_size == ota_info.file_size) {
        ota_boot_env.which = 1 - ota_boot_env.which;
        if (ES_RET_SUCCESS != es_ota_save_boot_env(&ota_boot_env)) {
            ota_info.status = ES_OTA_STATUS_FAIL;
            return ES_RET_FAILURE;
        }

        es_ota_debug("ota success, reboot system now");
        es_hal_sys_reset();
    }


    return ES_RET_SUCCESS;
}

ES_S32 es_ota_init(ES_VOID)
{
    es_memset(&ota_info, 0x00, sizeof(ota_info));
    ota_info.status = ES_OTA_STATUS_IDLE;
    es_ota_load_boot_env();

    return ES_RET_SUCCESS;
}

ES_S32 es_ota_start(const es_ota_file_info_t *info)
{
    if (info == ES_NULL) {
        return ES_RET_FAILURE;
    }

    if (0 == info->file_size || (ES_OTA_FILE_MAX_SIZE < info->file_size)) {
        ota_info.error = 1;
        return ES_RET_FAILURE;
    }

    if (ota_info.rx_buf) {
        es_circle_buf_free(ota_info.rx_buf);
        ota_info.rx_buf = ES_NULL;
    }

    ota_info.rx_buf = es_circle_buf_new(ES_OTA_RX_BUF_SIZE);
    if (ES_NULL == ota_info.rx_buf) {
        ota_info.error = 1;
        ota_info.status = ES_OTA_STATUS_IDLE;
        return ES_RET_FAILURE;
    }

    ota_info.status = ES_OTA_STATUS_START;
    ota_info.file_size = info->file_size;
    ota_info.finish_size = 0;
    ota_info.write_flash_addr = ota_boot_env.boot1_addr;
    if (1 == ota_boot_env.which) {
        ota_info.write_flash_addr = ota_boot_env.boot0_addr;
    }
    ota_info.error = 0;
#ifdef ES_OTA_DEBUG
    es_ota_print_info();
#endif
    return ES_RET_SUCCESS;
}

ES_S32 es_ota_write(ES_U32 offset, const ES_BYTE *data, ES_U32 len)
{
    if (0 != ota_info.error || ES_NULL == ota_info.rx_buf) {
        return ES_RET_FAILURE;
    }

    if ((ota_info.finish_size + len) > ota_info.file_size) {
        ota_info.error = 1;
        return ES_RET_FAILURE;
    }

    if (ES_NULL == data || 0 == len) {
        return ES_RET_FAILURE;
    }

    
    if (0 == ota_info.version) { // the first 4 byte is version
        ota_info.version = (ES_U32)(data[3]<<24) + (ES_U32)(data[2]<<16) + (ES_U32)(data[1]<<8) +  (ES_U32)(data[0]);
        es_ota_debug("version:%d", ota_info.version);
        if (ota_info.version <= ES_SYS_SVER_VAL) {
            ota_info.error = 1;
            es_ota_error("ota version low");
            return ES_RET_FAILURE;
        }
        data = (const ES_BYTE *)(data + 4);
        len = len - 4;
        ota_info.finish_size = 4;
    }

    es_circel_write(ota_info.rx_buf, data, len);
    ota_info.finish_size += len;
    ota_info.status = ES_OTA_STATUS_DOWNLOAD;
    // es_ota_debug("finish_size:%d, len:%d", ota_info.finish_size, len);
    return ES_RET_SUCCESS;
}

ES_S32 es_ota_finish(ES_BOOL success)
{
    ES_S32 ret = ES_RET_SUCCESS;

    if (0 != ota_info.error) {
        ret = ES_RET_FAILURE;
        ota_info.status = ES_OTA_STATUS_FAIL;
        es_ota_debug("ota fail, error");
        goto FUNC_END;
    }

    if (ota_info.finish_size != ota_info.file_size) {
        ret = ES_RET_FAILURE;
        ota_info.status = ES_OTA_STATUS_FAIL;
        es_ota_debug("ota fail, finish_size:%d, file_size:%d", ota_info.finish_size, ota_info.file_size);
        goto FUNC_END;
    }

    if (((ota_info.finish_size - 5) % 4) != 0) {
        es_ota_error("ota fail, length");
        ret = ES_RET_FAILURE;
        goto FUNC_END;
    }

FUNC_END:
    return ret;
}

ES_S32 es_ota_write_all(const ES_BYTE *data, ES_U32 len)
{
    return ES_RET_SUCCESS;
}

// ref es_ota_status_e
ES_S32 es_ota_get_status(ES_VOID)
{
    return ota_info.status;
}

ES_VOID es_ota_run(ES_VOID)
{
#ifdef ES_OTA_DEBUG
    // es_ota_print_info();
#endif
    es_ota_check_and_write_flash();
    if (ES_OTA_STATUS_FAIL == ota_info.status) {
        if (ota_info.rx_buf) {
            es_circle_buf_free(ota_info.rx_buf);
            ota_info.rx_buf = ES_NULL;
        }
        ota_info.status = ES_OTA_STATUS_IDLE;
    }
}

ES_S32 es_ota_get_percent(ES_VOID)
{
    if (ES_OTA_STATUS_DOWNLOAD != ota_info.status) {
        return 0;
    }

    return ((ota_info.finish_size+1)* 100) / ota_info.file_size;
}

#endif
