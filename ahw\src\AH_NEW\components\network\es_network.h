/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_network.h
** bef: define the interface for network. 
** auth: lines<<EMAIL>>
** create on 2020.12.21
*/

#ifndef _ES_NETWORK_H_
#define _ES_NETWORK_H_

#ifdef __cplusplus 
extern "C" { 
#endif

#include "es_types.h"

typedef enum {
    ES_NETWORK_INIT, 
    ES_NETWORK_CONNECTING,
    ES_NETWORK_CONNECTED,
    ES_NETWORK_BAD
} es_network_status_e;

typedef enum {
    ES_NETWORK_PASS_BY_FACE,
    ES_NETWORK_PASS_BY_NFC,
    ES_NETWORK_PASS_BY_QR,
    ES_NETWORK_PASS_END
} es_network_pass_type_e;

ES_S32 es_network_init(ES_VOID);

ES_VOID es_network_task(ES_VOID);

ES_S32 es_network_get_status(ES_VOID);

ES_U32 es_network_get_mac(ES_BYTE *mac);

ES_U8 es_network_get_rssi(ES_VOID);

ES_S32 es_network_upload_pass_log(const ES_BYTE *uid, ES_U32 timestamp, es_network_pass_type_e type, ES_U32 expire, ES_U32 status);

// type is es_facecb_type_e.
ES_S32 es_ntework_upload_pic(const ES_BYTE *pic_data, ES_U32 pic_data_len, ES_U32 timestamp, ES_U8 type);


#ifdef __cplusplus 
}
#endif

#endif