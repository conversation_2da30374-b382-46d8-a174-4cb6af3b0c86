#include "es_inc.h"
#include "facelib_inc.h"


#pragma GCC diagnostic ignored "-Wunused-function"
#pragma GCC diagnostic ignored "-Wunused-variable"


/*****************************************************************************/
// Macro definitions
/*****************************************************************************/
#if 1
#define DEBUG_LINE()
#define DBG_HEAP()
#define DBG_TIME_INIT()
#define DBG_TIME()
#else
#define DEBUG_LINE() do{ printk("[mf_flow_vis2vis] L%d\r\n", __LINE__); } while(0)
#define DBG_HEAP() do{ printk("[mf_flow_vis2vis] L%d :heap:%ld KB\r\n", __LINE__, get_free_heap_size() / 1024); } while(0)
#define DBG_TIME_INIT() do{ uint64_t t0 = sysctl_get_time_us()/1000; } while(0)
#define DBG_TIME() do { uint64_t t1 = sysctl_get_time_us()/1000; printk("[v2v dbgT]L%d: %ld ms\r\n", __LINE__, t1-t0); t0 = sysctl_get_time_us()/1000; } while(0)
#endif
		
/*****************************************************************************/
// Function definitions
/*****************************************************************************/
#define FAST_FLOW		(0)



/*****************************************************************************/
// Private Var 局部变量
/*****************************************************************************/
static fr_ret_t l_ret_850;
static fr_ret_t l_ret_650;

//volatile uint8_t face_lib_draw_flag = 0;

typedef struct
{
	uint8_t first_time_run; //首次运行
    uint8_t open_led_cnt;   //打开补光灯的时间计数
    uint8_t open_lcd_cnt;   //打开LCD显示的时间计数
	uint8_t dark_cnt;       //ir有人脸，rgb无人脸的计数，增到阈值后补光    
	uint8_t all_have_ir;

} mf_flow_v2v_t;

static mf_flow_v2v_t flow = {
	.first_time_run = 1 ,
	.open_led_cnt   = 0 ,
	.open_lcd_cnt   = 30,
	.dark_cnt       = 0 ,
	.all_have_ir    = 0 ,
};


/*****************************************************************************/
// Driver  底层函数
/*****************************************************************************/
//注册回调函数
static mf_err_t reg_cb_list(flow_cb_list_t* list, flow_cb_t cb)
{
	if(list->idx >= 5) return MF_ERR_LIST_FULL;
	if(cb == NULL) return MF_ERR_CB_NULL;
	list->cbs[list->idx] = cb;
	list->idx ++;
	return MF_ERR_NONE;
}

static mf_err_t _reg_kpubuf_cb(flow_cb_t cb)
{
	// return reg_cb_list(&mf_flow.kpubuf_cb_list, cb);
	return MF_ERR_NONE;
}

static mf_err_t _reg_rgbbuf_cb0(flow_cb_t cb)
{
	// return reg_cb_list(&mf_flow.rgbbuf_cb0_list, cb);
	return MF_ERR_NONE;
}

static mf_err_t _reg_rgbbuf_cb1(flow_cb_t cb)
{
	// return reg_cb_list(&mf_flow.rgbbuf_cb1_list, cb);
	return MF_ERR_NONE;
}

static void _process_cblist(flow_cb_list_t* list)
{
	// for(uint8_t i = 0; i < list->idx; i++) {
	// 	if(list->cbs[i])list->cbs[i]();
	// }
	return;
}


static void change_xy(fr_ret_t *face)
{
    face_obj_info_t *face_obj = &(face->result->face_obj_info);
    face_obj_t *obj = NULL;
    uint32_t x1, y1, x2, y2;

    for(uint32_t i = 0; i < (face_obj->obj_number); i++)
    {
        obj = &(face_obj->obj[i]);

        obj->y1 = MF_CAM_W - 1 - obj->y1;
        obj->y2 = MF_CAM_W - 1 - obj->y2;

        x1 = obj->x1;
        y1 = obj->y1;
        x2 = obj->x2;
        y2 = obj->y2;

        obj->x1 = x1;
        obj->y1 = y2;
        obj->x2 = x2;
        obj->y2 = y1;
    }
}

static uint8_t ctrl_IR_by_face(uint8_t have_face)
{
    static uint8_t i = 0;
    static uint8_t face_mode = 1;

    if(face_mode == 0) //have no face before
    {
        if(have_face) //come in face
        {
            face_mode = 1;
            i = 0;
            mf_brd.set_IR_LED(1);
            mf_brd.dvp_open_ir = 0;
        } else
        {
            //stay no face
            if(i <= mf_brd.cfg.flow.ir_oni)
            {
                mf_brd.dvp_open_ir = 1;
            } else
            {
                mf_brd.dvp_open_ir = 0;
            }
            i = (i % (mf_brd.cfg.flow.ir_period));
        }
    } else
    {
        //have face before
        mf_brd.set_IR_LED(1);
        if(have_face) //still have face
        {
            i = 0; //reset i
        } else
        {
            //face leave
            if(i >= mf_brd.cfg.flow.open_ir_n) //too long time no face
            {
                //back to no face mode
                mf_brd.set_IR_LED(0);
                face_mode = 0;
                i = 0;
            }
        }
    }
    i++;

    return face_mode;
}

void _flow_v2v_update_allir(mf_dbops_type_t type, uint32_t id, void* _item)
{
	if(type != MF_DBOPS_GET) {
		mf_facedb.iterate_init();
		
		dbf_item_t* item     = (dbf_item_t*)mf_facedb.item_buf;
		dbmeta_t* meta  = (dbmeta_t*)item->meta;
		uint32_t vid, oft;
		while(mf_facedb.iterate(mf_facedb.item_buf, &vid, &oft)) {
			if (meta->stat == 0) {
				flow.all_have_ir = 0;
				return;
			}
		}
		flow.all_have_ir = 1;
	}
	return;
}


/*****************************************************************************/
// Private Func 局部函数
/*****************************************************************************/
/*
//摄像头调度策略
//摄像头在中断里自动轮换拍摄，
//rgb镜头存储到565格式的rgb_image缓存,ir存到r8g8b8的kpu_image缓存
//每个缓存均为两块，rgb_i，kpu_i指示的是前台缓存(用户程序使用)，另一个为后台缓存(中断使用)
//中断进入START中断，rgb摄像头需要进行亮度更新，IR补光灯控制等。
//中断进入FINISH中断，拍摄完成的镜头的fin_flag置位，并且设置dvp切换到另一个镜头(设置rgb_i/kup_i问题？)
//fin_flag表示后台缓存已捕捉完成，如果下次进入前，前台还没有清fin_flag，中断内不覆盖上次缓存(缩短时间)
//用户程序使用buf[i]运算，运算完成后清fin_flag，表示切换前后台buf，后台中断可以重新开始填充缓存

//在另一个摄像头的fin之前清了fin_flag, 并
*/

//进行850人脸预检测，黑暗人脸下白光补光的情况
//返回是否有人脸   L30
static uint32_t _flow_850_detect(void)
{
	uint32_t ir_face = 0;
	//850图像运行人脸检测及活体检测
	image_t img_850 = {(uint8_t*)kpu_buf, MF_CAM_W, MF_CAM_H, 3};
    l_ret_850 = mf_model.run_fr(&img_850, true, STEP_LIVING, FTR_850, 0); //20ms
    DEBUG_LINE();
    //确认当前的光照情况，判断是否要打开补光灯
    //night_mode = night_judge(mf_brd.cfg.flow.night_gate); 
	if(l_ret_850.result && (l_ret_850.result->face_obj_info.obj_number > 0))
	{	//如果有识别到人脸, 不需要黑夜条件，因为逆光也会造成650无人脸
		ir_face = l_ret_850.result->face_obj_info.obj_number;
		
		flow.open_led_cnt = mf_brd.cfg.flow.open_led_n;
		flow.open_lcd_cnt = mf_brd.cfg.flow.close_lcd ? mf_brd.cfg.flow.open_lcd_n : 0;

		// // mf_lcd.set_bl(1);
		if (flow.dark_cnt>=mf_brd.cfg.flow.darkface_n) {
			es_hal_led_open(ES_TRUE);
			es_facecb_update_detect_time();
		} else {
			es_hal_led_open(ES_FALSE);
			es_facecb_update_detect_time();
		}
	} else {
		/* 人脸消失之后仍然需要补光 */
		if(flow.open_led_cnt > 0) {
			ir_face = 0;
			flow.open_led_cnt--;
			if(flow.open_led_cnt == 0) {
				flow.dark_cnt = 0;	//ir无人脸，dark_cnt自动清空
				es_hal_led_close();//时间到，关补光
			}
		}

		/* 人脸消失之后开始准备进入待机/关闭屏幕 */
		// if((mf_brd.cfg.flow.close_lcd) && (flow.open_lcd_cnt > 0)) {
		// 	flow.open_lcd_cnt --;
		// 	if(flow.open_lcd_cnt == 0) {
		// 		// mf_lcd.set_bl(0);
		// 	}
		// }
	}

	return ir_face;
}

//返回真人脸数量，0表示没有，或者850/650人脸数不一致  L40
static uint32_t _flow_650_faceftr(void)
{
	DEBUG_LINE();
	image_rgb565_to_r8g8b8((uint16_t *)(rgb_buf), (uint8_t*)kpu_buf, MF_CAM_W, MF_CAM_H);
	DEBUG_LINE();
	//进行650人脸完整识别流程
	image_t img_650 = {(uint8_t*)kpu_buf, MF_CAM_W, MF_CAM_H, 3};
	l_ret_650 = mf_model.run_fr(&img_650, true, STEP_FETURE, FTR_650, 0); //102731 us @ 400M
	//face_recognition_run_merge(&l_ret_650, &l_ret_850);    //4 us @ 400M
#if FAST_FLOW
	/* 用完了kpubuf就标记一下 */
	memset((uint8_t*)kpu_buf, 0xFF, 320 * 120 * 2);
#endif

	if(mf_cam.dir == 1) {
		// change_xy(&l_ret_850);
	}

	//850/650 不同人脸情况的处理
	if(l_ret_850.result->face_obj_info.obj_number > 0 && \
		l_ret_650.result->face_obj_info.obj_number == 0) {
		//850有人脸，650无人脸，即黑暗或者逆光情况
		flow.dark_cnt++;
		if(flow.dark_cnt >= mf_brd.cfg.flow.darkface_n){
			es_hal_led_open(ES_TRUE);
			es_facecb_update_detect_time();
		}
	} else if(l_ret_850.result->face_obj_info.obj_number > 0 && \
		l_ret_650.result->face_obj_info.obj_number > 0){
		//850/650均有人脸，光照正常，清除黑暗人脸计数
		if(flow.dark_cnt<mf_brd.cfg.flow.darkface_n){
			flow.dark_cnt=0;
		}
	} else {
		if(l_ret_650.result->face_obj_info.obj->prob <= 0.88f) {
			flow.dark_cnt++;
			if(flow.dark_cnt >= mf_brd.cfg.flow.darkface_n){
				es_hal_led_open(ES_TRUE);
				es_facecb_update_detect_time();
			}
		}
	}
	if(l_ret_850.result->face_obj_info.obj_number != \
		l_ret_650.result->face_obj_info.obj_number)
	{	//两边人脸数量不一致，退出处理
		return 0;
	}

	if((l_ret_850.result->face_obj_info.obj_number != 1) || (l_ret_650.result->face_obj_info.obj_number != 1)) {
		return 0;
	}

	/* 检查是不是同一个人 */
	extern float cal_iou(face_obj_t *obj_850, face_obj_t *obj_650);

	uint32_t liveface_n = 0;
	for(uint32_t i = 0; i < l_ret_850.result->face_obj_info.obj_number; i++)
	{
		liveface_n += l_ret_850.result->face_obj_info.obj[i].true_face;

		face_obj_t *obj_850 = &(l_ret_850.result->face_obj_info.obj[i]);
		face_obj_t *obj_650 = &(l_ret_650.result->face_obj_info.obj[i]);
		
		float iou = cal_iou(obj_850, obj_650);

		if(iou < 0.2f) {
			// printk("iou: %d\r\n", (int)(iou * 1000));
			return 0;
		}
		obj_650->true_face = obj_850->true_face;
	}
	return liveface_n;
}

//有ir人脸的情况下执行所有人脸相关回调  L30
static void _flow_handle_cb(uint32_t liveface_n)
{	
	uint32_t face_cnt = l_ret_650.result->face_obj_info.obj_number;
	uint32_t i, detect_n, stranger_n, fake_n, pass_n;
	uint8_t detect_i = 0;
	uint8_t stranger_i = 0;
	uint8_t fake_i = 0;
	uint8_t pass_i = 0;
	
	DEBUG_LINE();
	/****** Stat every class face cnt ********/
	for(i = 0; i < face_cnt; i++) {
		face_obj_t *f650_obj = &(l_ret_650.result->face_obj_info.obj[i]);
		face_obj_t *f850_obj = &(l_ret_850.result->face_obj_info.obj[i]);
		detect_i++;
		//TODO: distinct every fake and live face
		if(f850_obj->true_face == 0) { //liveface_n==0
			fake_i++;
		} else if(f850_obj->true_face == 0xAA) {
			f850_obj->true_face = 0;
		}

		//liveface_n==l_ret_850.result->face_obj_info.obj_number && 
		if(f850_obj->true_face) {
			if(f650_obj->pass == 0) {
				stranger_i++;
			} else {
				pass_i++;
			}
		}
		if(f650_obj->pass && f850_obj->true_face ) {
			
		}
	}
	detect_n   = detect_i;
	stranger_n = stranger_i;
	fake_n     = fake_i;
	pass_n     = pass_i;
	detect_i=0; stranger_i=0; fake_i=0; pass_i=0;
	if(face_lib_flow_debug_en)
		printk("det %d, stranger %d, fake %d, pass %d\r\n", detect_n, stranger_n, fake_n, pass_n);
	/****** Process every class of face ********/
	for(i = 0; i < face_cnt; i++) {
		face_obj_t *face_obj = &(l_ret_650.result->face_obj_info.obj[i]);
		//detect cb: all faces callback, for record
		detect_i++;
		if(mf_flow.cb->detect) { 	
			mf_flow.cb->detect(face_obj, detect_i, detect_n);
		}
		//stranger cb: face not in db, or angle not right
		if(mf_flow.cb->stranger && face_obj->pass == 0) {
			stranger_i++;
			mf_flow.cb->stranger(face_obj, stranger_i, stranger_n);
		}
		//fake cb: fake face
		//TODO: distinct every fake and live face
		if(mf_flow.cb->fake && liveface_n==0 && fake_n != 0) {
			fake_i++;
			mf_flow.cb->fake(face_obj, fake_i, fake_n);
		}
		//pass cb: live pass face
		//TODO: distinct every fake and live face
		if(mf_flow.cb->pass && \
			liveface_n==l_ret_850.result->face_obj_info.obj_number && \
			face_obj->pass ) { 
			pass_i++;
			mf_flow.cb->pass(face_obj, pass_i, pass_n);
		}
	}
	return;
}

//执行界面显示更新
static void _flow_handle_display(void)
{
	/* 如果使能了背光控制，我们在这里判断是否要进行刷屏 */
	// if(mf_brd.cfg.flow.close_lcd) {
	// 	if(flow.open_lcd_cnt == 0) {
	// 		return;
	// 	}
	// }

	if(mf_flow.cb->pre_display) {
		mf_flow.cb->pre_display();
	}

	// mf_ui.refresh();

	if(mf_flow.cb->post_display) {
		mf_flow.cb->post_display();
	}

	return;
}

static void old_fr_flow(void)
{
	DBG_TIME_INIT();
	uint32_t ir_face = 0;                 //850图像中的人脸数
	uint32_t liveface_n = 0;              //活体人脸数

	return;
}

//这里双缓冲策略是，850图像以r8g8b8形式存在kpu_buf，650图像以rgb565形式存在rgb_buf
static mf_err_t _flow_v2v_loop(void)
{DBG_TIME_INIT();
	int ir_face = 0;                 //850图像中的人脸数
	uint32_t liveface_n = 0;              //活体人脸数
	DEBUG_LINE();DBG_HEAP();
    if(flow.first_time_run) {DEBUG_LINE();//首次运行，等待一会填好缓存
		mf_cam.first_run();DEBUG_LINE();
        flow.first_time_run = 0;
    }
	if(mf_flow.enable) {                  //使能识别流程
		mf_cam.lock_kpu(); DBG_TIME();  //锁定850图像
		mf_cam.lock_sync(); //任意帧都重新同步
		// _process_cblist(&mf_flow.kpubuf_cb_list);
		
		int ff_cnt = 0;
#if FAST_FLOW
		///////////////////////////////////////////////////////////////////////
		uint8_t *_kpu_buf = (uint8_t*)kpu_buf;
		/* 3.7ms */
		for(int _idx = 0; _idx < (320 * 120 * 2); _idx ++) {
			if(_kpu_buf[_idx] == 0xFF) {
				ff_cnt ++;
			}
		}
		///////////////////////////////////////////////////////////////////////
#endif /* FAST_FLOW */
		/* 只要有一半的像素点是0xFFFF,认为这一帧被污染了 */
		if(ff_cnt >= (320 * 120)) {
			printk("ff_cnt:%d\r\n", ff_cnt);

			ir_face = -1;
			_flow_850_detect();
			mf_model.free_fr(l_ret_850);
		} else {
			ir_face = _flow_850_detect();DEBUG_LINE();//850人脸预检测
		}
		//if(ir_face == 0) mf_cam.lock_sync();  //没有人脸的时候进行同步
		DBG_TIME();
		// printk("ir_face:%d\r\n", ir_face);
		mf_cam.lock_rgb();DBG_TIME();                   //锁定650图像
		// _process_cblist(&mf_flow.rgbbuf_cb0_list);
#if FAST_FLOW
		mf_cam.unlock_kpu();
#endif /* FAST_FLOW */
		//暂不使能，否则夜间RGB会被IR buf吃掉
		/*uint8_t night_mode = mf_cam.is_night(mf_brd.cfg.flow.night_gate); 
		if(night_mode) { //转换 ir rgb888 图像到 rgb565 图像
			image_rgb8882rgb565((uint16_t *)(rgb_buf), kpu_buf, MF_CAM_W, MF_CAM_H);
			convert_rgb565_order((uint16_t *)(rgb_buf), MF_CAM_W, MF_CAM_H);
		} */

		if(ir_face < 0) {
			es_ui_face_edge(0, 0, 0, 0, ES_FACECB_NONE);
			es_ui_cam_update(ES_FALSE);
			es_ui_cam2buf();
			// mf_ui.cam2buf();DBG_HEAP();                  //650摄像头图像转到 main buf
			// _process_cblist(&mf_flow.rgbbuf_cb1_list);
		}else if(0 == ir_face) {	DEBUG_LINE();                      //850没有人脸，不进入绘人脸框
#if !FAST_FLOW
			mf_cam.unlock_kpu();							//不再需要kpu_buf,释放之
#endif /* FAST_FLOW */
			es_ui_face_edge(0, 0, 0, 0, ES_FACECB_NONE);
			es_ui_cam_update(ES_FALSE);
			es_ui_cam2buf();
			// mf_ui.cam2buf();DBG_HEAP();                  //650摄像头图像转到 main buf
			// _process_cblist(&mf_flow.rgbbuf_cb1_list);
			mf_model.free_fr(l_ret_850);
		} else {DEBUG_LINE();DBG_HEAP();DBG_TIME();  //850有人脸(不检测真假就进入比对)
			liveface_n = _flow_650_faceftr();DBG_TIME(); //650人脸识别
#if 0
			/* 将850结果中的blur及true_face覆盖650的结果，如果有多个人脸，我们就将650的结果全部置0 */
			if((l_ret_850.result->face_obj_info.obj_number != 1) || \
				(l_ret_650.result->face_obj_info.obj_number != 1)) {
					for(uint32_t i = 0; i < l_ret_650.result->face_obj_info.obj_number; i ++) {
						l_ret_650.result->face_obj_info.obj[i].blur = 0;
						l_ret_650.result->face_obj_info.obj[i].score = 0;
						l_ret_650.result->face_obj_info.obj[i].true_face = 0;
						l_ret_650.result->face_obj_info.obj[i].pass = 0;
					}
			} else {
					l_ret_650.result->face_obj_info.obj[0].true_face = l_ret_850.result->face_obj_info.obj[0].true_face;
					l_ret_650.result->face_obj_info.obj[0].prob = l_ret_850.result->face_obj_info.obj[0].prob;
					l_ret_650.result->face_obj_info.obj[0].blur = l_ret_850.result->face_obj_info.obj[0].blur;
			}
#endif

#if !FAST_FLOW
			mf_cam.unlock_kpu();              //到这里为止kpu_image使用完毕
#endif
			// mf_ui.cam2buf();   DBG_TIME();               //650摄像头图像转到 main buf
			// _process_cblist(&mf_flow.rgbbuf_cb1_list);
			es_ui_cam_update(ES_FALSE);
			es_ui_cam2buf();
			_flow_handle_cb(liveface_n);   DBG_TIME();   //运行所有人脸相关回调
			mf_model.free_fr(l_ret_850);
			mf_model.free_fr(l_ret_650);DBG_HEAP();
		}
		DEBUG_LINE();DBG_TIME(); //liveface_n
		ctrl_IR_by_face(ir_face);         //根据人脸情况进行IR补光设置
	} else {DBG_TIME();                   //失能识别流程
		mf_brd.set_IR_LED(0);
		mf_cam.lock_kpu();  DEBUG_LINE();              
		mf_cam.lock_sync(); DEBUG_LINE();
		// _process_cblist(&mf_flow.kpubuf_cb_list);
		mf_cam.lock_rgb();  DEBUG_LINE(); 
		// _process_cblist(&mf_flow.rgbbuf_cb0_list);
		mf_cam.unlock_kpu();
		// es_ui_face_edge(0, 0, 0, 0, ES_FACECB_NONE);
		es_ui_cam_update(ES_FALSE);
		es_ui_cam2buf();
		// mf_ui.cam2buf();    DBG_HEAP();               
		// _process_cblist(&mf_flow.rgbbuf_cb1_list);DBG_TIME();

		flow.open_led_cnt = mf_brd.cfg.flow.open_led_n;
		flow.open_lcd_cnt = mf_brd.cfg.flow.close_lcd ? mf_brd.cfg.flow.open_lcd_n : 0;
		es_hal_led_close();
	}
	//显示处理
	_flow_handle_display();
    DEBUG_LINE();DBG_TIME();
	//mf_cam.unlock_kpu();
    mf_cam.unlock_rgb(); //到这里为止，rgb图像使用完毕
    DEBUG_LINE();DBG_HEAP();DBG_TIME();
    return MF_ERR_NONE;
}

//ir_or_rgb: 0 rgb, 1 ir,2 ir & rgb
static float cal_ftr_score(dbmeta_t *flash_ftr, int8_t *ftr, uint8_t ir_or_rgb)
{
#if FRETRUE_IR_ENABLE
    float v_score = 0.0f;
    if(flash_ftr->stat == 0) {	//只有rgb
		DEBUG_LINE();
        if(ir_or_rgb == FTR_650) {
            DEBUG_LINE();
            v_score = mf_model.ftr_compare(ftr, flash_ftr->ftr_rgb);
        } else {
            DEBUG_LINE();
            v_score = 0.0f;
        }
    } else if(flash_ftr->stat == 1) { //只有IR
        DEBUG_LINE();
        if(ir_or_rgb == FTR_850) {
            DEBUG_LINE();
            v_score = mf_model.ftr_compare(ftr, flash_ftr->ftr_ir);
        } else {
            DEBUG_LINE();
            v_score = 0.0f;
        }
    } else if(flash_ftr->stat == 2) { //都有
        DEBUG_LINE();
        //rgb 以及 ir 都有
        v_score = mf_model.ftr_compare(ftr,
        (ir_or_rgb == FTR_850) ? flash_ftr->ftr_ir : flash_ftr->ftr_rgb);
    } else {                          //其他值
        DEBUG_LINE();
        v_score = 0.0f;
    }
#else
    float v_score = 0.0f;
    if(ir_or_rgb == FTR_650) {
        DEBUG_LINE();
        v_score = mf_model.ftr_compare(ftr, flash_ftr->ftr_rgb);
	} else {
        DEBUG_LINE();
        v_score = 0.0f;
    } 
#endif
    return v_score;
}

static void dump_ftr(uint8_t* ftr)
{
	for(int i=0; i<mf_model.ftr_len; i++) {
		printk("%02x", ftr[i]);
		if(i%64==63)printk("\r\n");
	}
	printk("\r\n");
	return;
}

static mf_err_t _flow_db_search(int8_t *ftr, uint8_t ir_or_rgb, uint32_t* vid, float *score)
{
    int v_id = -1;
    float v_score, v_score_max = 0.0f;

    //printk("face_feature_max:%d\r\n", face_feature_max);
	//原来的new搜索，使用新增的iterate接口可以实现相同效率
	//if(flash_get_saved_feature_new(&v_saved_feature, i) == 0)
	mf_facedb.iterate_init();
	dbf_item_t* item     = (dbf_item_t*)mf_facedb.item_buf;
	dbmeta_t* meta  = (dbmeta_t*)item->meta;\
	uint32_t _vid, _oft;
	//dump_ftr(ftr);printk("=====\r\n");
	while(mf_facedb.iterate(mf_facedb.item_buf, &_vid, &_oft)) {
		v_score = cal_ftr_score(meta, ftr, ir_or_rgb);
		//dump_ftr(meta->rgb);
		//printk("[_flow_db_search] vid=%d, oft=%d, score = %d\r\n", _vid, _oft, (int)v_score);
		if(v_score > v_score_max) {
			v_score_max = v_score;
			v_id = _vid;
			if(v_score_max > FR_SCORE_MAX)
				break;
		}
	}
	// printk("max score=%d, vid=%d\r\n", (int)v_score_max, v_id);
    *score = v_score_max;
	*vid = v_id;
    return MF_ERR_NONE;
}


//buf 32+1 bytes
mf_err_t _flow_v2v_db_hash(uint8_t* sha256)
{
    static uint8_t flag = 0;
    uint8_t feature[MAX_FTR_LEN], tmp[MAX_FTR_LEN];
    uint32_t i = 0, cnt = 0;

    flag = 0;
    cnt = 0;
	
	mf_facedb.iterate_init();
	dbf_item_t* item     = (dbf_item_t*)mf_facedb.item_buf;
	dbmeta_t* meta  = (dbmeta_t*)item->meta;
	uint32_t _vid, _oft;
	while(mf_facedb.iterate(mf_facedb.item_buf, &_vid, &_oft)) {
		if(meta->stat != 1) {
			memcpy(tmp, meta->ftr_rgb, mf_model.ftr_len);
		} else {
			printk("flash_get_face_info failed, no rgb feature\r\n");
            return MF_ERR_FACEDB_HASH;
		}
		
		if (flag == 0) {
			memcpy(feature, tmp, (mf_model.ftr_len));
			flag = 1;
		} else {
			for (uint16_t j = 0; j < (mf_model.ftr_len); j++)
			{
				feature[j] ^= tmp[j];
			}
		}
		cnt++;
	}
	
    if (cnt == 0) {
        printk("flash_get_saved_feature_sha256 cnt == 0\r\n");
        memset(sha256, 0xff, 32);
        sha256[32] = 0;
        return MF_ERR_NONE;
    }
    sha256_hard_calculate(feature, (mf_model.ftr_len), sha256);
    sha256[32] = 0;

#if 0
    printk("feature: \r\n");
    for (uint16_t i = 0; i < mf_model.ftr_len * 4; i++)
    {
        printk("%02x ", *(feature + i));
        if (i % 4 == 4)
            printk("\r\n");
    }
    printk("\r\n************************************\r\n");
#endif
#if 1
    for (uint16_t i = 0; i < 32; i++)
    {
        printk("%02X", sha256[i]);
    }
    printk("\r\n");
#endif
    return MF_ERR_NONE;
}

static mf_err_t _flow_v2v_init(mf_facecb_t* cb)
{	//目前直接使用了静态变量，没有申请内存
	mf_flow.cb = cb;
	// mf_facedb.ops_cb = _flow_v2v_update_allir;
	mf_model.db_search = _flow_db_search;
	return MF_ERR_NONE;
}
static void _flow_v2v_deinit(void)
{
	return;
}

/*****************************************************************************/
// Public Var 全局变量
/*****************************************************************************/
mf_flow_t mf_flow_vis2vis = {
	.init_flag = 0,
	.type      = MF_FLOW_DUALCAM_VIS2VIS,
	.enable    = 1,
	.kpubuf_cb_list  = {0},
	.rgbbuf_cb0_list = {0},
	.rgbbuf_cb1_list = {0},
	.init      = _flow_v2v_init,
	.deinit    = _flow_v2v_deinit,
	.loop      = _flow_v2v_loop,
	.hash      = _flow_v2v_db_hash,
	.reg_kpubuf_cb   = _reg_kpubuf_cb,
	.reg_rgbbuf_cb0  = _reg_rgbbuf_cb0,
	.reg_rgbbuf_cb1  = _reg_rgbbuf_cb1,
};

