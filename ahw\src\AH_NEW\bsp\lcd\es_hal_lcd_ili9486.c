#include "es_inc.h"

#if (ES_LCD_DRIVER_ILI9486 == ES_LCD_DRIVER_TYPE)
#include "es_lcd_tft_spi.h"


/* clang-format off */
#define NO_OPERATION            0x00
#define SOFTWARE_RESET          0x01
#define READ_ID                 0x04
#define READ_STATUS             0x09
#define READ_POWER_MODE         0x0A
#define READ_MADCTL             0x0B
#define READ_PIXEL_FORMAT       0x0C
#define READ_IMAGE_FORMAT       0x0D
#define READ_SIGNAL_MODE        0x0E
#define READ_SELT_DIAG_RESULT   0x0F
#define SLEEP_ON                0x10
#define SLEEP_OFF               0x11
#define PARTIAL_DISPALY_ON      0x12
#define NORMAL_DISPALY_ON       0x13
#define INVERSION_DISPALY_OFF   0x20
#define INVERSION_DISPALY_ON    0x21
#define GAMMA_SET               0x26
#define DISPALY_OFF             0x28
#define DISPALY_ON              0x29
#define HORIZONTAL_ADDRESS_SET  0x2A
#define VERTICAL_ADDRESS_SET    0x2B
#define MEMORY_WRITE            0x2C
#define COLOR_SET               0x2D
#define MEMORY_READ             0x2E
#define PARTIAL_AREA            0x30
#define VERTICAL_SCROL_DEFINE   0x33
#define TEAR_EFFECT_LINE_OFF    0x34
#define TEAR_EFFECT_LINE_ON     0x35
#define MEMORY_ACCESS_CTL       0x36
#define VERTICAL_SCROL_S_ADD    0x37
#define IDLE_MODE_OFF           0x38
#define IDLE_MODE_ON            0x39
#define PIXEL_FORMAT_SET        0x3A
#define WRITE_MEMORY_CONTINUE   0x3C
#define READ_MEMORY_CONTINUE    0x3E
#define SET_TEAR_SCANLINE       0x44
#define GET_SCANLINE            0x45
#define WRITE_BRIGHTNESS        0x51
#define READ_BRIGHTNESS         0x52
#define WRITE_CTRL_DISPALY      0x53
#define READ_CTRL_DISPALY       0x54
#define WRITE_BRIGHTNESS_CTL    0x55
#define READ_BRIGHTNESS_CTL     0x56
#define WRITE_MIN_BRIGHTNESS    0x5E
#define READ_MIN_BRIGHTNESS     0x5F
#define READ_ID1                0xDA
#define READ_ID2                0xDB
#define READ_ID3                0xDC
#define RGB_IF_SIGNAL_CTL       0xB0
#define NORMAL_FRAME_CTL        0xB1
#define IDLE_FRAME_CTL          0xB2
#define PARTIAL_FRAME_CTL       0xB3
#define INVERSION_CTL           0xB4
#define BLANK_PORCH_CTL         0xB5
#define DISPALY_FUNCTION_CTL    0xB6
#define ENTRY_MODE_SET          0xB7
#define BACKLIGHT_CTL1          0xB8
#define BACKLIGHT_CTL2          0xB9
#define BACKLIGHT_CTL3          0xBA
#define BACKLIGHT_CTL4          0xBB
#define BACKLIGHT_CTL5          0xBC
#define BACKLIGHT_CTL7          0xBE
#define BACKLIGHT_CTL8          0xBF
#define POWER_CTL1              0xC0
#define POWER_CTL2              0xC1
#define VCOM_CTL1               0xC5
#define VCOM_CTL2               0xC7
#define NV_MEMORY_WRITE         0xD0
#define NV_MEMORY_PROTECT_KEY   0xD1
#define NV_MEMORY_STATUS_READ   0xD2
#define READ_ID4                0xD3
#define POSITIVE_GAMMA_CORRECT  0xE0
#define NEGATIVE_GAMMA_CORRECT  0xE1
#define DIGITAL_GAMMA_CTL1      0xE2
#define DIGITAL_GAMMA_CTL2      0xE3
#define INTERFACE_CTL           0xF6
/* clang-format on */

static ES_U16 lcd_width = ES_LCD_WIDTH;
static ES_U16 lcd_height = ES_LCD_HEIGHT;


// static ES_BYTE pos_gamma_list[]={
// 0x0F, 0x1F, 0x1C, 0x0C, 0x0F, 0x08, 0x48, 0x98,0x37, 0x0A, 0x13, 0x04, 0x11, 0x0D, 0x00};
// static ES_BYTE neg_gamma_list[]={
// 0x0F, 0x32, 0x2E, 0x0B, 0x0D, 0x05, 0x47, 0x75,0x37, 0x06, 0x10, 0x03, 0x24, 0x20, 0x00};
// static ES_BYTE dig_gamma_list[]={
// 0x0F, 0x32, 0x2E, 0x0B, 0x0D, 0x05, 0x47, 0x75,0x37, 0x06, 0x10, 0x03, 0x24, 0x20, 0x00};

static ES_S32 es_hal_lcd_set_direction(ES_U8 dir)
{
    ES_U16 tmp = 0;
    dir |= 0x08;
    // dir = 0x08;

    if(dir & ES_LCD_DIR_XY_MASK) {
        if(lcd_width < lcd_height) {
            tmp = lcd_width;
            lcd_width = lcd_height;
            lcd_height = tmp;
        }
    } else {
        if(lcd_width > lcd_height) {
            tmp = lcd_width;
            lcd_width = lcd_height;
            lcd_height = tmp;
        }
    }

    // printk("dir:%02x, lcd_width:%d, lcd_height:%d\r\n", dir, lcd_width, lcd_height);
    es_tft_write_command(MEMORY_ACCESS_CTL);
    es_tft_write_byte((ES_BYTE *)&dir, 1);
    return ES_RET_SUCCESS;
}


static void _il9486_init_seq(void)
{
     //************* Start Initial Sequence **********//
    ES_BYTE t[15];

    es_tft_write_command(0XF1); /* Unk */
    t[0] = (0x36);
    t[1] = (0x04);
    t[2] = (0x00);
    t[3] = (0x3C);
    t[4] = (0X0F);
    t[5] = (0x8F);
    es_tft_write_byte(t, 6);

    es_tft_write_command(0XF2); /* Unk */
    t[0] = (0x18);
    t[1] = (0xA3);
    t[2] = (0x12);
    t[3] = (0x02);
    t[4] = (0XB2);
    t[5] = (0x12);
    t[6] = (0xFF);
    t[7] = (0x10);
    t[8] = (0x00);
    es_tft_write_byte(t, 9);

    es_tft_write_command(0XF8); /* Unk */
    t[0] = (0x21);
    t[1] = (0x04);
    es_tft_write_byte(t, 2);

    es_tft_write_command(0XF9); /* Unk */
    t[0] = (0x00);
    t[1] = (0x08);
    es_tft_write_byte(t, 2);

    es_tft_write_command(0x36); /* Memory Access Control */
    t[0] = (0x28);
    es_tft_write_byte(t, 1);

    es_tft_write_command(0xB4); /* Display Inversion Control */
    t[0] = (0x00);
    es_tft_write_byte(t, 1);

    es_tft_write_command(0xB6); /* Display Function Control */
    t[0] = (0x02);
    // t[1] = (0x22);
    es_tft_write_byte(t, 1);

    es_tft_write_command(0xC1); /* Power Control 2 */
    t[0] = (0x41);
    es_tft_write_byte(t, 1);
    
    es_tft_write_command(0xC5); /* Vcom Control */
    t[0] = (0x00);
    t[1] = (0x18);
    es_tft_write_byte(t, 2);

    es_tft_write_command(0xE0); /* Positive Gamma Control */
    t[0] = (0x0F);
    t[1] = (0x1F);
    t[2] = (0x1C);
    t[3] = (0x0C);
    t[4] = (0x0F);
    t[5] = (0x08);
    t[6] = (0x48);
    t[7] = (0x98);
    t[8] = (0x37);
    t[9] = (0x0A);
    t[10] = (0x13);
    t[11] = (0x04);
    t[12] = (0x11);
    t[13] = (0x0D);
    t[14] = (0x00);
    es_tft_write_byte(t, 15);

    es_tft_write_command(0xE1); /* Negative Gamma Control */
    t[0] = (0x0F);
    t[1] = (0x32);
    t[2] = (0x2E);
    t[3] = (0x0B);
    t[4] = (0x0D);
    t[5] = (0x05);
    t[6] = (0x47);
    t[7] = (0x75);
    t[8] = (0x37);
    t[9] = (0x06);
    t[10] = (0x10);
    t[11] = (0x03);
    t[12] = (0x24);
    t[13] = (0x20);
    t[14] = (0x00);
    es_tft_write_byte(t, 15);

    es_tft_write_command(0x3A); /* Interface Pixel Format */
    t[0] = (0x55);
    es_tft_write_byte(t, 1);

    es_tft_write_command(0x11); /* Sleep OUT */
    msleep(120);
    es_tft_write_command(0x29); /* Display ON */
}


static void ili9486_gpio_init(void)
{
    // backlight
    fpioa_set_function(ES_LCD_BL_PIN,   FUNC_GPIOHS0 + ES_LCD_BL_HS_NUM);
    gpiohs_set_drive_mode(ES_LCD_BL_HS_NUM, GPIO_DM_OUTPUT);
    gpiohs_set_pin(ES_LCD_BL_HS_NUM, 1);

    /* LCD IO*/
    fpioa_set_function(ES_LCD_RST_PIN, FUNC_GPIOHS0 + ES_LCD_RST_HS_NUM);
    fpioa_set_function(ES_LCD_DCX_PIN, FUNC_GPIOHS0 + ES_LCD_DCX_HS_NUM);
    fpioa_set_function(ES_LCD_CS_PIN, FUNC_SPI0_SS3);
    fpioa_set_function(ES_LCD_SCK_PIN, FUNC_SPI0_SCLK);

    fpioa_set_io_driving(ES_LCD_RST_PIN, FPIOA_DRIVING_15);
    fpioa_set_io_driving(ES_LCD_DCX_PIN, FPIOA_DRIVING_15);
    fpioa_set_io_driving(ES_LCD_CS_PIN, FPIOA_DRIVING_15);
    fpioa_set_io_driving(ES_LCD_SCK_PIN, FPIOA_DRIVING_15);

}

ES_S32 es_hal_lcd_init(ES_VOID)
{
    ili9486_gpio_init();
    es_tft_hard_init(15);
   _il9486_init_seq();
   printk("init lcd ili9486\r\n");

    es_hal_lcd_on();
    es_hal_lcd_set_direction(ES_LCD_DIR_PARAM);

    return ES_RET_SUCCESS;
}


ES_S32 es_hal_lcd_deinit(ES_VOID)
{
    /* 这里应该发送指令关闭屏幕，以及关闭屏幕的背光，但是我们基本用不上 */
    return ES_RET_SUCCESS;
}


ES_S32 es_hal_lcd_set_area(ES_U16 x, ES_U16 y, ES_U16 w, ES_U16 h)
{
    ES_BYTE data[4] = {0};

    // printk("x:%d, y:%d, w:%d, h:%d\r\n", x, y, w, h);

    data[0] = (ES_BYTE)(x >> 8);
    data[1] = (ES_BYTE)(x);
    data[2] = (ES_BYTE)((x + w - 1) >> 8);
    data[3] = (ES_BYTE)((x + w - 1));
#if (0 == ES_LCD_DIR)
    es_tft_write_command(HORIZONTAL_ADDRESS_SET); // 0x2A
#else
    es_tft_write_command(VERTICAL_ADDRESS_SET); // 0x2B
#endif
    es_tft_write_byte(data, 4);

    data[0] = (ES_BYTE)(y >> 8);
    data[1] = (ES_BYTE)(y);
    data[2] = (ES_BYTE)((y + h - 1) >> 8);
    data[3] = (ES_BYTE)((y + h - 1));
#if (0 == ES_LCD_DIR)
    es_tft_write_command(VERTICAL_ADDRESS_SET); // 0x2B
#else
    es_tft_write_command(HORIZONTAL_ADDRESS_SET); // 0x2A
#endif
    es_tft_write_byte(data, 4);

    es_tft_write_command(MEMORY_WRITE);

    return ES_RET_SUCCESS;
}

ES_S32 es_hal_lcd_clear(ES_U16 rgb565_color)
{
    uint32_t data = ((uint32_t)rgb565_color << 16) | (uint32_t)rgb565_color;

    es_hal_lcd_set_area(0, 0, lcd_width, lcd_height);
    es_tft_fill_data(&data, lcd_width * lcd_height / 2);
    // printk("lcd_width:%d, lcd_height:%d, rgb565_color:0x%04x\r\n", lcd_width, lcd_height, rgb565_color);
    return ES_RET_SUCCESS;
}

ES_S32 es_hal_lcd_draw_picture(ES_U16 x, ES_U16 y, ES_U16 w, ES_U16 h, const ES_BYTE *imamge)
{
    es_hal_lcd_set_area(x, y, w, h);
    es_tft_write_word((uint32_t*)imamge, w * h / 2, 0);
    return ES_RET_SUCCESS;
}


ES_U32 es_hal_lcd_get_width(ES_VOID)
{
    return lcd_width;
}

ES_U32 es_hal_lcd_get_height(ES_VOID)
{
    return lcd_height;
}

ES_S32 es_hal_lcd_on(ES_VOID)
{
    gpiohs_set_pin(ES_LCD_BL_HS_NUM, 0);
    return ES_RET_SUCCESS;
}

ES_S32 es_hal_lcd_standby(ES_VOID)
{
    fpioa_set_function(ES_LCD_BL_PIN, FUNC_TIMER0_TOGGLE1 + ES_LCD_PWDM_CHN_BL);
    pwm_init(ES_LCD_PWM_DEV_BL);
    pwm_set_frequency(ES_LCD_PWM_DEV_BL, ES_LCD_PWDM_CHN_BL, 1000, 0.2);
    pwm_set_enable(ES_LCD_PWM_DEV_BL, ES_LCD_PWDM_CHN_BL, 1);
    return ES_RET_SUCCESS;
}

#endif
