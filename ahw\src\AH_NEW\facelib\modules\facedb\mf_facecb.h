#ifndef _MF_FACECB_H
#define _MF_FACECB_H

#include <stdint.h>
#include "mf_model.h"

/*****************************************************************************/
// Types
/*****************************************************************************/
typedef struct
{//face_info, the i-th face, total n face in this frame this class
    void (*detect)(face_obj_t *face_info, uint8_t i, uint8_t n);
	void (*stranger)(face_obj_t *face_info, uint8_t i, uint8_t n);
    void (*fake)(face_obj_t *face_info, uint8_t i, uint8_t n);
    void (*pass)(face_obj_t *face_info, uint8_t i, uint8_t n);
    void (*pre_display)(void);
	void (*post_display)(void);
} mf_facecb_t;

/*****************************************************************************/
// Enums
/*****************************************************************************/


/*****************************************************************************/
// Functions
/*****************************************************************************/


/*****************************************************************************/
// Vars
/*****************************************************************************/

#endif

