#include "facelib_inc.h"
#include "es_inc.h"

#pragma GCC diagnostic ignored "-Wunused-function"
#pragma GCC diagnostic ignored "-Wunused-variable"

#define CAM_SCL_PIN                     (41)
#define CAM_SDA_650_PIN                 (42)
#define CAM_SDA_850_PIN                 (40)
#define CAM_PCLK_PIN               		(47)
#define CAM_XCLK_PIN               		(46)
#define CAM_HSYNC_PIN                	(45)
#define CAM_PWDN_PIN                 	(44)
#define CAM_VSYNC_PIN                 	(43)
#define CAM_RST_PIN                		(25)


/*****************************************************************************/
// Function definitions
/*****************************************************************************/
extern uint8_t gc0328_dual_rd_reg(uint8_t num, uint8_t reg);
extern void gc0328_dual_wr_reg(uint8_t num, uint8_t reg, uint8_t data);

extern void open_gc0328_850();
extern void open_gc0328_650();

extern int gc0328_dual_set_hmirror(uint8_t val);
extern int gc0328_dual_set_vflip(uint8_t val);
extern int gc0328_dual_modify_reg(uint8_t reg_data[][2]);

extern int gc0328_dual_read_id(uint16_t *manuf_id, uint16_t *device_id);

extern int gc0328_dual_config(void);

/*****************************************************************************/
// Private Var 局部变量
/*****************************************************************************/

/*****************************************************************************/
// Driver 底层驱动
/*****************************************************************************/
#define GC0328_ADDR (0x42)
#define I2C_SCL_650 (FUNC_I2C1_SCLK)
#define I2C_SDA_650 (FUNC_I2C1_SDA)
#define I2C_SCL_850 (FUNC_I2C0_SCLK)
#define I2C_SDA_850 (FUNC_I2C0_SDA)

void gc0328_dual_init_sccb(int num) // 1 650, 0 850
{
    if(num) {
        fpioa_set_function(CAM_SDA_650_PIN, I2C_SDA_650); // 650
        fpioa_set_function(CAM_SCL_PIN, I2C_SCL_650);
    } else {
        fpioa_set_function(CAM_SDA_850_PIN, I2C_SDA_850); // 850
        fpioa_set_function(CAM_SCL_PIN, I2C_SCL_850);
    }
    i2c_init(num, GC0328_ADDR >> 1, 7, 1500000);
}

/*
摄像头内存申请
*/
static mf_err_t _mf_cam_init_buf_gc0328_dual(void)
{
	mf_cam._kpu_image_original = (uint8_t *)es_malloc(MF_CAM_W * MF_CAM_H*3*2+255);
	mf_cam.kpu_image= (uint8_t (*)[MF_CAM_W * MF_CAM_H*3])(((uintptr_t)mf_cam._kpu_image_original+255)&(~255));
	if(mf_cam.kpu_image == NULL) return MF_ERR_CAM_NOMEM;
	
	mf_cam._rgb_image_original = (uint8_t *)es_malloc(MF_CAM_W * MF_CAM_H*2*2+255);
	mf_cam.rgb_image = (uint8_t (*)[MF_CAM_W * MF_CAM_H*2])(((uintptr_t)mf_cam._rgb_image_original+255)&(~255));
	if(mf_cam.rgb_image == NULL) {
		es_free(mf_cam._kpu_image_original);
		return MF_ERR_CAM_NOMEM;
	}
	return MF_ERR_NONE;
}

static void _mf_cam_deinit_buf_gc0328_dual(void)
{
	es_free(mf_cam._kpu_image_original);
	es_free(mf_cam._rgb_image_original);
	return;
}

/*
摄像头设备初始化
*/
static mf_err_t _mf_cam_init_dev_gc0328_dual(void)
{
	mf_err_t err = MF_ERR_NONE;
	/* DVP IO*/
    fpioa_set_function(CAM_PCLK_PIN,  FUNC_CMOS_PCLK);
    fpioa_set_function(CAM_XCLK_PIN,  FUNC_CMOS_XCLK);
    fpioa_set_function(CAM_HSYNC_PIN, FUNC_CMOS_HREF);
    fpioa_set_function(CAM_PWDN_PIN,  FUNC_CMOS_PWDN);
    fpioa_set_function(CAM_VSYNC_PIN, FUNC_CMOS_VSYNC);
	fpioa_set_function(CAM_RST_PIN,   FUNC_CMOS_RST);
	/* change to 1.8V */
	sysctl_set_spi0_dvp_data(1);
	
	// DVP init 
    mf_dvp_init(8);
    mf_dvp_set_xclk_rate(36 * 1000 * 1000);

    dvp_enable_burst();
    dvp_set_output_enable(0, 1);
    dvp_set_output_enable(1, 1);
    dvp_set_image_format(DVP_CFG_RGB_FORMAT);
    dvp_set_image_size(MF_CAM_W, MF_CAM_H);
	
	//基于双缓冲的地址设置
    dvp_set_ai_addr(_IOMEM_ADDR(mf_cam.kpu_image[1]),
        _IOMEM_ADDR(mf_cam.kpu_image[1]) + MF_CAM_W * MF_CAM_H,
        _IOMEM_ADDR(mf_cam.kpu_image[1]) + MF_CAM_W * MF_CAM_H * 2);
    dvp_set_display_addr((uint32_t)mf_cam.rgb_image[1]);

    dvp_config_interrupt(DVP_CFG_START_INT_ENABLE | DVP_CFG_FINISH_INT_ENABLE, 0);
    dvp_disable_auto();

    // DVP interrupt config 
    plic_set_priority(IRQN_DVP_INTERRUPT, 1);
    plic_irq_register(IRQN_DVP_INTERRUPT, dvp_irq_gc0328_dual, NULL);
    plic_irq_enable(IRQN_DVP_INTERRUPT);

	mf_cam.set_hmirror(ES_CAM_HMIRROR);
	mf_cam.set_vflip(ES_CAM_VFLIP);
	uint8_t reg_data[3][2]={{0x31, 0xff},{0x32, 0xff},{0,0}}; //0x31高4位，0x32低8位
	reg_data[0][1] = (mf_cam.exp_time>>8) & 0xff;
	reg_data[1][1] = mf_cam.exp_time & 0xff;
	mf_cam.set_reg(reg_data);	//设置曝光
    err = mf_cam.config();
	if(err != MF_ERR_NONE) return err;

    dvp_clear_interrupt(DVP_STS_FRAME_START | DVP_STS_FRAME_FINISH);
    dvp_config_interrupt(DVP_CFG_START_INT_ENABLE | DVP_CFG_FINISH_INT_ENABLE, 1);
	
	return err;
}

static void _mf_cam_deinit_dev_gc0328_dual(void)
{	
	//禁止中断，停止捕捉
    dvp_config_interrupt(DVP_CFG_START_INT_ENABLE | DVP_CFG_FINISH_INT_ENABLE, 0);
	return;
}

#define YLIST_LEN (4)

static uint8_t night_ylist[YLIST_LEN] = {0xff, 0xff, 0xff, 0xff};

static void _gc0328_dual_night_update(void)
{
    static int night_index = 0;
	
    gc0328_dual_wr_reg(1, 0xfe, 0x01);
    night_ylist[night_index] = gc0328_dual_rd_reg(1, 0x40);

    night_index = (night_index + 1) % YLIST_LEN;

    return;
}

static uint8_t _gc0328_dual_night_judge(uint8_t gate)
{
    int sum = 0;
    for(int i = 0; i < YLIST_LEN; i++)
    {
        sum += night_ylist[i];
    }
    sum = sum / YLIST_LEN;

    return (sum <= gate) ? 1 : 0;
}

static void _gc0328_dual_dvp_to_650(void)
{
    dvp_set_output_enable(0, 0); //disable to ai
    dvp_set_output_enable(1, 1); //enable to lcd
    open_gc0328_650();

    mf_cam.index = 1;
    return;
}

static void _gc0328_dual_dvp_to_850(void)
{
    dvp_set_output_enable(0, 1); //enable to ai
    dvp_set_output_enable(1, 0); //disable to lcd
    open_gc0328_850();

    mf_cam.index = 0;
    return;
}

/*
摄像头初始化函数
1. 申请摄像头缓存
	a. 双摄(RGB+KPU)*2 = (75*2+75*3)*2=750KB
	b. 单摄 TODO
2. 初始化摄像头配置
input:
	cam_type:	摄像头类型
	dir:		横竖屏摄像头，0横，1竖
	exp_time:	暗光曝光时间(0x05ff~0x0fff)
*/
static mf_err_t _gc0328_dual_init(uint8_t dir, uint8_t exp_time)
{
	mf_err_t err = MF_ERR_NONE;
	
	mf_cam.dir = dir;
	exp_time = exp_time<0x5f ? 0x5f : exp_time;
	mf_cam.exp_time = ((uint16_t)exp_time)<<4;
	//init buf
	err = mf_cam._init_buf();
	if(err != MF_ERR_NONE) goto err_init_buf;
	//init device
	err = mf_cam._init_dev();
	if(err != MF_ERR_NONE) goto err_init_dev;
	
	mf_cam.cam_type = MF_CAM_GC0328_DUAL;
	mf_cam.init_flag = 1;
	return MF_ERR_NONE;
	

//handle error, reverse order
err_init_dev:	//free buf
	mf_cam._deinit_buf();
err_init_buf:
	mf_cam.init_flag = 0;
	printk("Camera Module Init Failed!\r\n");
	return err;
}


/*
摄像头清理函数

1. 停止摄像头捕捉（才能释放内存）
2. 释放摄像头缓存
*/
static void _gc0328_dual_deinit(void)
{
	mf_cam._deinit_dev();
	mf_cam._deinit_buf();
	mf_cam.init_flag = 0;
	return;
}

static void _gc0328_dual_stop(void)
{
    dvp_set_output_enable(0, 0);
    dvp_set_output_enable(1, 0);

    dvp_config_interrupt(DVP_CFG_START_INT_ENABLE | DVP_CFG_FINISH_INT_ENABLE, 0);
	return;
}

static void _gc0328_dual_start(void)
{
    dvp_set_output_enable(0, 1);
    dvp_set_output_enable(1, 1);

    dvp_clear_interrupt(DVP_STS_FRAME_START | DVP_STS_FRAME_FINISH);
    dvp_config_interrupt(DVP_CFG_START_INT_ENABLE | DVP_CFG_FINISH_INT_ENABLE, 1);
	return;
}

/*****************************************************************************/
// Public Var 全局变量
/*****************************************************************************/

mf_cam_t mf_dualcam_gc0328 = {
	.init_flag     = 0,
	.kpu_buf_index = 0,
	.rgb_buf_index = 0,
	.index         = 0,
	._init_buf     = _mf_cam_init_buf_gc0328_dual,
	._deinit_buf   = _mf_cam_deinit_buf_gc0328_dual,
	._init_dev     = _mf_cam_init_dev_gc0328_dual,
	._deinit_dev   = _mf_cam_deinit_dev_gc0328_dual,
	.config        = gc0328_dual_config,
	.read_id       = gc0328_dual_read_id,
	.set_hmirror   = gc0328_dual_set_hmirror,
	.set_vflip     = gc0328_dual_set_vflip,
	.set_reg       = gc0328_dual_modify_reg,
	.switch_650    = _gc0328_dual_dvp_to_650,
	.switch_850    = _gc0328_dual_dvp_to_850,
	.update_lum    = _gc0328_dual_night_update,
	.is_night      = _gc0328_dual_night_judge,
	.lock_rgb      = rgb_lock_buf,
	.unlock_rgb    = rgb_unlock_buf,
	.lock_kpu      = kpu_lock_buf,
	.unlock_kpu    = kpu_unlock_buf,
	.first_run     = cam_dualbuf_first_run,
	.lock_sync     = cam_dualbuf_lock_sync,
	.lock_buf      = cam_dualbuf_lock_buf,
	//.unlock_buf    = cam_dualbuf_unlock_buf,
	
	.init          = _gc0328_dual_init,
	.deinit        = _gc0328_dual_deinit,
	.stop          = _gc0328_dual_stop,
	.start         = _gc0328_dual_start,
};
