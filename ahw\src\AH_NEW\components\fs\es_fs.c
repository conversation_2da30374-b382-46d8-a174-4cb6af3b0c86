/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_fs.c
** bef: define the interface for file system.
** auth: lines<<EMAIL>>
** create on 2021.12.01 
*/

#include "es_inc.h"

#if ES_FS_ENABLE

// #define ES_FS_DEBUG
#ifdef ES_FS_DEBUG
#define es_fs_debug es_log_info
#define es_fs_error es_log_error
#else
#define es_fs_debug(...)
#define es_fs_error(...)
#endif

#define FS_TEST_ENABLE      (0)
#define FS_TEST_FILE_NAME   ("pic.bin")
// #define FS_TEST_FILE_NAME   ("a.bin")

#define FS_WORK_SIZE        (4096)
#define FS_PATH             ("0:")

static FATFS sg_fatfs;

#if FS_TEST_ENABLE
ES_S32 es_fs_test(ES_VOID)
{
    FIL fp;
    ES_CHAR fname[32] = {0};
    ES_BYTE read_buf[1024] = {};
    FRESULT res;
    ES_U32 br;

    snprintf(fname, 32, "%s%s", FS_PATH, FS_TEST_FILE_NAME);
    res = f_open(&fp, fname, FA_READ|FA_WRITE);
    if (FR_OK != res) {
        es_fs_error("open %s faile", fname);
        return ES_RET_FAILURE;
    }

    res = f_read(&fp, read_buf, 1024, &br);
    if (FR_OK != res) {
        es_fs_error("read %s faile", fname);
        f_close(&fp);
        return ES_RET_FAILURE;
    }
    // es_fs_debug("%s\r\n%s", fname, read_buf);
    es_fs_debug("%s", fname);
    es_log_dump_hex(read_buf, br);

    f_close(&fp);

    es_fs_debug("fs test success");
    return ES_RET_SUCCESS;
}
#endif

ES_S32 es_fs_init(ES_VOID)
{
    FRESULT res;
    ES_BYTE work[FS_WORK_SIZE] = {0};

    res = f_mount(&sg_fatfs, FS_PATH, 1);
    if (FR_NO_FILESYSTEM == res) {
        // format disk
        es_fs_debug("format fatfs");
        res = f_mkfs(FS_PATH, ES_NULL, work, FS_WORK_SIZE);
        if (FR_OK != res) {
            es_fs_error("fs mount error(%d)", res);
            return ES_RET_FAILURE;
        }
    } else if (FR_OK != res) {
        es_fs_error("fs mount error(%d)", res);
        return ES_RET_FAILURE;
    }
    es_fs_debug("fs mount success");
#if FS_TEST_ENABLE
    es_fs_test();
#endif
    return ES_RET_SUCCESS;
}

#endif

