/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_voice.c
** bef: define the interface for voice.
** auth: lines<<EMAIL>>
** create on 2022.01.08 
*/

#include "es_inc.h"

#if ES_AUDIO_ENABLE

// #define ES_VOICE_DEBUG
#ifdef ES_VOICE_DEBUG
#define es_voice_debug es_log_info
#define es_voice_error es_log_error
#else
#define es_voice_debug(...)
#define es_voice_error(...)
#endif

#define VOICE_01_FACE_PASS_ADDR (0x200000)
#define VOICE_01_FACE_PASS_SIZE (9660)

#define VOICE_02_CAR_BOOT_ADDR  (0x2025bc)
#define VOICE_02_CAR_BOOT_SIZE  (49232)

#define VOICE_03_CAR_YEAR_EXPIRE_SOON_ADDR      (0x20e60c)
#define VOICE_03_CAR_YEAR_EXPIRE_SOON_SIZE      (24544)

#define VOICE_04_LOOK_AND_CHECK_ADDR    (0x2145ec)
#define VOICE_04_LOOK_AND_CHECK_SIZE    (21212)

#define VOICE_05_NOT_AUTH_AND_LEAVE_ADDR        (0x2198c8)
#define VOICE_05_NOT_AUTH_AND_LEAVE_SIZE        (19824)

#define VOICE_06_AUTH_PASS_ADDR (0x21e638)
#define VOICE_06_AUTH_PASS_SIZE (18388)

#define VOICE_07_UPDATE_DRIVER_INFO_OK_ADDR     (0x222e0c)
#define VOICE_07_UPDATE_DRIVER_INFO_OK_SIZE     (16056)

#define VOICE_08_SET_INFO_OK_ADDR       (0x226cc4)
#define VOICE_08_SET_INFO_OK_SIZE       (11872)

#define VOICE_09_RESET_INFO_OK_ADDR     (0x229b24)
#define VOICE_09_RESET_INFO_OK_SIZE     (11916)

#define VOICE_10_UNLOCK_CAR_OK_ADDR     (0x22c9b0)
#define VOICE_10_UNLOCK_CAR_OK_SIZE     (8772)

#define VOICE_11_LOCK_CAR_OK_ADDR       (0x22ebf4)
#define VOICE_11_LOCK_CAR_OK_SIZE       (9652)

#define VOICE_12_DRIVER_LICENSE_EXPIRE_ADDR     (0x2311a8)
#define VOICE_12_DRIVER_LICENSE_EXPIRE_SIZE     (11248)

#define VOICE_13_CAR_EXPIRE_AFTER_MONTH_ADDR    (0x233d98)
#define VOICE_13_CAR_EXPIRE_AFTER_MONTH_SIZE    (22452)

#define VOICE_14_EXPIRE_AND_LOCKED_ADDR (0x23954c)
#define VOICE_14_EXPIRE_AND_LOCKED_SIZE (17644)

#define VOICE_15_EXCEPTION_AND_CONTACT_ADDR     (0x23da38)
#define VOICE_15_EXCEPTION_AND_CONTACT_SIZE     (18104)

#define VOICE_16_CAR_LOCKED_ADDR        (0x2420f0)
#define VOICE_16_CAR_LOCKED_SIZE        (10184)

#define VOICE_17_DO_NOT_USE_PHONE_ADDR  (0x2448b8)
#define VOICE_17_DO_NOT_USE_PHONE_SIZE  (9636)

#define VOICE_18_NO_SMOKING_ADDR        (0x246e5c)
#define VOICE_18_NO_SMOKING_SIZE        (7044)

#define VOICE_19_FATIGUE_DRIVING_ADDR   (0x2489e0)
#define VOICE_19_FATIGUE_DRIVING_SIZE   (9960)

#define VOICE_20_SAFETY_BELT_ADDR       (0x24b0c8)
#define VOICE_20_SAFETY_BELT_SIZE       (11936)

#define VOICE_21_OVER_SPEED_ADDR        (0x24df68)
#define VOICE_21_OVER_SPEED_SIZE        (25024)

#define VOICE_22_POSTURE_DETECTION_ADDR (0x254128)
#define VOICE_22_POSTURE_DETECTION_SIZE (24448)

typedef struct {
    ES_U32 addr;
    ES_U16 size;
    ES_U16 type;
} es_voice_flash_info_t;


static es_voice_flash_info_t voice_map[] = {
    {VOICE_01_FACE_PASS_ADDR, VOICE_01_FACE_PASS_SIZE, ES_VOICE_01_FACE_PASS},
    {VOICE_02_CAR_BOOT_ADDR, VOICE_02_CAR_BOOT_SIZE, ES_VOICE_02_CAR_BOOT},
    {VOICE_03_CAR_YEAR_EXPIRE_SOON_ADDR, VOICE_03_CAR_YEAR_EXPIRE_SOON_SIZE, ES_VOICE_03_CAR_YEAR_EXPIRE_SOON},
    {VOICE_04_LOOK_AND_CHECK_ADDR, VOICE_04_LOOK_AND_CHECK_SIZE, ES_VOICE_04_LOOK_AND_CHECK},
    {VOICE_05_NOT_AUTH_AND_LEAVE_ADDR, VOICE_05_NOT_AUTH_AND_LEAVE_SIZE, ES_VOICE_05_NOT_AUTH_AND_LEAVE},
    {VOICE_06_AUTH_PASS_ADDR, VOICE_06_AUTH_PASS_SIZE, ES_VOICE_06_AUTH_PASS},
    {VOICE_07_UPDATE_DRIVER_INFO_OK_ADDR, VOICE_07_UPDATE_DRIVER_INFO_OK_SIZE, ES_VOICE_07_UPDATE_DRIVER_INFO_OK},
    {VOICE_08_SET_INFO_OK_ADDR, VOICE_08_SET_INFO_OK_SIZE, ES_VOICE_08_SET_INFO_OK},
    {VOICE_09_RESET_INFO_OK_ADDR, VOICE_09_RESET_INFO_OK_SIZE, ES_VOICE_09_RESET_INFO_OK},
    {VOICE_10_UNLOCK_CAR_OK_ADDR, VOICE_10_UNLOCK_CAR_OK_SIZE, ES_VOICE_10_UNLOCK_CAR_OK},
    {VOICE_11_LOCK_CAR_OK_ADDR, VOICE_11_LOCK_CAR_OK_SIZE, ES_VOICE_11_LOCK_CAR_OK},
    {VOICE_12_DRIVER_LICENSE_EXPIRE_ADDR, VOICE_12_DRIVER_LICENSE_EXPIRE_SIZE, ES_VOICE_12_DRIVER_LICENSE_EXPIRE},
    {VOICE_13_CAR_EXPIRE_AFTER_MONTH_ADDR, VOICE_13_CAR_EXPIRE_AFTER_MONTH_SIZE, ES_VOICE_13_CAR_EXPIRE_AFTER_MONTH},
    {VOICE_14_EXPIRE_AND_LOCKED_ADDR, VOICE_14_EXPIRE_AND_LOCKED_SIZE, ES_VOICE_14_EXPIRE_AND_LOCKED},
    {VOICE_15_EXCEPTION_AND_CONTACT_ADDR, VOICE_15_EXCEPTION_AND_CONTACT_SIZE, ES_VOICE_15_EXCEPTION_AND_CONTACT},
    {VOICE_16_CAR_LOCKED_ADDR, VOICE_16_CAR_LOCKED_SIZE, ES_VOICE_16_CAR_LOCKED},
    {VOICE_17_DO_NOT_USE_PHONE_ADDR, VOICE_17_DO_NOT_USE_PHONE_SIZE, ES_VOICE_17_DO_NOT_USE_PHONE},
    {VOICE_18_NO_SMOKING_ADDR, VOICE_18_NO_SMOKING_SIZE, ES_VOICE_18_NO_SMOKING},
    {VOICE_19_FATIGUE_DRIVING_ADDR, VOICE_19_FATIGUE_DRIVING_SIZE, ES_VOICE_19_FATIGUE_DRIVING},
    {VOICE_20_SAFETY_BELT_ADDR, VOICE_20_SAFETY_BELT_SIZE, ES_VOICE_20_SAFETY_BELT},
    {VOICE_21_OVER_SPEED_ADDR, VOICE_21_OVER_SPEED_SIZE, ES_VOICE_21_OVER_SPEED},
    {VOICE_22_POSTURE_DETECTION_ADDR, VOICE_22_POSTURE_DETECTION_SIZE, ES_VOICE_22_POSTURE_DETECTION},
};

ES_S32 es_voice_init(ES_VOID)
{
    return ES_RET_SUCCESS;
}

ES_S32 es_voice_play(es_voice_type_t type)
{
    ES_U32 i;

    for (i = 0; i < ES_ARRAY_SIZE(voice_map); i++) {
        if (type == voice_map[i].type) {
            return es_audio_play_flash(voice_map[i].addr, voice_map[i].size);
        }
    }

    return ES_RET_FAILURE;
}


#endif
