/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_utils.h
** bef: define the interface for configure 
** auth: lines<<EMAIL>>
** create on 2020.06.27 
*/

#ifndef _ES_BRD_CONFIG_N5_H_
#define _ES_BRD_CONFIG_N5_H_

#ifdef __cplusplus 
extern "C" { 
#endif

// Col is 96
/////////////////////////////////////// Temperature Module ////////////////////////////////////
#define ES_TEMPERATURE_MODULE_ENABLE        (0)
#define ES_TEMPERATURE_READ_TIMEOUT_MS      (3*1000)


/////////////////////////////////////// Wireless 433 Module ///////////////////////////////////
#define ES_433_MODULE_ENABLE                (1)
#define ES_433_ENABLE_DINGDONG_DOORBELL     (1)
#if ES_433_MODULE_ENABLE
#define ES_433_MODULE_SEND_PIN              (28)
#define ES_433_MODULE_READ_PIN              (27)
#define ES_433_MODULE_SEND_HS_NUM           (28)
#define ES_433_MODULE_READ_HS_NUM           (13)
#endif


/////////////////////////////////////// door app //////////////////////////////////////////////
#define ES_APP_MODULE_SBD_ENABLE            (0)
#define ES_APP_DOOR_JWZH_ENABLE             (0)


/////////////////////////////////////// Cloud Protocol ////////////////////////////////////////
#define ES_CLOUD_PROTOCOL_MODULE_ENABLE     (1)


/////////////////////////////////////// Motor Module //////////////////////////////////////////
#define ES_MOTOR_MODULE_ENABLE              (0)
#if ES_MOTOR_MODULE_ENABLE
#define ES_MOTOR_AN1_GPIO_PIN               (27)
#define ES_MOTOR_AN2_GPIO_PIN               (28)
#define ES_MOTOR_AN1_HS_NUM                 (29)
#define ES_MOTOR_AN2_HS_NUM                 (30)
#endif


/////////////////////////////////////// NFC Module ////////////////////////////////////////////
#define ES_NFC_MODULE_ENABLE                (1)
#define ES_NFC_CHIP_NONE                    (0)
#define ES_NFC_CHIP_WS1850S                 (1)
#define ES_NFC_ID_DATA_LEN                  (10)
#define ES_NFC_GPIO_IRQ_PIN                 (20)
#define ES_NFC_GPIO_RST_PIN                 (19)
#define ES_NFC_GPIO_LED_PIN                 (0)
#define ES_NFC_GPIO_IRQ_HS_NUM              (29)
#define ES_NFC_GPIO_RST_HS_NUM              (30)
#define ES_NFC_GPIO_LED_HS_NUM              (11)
#if ES_NFC_MODULE_ENABLE
#define ES_NFC_CHIP_TYPE                    (ES_NFC_CHIP_WS1850S)
#define ES_NFC_I2C_READ_ADDR                (0x28)
#define ES_NFC_I2C_WRITE_ADDR               (0x28)
#define ES_NFC_I2C_SPEED                    (100 * 1000)
#define WS1850S_ENABLE_GPIO_RST             (1)
#else
#define ES_NFC_CHIP_TYPE                    (ES_NFC_CHIP_NONE)
#endif/*  */


/////////////////////////////////////// BLE Module ////////////////////////////////////////////
#define ES_BLE_MODULE_ENABLE                (1)

#define ES_BLE_HAL_WS8100                   (1) /* WS8100 */
#define ES_BLE_HAL_JWZH                     (2) /* JWZH module. */
#define ES_BLE_HAL_571                      (3)

#if ES_BLE_MODULE_ENABLE
#define ES_BLE_HAL_TYPE                     (ES_BLE_HAL_571)
#else
#define ES_BLE_HAL_TYPE                     (0)
#endif

#define ES_BLE_UART_ID                      (ES_UART_ID_1)

#define ES_BLE_PAYLOAD_GPIO_PIN             (26)
#define ES_BLE_PAYLOAD_GPIO_HS_NUM          (29)



/////////////////////////////////////// Flash Module ///////////////////////////////////////////
#define ES_FLASH_MODULE_ENABLE              (1)

#define ES_FLASH_CFG_ADDR                   (0xFE700) // 0x1000000(16M) - 0x19000(100K)
#define ES_FLASH_CFG_LEN                    (1024*100)  // 100K

#define ES_FLASH_MAGIC_HEAD                 (0x12345678)
#define ES_FLASH_BLE_MAC_COUNT              (10)
#define ES_FLASH_NFC_COUNT                  (1000)

/////////////////////////////////////// test module ///////////////////////////////////////////
#define ES_TEST_MODULE_ENABLE               (0)


/////////////////////////////////////// Redefine Wechat config ////////////////////////////////
#if (0 == CONFIG_ENABLE_WIFI)
#undef CONFIG_ENABLE_EC200X
#define CONFIG_ENABLE_EC200X                (0)
#undef CONFIG_ENABLE_WIFI
#define CONFIG_ENABLE_WIFI                  (1)
#undef CONFIG_ENABLE_ETH5500
#define CONFIG_ENABLE_ETH5500				(0)
#endif

#ifdef BOARD_TYPE
#undef BOARD_TYPE
#define BOARD_TYPE                          (BOARD_MF5V)
#else
#define BOARD_TYPE                          (BOARD_MF5V)
#endif


#ifdef __cplusplus 
}
#endif
#endif