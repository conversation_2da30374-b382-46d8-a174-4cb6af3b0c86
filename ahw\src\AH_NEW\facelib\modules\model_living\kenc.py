#!/usr/bin/python3
# -*- coding: utf-8 -*-  
# kmodel stamp

import os
import sys
import struct
from k210_kpu import *

def kl_invalid_init(self, bin):
    return
def kl_add_init(self, bin):
    return
def kl_quantized_add_init(self, bin):
    return
def kl_global_max_pool2d_init(self, bin):
    return
def kl_quantized_global_max_pool2d_init(self, bin):
    return
def kl_global_average_pool2d_init(self, bin):
    arg_bin = bin[0:kgap2d_layer.size]
    (self.flags, self.main_mem_in_address, 
    self.main_mem_out_address, self.kernel_size, 
    self.channels) = \
    kgap2d_layer.unpack(arg_bin)
    print("GAP2D layer: kernel_size=%d, channels=%d"%(self.kernel_size, self.channels))
    
    return
def kl_quantized_global_average_pool2d_init(self, bin):
    return
def kl_max_pool2d_init(self, bin):
    return
def kl_quantized_max_pool2d_init(self, bin):
    return
def kl_average_pool2d_init(self, bin):
    return
def kl_quantized_average_pool2d_init(self, bin):
    return
def kl_quantize_init(self, bin):
    return
def kl_dequantize_init(self, bin):
    arg_bin = bin[0:kdeq_layer.size]
    (_,_,_,_,self.scale, self.bias) = kdeq_layer.unpack(arg_bin)
    print("scale=%f, bias=%f"%(self.scale, self.bias))
    return
def kl_requantize_init(self, bin):
    return
def kl_l2_normalization_init(self, bin):
    return
def kl_softmax_init(self, bin):
    return
def kl_concat_init(self, bin):
    return
def kl_quantized_concat_init(self, bin):
    return
def kl_fully_connected_init(self, bin):
    print("######################")
    arg_bin = bin[0:kfc_layer.size]
    (self.flags, self.main_mem_in_address, self.main_mem_out_address,
    self.in_channels, self.out_channels, 
    self.act) = \
    kfc_layer.unpack(arg_bin)
    print("main_mem_in_address=0x%x, main_mem_out_address=0x%x, in_channels=%d, out_channels=%d, atc=%d"%(self.main_mem_in_address, self.main_mem_out_address, self.in_channels, self.out_channels, self.act))
   
    return
def kl_quantized_fully_connected_init(self, bin):
    return
def kl_tensorflow_flatten_init(self, bin):
    return
def kl_quantized_tensorflow_flatten_init(self, bin):
    return
    

        
        
class conv_layer_para:
    def __init__(self, bin, oft):
        self.oft = oft    
        self.size = klayer_arg.size
        arg_bin = bin[self.oft:self.oft+self.size]
        tmp=[0]*12;
        (tmp[0],tmp[1],tmp[2],tmp[3],
        tmp[4],tmp[5],tmp[6],tmp[7],
        tmp[8],tmp[9],tmp[10],tmp[11])= \
        klayer_arg.unpack(arg_bin)
        self.interrupt_enabe = interrupt_enabe(tmp[0])
        self.image_addr = image_addr(tmp[1])
        self.image_channel_num = image_channel_num(tmp[2])
        self.image_size = image_size(tmp[3])
        self.kernel_pool_type_cfg = kernel_pool_type_cfg(tmp[4])
        self.kernel_load_cfg = kernel_load_cfg(tmp[5])
        self.kernel_offset = kernel_offset(tmp[6])
        self.kernel_calc_type_cfg = kernel_calc_type_cfg(tmp[7])
        self.write_back_cfg = write_back_cfg(tmp[8])
        self.conv_value = conv_value(tmp[9])
        self.conv_value2 = conv_value2(tmp[10])
        self.dma_parameter = dma_parameter(tmp[11])
       
        self.image_size.dump()
        return

class act_para:
    def __init__(self, val, bin, oft):
        self.bin = bin
        self.oft = oft
        self.shift_number = val&255;            #8
        self.y_mul = (val>>8)&(0xffff);            #16
        self.x_start = (val>>24)&(0xfffffffff);    #36
    def val(self):
        data = self.shift_number | (self.y_mul<<8) | (self.x_start<<24)
        return data
    def restruct(self):
        data = self.shift_number | (self.y_mul<<8) | (self.x_start<<24)
        #print("0x%X"%data)
        data = bytearray(data.to_bytes(8,'little'))
        self.bin[self.oft:self.oft+8]=data
        return data
    
class act_table:
    def __init__(self, bin, oft):
        self.oft = oft    
        self.size = kact_table.size
        act_bin = bin[self.oft:self.oft+self.size]
        self.act_para=[]
        self.act_para_bias=[]
        tmp = [0]*18
        (tmp[0],tmp[1],tmp[2],tmp[3],
        tmp[4],tmp[5],tmp[6],tmp[7],
        tmp[8],tmp[9],tmp[10],tmp[11],
        tmp[12],tmp[13],tmp[14],tmp[15],
        tmp[16],tmp[17])=kact_table.unpack(act_bin)
        for i in range(16):
            self.act_para.insert(i,act_para(tmp[i], bin, oft+8*i))
            if i < 8:
                bais = (tmp[16]>>(i*8))&0xff
            else :
                bais = (tmp[17]>>((i-8)*8))&0xff
            self.act_para_bias.insert(i, bais)
        return
    def dump(self):
        for i in range(16):
            print("act[%d]:data=0x%015x, shift:%2d, y_mul:%4d, x_start:0x%09x, bias:%d"%(i,self.act_para[i].val(), self.act_para[i].shift_number,self.act_para[i].y_mul,self.act_para[i].x_start, self.act_para_bias[i]))
            
    def stamp(self, tag):
        tag=tag[0:8]    #max 8byte as 16/2
        for i in range(8):
            data = ord(tag[i])
            data_L = data&0x0f
            data_H = (data>>4)&0x0f
            tmp = self.act_para[i*2].x_start
            tmp1 = ((tmp&0xff)>>4)%16
            tmp = (tmp&0xffffffff0)|((tmp1+data_L)%16)    #high 4bit+mask % 16
            self.act_para[i*2].x_start = tmp
            self.act_para[i*2].restruct()
            tmp = self.act_para[i*2+1].x_start
            tmp1 = ((tmp&0xff)>>4)%16
            tmp = (tmp&0xffffffff0)|((tmp1+data_H)%16)    #high 4bit+mask % 16
            self.act_para[i*2+1].x_start = tmp
            self.act_para[i*2+1].restruct()

class bn_parameter:
    def __init__(self, val, bin, oft):
        self.bin = bin
        self.oft = oft
        map=[24,32,4]; 
        (self.norm_mul, self.norm_add,self.norm_shift) = \
        helper_reg_data(val, map)
    def dump(self):
        print("norm_mul=0x%06x, norm_add=0x%08x, norm_shift=%2d"%(self.norm_mul, self.norm_add,self.norm_shift))
    def val(self):
        data = self.norm_mul | (self.norm_add<<24) | (self.norm_shift<<56)
        return data
    def restruct(self):
        data = self.norm_mul | (self.norm_add<<24) | (self.norm_shift<<56)
        #print("0x%X"%data)
        data = bytearray(data.to_bytes(8,'little'))
        self.bin[self.oft:self.oft+8]=data
        return data
            
class bn:
    def __init__(self, bin, oft, ch):
        self.oft = oft    
        self.size = kbn_arg.size
        self.bn_para = []
        for i in range(ch):
            bn_bin = bin[self.oft+i*kbn_arg.size:self.oft+(i+1)*kbn_arg.size]
            val = kbn_arg.unpack(bn_bin)
            self.bn_para.insert(i, bn_parameter(val[0], bin, oft+i*8))         
        return
    def dump(self):
        for i in range(len(self.bn_para)):
            self.bn_para[i].dump()
    def stamp(self, tag):
        tag=tag[0:8]    #max 8byte as 16/2
        for i in range(int(len(self.bn_para)/2)):
            data = ord(tag[i%8])
            data_L = data&0x0f
            data_H = (data>>4)&0x0f
            tmp = self.bn_para[i*2].norm_add
            tmp1 = ((tmp&0xff)>>4)%16
            tmp = (tmp&0xfffffff0)|((tmp1+data_L)%16)    #high 4bit+mask % 16
            self.bn_para[i*2].norm_add = tmp
            self.bn_para[i*2].restruct()
            tmp = self.bn_para[i*2+1].norm_add
            tmp1 = ((tmp&0xff)>>4)%16
            tmp = (tmp&0xfffffff0)|((tmp1+data_H)%16)    #high 4bit+mask % 16
            self.bn_para[i*2+1].norm_add = tmp
            self.bn_para[i*2+1].restruct()
    
class weights:
    def __init__(self, bin, oft, chi, cho, is_eight, kernel_type, is_dw):
        self.oft = oft
        self.kernel_type = kernel_type
        self.is_dw = is_dw
        self.is_eight = is_eight
        self.chi =chi
        self.cho = cho
        if kernel_type==1:        #3x3
            if is_eight:
                struct_func = kconv_3x3_8b_arg
            else:
                struct_func = kconv_3x3_16b_arg
            self.size = struct_func.size
            if is_dw==0:        #pw
                self.weights_para = [[[0 for i in range(9)] for i in range(chi)] for i in range(cho)] 
                for i in range(cho):
                    for j in range(chi):
                        w_bin = bin[self.oft+(i*chi+j)*self.size:self.oft+(i*chi+j+1)*self.size]
                        (self.weights_para[i][j][0], self.weights_para[i][j][1], 
                        self.weights_para[i][j][2], self.weights_para[i][j][3], 
                        self.weights_para[i][j][4], self.weights_para[i][j][5], 
                        self.weights_para[i][j][6], self.weights_para[i][j][7], 
                        self.weights_para[i][j][8])= struct_func.unpack(w_bin)
                self.cnt=cho*chi*9
            else:                #dw   
                self.weights_para = [[0 for i in range(9)] for i in range(chi)] 
                for i in range(cho):
                        w_bin = bin[self.oft+i*self.size:self.oft+(i+1)*self.size]
                        (self.weights_para[i][0], self.weights_para[i][1], 
                        self.weights_para[i][2], self.weights_para[i][3], 
                        self.weights_para[i][4], self.weights_para[i][5], 
                        self.weights_para[i][6], self.weights_para[i][7], 
                        self.weights_para[i][8])= struct_func.unpack(w_bin)
                self.cnt=cho*9
        else:                    #1x1
            if is_eight:
                struct_func = kconv_1x1_8b_arg
            else:
                struct_func = kconv_1x1_16b_arg
            self.size = struct_func.size    
            if is_dw==0:        #pw
                self.weights_para = [[[0 for i in range(1)] for i in range(chi)] for i in range(cho)] 
                for i in range(cho):
                    for j in range(chi):
                        w_bin = bin[self.oft+(i*chi+j)*self.size:self.oft+(i*chi+j+1)*self.size]
                        (self.weights_para[i][j][0])= struct_func.unpack(w_bin)
                self.cnt=cho*chi*1
            else:                #dw
                self.weights_para = [[0 for i in range(1)] for i in range(chi)] 
                for i in range(cho):
                        w_bin = bin[self.oft+i*self.size:self.oft+(i+1)*self.size]
                        (self.weights_para[i][0])= struct_func.unpack(w_bin)
                self.cnt=cho*1
        return
    def dump(self):
        print(self.weights_para)
    def stamp(self, tag):
        tag=tag[0:8]    #max 8byte as 16/2
        for i in range(int(len(self.cnt)/64)):
            for j in range(64):
                data = ord(tag[j>>3])
                data = (data>>(j%8))&0x01
                #tmp = self.weights_para[i*2]
                #self.weights_para[i*2].restruct()
            
        
        
def kl_k210_conv_dump(self):
    print('--------------------------------------------------'
    '--------------------------------------------------')
    for name,value in vars(self.conv_layer_para).items():
        if hasattr(value ,'dump'):
            print('%s:'%(name), end=' ')
            value.dump()
        else:
            print('%s=%d'%(name,value))
    print('--------------------------------------------------'
    '--------------------------------------------------')
    return
    
def kl_k210_conv_init(self, bin):
    arg_bin = bin[0:kconv_layer.size]
    (self.flags, self.main_mem_out_address, 
    self.layer_offset, self.weights_offset, 
    self.bn_offset, self.act_offset) = \
    kconv_layer.unpack(arg_bin)
    print("layer_offset=0x%x, weights_offset=0x%x, bn_offset=0x%x, act_offset=0x%x"%(self.layer_offset, self.weights_offset, self.bn_offset, self.act_offset))
    self.conv_layer_para = conv_layer_para(self.bin, self.layer_offset)
    print("is_eight=%d, chi=%d, cho=%d, dw=%d"%(self.is_eight, self.conv_layer_para.image_channel_num.i_ch_num+1, self.conv_layer_para.image_channel_num.o_ch_num+1,self.conv_layer_para.interrupt_enabe.depth_wise_layer))
    print("stride=%d, pool=%d, kernel=%d(0:1x1,1:3x3)"%(self.conv_layer_para.kernel_pool_type_cfg.first_stride, self.conv_layer_para.kernel_pool_type_cfg.pool_type,self.conv_layer_para.kernel_pool_type_cfg.kernel_type))
    self.act_table= act_table(self.bin, self.act_offset)
    self.bn = bn(self.bin, self.bn_offset, self.conv_layer_para.image_channel_num.o_ch_num+1)
    self.weights = weights(self.bin, self.weights_offset, self.conv_layer_para.image_channel_num.i_ch_num+1, self.conv_layer_para.image_channel_num.o_ch_num+1, self.is_eight, self.conv_layer_para.kernel_pool_type_cfg.kernel_type, self.conv_layer_para.interrupt_enabe.depth_wise_layer)
    self.dump=kl_k210_conv_dump
    #self.dump(self)
    return
    
def kl_k210_add_padding_init(self, bin):
    return
def kl_k210_remove_padding_init(self, bin):
    return
def kl_k210_upload_init(self, bin):
    return
    
layer_dict = {
    KL_INVALID                             :kl_invalid_init,
    KL_ADD                                :kl_add_init,
    KL_QUANTIZED_ADD                    :kl_quantized_add_init,
    KL_GLOBAL_MAX_POOL2D                :kl_global_max_pool2d_init,
    KL_QUANTIZED_GLOBAL_MAX_POOL2D        :kl_quantized_global_max_pool2d_init,
    KL_GLOBAL_AVERAGE_POOL2D            :kl_global_average_pool2d_init,
    KL_QUANTIZED_GLOBAL_AVERAGE_POOL2D    :kl_quantized_global_average_pool2d_init,
    KL_MAX_POOL2D                        :kl_max_pool2d_init,
    KL_QUANTIZED_MAX_POOL2D                :kl_quantized_max_pool2d_init,
    KL_AVERAGE_POOL2D                    :kl_average_pool2d_init,
    KL_QUANTIZED_AVERAGE_POOL2D            :kl_quantized_average_pool2d_init,
    KL_QUANTIZE                            :kl_quantize_init,
    KL_DEQUANTIZE                        :kl_dequantize_init,
    KL_REQUANTIZE                        :kl_requantize_init,
    KL_L2_NORMALIZATION                    :kl_l2_normalization_init,
    KL_SOFTMAX                            :kl_softmax_init,
    KL_CONCAT                            :kl_concat_init,
    KL_QUANTIZED_CONCAT                    :kl_quantized_concat_init,
    KL_FULLY_CONNECTED                    :kl_fully_connected_init,
    KL_QUANTIZED_FULLY_CONNECTED        :kl_quantized_fully_connected_init,
    KL_TENSORFLOW_FLATTEN                :kl_tensorflow_flatten_init,
    KL_QUANTIZED_TENSORFLOW_FLATTEN        :kl_quantized_tensorflow_flatten_init,
    KL_K210_CONV                         :kl_k210_conv_init,
    KL_K210_ADD_PADDING                    :kl_k210_add_padding_init,
    KL_K210_REMOVE_PADDING                :kl_k210_remove_padding_init,
    KL_K210_UPLOAD                        :kl_k210_upload_init
}


class kmodel_header:
    def __init__(self, kmodel_bin):
        self.oft = 0    
        self.size = kheader.size
        kmodel_header_bin = kmodel_bin[self.oft:self.oft+self.size]
        (self.version, self.flags, self.arch, self.layers_length, 
        self.max_start_address, self.main_mem_usage, self.output_count) = \
        kheader.unpack(kmodel_header_bin)

class kmodel_output:
    def __init__(self, kmodel_outputs_bin, base, i):
        self.oft = base+i*koutput.size
        self.size = koutput.size
        (self.addr, self.size) = koutput.unpack(kmodel_outputs_bin[i*koutput.size:(i+1)*koutput.size])
        
        
class kmodel_outputs:
    def __init__(self, kmodel_bin, base, output_count):
        self.oft = base
        self.size = output_count*koutput.size
        kmodel_outputs_bin = \
        kmodel_bin[self.oft:self.oft+self.size]
        self.output=[]
        for i in range(output_count):
            self.output.insert(i,kmodel_output(kmodel_outputs_bin, self.oft, i))


class kmodel_layer_header:
    def __init__(self, kmodel_bin, base, i):
        self.oft = base+i*klayer_header.size
        self.bin = kmodel_bin
        self.layer_index = i
        self.size = klayer_header.size
        (self.type, self.body_size) = klayer_header.unpack(kmodel_bin[self.oft:self.oft+klayer_header.size])
    def enc(self):
        new_type = self.type ^ ((self.layer_index+1) *0x103)
        new_header = klayer_header.pack(new_type, self.body_size)
        data = new_header  #bytearray(new_header.to_bytes(8,'little'))
        self.bin[self.oft:self.oft+klayer_header.size] = data
            
class kmodel_layer_headers:
    def __init__(self, kmodel_bin, base, layers_length):
        self.oft = base
        self.bin = kmodel_bin
        self.length = layers_length
        self.size = layers_length*klayer_header.size
        kmodel_layer_headers_bin = \
        kmodel_bin[self.oft:self.oft+self.size]
        self.layer_header=[]
        for i in range(layers_length):
            self.layer_header.insert(i,kmodel_layer_header(kmodel_bin, self.oft, i))
    def enc(self):
        for i in range(self.length):
            self.layer_header[i].enc()
        
class kmodel_layer:
    def __init__(self, kmodel_bin, base, type, body_size, is_eight):
        self.oft = base
        self.size = body_size
        self.type = type
        self.bin = kmodel_bin
        self.is_eight = is_eight
        body_bin = kmodel_bin[self.oft:self.oft+self.size]
        name = layer_name_dict.get(type, "invalid layer")
        print("type: %d, name: %s"%(type,name))
        layer_dict.get(type, kl_invalid_init)(self, body_bin) 

#layout

#kmodel_header
#kmodel_outputs
#    output[0]
#    output[1]
#    ……
#kmodel_layer_headers
#    layer_header[0]
#    layer_header[1]
#    ……
#body_start
#    layer[0]
#    layer[1]

    
class kmodel3:
    def __init__(self, fname):
        with open(fname, 'rb') as f:
            self.kmodel_bin = f.read()
            self.kmodel_bin = bytearray(self.kmodel_bin)
            self.header = kmodel_header(self.kmodel_bin)
            if self.header.version != 3 :
                print("not support version %d"%self.header.version)
                return
            self.is_eight = self.header.flags & 1;
            self.output_count = self.header.output_count
            self.layers_length = self.header.layers_length
            print("total layer count:%d, weight bits:%d"%(self.layers_length,16-8*self.is_eight))
            self.outputs = kmodel_outputs(self.kmodel_bin, self.header.oft+self.header.size, self.output_count)
            self.layer_headers = kmodel_layer_headers(self.kmodel_bin, self.outputs.oft + self.outputs.size, self.layers_length)
            self.body_start = self.layer_headers.oft + self.layer_headers.size
            self.layer = []
            current_body = self.body_start
            for i in range(self.layers_length):
                layer_header = self.layer_headers.layer_header[i]
                type = layer_header.type
                body_size = layer_header.body_size
                print("###Layer %d @0x%x"%(i, current_body))
                self.layer.insert(i,kmodel_layer(self.kmodel_bin, current_body, type, body_size, self.is_eight))
                current_body += body_size

        
        
print("usage: kenc.py xxx.kmodel")
kmodel = kmodel3(sys.argv[1])
tag="SIPEED!!"
tag_bytearray=b'SIPEED!!'


print("start enc model!")
kmodel.layer_headers.enc()


fname_new = sys.argv[1]+'.enc'
with open(fname_new, 'wb') as f:
    f.write(kmodel.kmodel_bin)
print("enc ok!")

    
    
    
    
    